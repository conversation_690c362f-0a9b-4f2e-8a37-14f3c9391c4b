<rep>
  <form>alibraries</form>
  <source>alibraries</source>
  <caption>Library Catalog - by Composer</caption>
  <classfile>LibraryCatalogByComposer/LibraryCatalogByComposer.php</classfile>
  <classname>customer\cal\reports\LibraryCatalogByComposer\LibraryCatalogByComposer</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Library Catalog - by Composer</title>
      <notes>Run for any set of Library entries to produce a catalog of holdings, grouped by Composer</notes>
      <report_title>Library Catalog - by Composer</report_title>
      <Col_1>Shelf No.</Col_1>
      <Col_2>Master Title</Col_2>
      <Col_3>Dur</Col_3>
      <Col_4>Work Instrumentation</Col_4>
      <Col_5>Publisher</Col_5>
      <Col_6>P</Col_6>
      <Col_7>S</Col_7>
      <Col_8>L*</Col_8>
      <printed>Printed: </printed>
      <legend>* P = Parts in Library | S = String Masters | L = Permanent Loan</legend>
    </default>
  </text>
  <icon>rtf</icon>
</rep>
