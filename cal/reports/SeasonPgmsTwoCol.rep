<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Season Programs - two column layout</caption>
  <classfile>SeasonPgmsTwoCol/SeasonPgmsTwoCol.php</classfile>
  <classname>customer\cal\reports\SeasonPgmsTwoCol\SeasonPgmsTwoCol</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Season Programs - two-column layout</title>
      <notes>Run for any set of PERFORMANCES in a single SEASON. The report shows each Project with Conductor, Soloists
        and Repertoire in a two-column format
      </notes>
      <report_title>Season Programs</report_title>
      <programno>Program</programno>
    </default>
    <australia>
      <title>Season Programs - two-column layout</title>
      <notes>Run for any set of PERFORMANCES in a single SEASON. The report shows each Project with Conductor, Soloists
        and Repertoire in a two-column format
      </notes>
      <report_title>Season Programs</report_title>
      <programno>Program</programno>
    </australia>
    <canada>
      <title>Season Programmes - two-column layout</title>
      <notes>Run for any set of PERFORMANCES in a single SEASON. The report shows each Project with Conductor, Soloists
        and Repert<PERSON> in a two-column format
      </notes>
      <report_title>Season Programmes</report_title>
      <programno>Programme</programno>
    </canada>
    <german>
      <title>Saisonprogramme - zweispaltiges</title>
      <notes>Führen Sie den Bericht für eine beliebige Anzahl von AUFFÜHRUNGEN in einer einzigen SAISON aus. Der Bericht
        zeigt jedes Projekt mit Dirigent, Solisten und Repertoire in einem zweispaltigen Format
      </notes>
      <report_title>Konzertprogramme in der Saison</report_title>
      <programNo_Prefix>- Programm</programNo_Prefix>
    </german>

  </text>
  <icon>rtf</icon>
</rep>
