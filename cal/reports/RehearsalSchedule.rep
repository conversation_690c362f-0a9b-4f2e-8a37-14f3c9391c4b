<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Rehearsal Schedule</caption>
  <classfile>RehearsalSchedule/RehearsalSchedule.php</classfile>
  <classname>customer\cal\reports\RehearsalSchedule\RehearsalSchedule</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Rehearsal Schedule by Week - large type for posting</title>
      <notes>Run for any number of Orchestra Events, typically within a single Week. The report shows the rehearsal order for each day, and programs for the Week.</notes>
      <report_title>REHEARSAL SCHEDULE</report_title>
      <posted>Posted</posted>
      <week>WEEK:</week>
      <date_format>D. M d</date_format>
      <with_soloist>(with soloist)</with_soloist>
      <program_header>Programs for the Week</program_header>
    </default>
<german>
      <title>Probenplan nach Woche - große Schrift zum Veröffentlichen</title>
      <notes>Laufen Sie für eine beliebige Anzahl von Orchester-Events, normalerweise innerhalb einer einzigen Woche. Der Bericht zeigt die Probenreihenfolge für jeden Tag und die Programme für die Woche.</notes>
      <report_title>PROBENPLAN</report_title>
      <posted>bekannt geben</posted>
      <week>WOCHE:</week>
      <date_format>D. d-M</date_format>
      <with_soloist>(mit Solist)</with_soloist>
      <program_header>Programme für die Woche</program_header>
    </german>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
