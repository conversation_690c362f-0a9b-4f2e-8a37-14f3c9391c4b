<rep>
  <form>adates; adates_2; adates_3; adates_planning</form>
  <source>adates</source>
  <caption>Season Artistic Planning Calenda</caption>
  <classfile>DatesPlanningCalWithProgramsExcel/DatesPlanningCalWithProgramsExcel.php</classfile>
  <classname>customer\cal\reports\DatesPlanningCalWithProgramsExcel\DatesPlanningCalWithProgramsExcel</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Season Planning Schedule (Excel)</title>
      <notes>Run for any set of events to produce a schedule of events, programs and service/hour calculations</notes>
      <report_title>Season Artistic Planning Calendar</report_title>
<col_Week>Wk.</col_Week>
      <col_Weekday> </col_Weekday>
      <col_Date>Date</col_Date>
      <col_Period>Holiday/Period</col_Period>
      <col_Start>Start</col_Start>
      <col_End>End</col_End>
      <col_Project>Project</col_Project>
      <col_Event>Event</col_Event>
      <col_Performance>Perf?</col_Performance>
      <col_Conductor>Conductor</col_Conductor>
      <col_Soloist>Soloist(s)</col_Soloist>
      <col_Program>Repertoire</col_Program>
      <col_Instrumentation>Max. Instrumentation</col_Instrumentation>
      <col_Venue>Venue</col_Venue>
      <col_Text>Event Text</col_Text>
      <col_Hours>Hours</col_Hours>
      <col_Services>Svcs.</col_Services>
      <col_PLevel>Level</col_PLevel>
    </default>
  </text>
  <icon>excel</icon>
</rep>
