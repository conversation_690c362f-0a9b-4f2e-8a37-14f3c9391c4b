<rep>
  <form>adates; adates2; adates3; adates4</form>
  <source>adates</source>
  <caption>Month Schedule - List Format with Programs</caption>
  <classfile>MonthScheduleList/MonthScheduleList.php</classfile>
  <classname>customer\cal\reports\MonthScheduleList\MonthScheduleList</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Month Schedule - List Format with Programs</title>
      <notes>Run for any set of activities within a SINGLE SEASON. The report shows events for that month followed by programs for the month</notes>
      <report_title>MONTH SCHEDULE</report_title>
      <week_header>WK</week_header>
      <activity_header>ACTIVITY</activity_header>
      <personnel_header>PERSONNEL</personnel_header>
      <venue_header>LOCATION and NOTES</venue_header>
      <repertoire_header>Repertoire for the Month</repertoire_header>
      <pgm_prefix>Program </pgm_prefix>
      <encore>*** ENCORE ***</encore>
      <premiere>premiere</premiere>
      <maxInstLabel>Total:</maxInstLabel>
      <and>and</and>
    </default>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
