<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Instrumentation Sheet</caption>
  <classfile>InstrumentationSheet/InstrumentationSheet.php</classfile>
  <classname>customer\cal\reports\InstrumentationSheet\InstrumentationSheet</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Instrumentation Sheet</title>
      <notes>Run the report for any set of PERFORMANCES. The report produces a formatted Instrumentation Sheet for each Project</notes>
      <Report_Title>Instrumentation Sheet</Report_Title>
      <Total_Header>Total Instrumentation</Total_Header>
      <WBP_TOTAL>W/B/P</WBP_TOTAL>
      <STRINGS>Strings</STRINGS>
      <GR_TOTAL>Total</GR_TOTAL>
      <DURATION>Duration</DURATION>
      <programNo_Prefix>- Program</programNo_Prefix>
      <FL>Fl</FL>
      <OB>Ob</OB>
      <CL>Cl</CL>
      <BN>Bn</BN>
      <HN>Hn</HN>
      <TP>Tp</TP>
      <TB>Tb</TB>
      <TU>Tu</TU>
      <TIMP>T</TIMP>
      <PERC>Prc</PERC>
      <HARP>Hp</HARP>
      <KYBD>Kbd</KYBD>
      <EXTRA>Ext</EXTRA>
      <CHORUS>Chorus</CHORUS>
      <PERCUSSION>Percussion</PERCUSSION>
      <SOLO>Solo Instruments</SOLO>
      <Printed>Printed on</Printed>
    </default>
    <german>
      <title>Konzertprogramm</title>
      <notes>Führen Sie den Bericht für eine beliebige Anzahl von PERFORMANCES aus. Der Bericht erzeugt ein formatiertes Konzertprogramm für jedes Projekt</notes>
      <programNo_Prefix>- Programm</programNo_Prefix>
    </german>
  </text>
  <icon>rtf</icon>
</rep>
