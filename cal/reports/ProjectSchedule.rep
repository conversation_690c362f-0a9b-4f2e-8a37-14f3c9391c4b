<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Project Schedule</caption>
  <classname>ProjectSchedule</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Project Schedule - program and event schedule</title>
      <notes>OUTPUT: Word (.docx) // Report shows the Project program, timings, instrumentation and schedule // Run this for... SEASON: One | PROJECTS: any One Project | EVENTS: All events within the selected Project</notes>
      <work_table_duration_minutes_postfix>'</work_table_duration_minutes_postfix>
      <work_table_duration_seconds_postfix>''</work_table_duration_seconds_postfix>
      <report_title>Project Schedule</report_title>
      <heading_1>EVENT:</heading_1>
      <heading_2>CONCERT DATES:</heading_2>
      <heading_3>CONDUCTOR:</heading_3>
      <heading_4>GUEST ARTIST(S):</heading_4>
      <heading_5>ORCH. SIZE:</heading_5>
      <program_heading>PROGRAM</program_heading>
      <schedule_heading>PROJECT SCHEDULE</schedule_heading>
    </default>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
