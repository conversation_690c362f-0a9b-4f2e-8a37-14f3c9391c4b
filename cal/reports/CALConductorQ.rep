<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert Program</caption>
  <classfile>CALConductorQ/CALConductorQ.php</classfile>
  <classname>customer\cal\reports\CALConductorQ\CALConductorQ</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Calgary Phil - Guest Cond. Questionnaire</title>
      <notes>Run the report for any set of Events. The report produces the schedule portion of the Season Calendar book with Programmes and Schedules for each week.</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </default>
    <australia>
      <title>Calgary Phil - Season Calendar Book</title>
      <notes>Run the report for any set of Events. The report produces the schedule portion of the Season Calendar book with Programmes and Schedules for each week.</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </australia>
    <canada>
      <title>Calgary Phil - Guest Cond. Questionnaire</title>
      <notes>Run the report for any set of Events within a Single Project. The report prints a conductor questionnaire form</notes>
      <programNo_Prefix>- Programme</programNo_Prefix>
    </canada>
    <german>
      <title>Calgary Phil - Guest Cond. Questionnaire</title>
      <notes>Run the report for any set of Events within a Single Project. The report prints a conductor questionnaire form</notes>
      <programNo_Prefix>- Programme</programNo_Prefix>
    </german>
  </text>
  <icon>rtf</icon>
</rep>
