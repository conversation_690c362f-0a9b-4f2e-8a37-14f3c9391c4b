<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert Program</caption>
  <classfile>ConcertProgram/ConcertProgram.php</classfile>
  <classname>customer\cal\reports\ConcertProgram\ConcertProgram</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Concert Program</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted Concert Program for each Project</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </default>
    <australia>
      <title>Concert Program</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted Concert Program for each Project</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </australia>
    <canada>
      <title>Concert Programme</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted Concert Programme for each Project</notes>
      <programNo_Prefix>- Programme</programNo_Prefix>
    </canada>
    <german>
      <title>Konzertprogramm</title>
      <notes>Führen Sie den Bericht für eine beliebige Anzahl von PERFORMANCES aus. Der Bericht erzeugt ein formatiertes Konzertprogramm für jedes Projekt</notes>
      <programNo_Prefix>- Programm</programNo_Prefix>
    </german>
  </text>
  <icon>rtf</icon>
</rep>
