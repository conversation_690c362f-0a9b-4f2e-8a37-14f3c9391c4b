<?php
namespace Customer\cal\reports;

use App\Model\Entity\AdateWork;
use App\Model\Entity\AdateworkMovement;
use Cake\Core\Configure;
use Customer\cal\PHPWordHelper;
use Customer\cal\reports\ReportWordFas;
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;

class BaseConcertProgram extends ReportWordFas
{
    public function render($view = null, $layout = null): string
    {
        foreach ($this->projects as $project) {
            $this->renderToWord($project, $this->wordHelper);
        }

        // Create the file and return the filename
        $fileName = $this->_createFileName() . $this->getFileExtension();

        ob_end_clean();

        $this->wordHelper->write($fileName);

        return $fileName;
    }


    public function renderToWord($project, PHPWordHelper $wordHelper)
    {
        $section = $wordHelper->addSection();
        $this->renderPerformanceDatesOnTop($section, $project, $wordHelper);
        $this->renderOrchestraAndProgram($section, $project, $wordHelper);
        $this->renderConductorSoloist($section, $project, $wordHelper);
        $this->renderTitle($section, $project, $wordHelper);
        $this->renderDateWorks($section, $project, $wordHelper);
    }

    /**
     * Render the works, primarily in 3 columns
     * composer                 piece                         year
     *  (dates)                 movements....
     *                          soloists
     *
     * @param Section $section
     * @param $project
     * @param PHPWordHelper $wordHelper
     */
    public function renderDateWorks(Section $section, $project, PHPWordHelper $wordHelper)
    {
        $Adate = $this->reportHelper->getAdateInfoFromProjectRow($project);

        $table = $wordHelper->addTable($section);

        $works = $this->reportHelper->getWorksForAdateId($Adate->id);

        /** @var AdateWork $work */
        foreach ($works as $work) {
            if ($work->l_encore != 0) {
                continue;
            }
            // print only Work Title for Intermission
            if ((int)$work->swork->l_intermission === 1) {
                // row will still have 3 columns... address the second cell
                $row = $wordHelper->addRow($table);
                $string = trim($work->title2) !== '' ? $work->title2 : $work->swork->title1;
                $row->getCells()[0]->gridSpan = 3;
                // @fixme
                // need to orient the spacing to make
                // the center span all 3 cols in the row
                // adding just one cell in this row breaks
                // the rest of the row/cells in the table
                $wordHelper->addTableText(
                    $row,
                    1,
                    htmlspecialchars($string),
                    $this->reportFontDefault,
                    $this->reportParagraphCenter
                );

                // add another spacer...
                $wordHelper->addRow($table);

                continue;
            }

            // otherwise, use 3 column row for most info in this section
            $row = $wordHelper->addRow($table, PHPWordHelper::ROW_COLS_256015);

            $composerName = $this->reportHelper->getComposerNameFirstLast($work->swork->scomposer);

            // add work composer and arrangement info
            $composerName .= trim($work->swork->arrangement) ? " / " . $work->swork->arrangement : '';

            $finalString = '<b>' . htmlspecialchars($composerName) . '</b>';
            $finalString .= ' <br/> ';
            $finalString .= $this->reportHelper->getComposerYears($work->swork->scomposer);
            $finalString .= ' <br/> ';
            $wordHelper->addTableText(
                $row,
                0,
                $finalString,
                $this->reportFontDefault,
                $this->reportParagraphRight,
            );

            // add work title
            $mainWorks = trim($work->title2) !== '' ? $work->title2 : $work->swork->title1;
            if ($work->premiere_id) {
                $info = $this->reportHelper->getPremierInfo($work->premiere_id);

                $mainWorks .= " <i>- ".$info->name . ' premiere</i>';
            }
            // add main works info in middle cell for the row
            $wordHelper->addTableText(
                $row,
                1,
                $mainWorks,
                (new Font())->setSize(10)->setName('Calibri'), // for some reason, $reportFontDefault doesn't register here?
              $this->reportParagraphDefault,
            );

            // add movements for work below title - use separate fetch so paragraph can be indented
            $movements = $this->reportHelper->getMovementsForAdateWorkId($work->id);
            /* @var AdateworkMovement $movement */
            foreach ($movements as $movement) {
                $printMovement = $movement->name;
                $wordHelper->addTableText(
                    $row,
                    1,
                    $printMovement,
                    (new Font())->setSize(10)->setName('Calibri'), // for some reason, $reportFontDefault doesn't register here?
                  (new Paragraph())->setSpaceAfter(0)->setIndent(360)  // 720 twips = 1/2 inch
                );
            }

            // add soloists
            // these will be centered separately
            $soloistsList = '';
            $soloists = $this->reportHelper->getSoloistsForAdateWork($work->id);
            foreach ($soloists as $soloistInfo) {
                $soloist = $this->reportHelper->getSoloist($soloistInfo->artist_id);
                $soloistName = $this->reportHelper->getSoloistNameFirstLast($soloist);
                $instrument = $soloistInfo->sinstrinstrument->name ? ', '.$soloistInfo->sinstrinstrument->name : '';
                $soloistsList .=  $soloistName . $instrument . '<br/>';
            }

            // add the composition date
            $compositionYear = $this->reportHelper->getCompositionYear($work->swork);
            $wordHelper->addTableText(
                $row,
                2,
                $compositionYear,
                (new Font())->setSize(10)->setName('Calibri'), // for some reason, $reportFontDefault doesn't register here?
                $this->reportParagraphRight
            );

            //  empty blank row; removed to tighten up spacing
            //    $wordHelper->addRow($table);

            $wordHelper->addTableText($row, 0, '');
            $wordHelper->addTableText(
                $row,
                1,
                $soloistsList,
                (new Font())->setSize(10)->setName('Calibri'), // for some reason, $reportFontDefault doesn't register here?
                $this->tableCellCenterParagraph
            );
            $wordHelper->addTableText($row, 2, '');
            $wordHelper->addRow($table);
        }
    }


    public function renderTitle(Section $section, $project, PHPWordHelper $helper)
    {
        $Adate = $this->reportHelper->getAdateInfoFromProjectRow($project);

        $title = $Adate->programtitle ?? "";

        $helper->addSectionText(
            $section,
            '<i>' . htmlspecialchars($title) . '</i><br/>',
            $this->reportFontDefault,
            $this->reportParagraphCenter
        );
    }

    public function renderConductorSoloist(Section $section, $projectRow, PHPWordHelper $helper)
    {
        [
            'conductor' => $conductor,
            'conductorName' => $conductorName,
            'soloists' => $soloists
        ] = $this->reportHelper->getConductorAndSoloistsForProjectRow($projectRow);

        $helper->addSectionText(
            $section,
            '<b>' . htmlspecialchars($conductorName) . '</b>',
            $this->reportFontDefault,
            $this->reportParagraphDefault
        );

        foreach ($soloists as $soloist) {
            $string = $soloist['data']->name2Name1;
            $ins = [];
            foreach ($soloist['instruments'] as $instrument) {
                $ins[$instrument->name] = $instrument->name;
            }
            $string .= ", " . implode(", ", array_keys($ins));
            $helper->addSectionText(
                $section,
                $string,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );
        }
    }

    public function renderOrchestraAndProgram(Section $section, $project, PHPWordHelper $helper)
    {
        $Adate = $this->reportHelper->getAdateInfoFromProjectRow($project);
        $orchestra = $this->reportHelper->getOrchestraInfoById($Adate->orchestra_id);

        $string = $orchestra->name1;

        $helper->addSectionText(
            $section,
            htmlspecialchars($string),
            (new Font())->setSize(18)->setBold(false)->setName('Calibri'),
            $this->reportParagraphCenter
        );

        $string = $Adate->sproject->name;
        $helper->addSectionText(
            $section,
            htmlspecialchars($string),
            (new Font())->setSize(14)->setBold(false)->setName('Calibri'),
            $this->tableCellCenterParagraph
        );
        // blank line after project
        $section->addText(
            '  ',
            $this->reportFontDefault,
            $this->reportParagraphDefault
        );
    }

    /**
     * The 'collect' gives us all dates retrieved for a project/season
     * repeated on each 'project' object ('adate' entity?)
     * in 'adates' key
     *
     * @param Section $section
     * @param $project
     * @param PHPWordHelper $helper
     */
    public function renderPerformanceDatesOnTop(Section $section, $project, PHPWordHelper $helper)
    {
        $topDateFormat = Configure::read('Formats.programHeaderDate');
        $topTimeFormat = Configure::read('Formats.programHeaderTime');

        foreach ($project['adates'] as $row) {
            if ($row->seventtype->l_performance == 1) {
                $string = $row->date_->format($topDateFormat);
                $string .= ' ';
                $string .= 'at ' . $row->start_->format($topTimeFormat);
                $string .= ' - ';
                $string .= $row->locationaddress->name1;

                $helper->addSectionText(
                    $section,
                    '<i>' . $string . '</i>',
                    $this->reportFontDefault,
                    $this->reportParagraphCenter,
                );
            }
        }
        // blank line after concert dates
        $section->addText(
            '  ',
            $this->reportFontDefault,
            $this->reportParagraphDefault
        );
    }
}
