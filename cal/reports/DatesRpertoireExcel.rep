<rep>
  <form>adates; adates2; adates3; adates4</form>
  <source>adates</source>
  <caption>Repertoire/Programming details (Excel)</caption>
  <classfile>DatesRepertoireExcel/DatesRepertoireExcel.php</classfile>
  <classname>customer\cal\reports\DatesRepertoireExcel\DatesRepertoireExcel</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Repertoire/Programming details (Excel)</title>
      <notes>Run for any set of PERFORMANCES. The report shows each work performed with instrumentation, performer and other details</notes>
      <report_title>Repertoire Detail</report_title>
      <report_subTitle>Use the Excel filter and sort features to manage this table</report_subTitle>
      <col_Project>Project</col_Project>
      <col_Composer>Composer</col_Composer>
      <col_Title1>Master Title</col_Title1>
      <col_Title2>Print Title</col_Title2>
      <col_Arranger>Arrangement</col_Arranger>
      <col_Duration>Dur.</col_Duration>
      <col_Perf>First Perf.</col_Perf>
      <col_Reh>First Reh.</col_Reh>
      <col_Winds>Winds</col_Winds>
      <col_Brass>Brass</col_Brass>
      <col_Timp>Timp/Perc/Harp</col_Timp>
      <col_Strings>Strings</col_Strings>
      <col_Keyboard>Kybd</col_Keyboard>
      <col_Extra>Extra</col_Extra>
      <col_Chorus>Chorus</col_Chorus>
      <col_Solo>Solo Inst.</col_Solo>
      <col_Size>Ens. Size</col_Size>
      <col_Title>Program Title</col_Title>
      <col_Conductor>Conductor</col_Conductor>
      <col_Soloist>Soloist(s)</col_Soloist>
      <col_Library>In Library?</col_Library>
      <col_LibraryInfo>Shelf-Collection-Publisher</col_LibraryInfo>
    </default>
    <australia>
      <title>Repertoire/Programming details (Excel)</title>
      <notes>Run for any set of PERFORMANCES. The report shows each work performed with instrumentation, performer and other details</notes>
      <report_title>Repertoire Detail</report_title>
      <report_subTitle>Use the Excel filter and sort features to manage this table</report_subTitle>
      <col_Project>Project</col_Project>
      <col_Composer>Composer</col_Composer>
      <col_Title1>Master Title</col_Title1>
      <col_Title2>Print Title</col_Title2>
      <col_Arranger>Arrangement</col_Arranger>
      <col_Duration>Dur.</col_Duration>
      <col_Perf>First Perf.</col_Perf>
      <col_Reh>First Reh.</col_Reh>
      <col_Winds>Winds</col_Winds>
      <col_Brass>Brass</col_Brass>
      <col_Timp>Timp/Perc/Harp</col_Timp>
      <col_Strings>Strings</col_Strings>
      <col_Keyboard>Kybd</col_Keyboard>
      <col_Extra>Extra</col_Extra>
      <col_Chorus>Chorus</col_Chorus>
      <col_Solo>Solo Inst.</col_Solo>
      <col_Size>Ens. Size</col_Size>
      <col_Title>Program Title</col_Title>
      <col_Conductor>Conductor</col_Conductor>
      <col_Soloist>Soloist(s)</col_Soloist>
      <col_Library>In Library?</col_Library>
      <col_LibraryInfo>Shelf-Collection-Publisher</col_LibraryInfo>
    </australia>
    <canada>
      <title>Repertoire/Programming details (Excel)</title>
      <notes>Run for any set of PERFORMANCES. The report shows each work performed with instrumentation, performer and other details</notes>
      <report_title>Repertoire Detail</report_title>
      <report_subTitle>Use the Excel filter and sort features to manage this table</report_subTitle>
      <col_Project>Project</col_Project>
      <col_Composer>Composer</col_Composer>
      <col_Title1>Master Title</col_Title1>
      <col_Title2>Print Title</col_Title2>
      <col_Arranger>Arrangement</col_Arranger>
      <col_Duration>Dur.</col_Duration>
      <col_Perf>First Perf.</col_Perf>
      <col_Reh>First Reh.</col_Reh>
      <col_Winds>Winds</col_Winds>
      <col_Brass>Brass</col_Brass>
      <col_Timp>Timp/Perc/Harp</col_Timp>
      <col_Strings>Strings</col_Strings>
      <col_Keyboard>Kybd</col_Keyboard>
      <col_Extra>Extra</col_Extra>
      <col_Chorus>Chorus</col_Chorus>
      <col_Solo>Solo Inst.</col_Solo>
      <col_Size>Ens. Size</col_Size>
      <col_Title>Programme Title</col_Title>
      <col_Conductor>Conductor</col_Conductor>
      <col_Soloist>Soloist(s)</col_Soloist>
      <col_Library>In Library?</col_Library>
      <col_LibraryInfo>Shelf-Collection-Publisher</col_LibraryInfo>
    </canada>
  </text>
  <icon>excel</icon>
</rep>
