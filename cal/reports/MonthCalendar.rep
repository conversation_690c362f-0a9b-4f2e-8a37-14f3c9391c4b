<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Month Calendar (by Week)</caption>
  <classfile>MonthCalendar/MonthCalendar.php</classfile>
  <classname>customer\cal\reports\MonthCalendar\MonthCalendar</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Month Calendar with Programs and Week Service Totals</title>
<week_column>WK</week_column>
<service_column>SVC</service_column>
<time_format>g:i A</time_format>
<start_time_format>g:i</start_time_format>
<pgm_prefix>Pgm </pgm_prefix>
<cond_prefix> (</cond_prefix>
<cond_suffix>)</cond_suffix>
<venue_prefix>/</venue_prefix>
<venue_suffix> </venue_suffix>
<event_separator>: </event_separator>
      <printed>Printed</printed>
      <notes>Run for any set of activities within a Single Season.</notes>
    </default>
  </text>
  <icon>rtf</icon>
</rep>
