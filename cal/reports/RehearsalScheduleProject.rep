<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Rehearsal Schedule</caption>
  <classfile>RehearsalScheduleProject/RehearsalScheduleProject.php</classfile>
  <classname>customer\cal\reports\RehearsalScheduleProject\RehearsalScheduleProject</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Rehearsal Schedule by Project</title>
      <notes>Run for any number of Orchestra Events, typically within a single Project. The report shows the rehearsal order for each day, and program/instrumentation for the Project.</notes>
      <report_title>REHEARSAL SCHEDULE</report_title>
      <posted>As of</posted>
      <week>WEEK:</week>
      <date_format>l, F j</date_format>
      <with_soloist>(with soloist)</with_soloist>
      <schedule_header>Schedule of Activities</schedule_header>
      <program_header>Concerts and Program</program_header>
      <concert_total>Concert Total</concert_total>
    </default>
<german>
  <title>Probenzeitplan nach Projekt</title>
  <notes>Wird für eine beliebige Anzahl von Orchesterveranstaltungen ausgeführt, in der Regel innerhalb eines einzigen Projekts. Der Bericht zeigt die Reihenfolge der Proben für jeden Tag und das Programm/die Instrumentierung für das Projekt.</notes>
  <report_title>PROBENPLAN</report_title>
  <posted>Gedruckt am:</posted>
  <week>WOCHE:</week>
  <date_format>l, j F</date_format>
  <with_soloist>(mit Solist)</with_soloist>
  <schedule_header>Zeitplan der Aktivitäten</schedule_header>
  <program_header>Konzerte und Programm</program_header>
  <concert_total>Konzert Gesamt</concert_total>
    </german>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
