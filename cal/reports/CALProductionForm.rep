<rep>
  <form>adates; adates2; adates3; adates4</form>
  <source>adates</source>
  <caption>Date Export to Excel</caption>
  <classfile>CALProductionForm/CALProductionForm.php</classfile>
  <classname>customer\cal\reports\CALProductionForm\CALProductionForm</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Calgary Phil - Production Form</title>
      <notes>Run for any set of Events within a Single Project</notes>
      <report_title>Date Export</report_title>
	    <col_Date>Date</col_Date>
      <col_Month>Month</col_Month>
      <col_Week>Week</col_Week>
      <col_Weekday>Day</col_Weekday>
      <col_Start>Start</col_Start>
      <col_End>End</col_End>
      <col_Project>Project</col_Project>
      <col_Event>Activity</col_Event>
      <col_Performance>Perf?</col_Performance>
	    <col_Venue>Venue</col_Venue>
      <col_VenueCode>Ven. Code</col_VenueCode>
      <col_Conductor>Conductor</col_Conductor>
      <col_Soloist>Soloist(s)</col_Soloist>
      <col_Program>Repertoire</col_Program>
	    <col_Orchestra>Orchestra/Ensemble</col_Orchestra>
      <col_Instrumentation>Max Instrumentation</col_Instrumentation>
	    <col_Text>Text</col_Text>
	    <col_Title>Program Title</col_Title>
      <col_PgmNo>Pgm. No</col_PgmNo>
	    <col_Season>Season</col_Season>
	    <col_Status>Event Status</col_Status>
	    <col_Dress>Attire</col_Dress>
	    <col_Notes>Notes</col_Notes>
    </default>
  </text>
  <icon>excel</icon>
</rep>
