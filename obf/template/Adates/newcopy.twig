{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Adates/edit.twig' %}

{% set edit = false %}
{% set newcopy = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-calendar',
    'icon': 'fa-plus',
    'headline': {0: __('adates') ~ ' ('~ __('adates.planninglevel') ~' '~ planninglevel ~')', 1: __('new+copy')}
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: currentController,
    1: {
      1: 'list',
      2: 'new',
      3: {'save': {
        'disabled': true,
        'data-bind': 'enable: formChanged, event: {click: submitForm}'}},
      4: {'revert': {
        'disabled': true,
        'data-bind': "enable: formChanged, click: function(){uiService.showModal('confirmRevertForm')}"}}
    },
    2: adate.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-plus', 1: __('new+copy')} %}
{% endblock %}

{% block form_definition %}
  {{ Oform.create(adate, {
    'url': '/'~ currentController ~'/newcopy/' ~ ancestorId,
    'name': 'AdatesForm',
    'id': 'AdatesForm',
    'class': 'smart-form',
    'method': 'post'
  }) | raw }}
{% endblock %}
