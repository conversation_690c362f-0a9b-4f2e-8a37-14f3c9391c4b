{% extends '../Index/index.twig' %}


{% block calendar_widget %}
  {% embed '../Index/widgets/calendar.twig' with {widget_id: 1,
    hidden: userUiSettings['Plugin_settings__widget-grid']['widget'][0]['hidden']} %}

    {% block jstemplates %}
      {% embed '../Calendar/jstemplates.twig' %}
        {% block adatesTextMonthTemplate %}
          <script type="text/x-jsrender" id="adates_text_month_template">
            <div class="event-text-month">
              <%if event.extendedProps.model.programtitle() %>
                <%: event.extendedProps.model.programtitle() + '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.project_id() && event.extendedProps.model.sproject.code() %>
                <%: event.extendedProps.model.sproject.code() %>
              <%else event.extendedProps.model.project_id() && event.extendedProps.model.sproject.name() %>
                <%: event.extendedProps.model.sproject.name() %>
              <%/if%>

              <%if event.extendedProps.model.project_id() %>
                <%: ':' %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.code() %>
                <%: event.extendedProps.model.seventtype.code() %>
              <%else event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.name() %>
                <%: event.extendedProps.model.seventtype.name() %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() || event.extendedProps.model.project_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() %>
                <%: event.extendedProps.model.conductoraddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() && event.extendedProps.model.location_id() %>
                <%: ' / ' %>
              <%/if%>

              <%if event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.code() %>
                <%: event.extendedProps.model.locationaddress.code() %>
              <%else event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.name1() %>
                <%: event.extendedProps.model.locationaddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() || event.extendedProps.model.location_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.text() !== null && event.extendedProps.model.text() !== '' %>
                <%: event.extendedProps.model.text() %>
              <%/if%>
            </div>
          </script>
        {% endblock %}

        {% block adatesTextWeekTemplate %}
          <script type="text/x-jsrender" id="adates_text_week_template">
            <div class="event-text-week">
              <%if event.extendedProps.model.programtitle() %>
                <%: event.extendedProps.model.programtitle() + '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.project_id() && event.extendedProps.model.sproject.code() %>
                <%: event.extendedProps.model.sproject.code() %>
              <%else event.extendedProps.model.project_id() && event.extendedProps.model.sproject.name() %>
                <%: event.extendedProps.model.sproject.name() %>
              <%/if%>

              <%if event.extendedProps.model.project_id() %>
                <%: ':' %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.code() %>
                <%: event.extendedProps.model.seventtype.code() %>
              <%else event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.name() %>
                <%: event.extendedProps.model.seventtype.name() %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() || event.extendedProps.model.project_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() %>
                <%: event.extendedProps.model.conductoraddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() && event.extendedProps.model.location_id() %>
                <%: ' / ' %>
              <%/if%>

              <%if event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.code() %>
                <%: event.extendedProps.model.locationaddress.code() %>
              <%else event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.name1() %>
                <%: event.extendedProps.model.locationaddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() || event.extendedProps.model.location_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.text() !== null && event.extendedProps.model.text() !== '' %>
                <%: event.extendedProps.model.text() %>
              <%/if%>
            </div>
          </script>
        {% endblock %}

        {% block adatesTextDayTemplate %}
          <script type="text/x-jsrender" id="adates_text_day_template">
            <div class="event-text-day">
              <%if event.extendedProps.model.programtitle() %>
                <%: event.extendedProps.model.programtitle() + '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.project_id() && event.extendedProps.model.sproject.code() %>
                <%: event.extendedProps.model.sproject.code() %>
              <%else event.extendedProps.model.project_id() && event.extendedProps.model.sproject.name() %>
                <%: event.extendedProps.model.sproject.name() %>
              <%/if%>

              <%if event.extendedProps.model.project_id() %>
                <%: ':' %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.code() %>
                <%: event.extendedProps.model.seventtype.code() %>
              <%else event.extendedProps.model.eventtype_id() && event.extendedProps.model.seventtype.name() %>
                <%: event.extendedProps.model.seventtype.name() %>
              <%/if%>

              <%if event.extendedProps.model.eventtype_id() || event.extendedProps.model.project_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() %>
                <%: event.extendedProps.model.conductoraddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() && event.extendedProps.model.location_id() %>
                <%: ' / ' %>
              <%/if%>

              <%if event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.code() %>
                <%: event.extendedProps.model.locationaddress.code() %>
              <%else event.extendedProps.model.location_id() && event.extendedProps.model.locationaddress.name1() %>
                <%: event.extendedProps.model.locationaddress.name1() %>
              <%/if%>

              <%if event.extendedProps.model.conductor_id() || event.extendedProps.model.location_id() %>
                <%: '<br/>' %>
              <%/if%>

              <%if event.extendedProps.model.text() !== null && event.extendedProps.model.text() !== '' %>
                <%: event.extendedProps.model.text() %>
              <%/if%>
            </div>
          </script>
        {% endblock %}
      {% endembed %}
      {# end embed jstemplate #}
    {% endblock %}
    {#endblock jstemplates#}
  {% endembed %}
  {#endembed calendar.twig#}
{% endblock %}
{#endblock calendar_widget#}
