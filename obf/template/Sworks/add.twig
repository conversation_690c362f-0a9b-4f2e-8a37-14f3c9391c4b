{% extends '../Sworks/add.twig' %}

{% block tab1_content %}
  <div class="col col-xs-12">
    <fieldset>
      <div class="row">
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('composer_id', {
              'class': 'select2',
              'disabled': (disabled) ? true : false,
              'empty': '-',
              'label': __('sworks.composer_id'),
              'options': scomposers,
            }) | raw }}
          </label>
        </section>
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('title1', {
              'disabled': (disabled) ? true : false,
              'label': __('sworks.title1'),
            }) | raw }}
          </label>
        </section>

      </div>

      <div class="row">

        <section class="col col-6">
          <label class="input">
            {{ Oform.input('title2', {
              'disabled': (disabled) ? true : false,
              'label': __('sworks.title2'),
            }) | raw }}
          </label>
        </section>

        <section class="col col-6">
          <label class="input">
            {{ Oform.input('title3', {
              'disabled': (disabled) ? true : false,
              'label': __('sworks.title3'),
            }) | raw }}
          </label>
        </section>
      </div>

      <div class="row">

        <section class="col col-6">
          {{ Oform.input('arrangement', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.arrangement'),
          }) | raw }}
        </section>

        <section class="col col-6">
          <opas-tags
            params="controller: 'SWORKS', tags: {{ tags|json_encode }}, selectedIds: {{ swork.tags|column('id')|json_encode }}, disabled: {{ disabled ? 'true' : 'false' }}"></opas-tags>
        </section>

      </div>

      <div class="row">
        <section class="col col-4">
          <!-- DURATION -->
          {{ Oform.input('duration', {
            'class': 'inputmask-duration-long',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.duration'),
          }) | raw }}
        </section>

        <section class="col col-4">
          <!-- Composed from -->
          {{ Oform.input('compyear', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.compyear'),
          }) | raw }}
        </section>

        <section class="col col-4">
          <!-- Composed to -->
          {{ Oform.input('compyear2', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.compyear2'),
          }) | raw }}
        </section>
      </div>

      <div class="row">
        <section class="col col-4">
          <!-- Opus / Cat. -->
          {{ Oform.input('catalog', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.catalog'),
          }) | raw }}
        </section>

        <section class="col col-4">
          {{ Oform.input('compyearstatus', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.compyearstatus')
          }) | raw }}
        </section>
      </div>

    </fieldset>

    <fieldset>
      <div class="row">
        {% embed '../Components/Sworks/sworks_movements.twig' with {'rights': 'A'} %}{% endembed %}
        <sworks-movements-component params="parent_id:{{ swork.id }}">
          <span>{{ __('loading') }} ...</span>
        </sworks-movements-component>
      </div>
    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col-6">
          {{ Oform.input('notes', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.notes'),
          }) | raw }}
        </section>
        <section class="col col-6">
          {{ Oform.input('key_', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.key_'),
          }) | raw }}
        </section>
      </div>

      <div class="row">
        <section class="col col-6">
          {{ Oform.input('genre_id', {
            'class': 'select2',
            'data-listlength': sworkgenres|length,
            'disabled': (disabled) ? true : false,
            'empty': '-',
            'label': __('sworks.genre_id'),
            'options': sworkgenres,
          }) | raw }}
        </section>
        <section class="col col-6">
          {{ Oform.input('style_id', {
            'class': 'select2',
            'data-listlength': sworkstyles|length,
            'disabled': (disabled) ? true : false,
            'empty': '-',
            'label': __('sworks.style_id'),
            'options': sworkstyles,
          }) | raw }}
        </section>
      </div>
    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('mark', {
              'disabled': (disabled) ? true : false,
              'label': __('sworks.mark'),
            }) | raw }}
          </label>
        </section>
        <section class="col col-4 mt-18">
          {% embed '../Element/Snippets/toggle_yes_no.twig' with {'name': 'l_activated',
            'disabled': disabled, 'value': swork.l_activated, 'label': __('activated')} %}
          {% endembed %}
        </section>
        <section class="col col-4 mt-18">
          {% embed '../Element/Snippets/toggle_yes_no.twig' with {'name': 'l_lock',
            'disabled': disabled, 'value': swork.l_lock, 'label': __('sworks.l_lock')} %}
          {% endembed %}
        </section>
      </div>
    </fieldset>
  </div>
{% endblock %}

{# tab2 content #}
{% block tab2_instrumentation %}
  <fieldset>
    <div class="row">
      <div>
        <section class="col col-1">
          {{ Oform.input('flute', {
            'data-bind': 'numeric, bindValue: flute',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.flute'),
            'type': 'text',
            'value': (swork.flute is null) ? null : swork.flute,
          }) | raw }}
        </section>
        <section class="col col-2">
          {{ Oform.input('flute_text', {
            'data-bind': 'bindValue: flute_text',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.flute_text'),
          }) | raw }}
        </section>
        <section class="col col-1">
          {{ Oform.input('oboe', {
            'data-bind': "numeric, bindValue: oboe",
            'disabled': (disabled) ? true : false,
            'label': __('sworks.oboe'),
            'type': 'text',
            'value': (swork.oboe is null) ? null : swork.oboe,
          }) | raw }}
        </section>
        <section class="col col-2">
          {{ Oform.input('oboe_text', {
            'data-bind': 'bindValue: oboe_text',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.oboe_text'),
          }) | raw }}
        </section>
        <section class="col col-1">
          {{ Oform.input('clarinet', {
            'data-bind': "numeric, bindValue: clarinet",
            'disabled': (disabled) ? true : false,
            'label': __('sworks.clarinet'),
            'type': 'text',
            'value': (swork.clarinet is null) ? null : swork.clarinet,
          }) | raw }}
        </section>
        <section class="col col-2">
          {{ Oform.input('clarinet_text', {
            'data-bind': 'bindValue: clarinet_text',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.clarinet_text'),
          }) | raw }}
        </section>
        <section class="col col-1">
          {{ Oform.input('bassoon', {
            'data-bind': "numeric, bindValue: bassoon",
            'disabled': (disabled) ? true : false,
            'label': __('sworks.bassoon'),
            'type': 'text',
            'value': (swork.bassoon is null) ? null : swork.bassoon,
          }) | raw }}
        </section>
        <section class="col col-2">
          {{ Oform.input('bassoon_text', {
            'data-bind': 'bindValue: bassoon_text',
            'disabled': (disabled) ? true : false,
            'label': __('sworks.bassoon_text'),
          }) | raw }}
        </section>
      </div>
    </div>

    <div class="row">
      <section class="col col-1">
        {{ Oform.input('horn', {
          'data-bind': "numeric, bindValue: horn",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.horn'),
          'type': 'text',
          'value': (swork.horn is null) ? null : swork.horn,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('horn_text', {
          'data-bind': 'bindValue: horn_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.horn_text'),
        }) | raw }}
      </section>
      <section class="col col-1">
        {{ Oform.input('trumpet', {
          'data-bind': "numeric, bindValue: trumpet",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.trumpet'),
          'type': 'text',
          'value': (swork.trumpet is null) ? null : swork.trumpet,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('trumpet_text', {
          'data-bind': 'bindValue: trumpet_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.trumpet_text'),
        }) | raw }}
      </section>
      <section class="col col-1">
        {{ Oform.input('trombone', {
          'data-bind': "numeric, bindValue: trombone",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.trombone'),
          'type': 'text',
          'value': (swork.trombone is null) ? null : swork.trombone,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('trombone_text', {
          'data-bind': 'bindValue: trombone_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.trombone_text'),
        }) | raw }}
      </section>

      <section class="col col-1">
        {{ Oform.input('tuba', {
          'data-bind': "numeric, bindValue: tuba",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.tuba'),
          'type': 'text',
          'value': (swork.tuba is null) ? null : swork.tuba,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('tuba_text', {
          'data-bind': 'bindValue: tuba_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.tuba_text'),
        }) | raw }}
      </section>
    </div>

    <div class="row">
      <section class="col col-1">
        {{ Oform.input('timpani', {
          'data-bind': "numeric, bindValue: timpani",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.timpani'),
          'type': 'text',
          'value': (swork.timpani is null) ? null : swork.timpani,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('timpani_text', {
          'data-bind': 'bindValue: timpani_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.timpani_text'),
        }) | raw }}
      </section>
      <section class="col col-1">
        {{ Oform.input('percussion', {
          'data-bind': "numeric, bindValue: percussion",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.percussion'),
          'type': 'text',
          'value': (swork.percussion is null) ? null : swork.percussion,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('percussion_text', {
          'data-bind': 'bindValue: percussion_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.percussion_text'),
          'type': 'text',
        }) | raw }}
      </section>

      <section class="col col-1">
        {{ Oform.input('harp', {
          'data-bind': "numeric, bindValue: harp",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.harp'),
          'type': 'text',
          'value': (swork.harp is null) ? null : swork.harp,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('harp_text', {
          'data-bind': 'bindValue: harp_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.harp_text'),
        }) | raw }}
      </section>

      <section class="col col-1">
        {{ Oform.input('keyboard', {
          'data-bind': "numeric, bindValue: keyboard",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.keyboard'),
          'type': 'text',
          'value': (swork.keyboard is null) ? null : swork.keyboard,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('keyboard_text', {
          'disabled': (disabled) ? true : false,
          'label': __('sworks.keyboard_text'),
          'type': 'text',
        }) | raw }}
      </section>
    </div>

    <div class="row">
      <section class="col col-1">
        {{ Oform.input('extra', {
          'data-bind': "numeric, bindValue: extra",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.extra'),
          'type': 'text',
          'value': (swork.extra is null) ? null : swork.extra,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('extra_text', {
          'data-bind': 'bindValue: extra_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.extra_text'),
          'type': 'text',
        }) | raw }}
      </section>

      <section class="col col-1">
        {{ Oform.input('vocals', {
          'data-bind': "numeric, bindValue: vocals",
          'disabled': (disabled) ? true : false,
          'label': __('sworks.vocals'),
          'type': 'text',
          'value': (swork.vocals is null) ? null : swork.vocals,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('vocals_text', {
          'data-bind': 'bindValue: vocals_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.vocals_text'),
          'type': 'text',
        }) | raw }}
      </section>
    </div>

    <div class="row">
      <section class="col col-2">
        <div class="input">
          {{ Oform.input('stringsSum', {
            'class': 'highlighted',
            'data-bind': 'textInput: stringsSum',
            'disabled': true,
            'label': __('sworks.strings'),
            'type': 'text'
          }) | raw }}
        </div>
      </section>
      <section class="col col-2">
        {{ Oform.input('violin1', {
          'data-bind': 'numeric, bindValue: violin1',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.violin1'),
          'type': 'text',
          'value': (swork.violin1 is null) ? null : swork.violin1,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('violin2', {
          'data-bind': 'numeric, bindValue: violin2',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.violin2'),
          'type': 'text',
          'value': (swork.violin2 is null) ? null : swork.violin2,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('viola', {
          'data-bind': 'numeric, bindValue: viola',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.viola'),
          'type': 'text',
          'value': (swork.viola is null) ? null : swork.viola,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('cello', {
          'data-bind': 'numeric, bindValue: cello',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.cello'),
          'type': 'text',
          'value': (swork.cello is null) ? null : swork.cello,
        }) | raw }}
      </section>
      <section class="col col-2">
        {{ Oform.input('bass', {
          'data-bind': 'numeric, bindValue: bass',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.bass'),
          'type': 'text',
          'value': (swork.bass is null) ? null : swork.bass,
        }) | raw }}
      </section>
    </div>

    <div class="row">
      <section class="col col-6">
        {{ Oform.input('strings_text', {
          'data-bind': 'bindValue: strings_text',
          'disabled': (disabled) ? true : false,
          'label': __('sworks.strings_text'),
          'type': 'text'
        }) | raw }}
      </section>
      <section class="" data-bind="attr: {class: alternative() ? 'col col-12' : 'col col-6' }">
        {{ Oform.input('sworkstrings', {
          'class': 'select2',
          'data-listlength': sworkstrings|length,
          'disabled': (disabled) ? true : false,
          'data-bind': 'event: {change: loadSworkstrings}',
          'empty': '-',
          'label': __('sworkstrings'),
          'options': sworkstrings,
        }) | raw }}
      </section>
    </div>

    <div class="row">
      <section class="col col-12" }>
        {{ Oform.input('details', {
          'disabled': (disabled) ? true : false,
          'label': __('sworks.details'),
        }) | raw }}
      </section>
    </div>
  </fieldset>

  <fieldset>
    <div class="row">
      {% block tab2_col1_21 %}
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">

          {% embed '../Components/Sworks/sworks_percussions.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-percussions-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-percussions-component>

        </div>
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_keyboards.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-keyboards-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-keyboards-component>
        </div>
      {% endblock %}
    </div>

    <div class="row">
      {% block tab2_col1_22 %}
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_soloinstr.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-soloinstr-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-soloinstr-component>
        </div>
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_extras.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-extras-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-extras-component>
        </div>
      {% endblock %}
    </div>

    <div class="row">
      {% block tab2_col1_23 %}
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_vocals.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-vocals-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-vocals-component>
        </div>
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% element 'Subviews/Sworks/sworks_addsections' {'rights': 'A'} %}
        </div>
      {% endblock %}
    </div>
  </fieldset>
{% endblock %}

{% block tab3_content %}
  <div class="col col-xs-12">
    <fieldset>
      <div class="row">
        <section class="col col-6">
          {{ Oform.input('commission', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.commission'),
          }) | raw }}
        </section>
        <section class="col col-6">
          {{ Oform.input('sourcetext', {
            'disabled': (disabled) ? true : false,
            'label': __('sworks.sourcetext'),
          }) | raw }}
        </section>
      </div>
    </fieldset>
  </div>
  <div class="col col-xs-12">
    <fieldset>
      <div class="row">
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Element/Subviews/Sworks/sworks_themes.twig' with {'rights': 'A'} %}{% endembed %}
        </div>

        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Element/Subviews/Sworks/sworks_languages.twig' with {'rights': 'A'} %}{% endembed %}
        </div>
      </div>

      <div class="row">
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Element/Subviews/Sworks/sworks_premieres.twig' with {'rights': 'A'} %}{% endembed %}
        </div>

        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Element/Subviews/Sworks/sworks_rights.twig' with {'rights': 'A'} %}{% endembed %}
        </div>
      </div>

      <div class="row">
        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_composers.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-composers-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-composers-component>
        </div>

        <div class="col col-xs-12 col-sm-12 col-md-6 col-lg-6">
          {% embed '../Components/Sworks/sworks_additionaltitles.twig' with {'rights': 'A'} %}{% endembed %}
          <sworks-additionaltitles-component params="parent_id:{{ swork.id }}">
            <span>{{ __('loading') }} ...</span>
          </sworks-additionaltitles-component>
        </div>
      </div>
    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col-6">
          <label for="composer-sworks">{{ __('sworks.workoriginal_id') }}</label>
          {{ Oform.input('workoriginal_id', {
            'class': 'select2',
            'data-bind': 'options: workoriginals, optionsText:"text", optionsValue:"id", optionsCaption:"-", value: workoriginal_id',
            'disabled': (disabled) ? true : false,
            'empty': '-',
            'label': false,
            'type': 'select'
          }) | raw }}
          <span data-bind="visible: !workoriginal_composer_id()"
                class="small">{{ __('please choose a composer first to load') ~ ' ' ~ __('sworks.workoriginal_id') }}</span>
        </section>
        <section class="col col-6">
          <label></label>
          {{ Oform.input('composer_sworkoriginals', {
            'class': 'select2',
            'data-bind': 'value: workoriginal_composer_id, event:{ change: function(){ loadSworks("composer-workoriginal-id", "workoriginals") }}',
            'disabled': (disabled) ? true : false,
            'empty': __('choose a composer'),
            'id': 'composer-workoriginal-id',
            'label': false,
            'options': scomposers,
            'type': 'select'
          }) | raw }}
        </section>
      </div>
    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col4">
          {% embed '../Element/Snippets/toggle_yes_no.twig' with {
            'name': 'l_intermission', 'disabled': disabled, 'value': swork.l_intermission,
            'label': __('sworks.l_intermission')} %}{% endembed %}
        </section>
        <section class="col col4">
          {% embed '../Element/Snippets/toggle_yes_no.twig' with {
            'name': 'l_regular', 'disabled': disabled, 'value': swork.l_regular,
            'label': __('sworks.l_regular')
          } %}{% endembed %}
        </section>
      </div>
    </fieldset>
  </div>
{% endblock %}

