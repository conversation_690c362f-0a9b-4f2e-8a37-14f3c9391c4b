{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Sworks/add.twig' %}

{% set edit = true %}
{% set disabled = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-music',
    'icon': 'fa-cog',
    'headline': {0: __('sworks'), 1: __('view'), 2: swork.code },
    'data': swork
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: 'sworks',
    1: {
      1: 'list',
      2: 'new',
      3: {'newcopy': {'link': "/sworks/newcopy/" ~ swork.id}},
      4: 'edit',
      5: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
    },
    2: swork.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-cog', 1: __('view')} %}
{% endblock %}

{% block dialogs %}
  {% cell 'Dialog::confirm' {0:{
    'id': 'confirmDeleteItem',
    'headline': __('delete record'),
    'message': __('delete record {0}?',swork.title1),
    'url': '/sworks/delete/' ~ swork.id
  }} %}
{% endblock %}
