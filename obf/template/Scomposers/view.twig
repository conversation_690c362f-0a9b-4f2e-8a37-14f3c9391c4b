{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Scomposers/add.twig' %}

{% set disabled = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-magic',
    'icon': 'fa-magic',
    'headline': {0: __('scomposers'), 1: __('edit'), 2: scomposer.lastname },
    'data': scomposer
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: 'scomposers',
    1: {
      1: 'list',
      2: 'new',
      3: {'newcopy': {'link': "/scomposers/newcopy/" ~ scomposer.id}},
      4: 'edit',
      5: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
    },
    2: scomposer.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-magic', 1: __('view')} %}
{% endblock %}

{% block dialogs %}
  {% cell 'Dialog::confirm' {0:{
    'id': 'confirmDeleteItem',
    'headline': __('delete record'),
    'message': __('delete record {0}?',scomposer.lastname),
    'url': '/scomposers/delete/' ~ scomposer.id
  }} %}
{% endblock %}
