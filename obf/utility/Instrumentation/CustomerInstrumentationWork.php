<?php


namespace Customer\obf\utility\Instrumentation;

use Cake\Core\Configure;

/**
 * Class CustomerInstrumentation
 *
 * @package Customer\obf\utility\Instrumentation
 *
 * This is a companion to CustomerInstrumentation but is for sWorks records, not aDate-Works
 *    while there can be formatting differences between the two, the main difference is the use
 *    of instrumentation grida (swork_percussions instead of adatework_percussions)
 *
 * Max instrumentation would also be rarely used [ only in the case that a client creates a hypothetical
 *    program in the Works area ]
 *
 */
class CustomerInstrumentationWork
{

    /**
     * Double marker
     * @var string
     */
    protected $dblMark;

    /**
     * Prefix for the instrumentation text
     * @var string
     */
    protected $textPrefix;

    /**
     * Suffix for the instrumentation text
     * @var string
     */
    protected $textSuffix;

    /**
     * Separator between the single instrumentation
     * @var string
     */
    protected $singleSeparator;

    /**
     * Separator between the instrumentation groups
     * @var string
     */
    protected $groupSeparator;

    /**
     * Separator between the strings
     * @var string
     */
    protected $stringSeparator;

    /**
     * Label for the timpani instrumentation
     * @var string
     */
    protected $timpaniLabel;

    /**
     * Label for the percussion instrumentation
     * @var string
     */
    protected $percussionLabel;

    /**
     * Connector for the timpani and the percussion part
     * @var string
     */
    protected $timpaniPercussionConnector;

    /**
     * Label for the harp instrumentation
     * @var string
     */
    protected $harpLabel;

    /**
     * Label for the keyboard instrumentation
     * @var string
     */
    protected $keyboardLabel;

    /**
     * Label for the extra instrumentation
     * @var string
     */
    protected $extraLabel;

    /**
     * Label for the vocals instrumentation
     * @var string
     */
    protected $vocalsLabel;

    /**
     * Label for the string instrumentation
     * @var string
     */
    protected $stringLabel;

    /**
     * Prefix for the grid string
     * @var string
     */
    protected $gridPrefix;

    /**
     * Suffix for the grid string
     * @var string
     */
    protected $gridSuffix;

    /**
     * Separator for the grid elements
     * @var string
     */
    protected $gridSeparator;

    /**
     * Suffix for the text of the grid elements
     * @var string
     */
    protected $gridTextPrefix;

    /**
     * Prefix for the text of the grid elements
     * @var string
     */
    protected $gridTextSuffix;

    /**
     * Separator for the grid string
     * @var string
     */
    protected $ignoreNumbersText;

    /**
     * Array for the maximum instrumentation
     * @var mixed[]
     */
    protected $maxInstrumentation = [
        'flute' => ['number' => 0, 'text' => false],
        'oboe' => ['number' => 0, 'text' => false],
        'clarinet' => ['number' => 0, 'text' => false],
        'bassoon' => ['number' => 0, 'text' => false],
        'horn' => ['number' => 0, 'text' => false],
        'trumpet' => ['number' => 0, 'text' => false],
        'trombone' => ['number' => 0, 'text' => false],
        'tuba' => ['number' => 0, 'text' => false],
        'timpani' => ['number' => 0, 'text' => false],
        'percussion' => ['number' => 0, 'text' => false],
        'harp' => ['number' => 0, 'text' => false],
        'keyboard' => ['number' => 0, 'text' => false],
        'extra' => ['number' => 0, 'text' => false],
        'vocals' => ['number' => 0, 'text' => false],
        'violin1' => ['number' => 0, 'text' => false],
        'violin2' => ['number' => 0, 'text' => false],
        'viola' => ['number' => 0, 'text' => false],
        'cello' => ['number' => 0, 'text' => false],
        'bass' => ['number' => 0, 'text' => false]
    ];

    /**
     * Array for the maximum instrumentation grid
     * @var mixed[]
     */
    protected $maxInstrumentationGrid = [
        'percussions' => [],
        'keyboards' => [],
        'extras' => [],
        'vocals' => []
    ];

    /**
     * Instrumentation constructor.
     *
     * Get the settings from the config.
     */
    public function __construct()
    {
        $this->dblMark = Configure::read('Instrumentation.dblMark', '*');
        $this->textPrefix = Configure::read('Instrumentation.textPrefix', '[');
        $this->textSuffix = Configure::read('Instrumentation.textSuffix', ']');
        $this->singleSeparator = Configure::read('Instrumentation.singleSeparator', ' ');
        $this->groupSeparator = Configure::read('Instrumentation.groupSeparator', ' ');
        $this->stringSeparator = Configure::read('Instrumentation.stringSeparator', '.');
        $this->timpaniLabel = Configure::read('Instrumentation.timpaniLabel', 'tmp');
        $this->percussionLabel = Configure::read('Instrumentation.percussionLabel', 'perc');
        $this->timpaniPercussionConnector = Configure::read('Instrumentation.timpaniPercussionConnector', '+');
        $this->harpLabel = Configure::read('Instrumentation.harpLabel', 'hp');
        $this->keyboardLabel = Configure::read('Instrumentation.keyboardLabel', 'kybd');
        $this->extraLabel = Configure::read('Instrumentation.extraLabel', 'extra');
        $this->vocalsLabel = Configure::read('Instrumentation.vocalsLabel', 'extra');
        $this->stringLabel = Configure::read('Instrumentation.stringLabel', 'str: ');
        $this->gridPrefix = Configure::read('Instrumentation.gridPrefix', ' [');
        $this->gridSuffix = Configure::read('Instrumentation.gridSuffix', ']');
        $this->gridSeparator = Configure::read('Instrumentation.gridSeparator', '; ');
        $this->gridTextPrefix = Configure::read('Instrumentation.gridTextPrefix', ' (');
        $this->gridTextSuffix = Configure::read('Instrumentation.gridTextSuffix', ')');
        $this->ignoreNumbersText = Configure::read('Instrumentation.ignoreNumbersText', true);
    }

    /**
     * @param string $gridPrefix
     */
    public function setGridPrefix($gridPrefix): void
    {
        $this->gridPrefix = $gridPrefix;
    }

    /**
     * @return string
     */
    public function getGridPrefix()
    {
        return $this->gridPrefix;
    }

    /**
     * @param string $gridSuffix
     */
    public function setGridSuffix($gridSuffix): void
    {
        $this->gridSuffix = $gridSuffix;
    }

    /*
    * @return string
    */
    public function getGridSuffix()
    {
        return $this->gridSuffix;
    }

    /**
     * @param string $groupSeparator
     */
    public function setGroupSeparator($groupSeparator): void
    {
        $this->groupSeparator = $groupSeparator;
    }

    /**
     * @return string
     */
    public function getGroupSeparator()
    {
        return $this->groupSeparator;
    }


    // MAIN FUNCTION CALLED FROM A REPORT; USED FOR EITHER AN ARRAY OR A SINGLE WORK
    public function addInstrumentationStringToWork(&$works)
    {
        if (is_array($works)) {
            foreach ($works as $work) {
                $this->resetMaxInstrumentation();
                $this->formatInstrumentationString($work);
                $this->formatMaxInstrumentationString($work, $this->maxInstrumentation);
            }
        } else {
            $this->resetMaxInstrumentation();
            $this->formatInstrumentationString($works);
            $this->formatMaxInstrumentationString($works, $this->maxInstrumentation);
        }
    }

    /**
     * Format the standard and detail instrumentation string based on the defined in confluence
     *
     * @param $work
     * @link https://opas.atlassian.net/wiki/spaces/KUN/pages/1345126401/Instrumentation+US
     */
    public function formatInstrumentationString(&$work)
    {
        $groupsStandard = [];
        $groupsDetail = [];
        $groupsStandardZeroWinds = [];  // suppress winds if sum is zero

        $windCount = ($work->flute + $work->oboe + $work->clarinet + $work->bassoon
            + $work->horn + $work->trumpet + $work->trombone + $work->tuba);

        // Set the winds group
        $groupsStandard['winds'] = $this->getSimpleInstrument($work->flute, $work->flute_text);
        $groupsStandard['winds'] .= $this->getSimpleInstrument($work->oboe, $work->oboe_text);
        $groupsStandard['winds'] .= $this->getSimpleInstrument($work->clarinet, $work->clarinet_text);
        $groupsStandard['winds'] .= $this->getSimpleInstrument($work->bassoon, $work->bassoon_text);

        $groupsDetail['winds'] = $this->getDetailedInstrument($work->flute, $work->flute_text);
        $groupsDetail['winds'] .= $this->getDetailedInstrument($work->oboe, $work->oboe_text);
        $groupsDetail['winds'] .= $this->getDetailedInstrument($work->clarinet, $work->clarinet_text);
        $groupsDetail['winds'] .= $this->getDetailedInstrument($work->bassoon, $work->bassoon_text, false);

        // Set the brass group
        $groupsStandard['brass'] = $this->getSimpleInstrument($work->horn, $work->horn_text);
        $groupsStandard['brass'] .= $this->getSimpleInstrument($work->trumpet, $work->trumpet_text);
        $groupsStandard['brass'] .= $this->getSimpleInstrument($work->trombone, $work->trombone_text);
        $groupsStandard['brass'] .= $this->getSimpleInstrument($work->tuba, $work->tuba_text);

        $groupsDetail['brass'] = $this->getDetailedInstrument($work->horn, $work->horn_text);
        $groupsDetail['brass'] .= $this->getDetailedInstrument($work->trumpet, $work->trumpet_text);
        $groupsDetail['brass'] .= $this->getDetailedInstrument($work->trombone, $work->trombone_text);
        $groupsDetail['brass'] .= $this->getDetailedInstrument($work->tuba, $work->tuba_text, false);

        if ($windCount > 0) {
            $groupsStandardZeroWinds['winds'] = $groupsStandard['winds'];
            $groupsStandardZeroWinds['brass'] = $groupsStandard['brass'];
        } else {
            $groupsStandardZeroWinds['winds'] = '';
            $groupsStandardZeroWinds['brass'] = '';
        }

        // Timpani and Percussion
        $timpaniPercussionString = '';

        if ($work->timpani > 0) {
            $timpaniPercussionString = $this->getLabeledInstrument(
                $work->timpani,
                $work->timpani_text,
                $this->timpaniLabel
            );
        }

        $timpaniPercussionString .= ((int)$work->timpani > 0 && (int)$work->percussion > 0) ? $this->timpaniPercussionConnector : '';

        if ($work->percussion > 0) {
            $timpaniPercussionString .= $this->getLabeledInstrument(
                $work->percussion,
                $work->percussion_text,
                $this->percussionLabel
            );
        }

        if ($timpaniPercussionString !== '') {
            $groupsStandard['timpani_percussion'] = $groupsDetail['timpani_percussion'] = $timpaniPercussionString;
        }


            $work['percussion_grid'] = $this->addInstrumentationGrid($work, 'percussions', false);



        // Set the harp group
        if ($work->harp > 0) {
            $groupsStandard['harp'] = $groupsDetail['harp'] = $this->getLabeledInstrument(
                $work->harp,
                $work->harp_text,
                $this->harpLabel
            );
        }

        // Set the keyboard group
        if ($work->keyboard > 0) {
            $groupsStandard['keyboard'] = $groupsDetail['keyboard'] = $this->getLabeledInstrument(
                $work->keyboard,
                $work->keyboard_text,
                $this->keyboardLabel,
                $work,
                'keyboards'
            );
        }

        // Set the extra group
        if ($work->extra > 0) {
            $groupsStandard['extra'] = $groupsDetail['extra'] = $this->getLabeledInstrument(
                $work->extra,
                $work->extra_text,
                $this->extraLabel,
                $work,
                'extras'
            );
        }

        // Set the vocals group
        if ($work->vocals > 0) {
            $vocalsString = $work->vocals > 1 ? $work->vocals : '';
            $vocalsString .= $this->checkText(
                $work->vocals_text
            ) ? $this->textPrefix . $work->vocals_text . $this->textSuffix : '';
            $vocalsString .= $this->vocalsLabel;
            $vocalsString .= $this->addVocalsGrid($work);

            $groupsStandard['vocals'] = $groupsDetail['vocals'] = $vocalsString;
        } elseif ($work['sworks_vocals']) {
            $groupsStandard['vocals'] = $groupsDetail['vocals'] = $this->addVocalsGrid($work);
        }

        // Set the string group
        if (($work->violin1 + $work->violin2 + $work->viola + $work->cello + $work->bass) > 0) {
            $groupsDetail['string'] = $this->stringLabel;
            $groupsDetail['string'] .= $work->violin1 . $this->stringSeparator;
            $groupsDetail['string'] .= $work->violin2 . $this->stringSeparator;
            $groupsDetail['string'] .= $work->viola . $this->stringSeparator;
            $groupsDetail['string'] .= $work->cello . $this->stringSeparator;
            $groupsDetail['string'] .= $work->bass;
        } else {
            $groupsDetail['string'] = trim($work->strings_text);
        }

        $groupsStandard['string'] = $groupsDetail['string'];

        //except for winds and brass (defined above) 'suppress Zero Winds' instrumentation string
        //  is almost always identical to the Standard output
        $groupsStandardZeroWinds['timpani_percussion'] = $groupsStandard['timpani_percussion'];
        $groupsStandardZeroWinds['harp'] = $groupsStandard['harp'];
        $groupsStandardZeroWinds['keyboard'] = $groupsStandard['keyboard'];
        $groupsStandardZeroWinds['extra'] = $groupsStandard['extra'];
        $groupsStandardZeroWinds['vocals'] = $groupsStandard['vocals'];
        $groupsStandardZeroWinds['string'] = $groupsStandard['string'];

        $work['instrumentation_groups_standard'] = $groupsStandard;
        $work['instrumentation_groups_detail'] = $groupsDetail;

        $work['instrumentation_standard'] = implode($this->groupSeparator, $groupsStandard);
        $work['instrumentation_detail'] = implode($this->groupSeparator, $groupsDetail);
        $work['instrumentation_zeroWinds'] = implode($this->groupSeparator, array_filter($groupsStandardZeroWinds));

        $this->checkMaxInstrumentation($work);
    }

    /**
     * Format instruments of the winds or brass group for the simple instrumentation string
     *
     * @param mixed $instrument number of the instruments
     * @param mixed $text additional text of the instrument
     * @return string
     */
    protected function getSimpleInstrument($instrument, $text)
    {
        $instrument = $instrument ? $instrument : 0;

        return $this->checkText($text) ? $this->dblMark . $instrument : $instrument;
    }

    /**
     * Format instruments of the winds or brass group for the detail instrumentation string
     *
     * @param mixed $instrument number of the instruments
     * @param mixed $text additional text of the instrument
     * @param bool $withSeparator optional with separator. For the last instrument we need to set this on false.
     * @return string
     */
    protected function getDetailedInstrument($instrument, $text, $withSeparator = true)
    {
        $instrument = $instrument ? $instrument : 0;

        return $instrument . (trim(
                $text
            ) !== '' ? ($this->textPrefix . $text . $this->textSuffix) : '') . ($withSeparator ? $this->singleSeparator : '');
    }

    /**
     * Format the instrument for other groups than winds or brass additional with the grid string
     *
     * @param mixed $instrument number of the instruments
     * @param mixed $text additional text of the instrument
     * @param string $label label for the instrument based on the settings
     * @param null $work the work object to set the grid string
     * @param string $grid the indicator inside the Work for the grid data
     * @return string
     */
    protected function getLabeledInstrument($instrument, $text, string $label, $work = null, $grid = '')
    {
        $instrumentString = $instrument > 1 ? $instrument : '';
        $instrumentString .= $label;
        $instrumentString .= $this->checkText($text) ? $this->textPrefix . $text . $this->textSuffix : '';

        if ($grid !== '') {
            $instrumentString .= $this->addInstrumentationGrid($work, $grid);
        }

        return $instrumentString;
    }

    /**
     * Iterate over the grid entries of the defined group and add the number, the code or name of the
     * instrument and the text
     *
     * @param $work
     * @param string $gridKey
     * @param bool $withSeparator
     * @return string
     */
    protected function addInstrumentationGrid($work, string $gridKey, $withSeparator = true)
    {
        $gridString = '';

        if ($work['sworks_' . $gridKey]) {
            $gridString = $withSeparator ? $this->gridPrefix : '';
            $gridElements = [];

            $instrumentGridArray = $work['sworks_' . $gridKey];
            usort($instrumentGridArray, function ($a, $b) {
                $sortValue = $a['instrument_order'] <=> $b['instrument_order'];
                if ($sortValue == 0) {
                    $sortValue = $a['sinstrinstrument']['code'] <=> $b['sinstrinstrument']['code'];
                }
                return $sortValue;
            });

            foreach ($instrumentGridArray as $gridElement) {
                $gridElementString = '';

                if ($gridElement['number_'] > 1) {
                    $gridElementString .= $gridElement['number_'] . ' ';
                }

                if ( trim($gridElement['sinstrinstrument']['code']) !== '') {
                    $gridElementString .= strtolower($gridElement['sinstrinstrument']['code']);
                } else {
                    $gridElementString .= strtolower($gridElement['sinstrinstrument']['name']);
                }

                if ($gridElement['text']) {
                    $gridElementString .= $this->gridTextPrefix . $gridElement['text'] . $this->gridTextSuffix;
                }

                $gridElements[] = $gridElementString;
            }

            $gridString .= implode($this->gridSeparator, $gridElements);
            $gridString .= $withSeparator ? $this->gridSuffix : '';
        }

        return $gridString;
    }

    /**
     * Iterate over the grid entries of the vocals and add optional the number, the name of the
     * instrument and the text
     *
     * @param $dateWork
     * @return string
     */
    protected function addVocalsGrid($work)
    {
        $gridString = '';

        if ($work['sworks_vocals']) {
            $gridElements = [];

            foreach ($work['sworks_vocals'] as $gridElement) {
                $gridElementString = '';

                if ($gridElement['number_'] > 1) {
                    $gridElementString .= $gridElement['number_'] . ' ';
                }

                $gridElementString .= strtolower($gridElement['sinstrinstrument']['name']);

                if ($gridElement['text']) {
                    $gridElementString .= $this->gridTextPrefix . $gridElement['text'] . $this->gridTextSuffix;
                }

                $gridElements[] = $gridElementString;
            }

            $gridString .= implode($this->gridSeparator, $gridElements);
        }

        return $gridString;
    }

    /**
     * Check if a text is empty or not based on the setting ignoreNumbersText
     *
     * @param mixed $text
     * @return bool|int
     */
    protected function checkText($text)
    {
        return $this->ignoreNumbersText ? preg_match('/[a-zA-Z]/', $text) : $text !== '';
    }

    /**
     * Reset the maximum instrumentation
     */
    private function resetMaxInstrumentation()
    {
        foreach (array_keys($this->maxInstrumentation) as $instrument) {
            $this->maxInstrumentation[$instrument]['number'] = 0;
            $this->maxInstrumentation[$instrument]['text'] = false;
        }

        foreach (array_keys($this->maxInstrumentationGrid) as $grid) {
            $this->maxInstrumentationGrid[$grid] = [];
        }
    }

    /**
     * Check the maximum instrumentation and set the new value
     *
     * Check if the new instrumentation has an higher number and set this in case. ALso check if there is a text to
     * show also the doubling in the maximum instrumentation string. Ew do the same for the instrumentation grids.
     *
     * @param $work
     */
    private function checkMaxInstrumentation($work)
    {
        foreach (array_keys($this->maxInstrumentation) as $instrument) {
            if ($work[$instrument] > $this->maxInstrumentation[$instrument]['number']) {
                $this->maxInstrumentation[$instrument]['number'] = $work[$instrument];
            }

            if ($this->checkText($work[$instrument . '_text'])) {
                $this->maxInstrumentation[$instrument]['text'] = true;
            }
        }

        foreach (array_keys($this->maxInstrumentationGrid) as $grid) {
            if (array_key_exists('swork_' . $grid, $work->toArray())) {
                foreach ($work['swork_' . $grid] as $gridElement) {
                    if (array_key_exists(
                        $gridElement['sinstrinstrument']['code'],
                        $this->maxInstrumentationGrid[$grid]
                    )) {
                        if ($gridElement['number_'] > $this->maxInstrumentationGrid[$grid][$gridElement['sinstrinstrument']['code']]['number']) {
                            $this->maxInstrumentationGrid[$grid][$gridElement['sinstrinstrument']['code']]['number'] = $gridElement['number_'];
                        }
                    } else {
                        $this->maxInstrumentationGrid[$grid][$gridElement['sinstrinstrument']['code']]['number'] = $gridElement['number_'];
                        $this->maxInstrumentationGrid[$grid][$gridElement['sinstrinstrument']['code']]['code'] = $gridElement['sinstrinstrument']['code'];
                        $this->maxInstrumentationGrid[$grid][$gridElement['sinstrinstrument']['code']]['name'] = $gridElement['sinstrinstrument']['name'];
                    }
                }
            }
        }
    }

    /**
     * Format the maximum instrumentation string
     * Maximum instrumentation is typically output in one of two ways: Standard or In-Line
     * Standard outputs the Instrumentation String with grids below it:
     *
     * In-Line outputs the Max Instrumentation in a single line and almost always excludes percussion
     *
     * The Max Instrumentation elements are broken up below so that in the report file, the author can easily
     * create either Standard or In-Line or any other variation
     *
     * @param $entry
     * @param $maxInstrumentation
     */
    public function formatMaxInstrumentationString(&$entry, $maxInstrumentation)
    {
        $maxWindBrassString = '';
        $maxTimpaniPercussionString = '';
        $maxHarpString = '';
        $maxKeyboardString = '';
        $maxExtraString = '';
        $maxStringsString = '';

        $maxInstrumentationArray = [];

        if (
            ($maxInstrumentation['flute']['number'] > 0) ||
            ($maxInstrumentation['oboe']['number'] > 0) ||
            ($maxInstrumentation['clarinet']['number'] > 0) ||
            ($maxInstrumentation['bassoon']['number'] > 0) ||
            ($maxInstrumentation['horn']['number'] > 0) ||
            ($maxInstrumentation['trumpet']['number'] > 0) ||
            ($maxInstrumentation['trombone']['number'] > 0) ||
            ($maxInstrumentation['tuba']['number'] > 0)
        ) {
            $maxWindBrassString = $this->getSimpleInstrument(
                $maxInstrumentation['flute']['number'],
                $maxInstrumentation['flute']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['oboe']['number'],
                $maxInstrumentation['oboe']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['clarinet']['number'],
                $maxInstrumentation['clarinet']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['bassoon']['number'],
                $maxInstrumentation['bassoon']['text'] ? 'withText' : ''
            );
            $maxWindBrassString .= $this->groupSeparator;
            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['horn']['number'],
                $maxInstrumentation['horn']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['trumpet']['number'],
                $maxInstrumentation['trumpet']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['trombone']['number'],
                $maxInstrumentation['trombone']['text'] ? 'withText' : ''
            );

            $maxWindBrassString .= $this->getSimpleInstrument(
                $maxInstrumentation['tuba']['number'],
                $maxInstrumentation['tuba']['text'] ? 'withText' : ''
            );
        }

        $maxInstrumentationArray[] = $maxWindBrassString;

        // Set the Max Timpani/Percussion
        if ($maxInstrumentation['timpani']['number'] > 0) {
            $maxTimpaniPercussionString = $this->getLabeledInstrument(
                $maxInstrumentation['timpani']['number'],
                '',
                $this->timpaniLabel
            );
        }

        $maxTimpaniPercussionString .= ((int)$maxInstrumentation['timpani']['number'] > 0 && (int)$maxInstrumentation['percussion']['number'] > 0) ? $this->timpaniPercussionConnector : '';

        if ($maxInstrumentation['percussion']['number'] > 0) {
            $maxTimpaniPercussionString .= $this->getLabeledInstrument(
                $maxInstrumentation['percussion']['number'],
                '',
                $this->percussionLabel
            );
        }

        $maxInstrumentationArray[] = $maxTimpaniPercussionString;

        // Set the maximum harp group
        if ($maxInstrumentation['harp']['number'] > 0) {
            $maxHarpString = $this->getLabeledInstrument(
                $maxInstrumentation['harp']['number'],
                '',
                $this->harpLabel
            );
        }

        $maxInstrumentationArray[] = $maxHarpString;

        // Set the maximum keyboard group
        if ($maxInstrumentation['keyboard']['number'] > 0) {
            $maxKeyboardString = $this->getLabeledInstrument(
                $maxInstrumentation['keyboard']['number'],
                '',
                $this->keyboardLabel
            );
        }

        $maxInstrumentationArray[] = $maxKeyboardString;

        // Set the maximum extra group
        if ($maxInstrumentation['extra']['number'] > 0) {
            $maxExtraString = $this->getLabeledInstrument(
                $maxInstrumentation['extra']['number'],
                '',
                $this->extraLabel
            );
        }

        $maxInstrumentationArray[] = $maxExtraString;

        // Output strings only if sum of max strings > 0. (future-proof for wind band and alt. instrumentations)
        if (
            ($maxInstrumentation['violin1']['number'] > 0) ||
            ($maxInstrumentation['violin2']['number'] > 0) ||
            ($maxInstrumentation['viola']['number'] > 0) ||
            ($maxInstrumentation['cello']['number'] > 0) ||
            ($maxInstrumentation['bass']['number'] > 0)
        ) {
            $maxStringsString = $this->stringLabel;
            $maxStringsString .= $maxInstrumentation['violin1']['number'] . $this->stringSeparator;
            $maxStringsString .= $maxInstrumentation['violin2']['number'] . $this->stringSeparator;
            $maxStringsString .= $maxInstrumentation['viola']['number'] . $this->stringSeparator;
            $maxStringsString .= $maxInstrumentation['cello']['number'] . $this->stringSeparator;
            $maxStringsString .= $maxInstrumentation['bass']['number'];
        }

        $maxInstrumentationArray[] = $maxStringsString;

        $entry['max_instrumentation'] = implode($this->groupSeparator, array_filter($maxInstrumentationArray));

// each element is available to construct In-Line Instrumentation within an individual report file
        $entry['max_WindsBrass'] = $maxWindBrassString;
        $entry['max_TimpPerc'] = $maxTimpaniPercussionString;
        $entry['max_Harp'] = $maxHarpString;
        $entry['max_Keyboard'] = $maxKeyboardString;
        $entry['max_Extra'] = $maxExtraString;
        $entry['max_Strings'] = $maxStringsString;
        // individual elements for output (rarely used except for some Instrumentation Sheets)
        $entry['maxTimp'] = $maxInstrumentation['timpani']['number'];
        $entry['maxPerc'] = $maxInstrumentation['percussion']['number'];

        //output max grids with either CODE or full instrument NAME
        $entry['max_instrumentation_grid_code'] = [];
        $entry['max_instrumentation_grid_name'] = [];

        foreach ($this->maxInstrumentationGrid as $gridKey => $grid) {
            $gridString = '';
            $gridElements = [];

            usort(
                $grid,
                function ($a, $b) {
                    return strcmp(mb_strtolower($a['code']), mb_strtolower($b['code']));
                }
            );

            foreach ($grid as $gridElement) {
                $gridElementString = '';

                if ($gridElement['number'] > 1) {
                    $gridElementString .= $gridElement['number'] . ' ';
                }

                if ($gridElement['code'] !== '') {
                    $gridElementString .= mb_strtolower($gridElement['code']);
                } else {
                    $gridElementString .= mb_strtolower($gridElement['name']);
                }

                $gridElements[] = $gridElementString;
            }
            $gridString .= implode($this->gridSeparator, $gridElements);
            $entry['max_instrumentation_grid_code'][$gridKey] = $gridString;
        }

        foreach ($this->maxInstrumentationGrid as $gridKey => $grid) {
            $gridString = '';
            $gridElements = [];

            usort(
                $grid,
                function ($a, $b) {
                    return strcmp(mb_strtolower($a['name']), mb_strtolower($b['name']));
                }
            );

            foreach ($grid as $gridElement) {
                $gridElementString = '';

                if ($gridElement['number'] > 1) {
                    $gridElementString .= $gridElement['number'] . ' ';
                }

                $gridElementString .= mb_strtolower($gridElement['name']);

                $gridElements[] = $gridElementString;
            }
            //$gridElements = asort($gridElements);
            $gridString .= implode($this->gridSeparator, $gridElements);
            $entry['max_instrumentation_grid_name'][$gridKey] = $gridString;
        }
    }

}
