<?php

namespace Customer\obf\reports\DateExportExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\obf\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders basic date information to Excel. This version omits repertoire.
 *
 */

class DateExportExcel extends Report
{

//    Change these values to add a title or other heading
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 1;
    const DATA_ROW = 2;

    /**
     * Helper class for selected date records
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for Soloists attached to Date Record
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    // SET COLUMN ADDRESSES
    private $col_Date = 'A';
    private $col_Month = 'B';
    private $col_Week = 'C';
    private $col_Weekday = 'D';
    private $col_Start = 'E';
    private $col_End = 'F';
    private $col_Project = 'G';
    private $col_Event = 'H';
    private $col_Performance = 'I';
    private $col_Venue = 'J';
    private $col_VenueCode = 'K';
    private $col_Conductor = 'L';
    private $col_Soloist = 'M';
    private $col_Persons = 'N';
    private $col_Orchestra = 'O';
    private $col_Text = 'P';
    private $col_Title = 'Q';
    private $col_Season = 'R';
    private $col_Status = 'S';
    private $col_Dress = 'T';
    private $col_Notes = 'U';


    public function initialize()
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withPersons()
            ->withStatus()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

//      +++++ Render DATA HEADER ROW as single-item array in row 1
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

        $row = self::DATA_ROW;
        $upperLeftCell = $this->col_Date . $row;

//        +++++ FETCH EACH DATE RECORD AND RENDER by Individual Cell +++++
//        render by individual cell so format text can be used

        foreach ($this->datesResult as $dateResult) {

//           date info that requires formatting
            if ($dateResult['seventtype']['l_performance'] == 1) {
                $l_performance = 'yes';
            } else {
                $l_performance = 'no';
            }
            $month = $dateResult['date_']->format('M');
//            use PWeek if North America, chron week if not
            if (Configure::read('Formats.region') === 'North America') {
                $week = $dateResult['pweek'];
            } else {
                $week = $dateResult['week'];
            }

            if (!empty($dateResult['start_'])) {
                $startTime = $dateResult['start_']->format(Configure::read('Formats.time_short'));
            } else {
                $startTime = '';
            }
            if (!empty($dateResult['end_'])) {
                $endTime = $dateResult['end_']->format(Configure::read('Formats.time_short'));
            } else {
                $endTime = '';
            }

            $venueName = trim(
                htmlspecialchars($dateResult['locationaddress']['name2']) . ' ' . htmlspecialchars(
                    $dateResult['locationaddress']['name1']
                )
            );
            $conductorName = trim(
                htmlspecialchars($dateResult['conductoraddress']['name2']) . ' ' . htmlspecialchars(
                    $dateResult['conductoraddress']['name1']
                )
            );
            $orchestraName = trim(
                htmlspecialchars($dateResult['orchestraaddress']['name2']) . ' ' . htmlspecialchars(
                    $dateResult['orchestraaddress']['name1']
                )
            );

//            Function returns Date Soloists in one string. Setting conductor ID to zero ensures that if someone
//            is both a conductor AND a soloist, he/she will appear in both columns which is desired for an Export
            if(!empty($dateResult['adate_works'])) {
                $dateSoloists = $this->datePerformerHelper->getSoloists($dateResult['id'], 0, ': ', 0);
            } else {
                $dateSoloists = '';
            }
            if(!empty($dateResult['adate_persons'])) {
                $datePersons = $this->getDatePersons($dateResult['id']);
            } else {
                $datePersons = '';
            }


//             +++++++ RENDER CELLS +++++++
            $spreadsheet->getActiveSheet()
                ->setCellValue(
                    $this->col_Date . $row,
                    $dateResult['date_']->format(Configure::read('Formats.date'))
                );
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Month . $row, $month);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Week . $row, $week);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Weekday . $row, $dateResult['weekday']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Start . $row, $startTime);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_End . $row, $endTime);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Project . $row, htmlspecialchars($dateResult['sproject']['name']));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Event . $row, htmlspecialchars($dateResult['seventtype']['name']));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Performance . $row, $l_performance);

//        +++++ CONDITIONAL FORMATTING FOR PERFORMANCE ROWS ++++++
            if ($dateResult['seventtype']['l_performance'] == 1) {
                $spreadsheet->getActiveSheet()->getStyle($this->col_Date . $row . ':' . $this->col_Notes . $row)
                    ->applyFromArray($this->getPerformanceRowFormat());
            }

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Venue . $row, $venueName);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_VenueCode . $row, htmlspecialchars($dateResult['locationaddress']['code']));

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Conductor . $row, $conductorName);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Soloist . $row, $dateSoloists);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Persons . $row, $datePersons);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Orchestra . $row, $orchestraName);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Text . $row, htmlspecialchars($dateResult['text']));
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted($this->col_Title . $row, $dateResult['programtitle']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Season . $row, $dateResult['sseason']['code']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Status . $row, $dateResult['sdatestatus']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Dress . $row, $dateResult['sdress']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Notes . $row, htmlspecialchars($dateResult['notes']))
                ->getStyle($this->col_Notes . $row)
                ->getAlignment()->setWrapText(true);


            $row++;
        }

//        ++++++ FORMAT SPREADSHEET ++++++
        $this->getFormatting($upperLeftCell, $row, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }


    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['col_Date'] ?? __('adates.date_');
        $columnB = $repFileText['col_Month'] ?? __('adates.month');
        $columnC = $repFileText['col_Week'] ?? __('adates.pweek');
        $columnD = $repFileText['col_Weekday'] ?? __('adates.weekday');
        $columnE = $repFileText['col_Start'] ?? __('adates.start_');
        $columnF = $repFileText['col_End'] ?? __('adates.end_');
        $columnG = $repFileText['col_Project'] ?? __('adates.project_id');
        $columnH = $repFileText['col_Event'] ?? __('adates.eventtype_id');
        $columnI = $repFileText['col_Performance'] ?? __('seventtypes.l_performance');
        $columnJ = $repFileText['col_Venue'] ?? __('adates.location_id');
        $columnK = $repFileText['col_VenueCode'] ?? __('adates.location_id');
        $columnL = $repFileText['col_Conductor'] ?? __('adates.conductor_id');
        $columnM = $repFileText['col_Soloist'] ?? __('soloist');
        $columnN = $repFileText['col_Persons'] ?? __('persons');
        $columnO = $repFileText['col_Orchestra'] ?? __('adates.orchestra_id');
        $columnP = $repFileText['col_Text'] ?? __('adates.text');
        $columnQ = $repFileText['col_Title'] ?? __('adates.programtitle');
        $columnR = $repFileText['col_Season'] ?? __('adates.season_id');
        $columnS = $repFileText['col_Status'] ?? __('adates.status_id');
        $columnT = $repFileText['col_Dress'] ?? __('adates.dress_id');
        $columnU = $repFileText['col_Notes'] ?? __('adates.notes');


        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK,
            $columnL,
            $columnM,
            $columnN,
            $columnO,
            $columnP,
            $columnQ,
            $columnR,
            $columnS,
            $columnT,
            $columnU
        );
    }

    public function getDatePersons($dateId)
    {
        $persons = $this->dateQueryHelper->getDatePersons($dateId)->getQuery()->toArray();

            foreach ($persons as $person) {
                $datePerson = trim(htmlspecialchars($person['saddress']['name2'] . ' ' . $person['saddress']['name1']));
                if ($person['addressgroup_id'] > 0) {
                    $datePerson .= ', ' . $person['saddressgroup']['name'];
                }
                if ($person['instrument_id'] > 0) {
                    $datePerson .= ' - ' . strtolower($person['sinstrinstrument']['name']);
                }
                if ($person['function_id'] > 0) {
                    $datePerson .= ', ' . $person['Saddressfunctionitems']['name'];
                }
                $datePerson .= $person['text'] ? '; ' . $person['text'] : '';
                $datePerson .= $person['notes'] ? ' (' . $person['notes'] . ')' : '';
                $datePersonArray[] = $datePerson;
            }

        return implode('; ', $datePersonArray);
    }


    protected function getFormatting($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row - 1;
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;

//        BOLD Header (row 1) and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':U' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//        +++++ SET FONT ++++++
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $this->col_Notes . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);


//        +++++ SET COLUMN WIDTH +++++
//        Set first six columns - dates and times - to auto width
        foreach (range($this->col_Date, $this->col_End) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setAutoSize(true);
        }

        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Project)
            ->setWidth(20);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Event)
            ->setWidth(20);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Performance)
            ->setAutoSize(true);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Venue)
            ->setWidth(20);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_VenueCode)
            ->setAutoSize(true);
//      Set Conductor, Soloist, Persons, Orchestra, Text, Title all to 20
        foreach (range($this->col_Conductor, $this->col_Title) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setWidth(20);
        }
//        Set Season, Status and Dress to 15
        foreach (range($this->col_Season, $this->col_Dress) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setWidth(15);
        }
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Notes)
            ->setWidth(55);


//        +++++ SET COLUMN ALIGNMENT +++++
//        Set first six columns - dates and times - to right align
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Date . self::HEADER_ROW . ':' . $this->col_End . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Performance . self::HEADER_ROW . ':' . $this->col_Performance . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

//       +++++ Freeze Panes +++++ after end time
        $spreadsheet->getActiveSheet()->freezePane('G' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');

    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => '29465B') // Dark Blue Grey
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),

//            'alignment' => [
//                'setWrapText' => true
//            ],

        );
    }

    protected function getPerformanceRowFormat()
    {
        return array(
//            fills the entire range; can also gradient fill, but this is
//            typically all that is needed
            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => 'DBE1E4') // light blue
            ),
        );
    }
}
