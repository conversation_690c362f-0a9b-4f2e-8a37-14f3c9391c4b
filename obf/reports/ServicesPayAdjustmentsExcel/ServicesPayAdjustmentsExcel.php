<?php

namespace Customer\obf\reports\ServicesPayAdjustmentsExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\fasutilities\reports\utility\DutyQueryHelper;
use Customer\fasutilities\reports\utility\NameFormatHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders basic service information to Excel. This version omits repertoire.
 *
 */

class ServicesPayAdjustmentsExcel extends Report
{
    //    Change these values to add a title or other heading
    const HEADER_ROW = 4;
    const DATA_ROW = 5;

    /**
     * Helper class for the selected Service records
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;
    private $dutiesResult;

    /**
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

// SET COLUMN ADDRESSES
//    private $col_Week = 'A';
    private $col_Date = 'D';
    private $col_Start = 'E';
    private $col_Project = 'B';
    private $col_Event = 'C';
    private $col_Musician = 'A';
    private $col_Instrument = 'F';
//    private $col_Group = 'H';
    private $col_Classification = 'G';
    private $col_Attendance = 'I';
    private $col_LPresent = 'K';
    private $col_Notes = 'L';
    private $col_Order1 = 'M';
    private $col_Seat = 'N';
    private $col_AccountCat = 'H';
    private $col_SvcVal = 'P';
    private $col_Adjustments = 'J';

    public function initialize()
    {
        $this->dutyQueryHelper = new DutyQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dutyQuery = $this->dutyQueryHelper
            ->getDuties($this->getRequest()->getData()['dataItemIds'])
            ->withMusicianFunction()
            ->getQuery();

        $this->dutiesResult = $dutyQuery->toArray();

        return $this;
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

//        Render Title
        $spreadsheet->getActiveSheet()
            ->setCellValue('A1', $repFileText['report_title']);
        $spreadsheet->getActiveSheet()
            ->setCellValue('A2', $repFileText['report_subtitle']);

        //      +++++ Render DATA HEADER ROW as single-item array in row 1
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

        $dutyArray = $this->dutiesResult;
        /**
         * @see https://stackoverflow.com/a/2699159
         */
        usort(
            $dutyArray,
            function ($a, $b) {
                return $a['artistaddress']['name1'] . $a['artistaddress']['name2'] . $a['adate']['date_'] . $a['adate']['start_']
                    <=> $b['artistaddress']['name1'] . $b['artistaddress']['name2'] . $b['adate']['date_'] . $b['adate']['start_'];
            }
        );

//        Loop through all selected duties. For each, get any pay adjustments and create two arrays.
//              One will be for ALL EXPENSE TYPES represented in all Pay Adjustment grids. These will be the columns after the service info
//           The other is just the PAY ADJUSTMENTS for a single musician
        $allPayAdjustments = [];
        $thisMusicianPayAdjustments = [];
        foreach ($dutyArray as $duty) {
            $dutyAccountings = $this->dutyQueryHelper->getDutyAccountingsForDutyId($duty['id'])->getQuery()->toArray();
            foreach ($dutyAccountings as $dutyAccounting) {
                $allPayAdjustments[] = $dutyAccounting['expensetype_id'];

                $thisMusicianPayAdjustments[] = array(
                    'dutyId' => $duty['id'],
                    'expenseTypeId' => $dutyAccounting['expensetype_id'],
                    'expenseType' => $dutyAccounting['sexpensetype']['name'],
                    'expenseText' => $dutyAccounting['text']
                );
            }
        }

//        Every expense type found in all duties, and the number of columns needed
        $allPayAdjustments = array_unique($allPayAdjustments);
        $payAdjustmentColumns = sizeof($allPayAdjustments) * 2;

//      render each service record and append any pay adjustments FOR THAT MUSICIAN
        $row = self::DATA_ROW;
        $upperLeftCell = $this->col_Musician . $row;

        foreach ($dutyArray as $duty) {
            $musicianName = $this->nameFormatHelper->getEntityName(
                $duty['artistaddress']['name1'],
                $duty['artistaddress']['name2'],
                2
            );
            if (!empty($duty['adate']['start_'])) {
                $startTime = $duty['adate']['start_']->format(Configure::read('Formats.time_short'));
            } else {
                $startTime = '';
            }
            $musicianAccountCategory = $this->dutyQueryHelper->getAcctCatNameFoDutyAcctCat(
                $duty['accounting_category']
            )->getQuery()->toArray()[0];


//            RENDER SERVICE INFORMATION
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Musician . $row, $musicianName);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Project . $row, $duty['adate']['sproject']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Event . $row, $duty['adate']['seventtype']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue(
                    $this->col_Date . $row,
                    $duty['adate']['date_']->format(Configure::read('Formats.date'))
                );
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Start . $row, $startTime);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Instrument . $row, $duty['sinstrinstrument']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Classification . $row, $duty['saddressfunctionitem']['name']);

//            inefficient - get the name of the Account Category for this duty
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_AccountCat . $row, $musicianAccountCategory['name']);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Attendance . $row, $duty['sdutytype']['name']);

//RENDER ADJUSTMENTS
//      Loop through All Pay Adjustments. When the duty ID matches, stay in this row and iterate over columns,
//      outputing the expense type and pay adjustment text
//            Number of columns is total number of expense types across all services.
//            this will make the pay adjustment expense types line up vertically on the sheet

//            represent columns as rows. Start in column J = 10
            $adjustmentCol = 10;
            foreach ($allPayAdjustments as $payAdjustment) {
                foreach ($thisMusicianPayAdjustments as $musicianPayAdjustment) {
                    if ($musicianPayAdjustment['dutyId'] === $duty['id'] &&
                        $musicianPayAdjustment['expenseTypeId'] === $payAdjustment) {
                        $spreadsheet->getActiveSheet()
                            ->setCellValue([$adjustmentCol, $row], $musicianPayAdjustment['expenseType']);
                        $spreadsheet->getActiveSheet()
                            ->setCellValue([$adjustmentCol + 1, $row], $musicianPayAdjustment['expenseText']);
                    }
                }
                $adjustmentCol++;
                $adjustmentCol++;
            }

            $row++;
        }


        //        ++++++ FORMAT SPREADSHEET ++++++
        $this->formatSheet($upperLeftCell, $row, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }


    public function formatSheet($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row - 1;
        $finalColumn = $spreadsheet->getActiveSheet()->getHighestColumn();
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;


//        BOLD Header (row 1) and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':' . $finalColumn . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//        +++++ SET FONT ++++++
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $finalColumn . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

//        +++ SET TITLE FONT +++
        $spreadsheet->getActiveSheet()->getStyle('A1')
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize + 2)
        ->setBold(true);


//         +++++ SET COLUMN WIDTHS +++++
        $mediumColumns = array(
            $this->col_Musician,
            $this->col_Project,
            $this->col_Event,
            $this->col_Instrument,
            $this->col_Classification,
            $this->col_AccountCat,
            $this->col_Attendance
        );
        foreach ($mediumColumns as $mediumColumn) {
            $spreadsheet->getActiveSheet()->getColumnDimension($mediumColumn)
                ->setWidth(15);
        }

        $dateColumns = array($this->col_Date, $this->col_Start);
        foreach ($dateColumns as $dateColumn) {
            $spreadsheet->getActiveSheet()->getColumnDimension($dateColumn)
                ->setWidth(10);

            $spreadsheet->getActiveSheet()->getStyle($dateColumn)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        }
//      Pay Adjustment column widths
        foreach (range($this->col_Adjustments, $finalColumn) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setWidth(12);
        }

//        Draw a Border between Musicians - loop through all rows and compare the current cell to value of the cell below it
        for ($row = self::DATA_ROW; $row <= $finalRow; $row++) {
            if ($spreadsheet->getActiveSheet()->getCell('A' . $row)->getValue() !== $spreadsheet->getActiveSheet(
                )->getCell('A' . ($row + 1))->getValue()) {
                $spreadsheet->getActiveSheet()->getStyle('A' . $row . ':' . $finalColumn . $row)
                    ->getBorders()->getBottom()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
            }
        }

        //       +++++ Freeze Panes +++++ after end time
        $spreadsheet->getActiveSheet()->freezePane('D' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['col_Musician'];
        $columnD = $repFileText['col_Date'] ?? __('adates.date_');
        $columnE = $repFileText['col_Start'] ?? __('adates.start_');
        $columnF = $repFileText['col_Instrument'] ?? __('aduties.instrument_id');
        $columnB = $repFileText['col_Project'] ?? __('adates.project_id');
        $columnC = $repFileText['col_Event'] ?? __('adates.eventtype_id');
        $columnJ = $repFileText['col_Adjustment'];
        $columnG = $repFileText['col_Classification'] ?? __('aduties.function_id');
        $columnI = $repFileText['col_Attendance'] ?? __('aduties.dutytype_id');
        $columnH = $repFileText['col_AccountCat'] ?? __('aduties.accounting_category');

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ
        );
    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => '3E5634') // Green House
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),

//            'alignment' => [
//                'setWrapText' => true
//            ],

        );
    }
}
