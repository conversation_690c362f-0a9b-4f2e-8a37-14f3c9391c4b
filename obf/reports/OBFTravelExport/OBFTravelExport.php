<?php

namespace Customer\obf\reports\OBFTravelExport;

use App\Reports\Report;
use App\Reports\ReportsInterface;
use Cake\Core\Configure;

use Customer\obf\reports\OnSpreadsheet\OnSpreadsheet;

use Customer\obf\reports\utility\ContractSubstituteQueryHelper;
use Customer\obf\reports\utility\NameFormatHelper;
use Customer\obf\reports\utility\AddressQueryHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class OBFTravelExport extends Report
{

    //    Change these values to add a title or other heading
//      text in rows  1 or 2.
    const HEADER_ROW = 3;
    const DATA_ROW = 4;

    const HEADER_ROW_SHADING = '853333'; // maroon

    // SET COLUMN ADDRESSES
    private $col_Last = 'A';
    private $col_First = 'B';
    private $col_Instrument = 'C';
    private $col_Group = 'D';
    private $col_ArrDate = 'E';
    private $col_ArrTime = 'F';
    private $col_DepDate = 'G';
    private $col_DepTime = 'H';
    private $col_City = 'I';
    private $col_FlightNo = 'J';
    private $col_Airline = 'K';
    private $col_GroundCo = 'L';
    private $col_TravelNotes = 'M';
    private $col_Notes = 'N';

    /**
     * Helper for the contract
     * @var ContractSubstituteQueryHelper
     */
    private $contractSubstituteQueryHelper;
    private $contractsResult;

    /**
     * Helper for name formats
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * Helper for linked Addresses in Artist Addr. Book record
     * @var AddressQueryHelper
     */
    private $addressQueryHelper;

    function initialize()
    {
        parent::initialize();
        // Set query helpers
        $this->contractSubstituteQueryHelper = new ContractSubstituteQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->addressQueryHelper = new AddressQueryHelper();
    }

    /**
     * Get the CONTRACT RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Contract records selected by user to workQueryHelper
    {
        $contractQuery = $this->contractSubstituteQueryHelper
            ->getContracts($this->getRequest()->getData()['dataItemIds'])
            ->withDateDetail()
            ->withAdditionalDataAddresses()
            ->getQuery();

        $this->contractsResult = $contractQuery->toArray();

        return $this;
    }


    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);


        //      +++++ Render DATA HEADER ROW as single-item array in row 1
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

        $row = self::DATA_ROW;
        $upperLeftCell = $this->col_Last . $row;

        $spreadsheet->getActiveSheet()->setCellValue('A1', $repFileText['report_name']);

        foreach ($this->contractsResult as $contract) {
//            Assemble items into array; remember to use htmlspecial as formatted text cannot be used
//              when rendering from an array
            $lastName = htmlspecialchars($contract['artistaddress']['name1']);
            $firstName = htmlspecialchars($contract['artistaddress']['name2']);
            $instrumentName = trim(
                htmlspecialchars($contract['sinstrinstrument']['name'])
            );
//            $group = $contract['scontractgroup']['name'];
            $group = $contract['code'];

            if (!is_null($contract['date_3']) && (string)trim($contract['date_3']) !== '') {
                $arrivalDate = $contract['date_3']->format('m/d/Y');
            } else {
                $arrivalDate = '';
            }
            if (!is_null($contract['time_1']) && (string)trim($contract['time_1']) !== '') {
                $arrivalTime = $contract['time_1']->format('g:i A');
            } else {
                $arrivalTime = '';
            }

            if (!is_null($contract['date_4']) && (string)trim($contract['date_4']) !== '') {
                $departureDate = $contract['date_4']->format('m/d/Y');
            } else {
                $departureDate = '';
            }
            if (!is_null($contract['time_2']) && (string)trim($contract['time_2']) !== '') {
                $departureTime = $contract['time_2']->format('g:i A');
            } else {
                $departureTime = '';
            }

            $city = htmlspecialchars($contract['text_4']);
            $flightNo = $contract['text_5'];
            $airline = trim(
                htmlspecialchars($contract['saddresses1']['name2'] . ' ' . $contract['saddresses1']['name1'])
            );

            $groundTransportCo = trim(
                htmlspecialchars($contract['saddresses2']['name2'] . ' ' . $contract['saddresses2']['name1'])
            );
            $travelNotes = htmlspecialchars($contract['memo_2']);
            $notes = htmlspecialchars($contract['notes']);

            $outputArray = array(
                $lastName,
                $firstName,
                $instrumentName,
                $group,
                $arrivalDate,
                $arrivalTime,
                $departureDate,
                $departureTime,
                $city,
                $flightNo,
                $airline,
                $groundTransportCo,
                $travelNotes,
                $notes
            );
            $spreadsheet->getActiveSheet()
                ->fromArray(
                    $outputArray,
                    null,
                    'A' . $row
                );

            $row++;
        }


        //        ++++++ FORMAT SPREADSHEET ++++++
       $this->getFormatting($upperLeftCell, $row, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    protected function getRoomMateName($addressId)
    {
        $roomMate = $this->addressQueryHelper->getAddresses($addressId)->getQuery()->toArray()[0];
        $roomMateName = trim(htmlspecialchars($roomMate['name2'] . ' ' . $roomMate['name1']));
        return $roomMateName;
    }

    protected function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['last_name'];
        $columnB = $repFileText['first_name'];
        $columnC = $repFileText['instrument'];
        $columnD = $repFileText['group'];
        $columnE = $repFileText['arr_date'];
        $columnF = $repFileText['arr_time'];
        $columnG = $repFileText['dep_date'];
        $columnH = $repFileText['dep_time'];
        $columnI = $repFileText['city'];
        $columnJ = $repFileText['flight_no'];
        $columnK = $repFileText['airline'];
        $columnL = $repFileText['ground_co'];
        $columnM = $repFileText['travel_notes'];
        $columnN = $repFileText['general_notes'];

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK,
            $columnL,
            $columnM,
            $columnN
        );
    }

    protected function getFormatting($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row - 1;
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;

        $largerColumns = [$this->col_GroundCo, $this->col_TravelNotes,  $this->col_Notes];
        foreach ($largerColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setWidth(35);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $mediumColumns = [
            $this->col_Last,
            $this->col_First,
            $this->col_Instrument,
            $this->col_City,
            $this->col_Airline
        ];
        foreach ($mediumColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setWidth(25);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }


        //        BOLD Header and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':N' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//       +++++ Freeze Panes +++++ after end time
        $spreadsheet->getActiveSheet()->freezePane('C' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => self::HEADER_ROW_SHADING) // Dark Red
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),
        );
    }
}
