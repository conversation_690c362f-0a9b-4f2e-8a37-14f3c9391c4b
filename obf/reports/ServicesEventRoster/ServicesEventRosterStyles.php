<?php

namespace Customer\obf\reports\ServicesEventRoster;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Fill;
use ReflectionException;

class ServicesEventRosterStyles
{
    const BORDER_COLOR = '8A0808';  // DARK RED
    const ROSTER_BORDER_ = '787873';  // Dark grey
    const PARAGRAPH_SHADING = 'eaeae1'; // Light tan

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setSize(16);

        $this->programFont = clone($this->defaultFont);
        $this->programFont->setSize(9);

        $this->programFontBold = clone($this->programFont);
        $this->programFontBold->setBold(true);


        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(12);
    }

    /**
     * Define the styles for the report: Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->headerParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,
            'shading' => array('fill' => self::PARAGRAPH_SHADING)
        ];

        $this->programParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,
            'indentation' => ['left' => 240, 'hanging' => 240]
//            'indent' => (360),
//        'hanging' => (360)
        ];

        $this->footerParagraph = [
            'alignment' => 'left',  // left; Right; Center; other options available
            'spaceBefore' => 100,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
            'borderTopSize' => 6,
            'borderColor' => self::PARAGRAPH_SHADING,
            'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
                new \PhpOffice\PhpWord\Style\Tab('center', 5040),
                new \PhpOffice\PhpWord\Style\Tab('right', 10400),
            ),
        ];

//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10440, // 7.25 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];

        $this->programTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10440, // 7.25 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word
            'borderTopColor' => self::BORDER_COLOR,
            'borderTopSize' => 6,
            'borderBottomColor' => self::BORDER_COLOR,
            'borderBottomSize' => 6
        ];

        $this->rosterTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10440, // 7.25 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word
            'borderTopColor' => self::ROSTER_BORDER_,
            'borderTopSize' => 6,
        ];

//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

        $this->shadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'ECEFE0'
        );
    }


}
