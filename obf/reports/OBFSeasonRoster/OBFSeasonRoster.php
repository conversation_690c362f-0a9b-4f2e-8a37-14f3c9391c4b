<?php

namespace Customer\obf\reports\OBFSeasonRoster;

use Cake\Core\Configure;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Customer\fasutilities\reports\utility\AddressQueryHelper;
use Customer\fasutilities\reports\utility\NameFormatHelper;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/**
 * Produce a roster / list of all musicians participating in a given season. Group by the
 *   Ensemble they perform with (Address Group) and by Instrument
 */
class OBFSeasonRoster extends ReportWord
{
    /**
     * Prepared array for the rendering
     * @var addressQueryHelper
     */
    private $addressQueryHelper;
    private $addressesResult;

    /**
     * Name formatter
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function initialize()
    {
        parent::initialize();
        $this->addressQueryHelper = new AddressQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->reportStyles = new OBFSeasonRosterStyles();
    }

    public function collect(array $where = []): ReportsInterface
    {
        $addressQuery = $this->addressQueryHelper
            ->getAddresses($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->addressesResult = $addressQuery->toArray();
        return $this;
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
the <text></text> section of the .rep file. This allows for headings and other report text to output in a
variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    public function renderReport()
    {
//        Fetch regional and client settings from settings.php
        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');
        $repFileText = $this->getRepFileParams();

//        +++++ STYLES AND FORMATS +++++
//      default font and paragraph styles created in RehearsalScheduleStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => "portrait",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//      +++ LOOP THROUGH each ADDRESS GROUP among all selected musicians +++
//         TG: 2024-06 - get MAIN group only at client request
        foreach ($this->getMainAddressGroup() as $eachAddressGroup) {

            $pageSection->addText($eachAddressGroup, $this->reportStyles->defaultFontBold, $defaultParagraph);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//            Loop through Musicians
            $musicianArray = [];
            foreach ($this->addressesResult as $musician) {
//             if the musician belongs to the current Address Group,
//            put the data to be rendered in an ad-hoc array $musicianArray

                foreach ($musician['saddress_addressgroups'] as $musicianAddressGroup) {
                    if ($musicianAddressGroup['saddressgroup']['name'] === $eachAddressGroup) {
//                        ELEMENTS OF THE ARRAY
                        $musicianId = $musician['id'];
                        if (!is_null($musician['order_1']) && (string)trim($musician['order_1']) !== '') {
                            $rosterSort = $musician['order_1'] ;
                        } else {
                            $rosterSort = 'ZZ';
                        }
//                        Additional Text field has the alternate / preferred 'program name'
//                        TG: 2024-06 - professional name moved to Name3 so client could see it on main Address Book screen
//                          in OPAS
                        if (!is_null($musician['name3']) && (string)trim($musician['name3']) !== '') {
                            $musicianName = $musician['name3'];
                        } else {
                            $musicianName = trim($musician['name2'] . ' ' . $musician['name1']);
                        }
                        $hometown = $musician['text_4'];

//                        $musicianInstrumentGrid is a two-element array of instrument name [0] and section order [1]
                        $musicianInstrumentGrid = $this->getInstruments($musician);
                        $musicianInstrument = $musicianInstrumentGrid[0][0];
                        $musicianInstrumentSort = $musicianInstrumentGrid[0][1];
//                        Position is the main classification / address function
                        $position = $this->getClassification($musician);

                        $musicianSort = trim(
                            $musicianInstrumentSort . $rosterSort . $musician['name1'] . $musician['name2']
                        );

                        $musicianArray[] = array(
                            $musicianSort,
                            $musicianInstrument,
                            $musicianId,
                            $musicianName,
                            $position,
                            $hometown
                        );
                    }
                }
            }
//            +++ RENDER MUSICIANS +++
            sort($musicianArray);
            $instrument = '';

            foreach ($musicianArray as $musicianOutput) {
//                Instrument
                if ($musicianOutput[1] !== $instrument) {
                    $pageSection->addText($musicianOutput[1], $this->reportStyles->instrumentFont, $defaultParagraph);
                }
//                Name
                $pageSection->addText($musicianOutput[3], $this->reportStyles->defaultFontBold, $defaultParagraph);
//                Position
                if (!empty($musicianOutput[4])) {
                    $pageSection->addText($musicianOutput[4], $this->reportStyles->defaultFontCaps, $defaultParagraph);
                }
//                Hometown
                if (!empty($musicianOutput[5])) {
                    $pageSection->addText('     ' . $musicianOutput[5], $this->reportStyles->defaultFontItalic, $defaultParagraph);
                }
                $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

                $instrument = $musicianOutput[1];
            }
        }
    }

    private function getAllAddressGroups()
    {
        $addressGroupArray = [];
        foreach ($this->addressesResult as $addressEntry) {
            foreach ($addressEntry['saddress_addressgroups'] as $addressGroup) {
                if ($addressGroup['saddressgroup']['sysgroup_id'] === 6) {
                    $addressGroupArray[] = $addressGroup['saddressgroup']['name'];
                }
            }
        }
        $addressGroupArray = array_unique(array_filter($addressGroupArray));
        sort($addressGroupArray);
        return ($addressGroupArray);
    }

    private function getMainAddressGroup()
    {
        $addressGroupArray = [];
        foreach ($this->addressesResult as $addressEntry) {
            foreach ($addressEntry['saddress_addressgroups'] as $addressGroup) {
                if ($addressGroup['saddressgroup']['sysgroup_id'] === 6  && $addressGroup['l_main'] == 1) {
                    $addressGroupArray[] = $addressGroup['saddressgroup']['name'];
                }
            }
        }
        $addressGroupArray = array_unique(array_filter($addressGroupArray));
        sort($addressGroupArray);
        return ($addressGroupArray);
    }

    private function getInstruments($addressEntry): array
    {
        $tempInstrumentGrid = [];
        foreach ($addressEntry['saddress_instruments'] as $mainInstrument) {
            if ($mainInstrument['l_main'] == 1) {
                if ($mainInstrument['sinstrinstrument']['sinstrsection']['section_order'] < 10) {
                    $sectionOrder = '0' . $mainInstrument['sinstrinstrument']['sinstrsection']['section_order'];
                } else {
                    $sectionOrder = $mainInstrument['sinstrinstrument']['sinstrsection']['section_order'];
                }

                $tempInstrumentGrid[] = array(
                    $mainInstrument['sinstrinstrument']['name'],
                    $sectionOrder
                );
            } else {
                $tempInstrumentGrid[] = array('', '99');
            }
        }
        return $tempInstrumentGrid;
    }

    private function getClassification($addressEntry): string
    {
        foreach ($addressEntry['saddress_functions'] as $function) {
            if ($function['l_main'] == 1) {
                $position = $function['saddressfunctionitem']['name'];
            }
        }

        return $position ?? '';
    }
}
