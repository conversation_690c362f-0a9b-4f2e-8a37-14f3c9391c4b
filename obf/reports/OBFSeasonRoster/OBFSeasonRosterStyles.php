<?php

namespace Customer\obf\reports\OBFSeasonRoster;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;

class OBFSeasonRosterStyles
{

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Cal<PERSON>ri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(11);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->defaultFontCaps = clone($this->defaultFont);
        $this->defaultFontCaps->setAllCaps(true);

        $this->instrumentFont = clone($this->defaultFontBold);
        $this->instrumentFont->setAllCaps(true);

    }

    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];



    }



}
