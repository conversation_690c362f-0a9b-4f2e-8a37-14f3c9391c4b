<?php
namespace Customer\obf\reports\MusicianContactList;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use ReflectionException;


class MusicianContactListStyles
{


    public function __construct() {

        $this->getFonts();
        $this->getStyles();

    }

    protected function getFonts() {

        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(14);


    }

    /**
     * Define the styles for the report: Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0  // space between lines in twips
        ];

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );


        $this->defaultTableStyle = array(
            'borderSize' => 6,
            'borderColor' => '29465B',  // dark blue grey
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115
        );


        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0
        );

        $this->defaultCellLeft = array_merge(
            $this->defaultCell,
            [
                'cellMarginLeft' => Converter::inchToTwip(1)
            ]
        );

        $this->shadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'EBF4FA' // Water
        );

        $this->vertRightBorderCell =  array_merge(
            $this->defaultCell,
            [
                'borderRightColor' => 'b3b6ba',
                'borderRightSize' => 4
            ]
        );






    }

}
