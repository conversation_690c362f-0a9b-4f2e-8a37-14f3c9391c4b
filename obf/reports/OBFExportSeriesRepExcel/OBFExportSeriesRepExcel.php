<?php

namespace Customer\obf\reports\OBFExportSeriesRepExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\obf\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders basic date information to Excel. This version includes repertoire
 *    and total instrumentation for the event
 */

class OBFExportSeriesRepExcel extends Report
{

//    Change these values to add a title or other heading
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 1;
    const DATA_ROW = 2;

    const PERF_ROW_SHADING = 'F9F8F1'; // tan
    const HEADER_ROW_SHADING = '853333'; // maroon

    /**
     * Helper class for selected date records
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for Soloists attached to Date Record
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    // SET COLUMN ADDRESSES
    private $col_Date = 'A';
    private $col_Weekday = 'B';
    private $col_Start = 'C';
    private $col_End = 'D';
    private $col_Series = 'E';
    private $col_Project = 'F';
    private $col_Title = 'G';
    private $col_Event = 'H';
    private $col_Performance = 'I';
//    private $col_PgmNo = 'J';
    private $col_Venue = 'J';
//    private $col_VenueCode = 'J';
    private $col_Program = 'K';
    private $col_Instrumentation = 'L';
//    Additional Data Fields
    private $col_Conductor = 'M';
    private $col_Soloist = 'N';
    private $col_Persons = 'O';
    private $col_Orchestra = 'P';
    private $col_Dress = 'Q';
    private $col_Text = 'R';
    private $col_Notes = 'S';
    private $col_Status = 'T';
    private $col_Season = 'U';
    private $col_Month = 'V';
    private $col_Week = 'W';
    private $col_Marketing = 'X';
    private $col_Pitch = 'Y';
    private $col_Temperament = 'Z';
    private $col_Catering = 'AA';
    private $col_CateringDetails = 'AB';
    private $col_Recording = 'AC';
    private $col_RecordingDetails = 'AD';
    private $col_Transportation = 'AE';
    private $col_Translations = 'AF';
    private $col_Supertitle = 'AG';



    public function initialize()
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->instrumentation = new CustomerInstrumentation();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withPersons()
            ->withStatus()
            ->withMarketing()
->withSeries()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

//      +++++ Render DATA HEADER ROW as single-item array in row 1
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

        $row = self::DATA_ROW;
        $upperLeftCell = $this->col_Date . $row;

//        +++++ FETCH EACH DATE RECORD AND RENDER by Individual Cell +++++
//        render by individual cell so format text can be used

        foreach ($this->datesResult as $dateResult) {

//           date info that requires formatting
            if ($dateResult['seventtype']['l_performance'] == 1) {
                $l_performance = 'yes';
            } else {
                $l_performance = 'no';
            }
            $month = $dateResult['date_']->format('M');
//            use PWeek if North America, chron week if not
            if (Configure::read('Formats.region') === 'North America') {
                $week = $dateResult['pweek'];
            } else {
                $week = $dateResult['week'];
            }

            if (!empty($dateResult['start_'])) {
                $startTime = $dateResult['start_']->format(Configure::read('Formats.time_short'));
            } else {
                $startTime = '';
            }
            if (!empty($dateResult['end_'])) {
                $endTime = $dateResult['end_']->format(Configure::read('Formats.time_short'));
            } else {
                $endTime = '';
            }

            $venueName = trim(
                 $dateResult['locationaddress']['name2'] . ' ' . $dateResult['locationaddress']['name1']);
            $conductorName = trim($dateResult['conductoraddress']['name2'] . ' ' . $dateResult['conductoraddress']['name1'] );
            $orchestraName = trim($dateResult['orchestraaddress']['name2']  . ' ' . $dateResult['orchestraaddress']['name1']);

//            Function returns Date Soloists in one string. Setting conductor ID to zero ensures that if someone
//            is both a conductor AND a soloist, he/she will appear in both columns which is desired for an Export
            if (!empty($dateResult['adate_works'])) {
                $dateSoloists = $this->datePerformerHelper->getSoloists($dateResult['id'], 0, ': ', 0);
            } else {
                $dateSoloists = '';
            }
            if (!empty($dateResult['adate_persons'])) {
                $datePersons = $this->getDatePersons($dateResult['id']);
            } else {
                $datePersons = '';
            }

//            Fetch Concert Program and Max Instrumentation as one string
            $concertProgram = $this->getProgram($dateResult);
            if ($dateResult['orchestra_id'] > 0) {
                $maxInstString = $this->getMaxInstrumentation($dateResult);
            } else {
                $maxInstString = '';
            }

//            Fetch all series in one string
            if (!empty($dateResult['adate_series'])) {
                $concertSeries = $this->getSeries($dateResult['adate_series']);
            } else {
                $concertSeries = '';
            }

//             +++++++ RENDER CELLS +++++++
            $spreadsheet->getActiveSheet()
                ->setCellValue(
                    $this->col_Date . $row,
                    $dateResult['date_']->format(Configure::read('Formats.date'))
                );
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Month . $row, $month);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Week . $row, $week);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Weekday . $row, $dateResult['weekday']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Start . $row, $startTime);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_End . $row, $endTime);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Project . $row,  $dateResult['sproject']['name'] );
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Event . $row,  $dateResult['seventtype']['name'] );
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Performance . $row, $l_performance);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Series . $row, $concertSeries);

//        +++++ CONDITIONAL FORMATTING FOR PERFORMANCE ROWS ++++++
            if ($dateResult['seventtype']['l_performance'] == 1) {
                $spreadsheet->getActiveSheet()->getStyle($this->col_Date . $row . ':' . $this->col_Supertitle . $row)
                    ->applyFromArray($this->getPerformanceRowFormat());
            }

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Persons . $row, $datePersons);
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted($this->col_Title . $row, $dateResult['programtitle']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Conductor . $row, $conductorName);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Soloist . $row, $dateSoloists);
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted($this->col_Program . $row, $concertProgram);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Instrumentation . $row, $maxInstString);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Orchestra . $row, $orchestraName);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Venue . $row, $venueName);

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Text . $row,  $dateResult['text'] );

            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Season . $row, $dateResult['sseason']['code']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Status . $row, $dateResult['sdatestatus']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Dress . $row, $dateResult['sdress']['name']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Notes . $row, $dateResult['notes'])
                ->getStyle($this->col_Notes . $row)
                ->getAlignment()->setWrapText(true);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Marketing . $row, $dateResult['adates_marketing']['notes'])
                ->getStyle($this->col_Marketing . $row)
                ->getAlignment()->setWrapText(true);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Pitch . $row, $dateResult['text_1']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Temperament . $row, $dateResult['text_2']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Catering . $row, $dateResult['l_logic_1'] == true ? 'yes' : 'no');
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_CateringDetails . $row, $dateResult['memo_1']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Recording . $row, $dateResult['l_logic_2'] == true ? 'yes' : 'no');
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_RecordingDetails . $row, $dateResult['text_4']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Transportation . $row, $dateResult['text_6']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Translations . $row, $dateResult['l_logic_3'] == true ? 'yes' : 'no');
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Supertitle . $row, $dateResult['text_5']);


            $row++;
        }

//        ++++++ FORMAT SPREADSHEET ++++++
        $this->getFormatting($upperLeftCell, $row, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }


    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['col_Date'] ?? __('adates.date_');
        $columnB = $repFileText['col_Weekday'] ?? __('adates.weekday');
        $columnC = $repFileText['col_Start'] ?? __('adates.start_');
        $columnD = $repFileText['col_End'] ?? __('adates.end_');
        $columnE = $repFileText['col_Series'] ?? __('adates.series');
        $columnF = $repFileText['col_Project'] ?? __('adates.project_id');
        $columnG = $repFileText['col_Title'] ?? __('adates.programtitle');
        $columnH = $repFileText['col_Event'] ?? __('adates.eventtype_id');
        $columnI = $repFileText['col_Performance'] ?? __('seventtypes.l_performance');
        $columnJ = $repFileText['col_Venue'] ?? __('adates.location_id');
        $columnK = $repFileText['col_Program'] ?? __('persons');
        $columnL = $repFileText['col_Instrumentation'] ?? __('max');
        $columnM = $repFileText['col_Conductor'] ?? __('adates.conductor_id');
        $columnN = $repFileText['col_Soloist'] ?? __('soloist');
        $columnO = $repFileText['col_Persons'] ?? __('adates.persons');
        $columnP = $repFileText['col_Orchestra'] ?? __('adates.orchestra_id');
        $columnQ = $repFileText['col_Dress'] ?? __('adates.dress_id');
        $columnR = $repFileText['col_Text'] ?? __('adates.text');
        $columnS = $repFileText['col_Notes'] ?? __('adates.notes');
        $columnT = $repFileText['col_Status'] ?? __('adates.status_id');
        $columnU = $repFileText['col_Season'] ?? __('adates.season_id');
        $columnV = $repFileText['col_Month'] ?? __('adates.month');
        $columnW = $repFileText['col_Week'] ?? __('adates.pweek');
        $columnX = $repFileText['col_Marketing'] ?? __('adates_marketing');
        $columnY = $repFileText['col_Pitch'];
        $columnZ = $repFileText['col_Temperament'];
        $columnAA = $repFileText['col_Catering'];
        $columnAB = $repFileText['col_CateringDetails'];
        $columnAC = $repFileText['col_Recording'];
        $columnAD = $repFileText['col_RecordingDetails'];
        $columnAE = $repFileText['col_Transportation'];
        $columnAF = $repFileText['col_Translations'];
        $columnAG = $repFileText['col_Supertitle'];
//        $columnJ = $repFileText['col_PgmNo'] ?? __('adates.programno');
//        $columnR = $repFileText['col_VenueCode'] ?? __('adates.location_id');

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK,
            $columnL,
            $columnM,
            $columnN,
            $columnO,
            $columnP,
            $columnQ,
            $columnR,
            $columnS,
            $columnT,
            $columnU,
            $columnV,
            $columnW,
            $columnX,
            $columnY,
            $columnZ,
            $columnAA,
            $columnAB,
            $columnAC,
            $columnAD,
            $columnAE,
            $columnAF,
            $columnAG
        );
    }

    public function getDatePersons($dateId)
    {
        $persons = $this->dateQueryHelper->getDatePersons($dateId)->getQuery()->toArray();

        foreach ($persons as $person) {
            $datePerson = trim( $person['saddress']['name2'] . ' ' . $person['saddress']['name1'] );
            if ($person['addressgroup_id'] > 0) {
                $datePerson .= ', ' . $person['saddressgroup']['name'];
            }
            if ($person['instrument_id'] > 0) {
                $datePerson .= ' - ' . strtolower($person['sinstrinstrument']['name']);
            }
            if ($person['function_id'] > 0) {
                $datePerson .= ', ' . $person['Saddressfunctionitems']['name'];
            }
            $datePerson .= $person['text'] ? '; ' . $person['text'] : '';
            $datePerson .= $person['notes'] ? ' (' . $person['notes'] . ')' : '';
            $datePersonArray[] = $datePerson;
        }

        return implode('; ', $datePersonArray);
    }

    protected function getProgram($date)
    {
//        make sure program is in proper order

        $dateProgramArray = [];
        $dateWorkArray = $date['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

        foreach ($dateWorkArray as $dateWork) {
            if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                $workEntry = mb_strtoupper($dateWork['swork']['scomposer']['lastname']);
                if (!is_null($dateWork['arrangement']) && (string)trim($dateWork['arrangement']) !== '') {
                    $workEntry .= ' [' . $dateWork['arrangement'] . ']: ';
                } else {
                    $workEntry .= ': ';
                }
            } else {
                $workEntry = '';
            }

            if (!is_null($dateWork['title2']) && (string)trim($dateWork['title2']) !== '') {
                $workEntry .= $dateWork['title2'];
            } else {
                $workEntry .= $dateWork['swork']['title1'];
            }
            $premiere = $dateWork['Sworkpremieres']['name'] ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                    'premiere'
                ) : ' ';
            $workEntry .= $premiere;
            $dateProgramArray[] = $workEntry;
        }

        return implode("\n", $dateProgramArray);
    }

    protected function getMaxInstrumentation($perfDate)
    {
        $this->instrumentation->addInstrumentationString($perfDate);

        /* this report outputs the Total Instrumentation in an in-line format
        *   elements of the max instrumentation are fetched individually and assembled
         */

        $maxInstArray = [];
        $maxInstArray[] = $perfDate['max_WindsBrass'];
        $maxInstArray[] = $perfDate['max_TimpPerc'];
        $maxInstArray[] = $perfDate['max_Harp'];

        $maxKeyboard = $perfDate['max_Keyboard'];
        $maxKeyboard .= $perfDate['max_instrumentation_grid_code']['keyboards'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['keyboards'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxKeyboard;

        $maxExtra = $perfDate['max_Extra'];
        $maxExtra .= $perfDate['max_instrumentation_grid_code']['extras'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['extras'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxExtra;

        $maxInstArray[] = $perfDate['max_instrumentation_grid_name']['vocals'];
        $maxInstArray[] = $perfDate['max_Strings'];

        $maxInstString = '';
        $maxInstString .= implode($this->instrumentation->getGroupSeparator(), array_filter($maxInstArray));

        return $maxInstString;
    }

    protected function getSeries($dateSeries)
    {
        $dateSeriesArray = [];
        foreach ($dateSeries as $series) {
            $dateSeriesArray[] = $series['sseries']['name'];
        }
        return implode('; ', array_filter($dateSeriesArray));
    }
    protected function getFormatting($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row - 1;
        $defaultFont = 'Calibri';
        $defaultFontSize = 11;

// Group similarly-formatted columns into arrays for ease of looping
        $dateTimeColumns = [
            $this->col_Date,
            $this->col_Month,
            $this->col_Week,
            $this->col_Weekday,
            $this->col_Start,
            $this->col_End
        ];
        foreach ($dateTimeColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setAutoSize(true);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_RIGHT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $centeredColumns = [$this->col_Performance, $this->col_Catering, $this->col_Recording, $this->col_Translations];
        foreach ($centeredColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setAutoSize(true);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_CENTER)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $standardColumns = [
            $this->col_Project,
            $this->col_Event,
            $this->col_Series,
            $this->col_Persons,
            $this->col_Title,
            $this->col_Conductor,
            $this->col_Soloist,
            $this->col_Orchestra,
            $this->col_Venue,
            $this->col_Dress,
            $this->col_Pitch,
            $this->col_Temperament,
            $this->col_CateringDetails,
            $this->col_RecordingDetails,
            $this->col_Transportation,
            $this->col_Supertitle
        ];
        foreach ($standardColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setWidth(20);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $autoFitColumns = [ $this->col_Season, $this->col_Status];
        foreach ($autoFitColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setAutoSize(true);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $largerColumns = [$this->col_Instrumentation, $this->col_Text];
        foreach ($largerColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setWidth(35);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP);
        }

        $noteColumns = [$this->col_Program, $this->col_Notes, $this->col_Marketing];
        foreach ($noteColumns as $column) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setWidth(55);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getFont()
                ->setName($defaultFont)
                ->setSize($defaultFontSize);

            $spreadsheet->getActiveSheet()
                ->getStyle($column . self::HEADER_ROW . ':' . $column . $finalRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_LEFT)
                ->setVertical(Alignment::VERTICAL_TOP)
                ->setWrapText(true);
        }

//        BOLD Header (row 1) and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':AG' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//       +++++ Freeze Panes +++++ after end time
        $spreadsheet->getActiveSheet()->freezePane('G' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => self::HEADER_ROW_SHADING) // Dark Red
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),
        );
    }

    protected function getPerformanceRowFormat()
    {
        return array(
//            fills the entire range; can also gradient fill, but this is
//            typically all that is needed
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => self::PERF_ROW_SHADING)
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'borderColor' => self::HEADER_ROW_SHADING
                ]
            ]
        );
    }

}
