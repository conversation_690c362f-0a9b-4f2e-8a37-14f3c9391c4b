<?php

namespace Customer\obf\reports\SeasonPgmsTwoCol;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;
use Customer\obf\reports\utility\NameFormatHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/* AUTHOR NOTES
*  Report produces a concise 2-col layout of season programs; alternate to Program Schedule and Season Overview reports
*  Re-creation of an OPAS Classic standard Word report
*/

class SeasonPgmsTwoCol extends ReportWord
{
    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for name formats
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->nameFormatHelper = new NameFormatHelper();

        // Set report font, table and paragraph styles
        $this->reportStyles = new SeasonPgmsTwoColStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//    Get all unique SEASONS from the dates selected by the user; report puts a page break between seasons
//      if the report is run for multiple seasons

    private function getSeasons(): array
    {
        $seasonArray = [];
        foreach ($this->datesResult as $dateResult) {
            $seasonArray[] = $dateResult['sseason'];
        }
        $seasonArray = array_unique(array_filter($seasonArray));

//        Make sure seasons are sorted chronologically
        usort(
            $seasonArray,
            function ($a, $b) {
                if ($a->datestart == $b->datestart) {
                    return 0;
                }
                return ($a->datestart < $b->datestart) ? -1 : 1;
            }
        );

        return $seasonArray;
    }


    private function renderReport()
    {
//        Set report formats based on client region and preferences
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }

//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

//        Render HEADER
        $topSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );
        $topSection->addText(
            $repFileText['report_title'],
            $this->reportStyles->titleFont,
            $this->reportStyles->defaultParagraphCenter
        );
        $topSection->addTextBreak(1, $defaultFont, $defaultParagraph);


        //        SWITCH TO 2-COLUMN LAYOUT
        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
                'colsNum' => (2),
                // "colsSpace" => () not known if this is twips or ?
            ]
        );

//        For Dates Selected by User, loop through each SEASON, putting a page break between them
        $s = 0;
        foreach ($this->getSeasons() as $season) {
            if ($s > 0) {
                $pageSection->addPageBreak();
            }

            $pageSection->addText(
                $season['name'],
                $this->reportStyles->headerFont,
                $this->reportStyles->headerParagraph
            );


//            Within each Season, loop through each Season+Project+Program Number combination
            foreach ($this->getProjectsInSeason($season['id']) as $project) {
//                Identify the Anchor Date (Date Object) for the Season + Project
                $anchorDate = $this->getAnchor($project);

//               Loop through all dates selected by the User. If the date is part of the
//                season+project+programNo combo, put performances in an array so the date range can be rendered
                $projectPerformanceDates = [];
                $projectPerformanceVenues = [];

                foreach ($this->datesResult as $performance) {
                    if ($performance['seventtype']['l_performance'] == 1
                        && $performance['season_id'] . ':' . $performance['project_id'] . ':' . $performance['programno'] === $project) {
                        $projectPerformanceDates[] = $performance['date_'];
                        $projectPerformanceVenues[] = $this->nameFormatHelper->getEntityName(
                            $performance['locationaddress']['name1'],
                            $performance['locationaddress']['name2'],
                            1
                        );
                    }
                }

//              +++ RENDER PROJECT NAME, PERFORMANCE DATES AND VENUE(S)
                if (!is_null($anchorDate['programno']) && (string)trim($anchorDate['programno'] != '')) {
                    $programNo = ' - ' . $repFileText['programno'] . ' ' . $anchorDate['programno'];
                } else {
                    $programNo = '';
                }

                $pageSection->addFormattedText(
                    $anchorDate['sproject']['name'] . $programNo,
                    $this->reportStyles->defaultFontBold,
                    $defaultParagraph
                );
                $pageSection->addText($this->getDateRange($projectPerformanceDates), $defaultFont, $defaultParagraph);
                $pageSection->addText(
                    implode('; ', array_filter(array_unique($projectPerformanceVenues))),
                    $defaultFont,
                    $defaultParagraph
                );

//              +++  RENDER PERSONNEL AND PROGRAM for the Anchor Date +++

                $mainConductor = $this->datePerformerHelper->getConductor(
                    $anchorDate['id'],
                    $anchorDate['conductor_id'],
                    0,
                    0
                );
                $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
                    $anchorDate['id'],
                    '</w:t><w:br/><w:t>',
                    0,
                    0,
                    0
                );
                $mainSoloists = $this->datePerformerHelper->getSoloists(
                    $anchorDate['id'],
                    $anchorDate['conductor_id'],
                    '</w:t><w:br/><w:t>',
                    0,
                    0
                );
                $pageSection->addText(
                    implode(
                        '</w:t><w:br/><w:t>',
                        array_filter(array($mainConductor, $dateWorkConductors, $mainSoloists))
                    ),
                    $defaultFont,
                    $defaultParagraph
                );

                if (!is_null($anchorDate['programtitle']) && (string)trim($anchorDate['programtitle']) !== '') {
                    $pageSection->addFormattedText(
                        $anchorDate['programtitle'],
                        $this->reportStyles->defaultFontItalic,
                        $defaultParagraph
                    );
                }

//              CONCERT PROGRAM:  ensure the date-works are in the right order
                $dateWorkArray = $anchorDate['adate_works'];
                foreach ($dateWorkArray as $key => $value) {
                    $programOrder[$key] = $value['work_order'];
                }
                array_multisort($programOrder, SORT_ASC, $dateWorkArray);

                foreach ($dateWorkArray as $dateWork) {
                    if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1 && $dateWork['l_encore'] == 0) {
                        $dateWorkOutput = $pageSection->addTextRun($this->reportStyles->programParagraph);
                        $dateWorkOutput->addFormattedText(
                            $dateWork['swork']['scomposer']['lastname'],
                            $this->reportStyles->defaultFontBold
                        );
                        $dateWorkOutput->addText(': ', $defaultFont);
                        if (!is_null($dateWork['title2']) && (string)trim($dateWork['title2']) !== '') {
                            $dateWorkOutput->addFormattedText($dateWork['title2'], $defaultFont);
                        } else {
                            $dateWorkOutput->addFormattedText($dateWork['swork']['title1'], $defaultFont);
                        }
                        if (!is_null($dateWork['arrangement']) && (string)trim($dateWork['arrangement']) !== '') {
                            $dateWorkOutput->addFormattedText(' (' . $dateWork['arrangement'] . ')', $defaultFont);
                        }
                        if ($dateWork['premiere_id'] > 0) {
                            $dateWorkOutput->addFormattedText(
                                ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __('premiere'),
                                $this->reportStyles->defaultFontItalic
                            );
                        }
                    }
                }


                $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            }

            $s++;
        }
    }

//    This function breaks the selected dates down into each unique season:project:program number set
//      Adjusted to work for reports that span multiple seasons
    private function getProjectsInSeason($seasonId): array
    {
        $seasonProject = '';
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] !== $seasonProject
                && $dateResult['season_id'] === $seasonId) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

    //    Concert Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the Header and program
    private function getAnchor(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
    }

    private function getDateRange($projectPerformances): string
    {
        $startDate = reset($projectPerformances);
        $endDate = end($projectPerformances);
        $dateRangeOutput = $this->dateQueryHelper->getFormattedDateRange($startDate, $endDate, 2);

        return $dateRangeOutput;
    }

}




