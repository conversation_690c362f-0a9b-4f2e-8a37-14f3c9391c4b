<?php

namespace Customer\obf\reports\WorkPerformanceHistory;

use App\Reports\Report;
use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;

use Customer\obf\reports\utility\WorkQueryHelper;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\NameFormatHelper;

use Customer\obf\reports\OnWord\OnWord;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;

use ReflectionException;

class WorkPerformanceHistory extends ReportWord
{
//    All column dimensions given in inches
    const COL_1_WIDTH = 0.36;
    const COL_2_WIDTH = 1.45;
    const COL_3_WIDTH = 1.80;
    const COL_4_WIDTH = 1.86;
    const COL_5_WIDTH = 1.92;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;

    /**
     * Helper class for the Work query (sworks).
     *
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * Helper class to format composer, conductor, soloist names
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * Helper class for the date query of the dates.
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Prepared array for the rendering
     * @var array
     */
    private $worksResult;
    private $reportStyles;

    /*********************************************/

    public function initialize()
    {
        parent::initialize();

        $this->workQueryHelper = new WorkQueryHelper();
        $this->dateQueryHelper = new DateQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();

        $this->reportStyles = new WorkPerformanceHistoryStyles();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the works selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): ReportsInterface
    {
        $workQuery = $this->workQueryHelper
            ->getWorks($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->worksResult = $workQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    private function renderReport()
    {
//        report title and column headings from .rep file
        $repFileText = $this->getRepFileParams();

//  default font and paragraph styles created in WorkPerformanceHistoryStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
            $dateRangeFormat = 1;
        } else {
            $paperSize = "A4";
            $dateRangeFormat = 3;
        }

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        render header and footer
        $this->renderHeader($pageSection, $repFileText);
        $this->renderFooter($pageSection);

//      fetch all works as a single array then sort by composer last,first and work title1
//        set composer names to upper to properly order names like diCaprio
        $workArray = $this->worksResult;
        foreach ($workArray as $key => $value) {
            $lastName[$key] = mb_strtoupper($value['scomposer']['lastname']);
            $firstName[$key] = mb_strtoupper($value['scomposer']['firstname']);
            $title[$key] = $value['title1'];
        }
        array_multisort($lastName, SORT_ASC, $firstName, SORT_ASC, $title, SORT_ASC, $workArray);

//        fetch unique composers
        $composerArray = [];
        foreach ($workArray as $workResult) {
            $composerArray[] = $workResult['scomposer'];
        }
        $composers = array_unique($composerArray);

//       +++ OUTER LOOP through composers +++
        foreach ($composers as $composer) {
            $composerName = $this->nameFormatHelper->getEntityName(
                $composer['lastname'],
                $composer['firstname'],
                1
            );

            $pageSection->addTextBreak(1, $this->reportStyles->headerFont, $defaultParagraph);
            $pageSection->addText($composerName, $this->reportStyles->headerFont, $defaultParagraph);

            $i = 0;  // work iteration; used to put a blank line between works 2 through n.
            foreach ($workArray as $workResult) {
                if ($composer['id'] === $workResult['composer_id'] && $workResult['l_intermission'] == 0) {
//                    FETCH ALL DATE-WORK Records for the given composition
                    $allDateWorksForTheWork = $this->dateQueryHelper->getDatesforWorkID($workResult['id'])->getQuery(
                    )->toArray();

                    $t = 0; // total number of performances
                    $workSeasonProjectPerformances = [];

//                    Fetch each Season:Project combination on which this work appeared.
//                    In the event of bad data entry - a missing project - substitute a zero so the row will still output
                    foreach ($allDateWorksForTheWork as $dateForTheWork) {
                        if ($dateForTheWork['Adates']['seventtype']['l_performance'] == 1 && $dateForTheWork['Adates']['planninglevel'] < 4) {
                            $workSeasonProjectPerformances[] = $dateForTheWork['Adates']['season_id'] . ':' . ($dateForTheWork['Adates']['project_id'] ?? 0);
                            $t++;
                        }
                    }
//                    Each unique Season:Project combination
                    $perfDateSeasonProject = array_unique($workSeasonProjectPerformances);

//                    +++ MAIN LOOP - WORK OUTPUT AND EACH PERFORMANCE ROW ++++
                    if (!empty($allDateWorksForTheWork)) {
//                     RENDER WORK TITLE AND TOTAL PERFORMANCES
//                    Blank line between works
                        if ($i > 0) {
                            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                        }
                        $workTitle = $workResult['title1'];
                        $workTitle .= $workResult['arrangement'] ? ' (' . $workResult['arrangement'] . ')' : '';

                        $workTitleTextRun = $pageSection->addTextRun($this->reportStyles->workTitleParagraph);
                        $workTitleTextRun->addFormattedText($workTitle, $this->reportStyles->workTitleFont);
//                        Copy/Paste a Tab from Word as "\t" seems to be not 100% reliable
                        $workTitleTextRun->addText(
                            "	",
                            $defaultFont,
                            $defaultParagraph
                        );
                        $workTitleTextRun->addText($repFileText['total'] . ': ' . $t, $defaultFont, $defaultParagraph);

                        $workTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);

                        $p = 1; // program loop for the work (how many season:projects represented)

//                        +++ LOOP FOR EACH PERFORMANCE ROW +++
                        foreach ($perfDateSeasonProject as $dateProject) {
//                            identify the anchor date for the season+project
                            foreach ($allDateWorksForTheWork as $dateWork) {
                                if ($dateWork['Adates']['season_id'] . ':' . ($dateWork['Adates']['project_id'] ?? 0) === $dateProject
                                    && $dateWork['Adates']['planninglevel'] < 4
                                    && $dateWork['Adates']['seventtype']['l_performance'] == 1
                                ) {
//                                    set the performance date and fetch the conductor and soloists for the Date-Work
                                    $perfDate = $dateWork['Adates'];
                                    $dateWorkSoloistString = $this->getDateWorkSoloists($dateWork);
                                    if ($dateWork['conductor_id'] > 0) {
                                        $dateWorkConductorName = $this->nameFormatHelper->getEntityName(
                                            $dateWork['conductoraddress']['name1'],
                                            $dateWork['conductoraddress']['name2'],
                                            1
                                        );
                                    } else {
                                        $dateWorkConductorName = '';
                                    }
                                }
                            }
//                            get all performance dates and render the date range
                            $dateRangeOutput = $this->getPerfDateRange(
                                $dateProject,
                                $allDateWorksForTheWork,
                                $dateRangeFormat
                            );

//                        shade even rows of the table
                            if ($p % 2 == 0) {
                                $historyCellStyle = $this->reportStyles->shadedCell;
                            } else {
                                $historyCellStyle = $this->reportStyles->defaultCell;
                            }

//                            +++ START RENDERING TABLE +++
                            $workTable->addRow();

//                           COLUMN 1 - Project/Program count
                            $workTable->addCell(
                                Converter::inchToTwip(self::COL_1_WIDTH),
                                $this->reportStyles->noBorderCell
                            )
                                ->addText($p, $defaultFont, $this->reportStyles->defaultParagraphRight);

//                           COLUMN 2 -  Performance Date Range
                            $workTable->addCell(
                                Converter::inchToTwip(self::COL_2_WIDTH),
                                $historyCellStyle
                            )
                                ->addText($dateRangeOutput, $defaultFont, $defaultParagraph);

//                           COLUMN 3 - Project
                            $workTable->addCell(
                                Converter::inchToTwip(self::COL_3_WIDTH),
                                $historyCellStyle
                            )
                                ->addText(
                                    $perfDate['sproject']['name'] ?? '',
                                    $defaultFont,
                                    $defaultParagraph
                                );

//                           COLUMN 4 - Conductor (date-work and primary)
//                                     Date Primary Conductor
                            if ($perfDate['conductor_id'] > 0) {
                                $conductorName = $this->nameFormatHelper->getEntityName(
                                    $perfDate['conductoraddress']['name1'],
                                    $perfDate['conductoraddress']['name2'],
                                    1
                                );
                            } else {
                                $conductorName = '';
                            }

//                         Date-Work Conductor fetched above; output here
                            if ((string)trim($dateWorkConductorName) !== '') {
                                $conductorOutput = $dateWorkConductorName;
                            } else {
                                $conductorOutput = $conductorName;
                            }

                            $workTable->addCell(
                                Converter::inchToTwip(self::COL_4_WIDTH),
                                $historyCellStyle
                            )
                                ->addFormattedText($conductorOutput, $defaultFont, $defaultParagraph);

//                         COLUMN 5 - Soloists
                            $workTable->addCell(
                                Converter::inchToTwip(self::COL_5_WIDTH),
                                $historyCellStyle
                            )
                                ->addText($dateWorkSoloistString, $defaultFont, $defaultParagraph);

                            $p++;
                        }
                        $i++;
                    }
                }
            }
//            Extra blank line between composers
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        }
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    private function getPerfDateRange(string $dateProject, array $allDateWorksForTheWork, string $dateRangeFormat)
    {
//        fetch each date in the season + project to which the work is assigned

        foreach ($allDateWorksForTheWork as $key => $value) {
            $date[$key] = $value['Adates']['date_'];
            $start[$key] = $value['Adates']['start_'];
        }
        array_multisort($date, SORT_ASC, $start, SORT_ASC, $allDateWorksForTheWork);

        $perfDateArray = [];
        foreach ($allDateWorksForTheWork as $performanceDate) {
            if ($performanceDate['Adates']['season_id'] . ':' . $performanceDate['Adates']['project_id'] === $dateProject &&
                $performanceDate['Adates']['season_id'] > 0 && $performanceDate['Adates']['project_id'] > 0
                && $performanceDate['Adates']['seventtype']['l_performance'] == 1) {
                $perfDateArray[] = $performanceDate['Adates']['date_'];
            }
        }
//       This is the first and last performance date within the season and project.
        $minDate = reset($perfDateArray);
        $maxDate = end($perfDateArray);

        $dateRange = $this->dateQueryHelper->getFormattedDateRange(
            $minDate,
            $maxDate,
            $dateRangeFormat
        );

        return $dateRange;
    }

    public function getDateWorkSoloists($dateWork)
    {
        $dateWorkSoloistArray = [];

        foreach ($dateWork['adatework_soloists'] as $soloist) {
            $soloistName = $this->nameFormatHelper->getEntityName(
                $soloist['saddress']['name1'],
                $soloist['saddress']['name2'],
                1
            );

//            do not output instrument if choir
            if ($soloist['sinstrinstrument']['sinstrsection']['syssection_id'] !== 19) {
                $soloistName .= ', ' . mb_strtolower($soloist['sinstrinstrument']['name']);
            }

            $dateWorkSoloistArray[] = $soloistName;
        }

        return implode("; ", $dateWorkSoloistArray);
    }

    public function renderHeader($pageSection, $repFileText)
    {
        $header = $pageSection->addHeader();

        $header->addText(
            $repFileText['report_title'],
            $this->reportStyles->titleFont,
            $this->reportStyles->defaultParagraphCenter
        );

        $headerTable = $header->addTable($this->reportStyles->defaultTableStyle);
        $headerTable->addRow();
        $headerTable->addCell(
            Converter::inchToTwip(self::COL_1_WIDTH),
            $this->reportStyles->titleCell
        )
            ->addText(
                $repFileText['Column_1'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );

        $headerTable->addCell(
            Converter::inchToTwip(self::COL_2_WIDTH),
            $this->reportStyles->titleCell
        )
            ->addText(
                $repFileText['Column_2'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );

        $headerTable->addCell(
            Converter::inchToTwip(self::COL_3_WIDTH),
            $this->reportStyles->titleCell
        )
            ->addText(
                $repFileText['Column_3'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );

        $headerTable->addCell(
            Converter::inchToTwip(self::COL_4_WIDTH),
            $this->reportStyles->titleCell
        )
            ->addText(
                $repFileText['Column_4'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );

        $headerTable->addCell(
            Converter::inchToTwip(self::COL_5_WIDTH),
            $this->reportStyles->titleCell
        )
            ->addText(
                $repFileText['Column_5'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );
    }

    public function renderFooter($pageSection)
    {
        $footer = $pageSection->addFooter();

        $footerText = htmlspecialchars(Configure::read('CustomerSettings.name'));
        $footerText .= "	"; // TAB copied/pasted from Word
        $footerText .= "	"; // TAB copied/pasted from Word
        $footerText .= date(Configure::read('Formats.date'));
        $footer->addText($footerText, $this->reportStyles->defaultFont, $this->reportStyles->footerParagraphStyle);
    }


}
