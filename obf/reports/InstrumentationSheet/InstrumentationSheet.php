<?php

namespace Customer\obf\reports\InstrumentationSheet;

// elements from settings.php
use App\Reports\ReportWord;
use Cake\Core\Configure;
use Carbon\Carbon;

use Carbon\CarbonInterval;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentation;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Re-creation of (old) OPAS Classic report, with some format updates
 * Run for a Project - shows performance dates and personnel, then detailed
 * instrumentation for each work
 */

class InstrumentationSheet extends ReportWord
{

    const WIND_COL = 0.31;
    const TIMP_COL = 0.35; // wider headings
    const STRING_COL = 0.95;
    const TOTAL_COL = 0.52;
    const DURATION_COL = 0.70;
    /**
     * Helper class for the Events selected by user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Helper class for the Conductor and Soloists in report header
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for Instrumentation
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    /*********************************************/

    function initialize()
    {
        parent::initialize(); //extend functionality from your parent controller to a child controller

        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->reportStyles = new InstrumentationSheetStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    public function collect(array $where = []) // date records selected by user to dateQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//    Typically the use case is to run this report for one project at a time. However, the user could run it for
//      more than one. this function breaks the selected dates down into each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concert Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the report body
    private function getAnchor(string $project): int
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult['id'];
            }
        }
    }

    private function renderReport()
    {
//        Set paper size based on client region and preferences
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.60),
                'breakType' => 'continuous',
            ]
        );
//      Default font and paragraph styles used most often. Variations are defined at the output.
        $defaultFont = $this->reportStyles->reportFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $centerParagraph = $this->reportStyles->defaultParagraphCenter;
        $repFileText = $this->getRepFileParams();

//        if the desired formal/long date is defined in settings.php, use that. if not use date
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');

//                    ++++ RENDER FOOTER ++++
        $footer = $pageSection->addFooter();
        $footer->addText($repFileText['Printed'] . ' ' . date($topDateFormat), $defaultFont, $defaultParagraph);

//      PROJECT LOOP - page break between
        $p = 0;
        foreach ($this->getProjects() as $project) {
            if ($p > 0) {
                $pageSection->addPageBreak();
            }

//          ANCHOR DATE LOOP
            foreach ($this->datesResult as $concertDate) {
                if ($concertDate['id'] === $this->getAnchor($project)) {
//            RENDER REPORT TITLE
                    $title = $repFileText['Report_Title'] . ' – ' .
                        trim(
                            htmlspecialchars(
                                $concertDate['orchestraaddress']['name2']
                            ) . $concertDate['orchestraaddress']['name1']
                        );
                    $pageSection->addText($title, $this->reportStyles->reportTitleFont, $centerParagraph);
                    $pageSection->addLine(
                        ['weight' => 1, 'width' => 500, 'height' => 0, 'posVertical' => 'center'],
                        $centerParagraph
                    );

//            +++ RENDER HEADER +++
                    $projectName = mb_strtoupper($concertDate['sproject']['name']);
                    if (!empty(trim($concertDate['programno']))) {
                        $projectName .= ' ' . $repFileText['programNo_Prefix'] ?? __('adates.programno');
                        $projectName .= ' ' . mb_strtoupper($concertDate['programno']);
                    }
                    $pageSection->addText(
                        $projectName,
                        $this->reportStyles->reportHeaderFont,
                        $defaultParagraph
                    );

                    if (!empty($concertDate['programtitle'])) {
                        $pageSection->addFormattedText(
                            $concertDate['programtitle'],
                            $this->reportStyles->reportHeaderFont,
                            $defaultParagraph
                        );
                    }
                    $this->renderHeaderDates(
                        $this->datesResult,
                        $project,
                        $pageSection,
                        $topDateFormat,
                        $topTimeFormat
                    );
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                    separate class for conductor and soloist(s) and date-work conductors in the report HEADING.
//                    Main conductor, dote-work conductor and soloist are output separately for greater control over formatting
                    $mainConductor = $this->datePerformerHelper->getConductor(
                        $concertDate['id'],
                        $concertDate['conductor_id'],
                        0
                    );
                    if (!empty($mainConductor)) {
                        $pageSection->addText($mainConductor, $this->reportStyles->reportFontBold, $defaultParagraph);
                    }

//                    method returns date-work conductors in a single text string
                    $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
                        $concertDate['id'],
                        '</w:t><w:br/><w:t>',
                        0
                    );
                    if (!empty($dateWorkConductors)) {
                        $pageSection->addText(
                            $dateWorkConductors,
                            $this->reportStyles->defaultFontBold,
                            $defaultParagraph
                        );
                    }

//                    method returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
                    $mainSoloist = $this->datePerformerHelper->getSoloists(
                        $concertDate['id'],
                        $concertDate['conductor_id'],
                        '</w:t><w:br/><w:t>',
                        0
                    );
                    if (!empty($mainSoloist)) {
                        $pageSection->addText($mainSoloist, $defaultFont, $defaultParagraph);
                    }

//              ++++ RENDER MAX INSTRUMENTATION ++++
               /* this report outputs the max instrumentation in a table (very rare) so instead of using the
               built-in max instrumentation screen we get the full max instrumentation array and output each
               element individually - one instrument per cell.
               */
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $pageSection->addText(
                        $repFileText['Total_Header'],
                        $this->reportStyles->reportFontBold,
                        $defaultParagraph
                    );

                    $workDuration = 0;
                    foreach ($concertDate['adate_works'] as $dateWork) {
                        if (!empty($dateWork['duration'])) {
                            $carbonDuration = Carbon::createFromFormat('H:i:s', trim($dateWork['duration']));
                            $workDuration += $carbonDuration->second + ($carbonDuration->minute * 60) + ($carbonDuration->hour * 3600);
                        }
                    }
//                    CarbonInterval::minutes($workMinute)->cascade()->forHumans()
                    $concertDuration = CarbonInterval::seconds($workDuration)->cascade()->toArray();
                    $concertDurationOutput = $concertDuration['hours'] . ':'
                        . str_pad($concertDuration['minutes'], 2, '0', STR_PAD_LEFT)
                        . ':'
                        . str_pad($concertDuration['seconds'], 2, '0', STR_PAD_LEFT);

                    $this->renderMaxInstrumentation(
                        $concertDate,
                        $pageSection,
                        $defaultFont,
                        $centerParagraph,
                        $repFileText,
                        $concertDurationOutput
                    );

//                ++++    RENDER CONCERT PROGRAM WITH INSTRUMENTATION  ++++
                    $pageSection->addTextBreak(2, $defaultFont, $defaultParagraph);
                    $programAndInstrumentation = new InstrumentationSheetProgram(
                        $concertDate,
                        $pageSection,
                        $repFileText
                    );
                    $programAndInstrumentation->renderProgram();
                }
            }

            $p++;
        }
    }

    private function renderHeaderDates($datesResult, $project, $pageSection, $topDateFormat, $topTimeFormat)
    {
        //            If all concerts take place in the same venue, venue name is below concert dates, otherwise after concert date
        foreach ($datesResult as $headerDate) {
            if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                && $headerDate['seventtype']['l_performance'] == 1) {
                $headerVenue = $this->getHeaderVenues($project);
                $printConcertDate = $headerDate['date_']->format($topDateFormat)
                    . ' ' . __('at') . ' ' . $headerDate['start_']->format($topTimeFormat);

//                   if $headerVenue is empty, then there is more than one concert venue so output venue after each concert date
                if (strlen(trim($headerVenue)) == 0 && $headerDate['location_id'] > 0) {
                    $headerVenueName = trim(
                        htmlspecialchars(
                            $headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1']
                        )
                    );
                    $printConcertDate .= ' - ' . $headerVenueName;
                }
                $pageSection->addText(
                    $printConcertDate,
                    $this->reportStyles->reportFont,
                    $this->reportStyles->defaultParagraph
                );
            }
        }

        //              If all concerts take place in the same venue, print that venue under the final concert date
        if (strlen(trim($headerVenue)) != 0) {
            $pageSection->addText(
                $headerVenue,
                $this->reportStyles->reportFont,
                $this->reportStyles->defaultParagraph
            );
        }
    }

    public function getHeaderVenues($project)
    {
        $headerVenues = [];
        foreach ($this->datesResult as $venueDate) {
            if ($venueDate['season_id'] . ':' . $venueDate['project_id'] . ':' . $venueDate['programno'] == $project
                && $venueDate['seventtype']['l_performance'] == 1) {
                $headerVenues[] = trim(
                    htmlspecialchars(
                        $venueDate['locationaddress']['name2'] . ' ' . $venueDate['locationaddress']['name1']
                    )
                );
            }
        }
        $venueCount = sizeof(array_unique($headerVenues));
//        if there is only one venue, return that one name as a string and it will be displayed UNDER the concert dates
        if ($venueCount == 1) {
            return implode('', array_unique($headerVenues));
        }
    }


    public function renderMaxInstrumentation(
        $perfDate,
        $pageSection,
        $defaultFont,
        $centerParagraph,
        $repFileText,
        $finalDurationString
    ) {
        $maxInstFont = $this->reportStyles->reportFontBold;

//        FETCH DATA
        $this->instrumentation->addInstrumentationString($perfDate);
        $maxInst = $perfDate['max_array'];
        $dblMark = Configure::read('Instrumentation.dblMark', '*');
        $maxFL = $maxInst['flute']['text'] ? $dblMark . $maxInst['flute']['number'] : $maxInst['flute']['number'];
        $maxOB = $maxInst['oboe']['text'] ? $dblMark . $maxInst['oboe']['number'] : $maxInst['oboe']['number'];
        $maxCL = $maxInst['clarinet']['text'] ? $dblMark . $maxInst['clarinet']['number'] : $maxInst['clarinet']['number'];
        $maxBN = $maxInst['bassoon']['text'] ? $dblMark . $maxInst['bassoon']['number'] : $maxInst['bassoon']['number'];
        $maxHN = $maxInst['horn']['text'] ? $dblMark . $maxInst['horn']['number'] : $maxInst['horn']['number'];
        $maxTP = $maxInst['trumpet']['text'] ? $dblMark . $maxInst['trumpet']['number'] : $maxInst['trumpet']['number'];
        $maxTB = $maxInst['trombone']['text'] ? $dblMark . $maxInst['trombone']['number'] : $maxInst['trombone']['number'];
        $maxTU = $maxInst['tuba']['text'] ? $dblMark . $maxInst['tuba']['number'] : $maxInst['tuba']['number'];
//      Timp, Perc and others are output directly in the table cells below

//        RENDER TABLE
        $maxInstTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);

//        Header ROW
        $maxInstTable->addRow();
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['FL'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['OB'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['CL'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['BN'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['HN'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['TP'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['TB'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['TU'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['TIMP'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->greyShadedCell)
            ->addText($repFileText['PERC'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['HARP'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['KYBD'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['EXTRA'], $defaultFont, $centerParagraph);

        $maxInstTable->addCell(Converter::inchToTwip(self::STRING_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['STRINGS'], $defaultFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TOTAL_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['WBP_TOTAL'], $this->reportStyles->reportFontBold, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TOTAL_COL), $this->reportStyles->greyShadedCellLeftBorder)
            ->addText($repFileText['STRINGS'], $this->reportStyles->reportFontBold, $centerParagraph);
        $maxInstTable->addCell(
            Converter::inchToTwip(self::TOTAL_COL),
            $this->reportStyles->greyShadedCellLeftRightBorder
        )
            ->addText($repFileText['GR_TOTAL'], $this->reportStyles->reportFontBold, $centerParagraph);
        $maxInstTable->addCell(
            Converter::inchToTwip(self::DURATION_COL),
            $this->reportStyles->greyShadedCellRightBorder
        )
            ->addText($repFileText['DURATION'], $defaultFont, $centerParagraph);

        $maxInstTable->addRow();
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxFL, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxOB, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxCL, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxBN, $maxInstFont, $centerParagraph);

        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxHN, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxTP, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxTB, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL), $this->reportStyles->defaultCell)
            ->addText($maxTU, $maxInstFont, $centerParagraph);

        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxInst['timpani']['number'], $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->defaultCell)
            ->addText($maxInst['percussion']['number'], $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxInst['harp']['number'], $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxInst['keyboard']['number'], $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TIMP_COL), $this->reportStyles->borderLeftCell)
            ->addText($maxInst['extra']['number'], $maxInstFont, $centerParagraph);


        $strings = $maxInst['violin1']['number'] . '.' . $maxInst['violin2']['number'] . '.' . $maxInst['viola']['number']
            . '.' . $maxInst['cello']['number'] . '.' . $maxInst['bass']['number'];
        $totalWBP = $maxInst['flute']['number'] + $maxInst['oboe']['number'] + $maxInst['clarinet']['number'] + $maxInst['bassoon']['number']
            + $maxInst['horn']['number'] + $maxInst['trumpet']['number'] + $maxInst['trombone']['number'] + $maxInst['tuba']['number']
            + $maxInst['timpani']['number'] + $maxInst['percussion']['number']
            + $maxInst['harp']['number'] + $maxInst['keyboard']['number'] + $maxInst['extra']['number'];
        $totalSTR = $maxInst['violin1']['number'] + $maxInst['violin2']['number'] + $maxInst['viola']['number']
            + $maxInst['cello']['number'] + $maxInst['bass']['number'];

        $maxInstTable->addCell(Converter::inchToTwip(self::STRING_COL), $this->reportStyles->borderLeftCell)
            ->addText($strings, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TOTAL_COL), $this->reportStyles->borderLeftCell)
            ->addText($totalWBP, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TOTAL_COL), $this->reportStyles->borderLeftCell)
            ->addText($totalSTR, $maxInstFont, $centerParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::TOTAL_COL), $this->reportStyles->borderLeftRightCell)
            ->addText($totalWBP + $totalSTR, $maxInstFont, $centerParagraph);

        $maxInstTable->addCell(Converter::inchToTwip(self::DURATION_COL), $this->reportStyles->borderRightCell)
            ->addText($finalDurationString, $maxInstFont, $this->reportStyles->defaultParagraphRight);
    }
}
