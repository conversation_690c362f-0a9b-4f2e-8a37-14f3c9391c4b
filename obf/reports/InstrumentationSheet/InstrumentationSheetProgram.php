<?php

namespace Customer\obf\reports\InstrumentationSheet;

use Customer\obf\reports\NSInstSheet\NSInstSheetStyles;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\ComposerQueryHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;


/* This class renders the Program Table along with detailed
*  instrumentation for each work. This layout is old-fashioned but
 * still used by some orchestras

*/

class InstrumentationSheetProgram
{
//    column width used for Instrumentation Table
    const WORK_LEFT_COL = 6.60;
    const WORK_RIGHT_COL = 0.65;

    const COL_1_WIDTH = 0.30;
    const COL_2_WIDTH = 0.41;
    const COL_3_WIDTH = 1.50;
    const COL_STRINGLABEL_WIDTH = 0.82;
    const COL_FULL_TABLE = 6.95; // includes the 'indent' COL_1 above

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct($concert, Section $pageSection, $repFileText)
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->concert = $concert;
        $this->pageSection = $pageSection;

//        labels from the report .rep file
        $this->repFileText = $repFileText;

        $this->reportStyles = new InstrumentationSheetStyles();
    }


    public function renderProgram()
    {
        $defaultFont = $this->reportStyles->reportFont;
        $boldFont = $this->reportStyles->reportFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultCell = $this->reportStyles->defaultCellStyle;
        $programCell = $this->reportStyles->programCell;


//        sort date-works by work_order
        $dateWorkArray = $this->concert['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

        /*  Each work is output in a block consisting of 3 tables (to keep formatting
        *  and future modifications easy).
         * Work Composer and title | duration
         * Work Instrumentation
         * Work notes and percussion list
         *
         */


        foreach ($dateWorkArray as $dateWork) {
            $programTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

            $composerName = '';
            if ($dateWork['l_encore'] == 1) {
                $composerName .= '** ENCORE ** ';
            }
            if ($dateWork['swork']['l_intermission'] == 0) {
                $composerName .= $this->composerQueryHelper->getComposerName(
                    $dateWork['swork']['scomposer']['lastname'],
                    $dateWork['swork']['scomposer']['firstname'],
                    1
                );
            }
            if ($dateWork['swork']['l_intermission'] == 0) {
                $composerName .= ': ';
            }
            if (strlen(trim($dateWork['title2'])) > 0) {
                $dateWorkTitle = $dateWork['title2'];
            } else {
                $dateWorkTitle = $dateWork['swork']['title1'];
            }
            $dateWorkTitle .= $dateWork['arrangement'] ? ' - ' . $dateWork['arrangement'] : '';
            if (substr($dateWork['duration'], 0, 2) == '00') {
                $dateWorkDuration = substr($dateWork['duration'], 3);
            } else {
                $dateWorkDuration = substr($dateWork['duration'], 1);
            }

            $programTable->addRow();
            $programTable->addCell(Converter::inchToTwip(self::WORK_LEFT_COL), $programCell)
                ->addFormattedText(
                    $composerName . $dateWorkTitle,
                    $boldFont,
                    $defaultParagraph
                );
            $programTable->addCell(Converter::inchToTwip(self::WORK_RIGHT_COL), $programCell)
                ->addText(
                    $dateWorkDuration,
                    $boldFont,
                    $this->reportStyles->defaultParagraphRight
                );

//            INSTRUMENTATION TABLE
            if ($dateWork['swork']['l_intermission'] == 0) {
                $instrumentationTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

//                ROW ONE - Flute Horn Timpani
                $instrumentationTable->addRow();
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['FL'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['flute'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['flute_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['HN'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['horn'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['horn_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['TIMP'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['timpani'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['timpani_text'], $defaultFont, $defaultParagraph);

 //                ROW TWO - Oboe Trumpet Percussion
                $instrumentationTable->addRow();
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['OB'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['oboe'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['oboe_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['TP'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['trumpet'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['trumpet_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['PERC'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['percussion'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['percussion_text'], $defaultFont, $defaultParagraph);

//                ROW THREE - Clarinet Trombone Harp
                $instrumentationTable->addRow();
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['CL'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['clarinet'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['clarinet_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['TB'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['trombone'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['trombone_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['HARP'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['harp'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['harp_text'], $defaultFont, $defaultParagraph);

//                ROW FOUR - Bassoon Tuba Keyboard (renders keyboard grid)
                $instrumentationTable->addRow();
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['BN'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['bassoon'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['bassoon_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['TU'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['tuba'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($dateWork['tuba_text'], $defaultFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['KYBD'] .':', $boldFont, $defaultParagraph);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['keyboard'], $defaultFont, $defaultParagraph);
                $keyboardGrid = $this->getInstrumentGrid($dateWork['adatework_keyboards']);
                $keyboardOutput = $dateWork['keyboard_text'];
                if (!empty($dateWork['keyboard_text']) && !empty($keyboardGrid)) {
                    $keyboardOutput .=  '</w:t><w:br/><w:t>';
                }
                $keyboardOutput .= $keyboardGrid;
                $instrumentationTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($keyboardOutput, $defaultFont, $defaultParagraph);

//                ROW FIVE - Blank | Strings (combine cells for label  Extra (renders extra grid)
//                Uses a dedicated table so the columns line up better
                $stringRowTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
                $stringRowTable->addRow();
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText('', $boldFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_STRINGLABEL_WIDTH), $defaultCell)
                    ->addText($this->repFileText['STRINGS'] .':', $boldFont, $defaultParagraph);

                if($dateWork['violin1'] + $dateWork['violin2'] + $dateWork['viola'] + $dateWork['cello'] + $dateWork['bass'] > 0 ){
                    $dateWorkStrings = $dateWork['violin1'] . '.' . $dateWork['violin2'] . '.'
                        . $dateWork['viola'] . '.' . $dateWork['cello'] . '.' . $dateWork['bass'];
                } else {
                    $dateWorkStrings = $dateWork['strings_text'];
                }
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $defaultCell)
                    ->addText($dateWorkStrings, $defaultFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $defaultCell)
                    ->addText($this->repFileText['EXTRA'] .':', $boldFont, $defaultParagraph);
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),$defaultCell)
                    ->addText($dateWork['extra'], $defaultFont, $defaultParagraph);
                $extraGrid = $this->getInstrumentGrid($dateWork['adatework_extras']);
                $extraOutput = $dateWork['extra_text'];
                if (!empty($dateWork['extra_text']) && !empty($extraGrid)) {
                    $extraOutput .=  '</w:t><w:br/><w:t>';
                }
                $extraOutput .= $extraGrid;
                $stringRowTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH),$defaultCell)
                    ->addText($extraOutput, $defaultFont, $defaultParagraph);

//                NOTE ROW - solo instruments; percussion; chorus; instrumentation notes
//                create new table to better align columns
                $noteRowTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
                $noteRowTable->addRow();
                $noteRowTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $defaultCell)
                    ->addText('', $defaultFont, $defaultParagraph);

                $noteCell = $noteRowTable->addCell(Converter::inchToTwip(self::COL_FULL_TABLE), $defaultCell);
                $noteCellTextRun = $noteCell->addTextRun($defaultParagraph);
                $soloInstr = $this->getInstrumentGrid($dateWork['adatework_soloinstr']);
                if (!empty($soloInstr)) {
                    $noteCellTextRun->addText($this->repFileText['SOLO'] .': ', $boldFont);
                    $noteCellTextRun->addText($soloInstr . '</w:t><w:br/><w:t>', $defaultFont);
                }
                $percussion = $this->getInstrumentGrid($dateWork['adatework_percussions']);
                if (!empty($percussion)) {
                    $noteCellTextRun->addText($this->repFileText['PERCUSSION'] . ': ', $boldFont);
                    $noteCellTextRun->addText($percussion . '</w:t><w:br/><w:t>', $defaultFont);
                }
                $chorus = $this->getInstrumentGrid($dateWork['adatework_vocals']);
                if (!empty($chorus)) {
                    $noteCellTextRun->addText($this->repFileText['CHORUS'] . ': ', $boldFont);
                    $noteCellTextRun->addText($chorus . '</w:t><w:br/><w:t>', $defaultFont);
                }
                $noteCellTextRun->addText( htmlspecialchars($dateWork['details']), $defaultFont);
            }

            $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        }
    }



    protected function getInstrumentGrid($gridName)
    {
        $instrumentArray = [];
        foreach ($gridName as $instrument) {
            $instrumentEntry = '';
            if ($instrument['number_'] > 1) {
                $instrumentEntry .= $instrument['number_'] . '-';
            }
            $instrumentEntry .= mb_strtolower($instrument['sinstrinstrument']['name']);
            $instrumentEntry .= $instrument['text'] ? ' (' . htmlspecialchars($instrument['text']) . ')' : '';
            $instrumentArray[] = $instrumentEntry;
        }
        return implode('; ', $instrumentArray);
    }

}
