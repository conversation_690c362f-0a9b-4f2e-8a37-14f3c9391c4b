<?php

namespace Customer\obf\reports\MonthCalPortraitNoWkCol;

use App\Reports\Report;
use Cake\I18n\Date;
use Customer\fasutilities\reports\OnWord\Element\TextRun;
use Customer\fasutilities\reports\utility\AddressQueryHelper;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use Customer\fasutilities\reports\OnWord\OnWord;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use ReflectionException;

/*
 * This Class can be used to render the program for each month of activities. It can be easily expanded to include instrumentation
 * Establish the page section in the main report.
 * Paragraph / Font / Table styles are taken from the associated Style.php for the report
 * The main month report passes the $monthPrograms array that contains key items for each Performance in the Month
 *
 */

class MonthCalendarPrograms

{
    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var AddressQueryHelper
     */
    private $addressHelper;

    /**
     * @var array DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    private $monthPrograms = [];

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(array $monthPrograms, Section $pageSection)
    {
        $this->monthPrograms = $monthPrograms;
        $this->pageSection = $pageSection;

        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->addressHelper = new AddressQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->reportStyles = new MonthCalendarStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file. This allows for headings and other report text to output in a
    variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    public function render($programNoPrefix)
    {
        // anchor date is defined as each project + program number combination
        $anchorDate = '';

        foreach ($this->monthPrograms as $monthProgram) {
            /* $monthProgram keys:
            *   ['dateID'] ['project'] ['pgmNo'] ['pgmTitle'] ['condID'] ['orchID'] ['pweek']
            */

            $performerHeader = [];

            if ($monthProgram['project'] . $monthProgram['pgmNo'] != $anchorDate) {
                $programTable = $this->pageSection->addTable($this->reportStyles->programTableStyle);
                $programTable->addRow();

                $projectCell = $programTable->addCell(Converter::inchToTwip(3.0), $this->reportStyles->defaultCell);

                $programHeading = $monthProgram['project'];
                $programHeading .= $monthProgram['pgmNo'] ? ' - ' . $programNoPrefix . ' ' . $monthProgram['pgmNo'] : '';
                $projectCell->addText(
                    $programHeading,
                    $this->reportStyles->defaultFontBold,
                    $this->reportStyles->defaultParagraph
                );

                if (!empty($monthProgram['pgmTitle'])) {
                    $projectCell->addText(
                        htmlspecialchars($monthProgram['pgmTitle']),
                        $this->reportStyles->defaultFontItalic,
                        $this->reportStyles->defaultParagraph
                    );
                }

//                assemble the Conductor, Date-Work Conductors and Soloists. Implode and render in the format required by the report.
                $performerHeader[] = $this->datePerformerHelper->getConductor(
                    $monthProgram['dateID'],
                    $monthProgram['condID']
                );
                $performerHeader[] = $this->datePerformerHelper->getDateWorkConductors($monthProgram['dateID']);
                $performerHeader[] = $this->datePerformerHelper->getSoloists(
                    $monthProgram['dateID'],
                    $monthProgram['condID'],
                );
                $projectCell->addText(
                    implode('</w:t><w:br/><w:t>', array_filter($performerHeader)),
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph
                );
                $programCell = $programTable->addCell(Converter::inchToTwip(6.5), $this->reportStyles->defaultCell);

//                fetch max instrumentation and pass the string to the program function so the max instrumentation
//                can be rendered after the last work on the program
                if($monthProgram['orchID']>0) {
                    $maxInstString = $this->getMaxInstrumentation($monthProgram['dateID']);
                } else {
                    $maxInstString = '';
                }
//              render the works on the program
                $this->getConcertProgram($monthProgram['dateID'], $programCell, $maxInstString);


//                blank line between programs
                $this->pageSection->addText(
                    '',
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph
                );

                $anchorDate = $monthProgram['project'] . $monthProgram['pgmNo'];
            }
        }
    }

    public function getConcertProgram($dateID, $programCell, $maxInstString)
    {

//        in month and other schedules, there is usually insufficient space for work instrumentation, movements, etc
        $concertWorks = $this->dateQueryHelper->getDateWorksForDateID($dateID)
            ->withDateWorkInstrumentationGrids()
            ->getQuery()
            ->toArray();

        foreach ($concertWorks as $concertWork) {
            if ($concertWork['swork']['l_regular'] == 1) {
                if ($concertWork['l_encore'] == 1) {
                    $encore = mb_strtoupper(__('encore')) . ' -- ';
                }
                if ($concertWork['swork']['l_intermission'] != 1) {
                    $composer = mb_strtoupper($concertWork['swork']['scomposer']['lastname']);
                    $composer .= $concertWork['arrangement'] ? ' (' . $concertWork['arrangement'] . ')' : '';
                    $composer .= ':';
                } else {
                    $composer = '';
                }

                if (trim($concertWork['title2']) !== '') {
                    $workTitle = $concertWork['title2'];
                } else {
                    $workTitle = $concertWork['swork']['title1'];
                }

//                the < > characters will turn the text to italics when addFormattedText is used
                $workTitle .= $concertWork['Sworkpremieres']['name'] ?
                    ' - <' . $concertWork['Sworkpremieres']['name'] . ' ' . __('premiere' . '>')
                    : ' ';

                $programCell->addFormattedText(
                    trim($encore . ' ' . $composer . ' ' . $workTitle),
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph,
                );
            }
        }
        $programCell->addFormattedText(
            '',
            $this->reportStyles->defaultFont,
            $this->reportStyles->defaultParagraph
        );
        $programCell->addFormattedText(
            trim( $maxInstString),
            $this->reportStyles->defaultFont,
            $this->reportStyles->defaultParagraph
        );

//        blank line to provide space before next Project
        $this->pageSection->addText(' ', $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
    }

    public function getMaxInstrumentation($dateID)
    {
        /* send $dateID to the getDates() function in dateQueryHelper and return the results as the first item in an array.
        *   this creates the object that CustomerInstrumentation.php expects to see and that works with
        *     Instrumentation functions.
        */
        $perfDate = $this->dateQueryHelper->getDates($dateID)->getQuery()->toArray()[0];
        $this->instrumentation->addInstrumentationString($perfDate);

        /* this report outputs the Total Instrumentation in an in-line format
        *   elements of the max instrumentation are fetched indvidually and assembled
         */

        $maxInstArray = [];
        $maxInstArray[] = $perfDate['max_WindsBrass'];
        $maxInstArray[] = $perfDate['max_TimpPerc'];
        $maxInstArray[] = $perfDate['max_Harp'];

        $maxKeyboard = $perfDate['max_Keyboard'];
        $maxKeyboard .= $perfDate['max_instrumentation_grid_code']['keyboards'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['keyboards'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxKeyboard;

        $maxExtra = $perfDate['max_Extra'];
        $maxExtra .= $perfDate['max_instrumentation_grid_code']['extras'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['extras'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxExtra;

        $maxInstArray[] = $perfDate['max_instrumentation_grid_name']['vocals'];
        $maxInstArray[] = $perfDate['max_Strings'];

        $maxInstString = '';
        $maxInstString .= implode($this->instrumentation->getGroupSeparator(), array_filter($maxInstArray));

        return $maxInstString;
    }
}
