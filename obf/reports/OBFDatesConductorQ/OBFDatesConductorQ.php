<?php

namespace Customer\obf\reports\OBFDatesConductorQ;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;

use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;

use ReflectionException;

/**
 *  Report groups by Project - header with performers, and program with instrumentation,
 *    followed by schedule. Most of this report is fixed text - questions that the
 *      guest conductor fills out and returns to the orchestra
 */
class OBFDatesConductorQ extends ReportWord
{
//  Column Widths for Rehearsal Schedule table. This is NON-STANDARD and will be adjusted for other clients
//   Table width = 7.25 in.
    const REH_COL_1 = 0.81;
    const REH_COL_2 = 0.76;
    const REH_COL_3 = 1.16;
    const REH_COL_4 = 3.00;

    const REH_DATE_FORMAT = 'm/d/Y';
    const REH_TIME_FORMAT = 'g:i A';

    const DATE = 'Date';
    const START = 'Start';
    const END = 'Stop';
    const ACTIVITY = 'Category';
    const DATE_TEXT = 'Soloist(s)/Chorus';

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    private $reportStyles;

    /**
     * Initialize
     * Set the query helper for this report container and define the styles
     */
    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();

        // Set report font, table and paragraph styles
        $this->reportStyles = new OBFDatesConductorQStyles();
    }


    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file.
     */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Work records selected by user to workQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    private function renderReport()
    {
        //        Set formats based on client location and other settings.php values
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');

//        headers/labels from the report .rep file
        $repFileText = $this->getRepFileParams();

        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $centerParagraph = $this->reportStyles->defaultParagraphCenter;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => 'portrait',
                'marginLeft' => Converter::inchToTwip(0.50),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );


//          +++  START RENDERING ++
//        Group all dates selected by user into unique season:project:programNumber. Loop through these and put a page break between them
        $fixedText = new OBFDatesConductorQFixedText($pageSection);
        $p = 0;
        foreach ($this->getProjects() as $project) {
//            Identify the Anchor Date (first performance) for season:project and use it for title, conductor, etc
            $anchorDate = $this->getAnchor($project);
            foreach ($this->datesResult as $dateResult) {
                if ($dateResult['id'] === $anchorDate) {
                    if ($p > 0) {
                        $pageSection->addPageBreak();
                    }
                    $dateId = $dateResult['id'];

//                    +++ RENDER TOP OF REPORT +++
//                   Client Logo, Season, program title, conductor soloist etc
                    $pageSection->addImage(
                        dirname(__FILE__) . DS . 'assets' . DS . 'obf_logo.jpg',
                        array(
                            'width' => Converter::inchToPixel(2.10),
                            'alignment' => 'center'
                        )
                    );
                    $pageSection->addText(
                        $repFileText['report_title'],
                        $this->reportStyles->titleFont,
                        $centerParagraph
                    );
                    $reportHeader = $dateResult['sseason']['name'];
                    if (!is_null($dateResult['programtitle']) && (string)trim($dateResult['programtitle']) !== '') {
                        $reportHeader .= ' - ' . $dateResult['programtitle'];
                    } else {
                        $reportHeader .= ' - ' . $dateResult['sproject']['name'];
                    }
                    $pageSection->addFormattedText(
                        $reportHeader,
                        $this->reportStyles->subTitleFont,
                        $centerParagraph
                    );
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $pageSection->addText(
                        $this->getConductor($dateResult),
                        $this->reportStyles->subTitleFont,
                        $centerParagraph
                    );
                    $pageSection->addTextBreak(1, $this->reportStyles->headerFont, $centerParagraph);
                    if ((string)trim($this->getDateWorkConductors($dateResult)) !== '') {
                        $pageSection->addText(
                            $this->getDateWorkConductors($dateResult),
                            $this->reportStyles->headerFont,
                            $centerParagraph
                        );
                    }
                    $pageSection->addText(
                        $this->getSoloists($dateResult),
                        $this->reportStyles->headerFont,
                        $centerParagraph
                    );
                    $pageSection->addTextBreak(1, $this->reportStyles->headerFont, $centerParagraph);


//              +++  RENDER PROGRAM SECTION ++++
                    $fixedText->renderProgramSection();
                    $concertProgram = new OBFDatesConductorQProgram(
                        $dateResult,
                        $pageSection,
                        'ENCORE: '
                    );
                    $concertProgram->renderProgram();


//        +++ REHEARSAL SECTION +++
//             For all selected events that share the same season and project, output the rehearsal schedule

                    $this->renderRehersals(
                        $this->datesResult,
                        $project,
                        $pageSection,
                        $fixedText,
                        $defaultFont,
                        $defaultParagraph
                    );
                    $pageSection->addTextBreak(2, $defaultFont, $defaultParagraph);

//                    +++ FIXED TEXT SECTIONS +++
                    $fixedText->renderSeating();
                    $fixedText->renderSeatingPlan();
                    $fixedText->renderLibrary();
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                    RENDER WORKS FOR LIBRARY SECTION
                    $librarySection = new OBFDatesConductorQLibrary($dateResult, $pageSection);
                    $librarySection->renderLibrary();
                    $pageSection->addTextBreak(2, $defaultFont, $defaultParagraph);

//                    RENDER MORE FIXED TEXT
                    $fixedText->renderMarkings();
                    $fixedText->renderContacts();
                }
            }
        }
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    //   Organize all selected events into season:project groups
    private function getProjects()
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                ) != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                        $dateResult['programno']
                    );
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                );
        }
        return array_unique(array_filter($projectArray));
    }


//    the Anchor Date is the first concert in the Season:Project. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the Header
    private function getAnchor($project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                ) === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult['id'];
            }
        }
    }

    private function getConductor($dateResult): string
    {
        $mainConductor = $this->datePerformerHelper->getConductor(
            $dateResult['id'],
            $dateResult['conductor_id'],
            0,
            0,
            ', ',
        );
        return $mainConductor;
    }

    private function getDateWorkConductors($dateResult): string
    {
        $dateWorkConductor = $this->datePerformerHelper->getDateWorkConductors(
            $dateResult['id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            ', ',
            '; '

        );
        return $dateWorkConductor;
    }

    private function getSoloists($dateResult): string
    {
        $dateSoloists = $this->datePerformerHelper->getSoloists(
            $dateResult['id'],
            $dateResult['conductor_id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            ', '
        );
        return $dateSoloists;
    }



    private function renderRehersals($dateResults, $project, $pageSection, $fixedText, $defaultFont, $defaultParagraph)
    {
        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        $fixedText->renderRehearsalSection();
        $this->renderRehearsalHeaderRow($pageSection);

        $rehearsalTable = $pageSection->addTable($this->reportStyles->rehearsalTableStyle);

//                    For rehearsals, eliminate the program number from the Season:Project:ProgramNo identifier -
//                    Compare only season and Project (program number is ignored when identifying rehearsals)
        list($seasonId, $projectId, $programNo) = explode(':', $project);

        foreach ($dateResults as $dateResult) {
 //          This is a non-standard table configuration and not efficient. Most client variations
//                        of this report will use a different configuration
//            CLIENT REVISION - include performances if selected by user: remove && $dateResult['seventtype']['l_performance'] == 0
            if ($dateResult['season_id'] == $seasonId && $dateResult['project_id'] == $projectId ) {
                $rehearsalTable->addRow();
                $rehearsalTable->addCell(
                    Converter::inchToTwip(self::REH_COL_1),
                    $this->reportStyles->rehCellStyle
                )
                    ->addText(
                        $dateResult['date_']->format(
                            self::REH_DATE_FORMAT
                        ) . '</w:t><w:br/><w:t>' . '</w:t><w:br/><w:t>' . '</w:t><w:br/><w:t>' . '</w:t><w:br/><w:t>',
                        $defaultFont,
                        $defaultParagraph
                    );

                $rehearsalTable->addCell(
                    Converter::inchToTwip(self::REH_COL_2),
                    $this->reportStyles->rehCellStyle
                )
                    ->addText(
                        $dateResult['start_']->format(self::REH_TIME_FORMAT),
                        $defaultFont,
                        $defaultParagraph
                    );

                $rehearsalTable->addCell(
                    Converter::inchToTwip(self::REH_COL_2),
                    $this->reportStyles->rehCellStyle
                )
                    ->addText(
                        $dateResult['end_']->format(self::REH_TIME_FORMAT),
                        $defaultFont,
                        $defaultParagraph
                    );

                $rehearsalTable->addCell(
                    Converter::inchToTwip(self::REH_COL_3),
                    $this->reportStyles->rehCellStyle
                )
                    ->addText(
                        $dateResult['seventtype']['name'],
                        $defaultFont,
                        $defaultParagraph
                    );

                $rehearsalTable->addCell(
                    Converter::inchToTwip(self::REH_COL_4),
                    $this->reportStyles->rehCellStyle
                )
                    ->addFormattedText(
                        $dateResult['text'],
                        $defaultFont,
                        $defaultParagraph
                    );
            }
        }
    }

    private function renderRehearsalHeaderRow($pageSection)
    {
        $rehearsalHeaderTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $rehearsalHeaderTable->addRow();
        $rehearsalHeaderTable->addCell(
            Converter::inchToTwip(self::REH_COL_1),
            $this->reportStyles->shadedCell
        )->addText(self::DATE, $this->reportStyles->defaultFontBold, $this->reportStyles->defaultParagraph);
        $rehearsalHeaderTable->addCell(
            Converter::inchToTwip(self::REH_COL_2),
            $this->reportStyles->shadedCell
        )->addText(self::START, $this->reportStyles->defaultFontBold, $this->reportStyles->defaultParagraph);
        $rehearsalHeaderTable->addCell(
            Converter::inchToTwip(self::REH_COL_2),
            $this->reportStyles->shadedCell
        )->addText(self::END, $this->reportStyles->defaultFontBold, $this->reportStyles->defaultParagraph);
        $rehearsalHeaderTable->addCell(
            Converter::inchToTwip(self::REH_COL_3),
            $this->reportStyles->shadedCell
        )->addText(self::ACTIVITY, $this->reportStyles->defaultFontBold, $this->reportStyles->defaultParagraph);
        $rehearsalHeaderTable->addCell(
            Converter::inchToTwip(self::REH_COL_4),
            $this->reportStyles->shadedCell
        )->addText(self::DATE_TEXT, $this->reportStyles->defaultFontBold, $this->reportStyles->defaultParagraph);
    }
}
