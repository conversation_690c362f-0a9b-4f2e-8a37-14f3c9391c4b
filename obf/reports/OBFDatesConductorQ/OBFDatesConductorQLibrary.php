<?php

namespace Customer\obf\reports\OBFDatesConductorQ;

use Cake\Core\Configure;
use Customer\obf\reports\utility\ComposerQueryHelper;
use Customer\obf\reports\utility\LibraryQueryHelper;
use Customer\obf\reports\utility\WorkQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

class OBFDatesConductorQLibrary
{

    const COL_1_WIDTH = 1.35;
    const COL_2_WIDTH = 2.96;
    const COL_3_WIDTH = 1.63;
    const COL_4_WIDTH = 1.46;

    const COMPOSER = 'Composer';
    const WORK = 'Work';
    const EDITION = 'Edition / OBF Library';
    const CONDUCTOR = 'Conductor Pref.';

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;



    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * Work helper for Library eitions
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct($date, Section $pageSection)
    {

        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->workQueryHelper = new WorkQueryHelper();

        $this->reportStyles = new OBFDatesConductorQStyles();

        $this->concertDate = $date;
        $this->pageSection = $pageSection;
    }

    public function renderLibrary()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $fixedText = new OBFDatesConductorQFixedText($this->pageSection);

        $libraryTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

        //        RENDER PROGRAM TABLE HEADER
        $libraryTable->addRow();
        $libraryTable->addCell(
            Converter::inchToTwip(self::COL_1_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::COMPOSER, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $libraryTable->addCell(
            Converter::inchToTwip(self::COL_2_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::WORK, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $libraryTable->addCell(
            Converter::inchToTwip(self::COL_3_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::EDITION, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $libraryTable->addCell(
            Converter::inchToTwip(self::COL_4_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::CONDUCTOR, $this->reportStyles->defaultFontBold, $defaultParagraph);


        //        sort date-works by work_order
        $dateWorkArray = $this->concertDate['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

        foreach ($dateWorkArray as $dateWork) {

            if ($dateWork['swork']['l_intermission'] == 0) {

                $libraryTable->addRow();
                $firstColumnCell = $libraryTable->addCell(
                    Converter::inchToTwip(self::COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                );
                $secondColumnCell = $libraryTable->addCell(
                    Converter::inchToTwip(self::COL_2_WIDTH),
                    $this->reportStyles->defaultCell
                );
                $thirdColumnCell = $libraryTable->addCell(
                    Converter::inchToTwip(self::COL_3_WIDTH),
                    $this->reportStyles->defaultCell
                );
                $fourthColumnCell = $libraryTable->addCell(
                    Converter::inchToTwip(self::COL_4_WIDTH),
                    $this->reportStyles->defaultCell
                );

                $composerName = $this->composerQueryHelper->getComposerName(
                    $dateWork['swork']['scomposer']['lastname'],
                    $dateWork['swork']['scomposer']['firstname'],
                    1
                );
                if (!is_null($dateWork['arrangement']) && (string)trim($dateWork['arrangement']) !== '') {
                    $composerName .= ' (' . $dateWork['arrangement'] . ')';
                }
                $firstColumnCell->addFormattedText($composerName, $defaultFont, $defaultParagraph);

                if ( !is_null($dateWork['title2']) && (string)(trim($dateWork['title2'])) !=='') {
                    $dateWorkTitle = $dateWork['title2'];
                } else {
                    $dateWorkTitle = $dateWork['swork']['title1'];
                }
                $secondColumnCell->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);

                $thirdColumnCell->addText($this->getLibraryPublishers($dateWork['work_id']),$defaultFont, $defaultParagraph )                ;

                $fourthColumnCell->addTextBreak(3,$defaultFont,$defaultParagraph);
            }


        }





    }

    private function getLibraryPublishers($workId)
    {
        $libraries = $this->workQueryHelper->getLibrariesForWork($workId)->getQuery()->toArray();
        $libraryArray = [];
        $n = 1;
        foreach ($libraries as $library) {
            $libraryEntry = $n . '. ';
            $libraryEntry .= htmlspecialchars(  $library['publisheraddress']['name1']);

            $libraryArray[] = $libraryEntry;
            $n++;
        }

        return implode('</w:t><w:br/><w:t>' . '</w:t><w:br/><w:t>', $libraryArray);
    }


}
