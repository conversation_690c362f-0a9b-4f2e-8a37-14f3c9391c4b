<?php

namespace Customer\obf\reports\OBFDatesConductorQ;

use PhpOffice\PhpWord\Shared\Converter;

// Fixed Text throughout the report

class OBFDatesConductorQFixedText
{
//    CONTACT NAMES / NUMBERS - table = 7.25 in.
    const COL_1_WIDTH = 1.90;
    const COL_2_WIDTH = 3.15;
    const COL_3_WIDTH = 2.30;

    private $reportStyles;
    private $pageSection;

    private $sectionHeaderFont;


    public function __construct($pageSection)
    {
        $this->pageSection = $pageSection;
        $this->reportStyles = new OBFDatesConductorQStyles();

        $this->sectionHeaderFont = $this->reportStyles->sectionHeaderFont;
        $this->defaultFont = $this->reportStyles->defaultFont;

        $this->defaultParagraph = $this->reportStyles->defaultParagraph;
        $this->sectionHeaderParagraph = $this->reportStyles->sectionHeaderParagraph;
    }


    public function renderProgramSection()
    {
        $this->pageSection->addText(
            'Program and String Complement' . "\t",
            $this->sectionHeaderFont,
            $this->sectionHeaderParagraph
        );
        $programTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $programTextRun->addText(
            'Please review each program entry for accuracy and note any requested ',
            $this->defaultFont
        );
        $programTextRun->addText(
            'adjustments to instrumentation, including reductions to string complements for individual works.',
            $this->defaultFont
        );
        $this->pageSection->addTextBreak(1, $this->defaultFont, $this->defaultParagraph);
    }

    public function renderRehearsalSection()
    {
        $this->pageSection->addText(
            'Schedule and Rehearsal Orders' . "\t",
            $this->sectionHeaderFont,
            $this->sectionHeaderParagraph
        );
        $rehearsalTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $rehearsalTextRun->addText(
            'For each rehearsal listed, please indicate requested rehearsal orders and if the soloist(s) should be called. ',
            $this->defaultFont
        );
        $rehearsalTextRun->addText(
            'Breaks are 15 minutes for 2 and 2.5-hour services and 20 minutes for 3-hour services.',
            $this->defaultFont
        );
        $this->pageSection->addTextBreak(1, $this->defaultFont, $this->defaultParagraph);
    }

    public function renderSeating()
    {
        $this->pageSection->addText('Late Seating' . "\t", $this->sectionHeaderFont, $this->sectionHeaderParagraph);
        $seatingTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $seatingTextRun->addText(
            'Please indicate your preference for when late seating can take place. ',
            $this->defaultFont
        );
        $seatingTextRun->addText(
            'Include approximate times if possible. ',
            $this->defaultFont
        );

        $this->pageSection->addTextBreak(9, $this->defaultFont, $this->defaultParagraph);
    }

    public function renderSeatingPlan()
    {
        $this->pageSection->addText('Seating Plan' . "\t", $this->sectionHeaderFont, $this->sectionHeaderParagraph);
        $seatingPlanTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $seatingPlanTextRun->addText(
            'Please indicate any unusual stage setting, such as position of keyboard, percussion instruments, ',
            $this->defaultFont
        );
        $seatingPlanTextRun->addText(
            'any unusual instrument requirements, and preferred string disposition. ',
            $this->defaultFont
        );
        $this->pageSection->addTextBreak(9, $this->defaultFont, $this->defaultParagraph);
    }

    public function renderLibrary()
    {
        $this->pageSection->addText('Library' . "\t", $this->sectionHeaderFont, $this->sectionHeaderParagraph);
        $libraryTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $libraryTextRun->addText(
            'Please indicate preferences for performance editions:',
            $this->defaultFont,
            $this->defaultParagraph
        );
    }

    public function renderMarkings()
    {
        $markingTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $markingTextRun->addText(
            'If the conductor has extensive markings to be placed in the orchestra parts, OBF strongly recommends that either ',
            $this->defaultFont
        );
        $markingTextRun->addText(
            'masters or a complete set be provided by the conductor for this program.  ',
            $this->defaultFont
        );
        $markingTextRun->addText(
            'If parts are indeed being sent, they should be received at least ',
            $this->defaultFont
        );
        $markingTextRun->addText('12 weeks ', $this->reportStyles->defaultFontBold);
        $markingTextRun->addText('in advance of the first rehearsal.', $this->defaultFont);
        $markingTextRun->addTextBreak(2, $this->defaultFont);
        $markingTextRun->addText(
            'Copies of OBF bowings may be requested in advance.  OBF asks that any bowing changes ',
            $this->defaultFont
        );
        $markingTextRun->addText('be received by the orchestra librarian no later than ', $this->defaultFont);
        $markingTextRun->addText('60 days ', $this->reportStyles->defaultFontBold);
        $markingTextRun->addText(
            'before the first rehearsal. Likewise, any cuts or editing should be supplied at least ',
            $this->defaultFont
        );
        $markingTextRun->addText('60 days ', $this->reportStyles->defaultFontBold);
        $markingTextRun->addText('prior to the first rehearsal.', $this->defaultFont);

        $this->pageSection->addTextBreak(2, $this->defaultFont, $this->defaultParagraph);
    }

    public function renderComments()
    {
        $this->pageSection->addText('Comments' . "\t", $this->sectionHeaderFont, $this->sectionHeaderParagraph);
        $commentTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $commentTextRun->addText(
            'Please share any additional comments or questions for OBF:',
            $this->defaultFont,
            $this->defaultParagraph
        );
    }

    public function renderContacts()
    {
        $this->pageSection->addText('OBF Contacts' . "\t", $this->sectionHeaderFont, $this->sectionHeaderParagraph);
        $contactTextRun = $this->pageSection->addTextRun($this->defaultParagraph);
        $contactTextRun->addText(
            'Should you have further questions, please do not hesitate to call or email:',
            $this->defaultFont,
            $this->defaultParagraph
        );

        $contactTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

        $contactTable->addRow();
        $contactTable->addCell(
            Converter::inchToTwip(self::COL_1_WIDTH),
            $this->reportStyles->shadedCell
        )->addText('Name', $this->reportStyles->defaultFontBold, $this->defaultParagraph);
        $contactTable->addCell(
            Converter::inchToTwip(self::COL_2_WIDTH),
            $this->reportStyles->shadedCell
        )->addText('Title', $this->reportStyles->defaultFontBold, $this->defaultParagraph);
        $contactTable->addCell(
            Converter::inchToTwip(self::COL_3_WIDTH),
            $this->reportStyles->shadedCell
        )->addText('Phone/Email', $this->reportStyles->defaultFontBold, $this->defaultParagraph);

        $i = 0;
        do {
            $contactTable->addRow();
            $nameCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_1_WIDTH),
                $this->reportStyles->defaultCell
            );
            $titleCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_2_WIDTH),
                $this->reportStyles->defaultCell
            );
            $phoneCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->defaultCell
            );

            switch ($i) {
                case 0:
                    $nameCell->addText('James Boyd ', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText(
                        'Director of Programming and Administration ',
                        $this->defaultFont,
                        $this->defaultParagraph
                    );
                    $phoneCell->addText('************ | office', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************ | mobile', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('<EMAIL>', $this->defaultFont, $this->defaultParagraph);
                    break;
                case 1:
                    $nameCell->addText('Greg Hamilton', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText('Librarian', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************ | mobile', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('<EMAIL>', $this->defaultFont, $this->defaultParagraph);
                    break;
                case 2:
                    $nameCell->addText('Thor Mikesell', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText('Producing Technical Director', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************ | office', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************ | mobile', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('<EMAIL>', $this->defaultFont, $this->defaultParagraph);
                    break;
                case 3:
                    $nameCell->addText('TBD', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText('Artist Liaison', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText(' | office', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText(' | mobile', $this->defaultFont, $this->defaultParagraph);
                    break;
                case 4:
                    $nameCell->addText('Kathy Saltzman Romey', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText('OBF Chorus Master', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************ | office', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('************| mobile', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('<EMAIL>', $this->defaultFont, $this->defaultParagraph);
                    break;
                default:
                    $nameCell->addText('', $this->defaultFont, $this->defaultParagraph);
                    $titleCell->addText('', $this->defaultFont, $this->defaultParagraph);
                    $phoneCell->addText('', $this->defaultFont, $this->defaultParagraph);
            }
            $i++;
        } while ($i <= 4);
    }


}
