<?php

namespace Customer\obf\reports\OBFDatesConductorQ;


use Cake\Core\Configure;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\ComposerQueryHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

class OBFDatesConductorQProgram
{

    const COL_1_WIDTH = 1.35;
    const COL_2_WIDTH = 3.50;
    const COL_3_WIDTH = 0.65;
    const COL_4_WIDTH = 1.90;

    const COMPOSER = 'Composer';
    const WORK = 'Work';
    const TIMING = 'Timing';
    const STRINGS = 'String Complement';

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * Instrumentation string output
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct($date, Section $pageSection, string $encore)
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->reportStyles = new OBFDatesConductorQStyles();

        $this->concertDate = $date;
        $this->pageSection = $pageSection;
        $this->encore = $encore;
    }

    public function renderProgram()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $fixedText = new OBFDatesConductorQFixedText($this->pageSection);

        $programTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

//        use separate method If you need movements, soloists and date-work conductors
//        $dateWorks = $this->dateQueryHelper->getDateWorksForDateID($this->dateId)->getQuery()->toArray();

        $concertDuration = 0;

//        RENDER PROGRAM TABLE HEADER
        $programTable->addRow();
        $programTable->addCell(
            Converter::inchToTwip(self::COL_1_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::COMPOSER, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $programTable->addCell(
            Converter::inchToTwip(self::COL_2_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::WORK, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $programTable->addCell(
            Converter::inchToTwip(self::COL_3_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::TIMING, $this->reportStyles->defaultFontBold, $defaultParagraph);
        $programTable->addCell(
            Converter::inchToTwip(self::COL_4_WIDTH),
            $this->reportStyles->shadedCell
        )->addText(self::STRINGS, $this->reportStyles->defaultFontBold, $defaultParagraph);

        //        sort date-works by work_order
        $dateWorkArray = $this->concertDate['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

        foreach ($dateWorkArray as $dateWork) {
            //            adds all the instrumentation fields and formatted strings to the $dateWork
            $this->instrumentation->addInstrumentationStringToWork($dateWork);

            $programTable->addRow();
            $firstColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_1_WIDTH),
                $this->reportStyles->defaultCell
            );
            $secondColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_2_WIDTH),
                $this->reportStyles->defaultCell
            );
            $thirdColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->defaultCell
            );
            $fourthColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_4_WIDTH),
                $this->reportStyles->defaultCell
            );

//            FIRST COLUMN - COMPOSER
            if ($dateWork['swork']['l_intermission'] == 0) {
                $composerTextRun = $firstColumnCell->addTextRun($defaultParagraph);
                if ($dateWork['l_encore'] == 1) {
                    $composerTextRun->addText($this->encore, $defaultFont, $defaultParagraph);
                    $composerTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);
                }
                $composerName = $this->composerQueryHelper->getComposerName(
                    $dateWork['swork']['scomposer']['lastname'],
                    $dateWork['swork']['scomposer']['firstname'],
                    1
                );
                $composerName .= $dateWork['arrangement'] ? ' (' . $dateWork['arrangement'] . ')' : '';
            } else {
                $composerName = '';
            }
            $composerTextRun->addFormattedText($composerName, $defaultFont, $defaultParagraph);

//            SECOND COLUMN - WORK TITLE AND INSTRUMENTATION


            $titleTextRun = $secondColumnCell->addTextRun($defaultParagraph);
            if ($dateWork['l_encore'] == 1) {
                $titleTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);
            }
            if (!empty(trim($dateWork['title2']))) {
                $dateWorkTitle = $dateWork['title2'];
            } else {
                $dateWorkTitle = $dateWork['swork']['title1'];
            }
            $titleTextRun->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);
            $premiere = $dateWork['Sworkpremieres']['name'] ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                    'premiere'
                ) : ' ';
            $titleTextRun->addText($premiere, $this->reportStyles->defaultFontItalic, $defaultParagraph);
            $titleTextRun->addTextBreak(2, $defaultFont, $defaultParagraph);

//      Determine where the string numbers start and strip out that part of the instrumentation detail text
//           as string numbers are output in their own cell.  This is non-standard
            $stringsStart = strpos(
                $dateWork['instrumentation_detail'],
                Configure::read('Instrumentation.groupSeparator') . 'str'
            );
            $instrumentationDetail = substr($dateWork['instrumentation_detail'], 0, $stringsStart);

            if ($dateWork['swork']['l_intermission'] == 0) {
                $titleTextRun->addText($instrumentationDetail, $defaultFont);
                if (!is_null($dateWork['detail']) && (string)trim($dateWork['detail']) !== '') {
                    $titleTextRun->addTextBreak(2, $defaultFont, $defaultParagraph);
                    $titleTextRun->addText($dateWork['detail'], $defaultFont, $defaultParagraph);
                }
            }
            $titleTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);

//        THIRD COLUMN -- DURATION
            $thirdColumnCell->addText(
                $this->dateWorkQueryHelper->getDateWorkDuration($dateWork['duration']),
                $defaultFont,
                $this->reportStyles->defaultParagraphRight
            );

//        FOURTH COLUMN - STRINGS
            if ($dateWork['swork']['l_intermission'] == 0) {
                $fourthColumnCell->addText(
                    $dateWork['violin1'] . '.' . $dateWork['violin2'] . '.' . $dateWork['viola'] . '.' . $dateWork['cello'] . '.' . $dateWork['bass'],
                    $defaultFont,
                    $defaultParagraph
                );
            }
            $fourthColumnCell->addTextBreak(1, $defaultFont, $defaultParagraph);
        }
    }


}
