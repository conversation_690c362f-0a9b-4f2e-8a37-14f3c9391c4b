<?php

namespace Customer\obf\reports\DatesEventRoster;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;
use Cake\Core\Configure;

use Customer\obf\reports\DatesEventRoster\DatesEventRosterProgram;
use Customer\obf\reports\utility\DutyQueryHelper;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;
use Customer\obf\reports\utility\NameFormatHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * This report has many inefficiencies but was designed for ease in creating customizations.
 * Clients have many different variations of event rosters and this structure will hopefully make it
 *   easier to re-arrange the elements of this report
 */

class DatesEventRoster extends ReportWord
{
    const HEADER_COLUMN_WIDTH = 3.65;
    const STRING_COL_WIDTH = 1.42;
    const WIND_COL_WIDTH = 1.78;

    /**
     * Helper class for each event selected by the user
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the  Service records associated with the selecte date
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;
    private $dutiesResult;


    /**
     * Helper class for nconductor/soloist formatting
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for all name formatting
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function initialize()
    {
        parent::initialize();
        $this->dutyQueryHelper = new DutyQueryHelper();
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->reportStyles = new DatesEventRosterStyles();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = []): ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    public function renderReport()
    {
//      +++ Set REPORT FORMATS based on client region and preferences +++
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');

//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();
//        most often-used font and paragraph styles, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        RENDER FOOTER
        $footer = $pageSection->addFooter();
        $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $defaultFont,
            $this->reportStyles->footerParagraph,
        );

//        Loop through each selected adates record; put a page break between
        $d = 0;
        foreach ($this->datesResult as $event) {
            if ($d > 0) {
                $pageSection->addPageBreak(1);
            }

//             Elements of the REPORT HEADER - project, conductor, etc. Identified individually to make future customizations easier
            $project = $event['sproject']['name'];
            $activity = $event['seventtype']['name'];
            $eventTitle = htmlspecialchars($event['programtitle']);
            $eventDate = $event['date_']->format($topDateFormat);
            if (!is_null($event['start_']) && (string)trim($event['start_']) !== '') {
                $eventTime = $event['start_']->format($topTimeFormat);
            } else {
                $eventTime = '';
            }
            if (!is_null($event['end_']) && (string)trim(
                    $event['end_']
                ) !== '' && $event['seventtype']['l_performance'] == 0) {
                $eventTime .= ' - ' . $event['end_']->format($topTimeFormat);
            }

            if ($event['orchestra_id'] > 0) {
                $orchestraName = $this->nameFormatHelper->getEntityName(
                    $event['orchestraaddress']['name1'],
                    $event['orchestraaddress']['name2'],
                    1
                );
            } else {
                $orchestraName = '';
            }
            if ($event['location_id'] > 0) {
                $venueName = $this->nameFormatHelper->getEntityName(
                    $event['locationaddress']['name1'],
                    $event['locationaddress']['name2'],
                    1
                );
            } else {
                $venueName = '';
            }

            if ($event['conductor_id'] > 0) {
                $conductorName = $this->datePerformerHelper->getConductor(
                    $event['id'],
                    $event['conductor_id'],
                    0,
                    0,
                    ', ',
                    ';'
                );
            } else {
                $conductorName = '';
            }

            $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
                $event['id'],
                '</w:t><w:br/><w:t>',
                0,
                0,
                ', ',
                '; '
            );
            $soloists = $this->datePerformerHelper->getSoloists($event['id'], $event['conductor_id'], '; ', 0, 0, ', ');


//            +++ RENDER HEADER +++
            $pageSection->addFormattedText(
                $orchestraName,
                $this->reportStyles->headerFont,
                $this->reportStyles->headerParagraph
            );
            $pageSection->addFormattedText(
                $repFileText['report_title'],
                $this->reportStyles->headerFont,
                $this->reportStyles->headerParagraph
            );
            $pageSection->addText(
                $repFileText['printed'] . ' ' . date($topDateFormat),
                $this->reportStyles->defaultFontItalic,
                $defaultParagraph
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            +++ RENDER HEADER TABLE +++
            $headerTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $headerTable->addRow();
            $leftHeaderCell = $headerTable->addCell(
                Converter::inchToTwip(self::HEADER_COLUMN_WIDTH),
                $this->reportStyles->defaultCellStyle
            );
            $rightHeaderCell = $headerTable->addCell(
                Converter::inchToTwip(self::HEADER_COLUMN_WIDTH),
                $this->reportStyles->defaultCellStyle
            );

//            Some elements are rendered as an imploded array so empty elements are properly suppressed
            $leftHeaderCell->addFormattedText($project . ': ' . $activity, $defaultFontBold, $defaultParagraph);
            $leftHeaderCell->addText($eventDate . __(' at ') . $eventTime, $defaultFont, $defaultParagraph);
            $leftHeaderCell->addFormattedText($venueName, $defaultFont, $defaultParagraph);

            $rightHeaderContent = implode(
                '</w:t><w:br/><w:t>',
                array_filter(
                    array($eventTitle, $conductorName, $dateWorkConductors, $soloists)
                )
            );
            $rightHeaderCell->addText($rightHeaderContent, $defaultFont, $defaultParagraph);

            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            +++ RENDER PROGRAM via dedicated class +++
            $programTable = new DatesEventRosterProgram($event['id'], $pageSection);
            $programTable->renderConcertProgram();
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//          FETCH ALL DUTIES associated with the event
            $duties = $this->dutyQueryHelper->getDutiesForDateId($event['id'])->getQuery()->toArray();
            $musicianArray = [];
            foreach ($duties as $duty) {
                if ($duty['sdutytype']['l_present'] == 1) {
                    $sortOrder = str_pad(
                        $duty['sinstrinstrument']['sinstrsection']['section_order'],
                        3,
                        '0',
                        STR_PAD_LEFT
                    );
                    $sortOrder .= trim($duty['seat']) ? str_pad($duty['seat'], 2, 0, STR_PAD_LEFT) : 'ZZ';
                    $sortOrder .= trim($duty['order_1']) ? str_pad($duty['order_1'], 2, 0, STR_PAD_LEFT) : 'ZZ';
                    $sortOrder .= $duty['artistaddress']['name1'] . $duty['artistaddress']['name2'];

                    $musicianEntry = array(
                        'sortOrder' => $sortOrder,
                        'sysSectionId' => $duty['sinstrinstrument']['sinstrsection']['syssection_id'],
                        'sectionId' => $duty['sinstrinstrument']['sinstrsection']['id'],
                        'section' => $duty['sinstrinstrument']['sinstrsection']['name'],
                        'instrument' => $duty['sinstrinstrument']['name'],
                        'seat' => $duty['seat'],
                        'musicianName' => $this->nameFormatHelper->getEntityName(
                            $duty['artistaddress']['name1'],
                            $duty['artistaddress']['name2'],
                            1
                        ),
                    );

                    $musicianArray[] = $musicianEntry;
                }

            }
            //        Sort the array by Section and musician order within the Section
            $rosterArray = $musicianArray;
            foreach ($rosterArray as $key => $value) {
                $rosterSort[$key] = $value['sortOrder'];
            }
            array_multisort($rosterSort, SORT_ASC, $rosterArray);

//         +++ STRING SECTIONS +++
            $stringHeaderArray = array(
                '1' => __('sworks.violin1'),
                '2' => __('sworks.violin2'),
                '3' => __('sworks.viola'),
                '4' => __('sworks.cello'),
                '5' => __('sworks.bass')
            );
            $stringTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
            $stringTable->addRow();

            for ($sysSection = 1; $sysSection <= 5; $sysSection++) {
                $stringCell = $stringTable->addCell(
                    Converter::inchToTwip(self::STRING_COL_WIDTH),
                    $this->reportStyles->defaultCellStyle
                );
                $stringCell->addText($stringHeaderArray[$sysSection], $defaultFontBold, $defaultParagraph);

                foreach ($rosterArray as $musician) {
                    if ($musician['sysSectionId'] === $sysSection) {
//                        If the musician plays a different INSTRUMENT than the SECTION, print that Instrument
                        if ($musician['section'] !== $musician['instrument']) {
                            $stringCell->addTextBreak(1, $defaultFont, $defaultParagraph);
                            $stringCell->addText($musician['instrument'], $defaultFontBold, $defaultParagraph);
                        }
                        $stringCell->addText(
                            $musician['musicianName'],
                            $defaultFont,
                            $defaultParagraph
                        );
                    }
                }
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//          +++  WIND SECTIONS +++
            $windHeaderArray = array(
                '6' => __('sworks.flute'),
                '7' => __('sworks.oboe'),
                '8' => __('sworks.clarinet'),
                '9' => __('sworks.bassoon')
            );
            $windTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
            $windTable->addRow();

            for ($sysSection = 6; $sysSection <= 9; $sysSection++) {
                $windCell = $windTable->addCell(
                    Converter::inchToTwip(self::WIND_COL_WIDTH),
                    $this->reportStyles->defaultCellStyle
                );
                $windCell->addText($windHeaderArray[$sysSection], $defaultFontBold, $defaultParagraph);

                foreach ($rosterArray as $musician) {
                    if ($musician['sysSectionId'] === $sysSection) {
//                        If the musician plays a different INSTRUMENT than the SECTION, print that Instrument
                        if ($musician['section'] !== $musician['instrument']) {
                            $windCell->addTextBreak(1, $defaultFont, $defaultParagraph);
                            $windCell->addText($musician['instrument'], $defaultFontBold, $defaultParagraph);
                        }
                        $windCell->addText(
                            $musician['musicianName'],
                            $defaultFont,
                            $defaultParagraph
                        );
                    }
                }
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//          +++  BRASS SECTIONS +++
            $brassHeaderArray = array(
                '10' => __('sworks.horn'),
                '11' => __('sworks.trumpet'),
                '12' => __('sworks.trombone'),
                '13' => __('sworks.tuba')
            );
            $brassTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
            $brassTable->addRow();

            for ($sysSection = 10; $sysSection <= 13; $sysSection++) {
                $brassCell = $brassTable->addCell(
                    Converter::inchToTwip(self::WIND_COL_WIDTH),
                    $this->reportStyles->defaultCellStyle
                );
                $brassCell->addText($brassHeaderArray[$sysSection], $defaultFontBold, $defaultParagraph);


                foreach ($rosterArray as $musician) {
                    if ($musician['sysSectionId'] === $sysSection) {
//                        If the musician plays a different INSTRUMENT than the SECTION, print that Instrument
                        if ($musician['section'] !== $musician['instrument']) {
                            $brassCell->addTextBreak(1, $defaultFont, $defaultParagraph);
                            $brassCell->addText($musician['instrument'], $defaultFontBold, $defaultParagraph);
                        }
                        $brassCell->addText(
                            $musician['musicianName'],
                            $defaultFont,
                            $defaultParagraph
                        );
                    }
                }
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//          +++  TIMP / PERC / HARP ETC SECTIONS +++
            $timpHeaderArray = array(
                '14' => __('sworks.timpani'),
                '15' => __('sworks.percussion'),
                '16' => __('sworks.harp'),
                '17' => __('sworks.keyboard'),
                '18' => __('sworks.extra')
            );
            $timpTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
            $timpTable->addRow();

            for ($sysSection = 14; $sysSection <= 17; $sysSection++) {
                $timpCell = $timpTable->addCell(
                    Converter::inchToTwip(self::WIND_COL_WIDTH),
                    $this->reportStyles->defaultCellStyle
                );
//                Print the Section Name ONLY for Timp / Perc / Harp sections only. For Keybd / Extra print
//                the Instrument name
                if ($sysSection < 17) {
                    $timpCell->addText($timpHeaderArray[$sysSection], $defaultFontBold, $defaultParagraph);
                }

                foreach ($rosterArray as $musician) {
                    if ($musician['sysSectionId'] === $sysSection || ($musician['sysSectionId'] === 18 && $sysSection === 17)) {
//                        If the musician plays a different INSTRUMENT than the SECTION, print that Instrument or
//                        if in the Keyboard or Extra section, print Instrument instead of Section
                        if ($musician['section'] !== $musician['instrument'] && $sysSection < 17) {
                            $timpCell->addTextBreak(1, $defaultFont, $defaultParagraph);
                            $timpCell->addText($musician['instrument'], $defaultFontBold, $defaultParagraph);
                        } elseif (($sysSection >= 17)) {
                            $timpCell->addText($musician['instrument'], $defaultFontBold, $defaultParagraph);
                        }
                        $timpCell->addText(
                            $musician['musicianName'],
                            $defaultFont,
                            $defaultParagraph
                        );
                    }
                }
            }
            $d++;
        }
    }

}
