<?php

use App\Reports\Report;
//use App\Utility\Instrumentation\Instrumentation;
//use App\Utility\Instrumentation\InstrumentationFactory;
use Cake\Core\Configure;

use Customer\obf\reports\utility\WorkQueryHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentationWork;
use Customer\obf\reports\BaseConcertProgram;
use Customer\obf\PHPWordHelper;

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;
use PhpOffice\PhpWord\Shared\Converter;


/**
 * AUTHOR NOTE
 *   Report produces a simple list of compositions, grouped by composer. Typically used to output the results of a search
 *    Includes detailed instrumentation and instrumentation notes
 *  FORMAT NOTES:   Standard left-aligned paragraph with no extra spacing.
 *          BaseConcertProgram extends all the necessary classes to italicize text in < >
 *          Consider moving to landscape format
 *
 */
class WorkListInstDetail extends BaseConcertProgram
{

    /**
     * Helper class for the date query of the date works.
     *
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * Helper class for the instrumentation.
     *
     * @var CustomerInstrumentationWork
     */
    private $instrumentationHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * Set the query helper and the instrumentation class
     *
     * @throws Exception
     */
    public function initialize()
    {
        $this->workQueryHelper = new WorkQueryHelper();
        $this->instrumentationHelper = new CustomerInstrumentationWork();
    }

    /**
     * Get works selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $dateQuery = $this->workQueryHelper
            ->getWorks($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     *
     * @param null $view
     * @param null $layout
     * @return mixed|string
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $this->phpWord = new PhpWord();
        $section = $this->wordHelper->addSection(
            [  // adding a section REQUIRES $phpWord
                'paperSize' => "Letter",
                // "Letter" , "A4" , others in PHPWord folder "Paper.php"
                // 'orientation' => "Portrait" ,   portrait is default - no need to indicate unless switching to landsacpe
                'marginTop' => Converter::inchToTwip(.5),
                'marginBottom' => Converter::inchToTwip(.75),
                'marginLeft' => Converter::inchToTwip(.75),
                'marginRight' => Converter::inchToTwip(.5),
                'breakType' => 'continuous',
                // prevents page break between sections when a report uses multiple sections
            ]
        );


        // insert footer
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');
        $runningTimeFormat = Configure::read(
            'Formats.programHeaderTime'
        );

        $footer = $section->addFooter();
        $footerOutput = $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $this->reportFontDefault,
            $this->reportParagraphFooter // ReportWordFas - standard footer layout
        );

        // insert title and table header
        $this->wordHelper->addSectionText(
            $section,
            'Works with Detailed Instrumentation' . '<br/>',
            (new Font())->setSize(14)->setName('Calibri')->setColor('901414')->setBold(true),
            $this->reportParagraphCenter
        );

        $table = $this->wordHelper->addTable($section);
        $row = $this->wordHelper->addRow(
            $table,
            PHPWordHelper::ROW_COLS_FIFTHS,
            $widths = [
                $this->wordHelper->getWidthTwpByPct(22),
                $this->wordHelper->getWidthTwpByPct(22),
                $this->wordHelper->getWidthTwpByPct(15),
                $this->wordHelper->getWidthTwpByPct(35),
                $this->wordHelper->getWidthTwpByPct(6),
            ],
        );

        $this->wordHelper->addTableText(
            $row,
            0,
            'Master Title',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            1,
            'Print Title',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            2,
            'Arrangement',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            3,
            'Instrumentation',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            4,
            'Dur.',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphRight
        );


// fetch all works as a single array then sort by composer last,first and work title1
        $workArray = $this->datesResult;


        function compareByName($a, $b)
        {
            return strnatcasecmp(
                $a['scomposer']['lastname'] . $a['somposer']['firstname'] . $a['title1'],
                $b['scomposer']['lastname'] . $b['somposer']['firstname'] . $b['title1']
            );
        }

        usort(
            $workArray,
            'comparebyName'
        );

// set composer ID to zero; used to compare current composer with previous to print composer name only once
        $previousComposerID = 0;

        // iterate through the sorted array of all work data
        foreach ($workArray as $work) {
            if ($work['composer_id'] != $previousComposerID) {
                $this->wordHelper->addSectionText(
                    $section,
                    '<br/>' . trim($work['scomposer']['firstname'] . ' ' . $work['scomposer']['lastname']),
                    (new Font())->setSize(12)->setName('Calibri')->setBold(true),
                    $this->reportParagraphDefault
                );
            }

            $previousComposerID = $work['composer_id'];

            $table = $this->wordHelper->addTableFullBorder($section);

            $row = $this->wordHelper->addRow(
                $table,
                PHPWordHelper::ROW_COLS_FIFTHS,
                $widths = [
                    $this->wordHelper->getWidthTwpByPct(22),
                    $this->wordHelper->getWidthTwpByPct(22),
                    $this->wordHelper->getWidthTwpByPct(15),
                    $this->wordHelper->getWidthTwpByPct(35),
                    $this->wordHelper->getWidthTwpByPct(6),
                ],
            );

            $this->wordHelper->addTableText(
                $row,
                0,
                $work['title1'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                1,
                $work['title2'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                2,
                $work['arrangement'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );
            $this->instrumentationHelper->addInstrumentationStringToWork($work);
            $workPercussion = $work['percussion_grid'] ? '<br/>' . '<b>Perc:</b> ' . $work['percussion_grid'] : '';
            $workNotes = $work['details'] ? '<br/>' . '<b>Notes:</b> ' . $work['details'] : '';

            $this->wordHelper->addTableText(
                $row,
                3,
                $work['instrumentation_detail'] . $workPercussion . $workNotes,
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                4,
                $this->workQueryHelper->getDurationStringForWork($work['duration'], ':', ''),
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphRight
            );
        }


        // Create the file and return the filename
        $fileName = $this->_createFileName() . $this->getFileExtension();

        ob_end_clean();

        $this->wordHelper->write($fileName);

        return $fileName;
    }

}
