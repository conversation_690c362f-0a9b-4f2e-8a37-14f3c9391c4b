<?php

namespace Customer\obf\reports\RehearsalSchedule;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;

// Style Sheet for report. Fonts | Paragraphs | Tables | Cells

class RehearsalScheduleStyles
{
    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $font = 'Calibri';

//        default font is larger as this document is posted on a bulletin board
        $this->reportFont = new Font();
        $this->reportFont->setBold(false);
        $this->reportFont->setName($font);
        $this->reportFont->setSize(14);

        $this->reportFontBold = clone($this->reportFont);
        $this->reportFontBold->setBold(true);

        $this->reportFontItalic = clone($this->reportFont);
        $this->reportFontItalic->setItalic(true);

        $this->reportTitleFont = clone($this->reportFontBold);
        $this->reportTitleFont->setSize(14);

        $this->reportSmallFont = clone($this->reportFont);
        $this->reportSmallFont->setSize(12);
    }

    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

//        this is how to have the paragraph flush left with a 0.5
//        inch hanging indent. values are in half-inches
        $this->hangingParagraph = array_merge(
            $this->defaultParagraph,
            [
                'indent' => 1,
                'hanging' => 1
            ]
        );

        $this->sectionHeadingParagraph = [
            'alignment' => 'center',
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,
            'shading' => array('fill' => 'e6e3d1')
        ];


//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10440, // 7.25 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];


//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0
        );

        $this->topBorderCell = array_merge(
            $this->defaultCell,
            [
                'borderTopColor' => '83303F',  // dark red
                'borderTopSize' => 6
            ]
        );

        $this->greyShadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'D3D3D3'
        );
    }

}
