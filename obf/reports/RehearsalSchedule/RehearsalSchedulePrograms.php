<?php

namespace Customer\obf\reports\RehearsalSchedule;

use Customer\obf\reports\utility\AddressQueryHelper;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;

use PhpOffice\PhpWord\Element\Section;

/*
 * This Class renders the concert programs for each week of rehearsals.
 * Establish the page section in the main report.
 * Paragraph / Font / Table styles are taken from the associated Style.php for the report
 * The rehearsal schedule report passes the $weekPrograms array that contains key items for each Performance in the Week
 *
 */

class RehearsalSchedulePrograms
{

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var AddressQueryHelper
     */
    private $addressHelper;

    /**
     * @var array DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(array $weekPrograms, Section $pageSection, $programDateFormat, $programTimeFormat)
    {
//        passed parameters
        $this->weekPrograms = $weekPrograms;
        $this->pageSection = $pageSection;
        $this->programDateFormat = $programDateFormat;
        $this->programTimeFormat = $programTimeFormat;

        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->addressHelper = new AddressQueryHelper();

        $this->reportStyles = new RehearsalScheduleStyles();
    }

    /* $weekProgram keys:
    *   ['dateID'] ['date'] ['start'] ['project'] ['pgmNo'] ['pgmTitle'] ['condID'] ['orchID'] ['venueID'] ['dress']
     *
     * $weekProgram contains every performance in the week. Loop through each record,
     *    rendering a new section for each Project + Program Number combination.
     * Within each Project + Program Number combination ...
     *  - render the date/time/venue/dress for Each Concert
     *  - extract the First Concert and use it to render conductor, soloist, program
     *
    */


    public function renderPrograms()
    {
        // default font and paragraph styles created in RehearsalScheduleStyles class
        $defaultFont = $this->reportStyles->reportFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $projectGroup = '';

        foreach ($this->weekPrograms as $weekProgram) {
            $performerHeader = [];

            if ($weekProgram['project'] . $weekProgram['pgmNo'] != $projectGroup) {
                $projectHeading = $weekProgram['project'];
                $projectHeading .= $weekProgram['pgmNo'] ? ' - ' . $weekProgram['pgmNo'] : '';

                $this->pageSection->addText(
                    $projectHeading,
                    $this->reportStyles->reportTitleFont,
                    $defaultParagraph
                );

                if(!empty($weekProgram['pgmTitle'])){
                    $this->pageSection->addText($weekProgram['pgmTitle'],
                    $this->reportStyles->reportFontItalic,
                    $defaultParagraph);
                }

//          render all concert dates
                $this->renderConcertDates(
                    $this->weekPrograms,
                    $weekProgram['project'] . $weekProgram['pgmNo'],
                    $defaultFont,
                    $defaultParagraph
                );

                $this->pageSection->addText(' ', $defaultFont, $defaultParagraph);

//                assemble the Conductor, Date-Work Conductors and Soloists. Implode and render in the format required by the report.
                $performerHeader[] = $this->datePerformerHelper->getConductor(
                    $weekProgram['dateID'],
                    $weekProgram['condID']
                );
                $performerHeader[] = $this->datePerformerHelper->getDateWorkConductors($weekProgram['dateID']);
                $performerHeader[] = $this->datePerformerHelper->getSoloists(
                    $weekProgram['dateID'],
                    $weekProgram['condID'],
                );
                $this->pageSection->addText(
                    implode('</w:t><w:br/><w:t>', array_filter($performerHeader)),
                    $defaultFont,
                    $defaultParagraph
                );

                $this->pageSection->addText(' ', $defaultFont, $defaultParagraph);
                $this->getConcertProgram($weekProgram['dateID'], $defaultFont, $defaultParagraph);

                $projectGroup = $weekProgram['project'] . $weekProgram['pgmNo'];
            }
        }
    }

    public function renderConcertDates($weekPrograms, $projectGroup, $defaultFont, $defaultParagraph)
    {
        foreach (array_filter($weekPrograms) as $concert) {

            if ($concert['project'] . $concert['pgmNo'] === $projectGroup) {

//            concert venue is single-item array; renders name only but address can be added
                $concertVenues = $this->addressHelper->getAddresses($concert['venueID'])->getQuery()->toArray();
                foreach ($concertVenues as $concertVenue) {
                    $venueName = trim(htmlspecialchars($concertVenue['name2'] . ' ' . $concertVenue['name1']));
                }

                $concertDate = $concert['date']->format($this->programDateFormat);
                $concertDate .= ' ' . __('at') . ' ';
                $concertDate .= $concert['start']->format($this->programTimeFormat);
                $concertDate .= $venueName ? ' - ' . $venueName : '';
                $concertDate .= $concert['dress'] ? ' (' . $concert['dress'] . ')' : '';

                $this->pageSection->addText($concertDate, $defaultFont, $defaultParagraph);

                $projectGroup = $concert['project'] . $concert['pgmNo'];
            }
        }
    }

    public function getConcertProgram($dateID, $defaultFont, $defaultParagraph)
    {
//        in month and other schedules, there is usually insufficient space for instrumentation, movements, etc
        $concertWorks = $this->dateQueryHelper->getDateWorksForDateID($dateID)
            ->withDateWorkInstrumentationGrids()
            ->getQuery()
            ->toArray();

        foreach ($concertWorks as $concertWork) {
            if ($concertWork['swork']['l_regular'] == 1) {
                if ($concertWork['l_encore'] == 1) {
                    $encore = mb_strtoupper(__('encore')) . ' -- ';
                }
                if ($concertWork['swork']['l_intermission'] != 1) {
                    $composer = mb_strtoupper($concertWork['swork']['scomposer']['lastname']);
                    $composer .= $concertWork['arrangement'] ? ' (' . $concertWork['arrangement'] . ')' : '';
                    $composer .= ':';
                } else {
                    $composer = '';
                }

                if (trim($concertWork['title2']) !== '') {
                    $workTitle = $concertWork['title2'];
                } else {
                    $workTitle = $concertWork['swork']['title1'];
                }

//                the < > characters will turn the text to italics when addFormattedText is used
                $workTitle .= $concertWork['Sworkpremieres']['name'] ?
                    ' - <' . $concertWork['Sworkpremieres']['name'] . ' ' . __('premiere' . '>')
                    : ' ';

                $this->pageSection->addFormattedText(
                    trim($encore . ' ' . $composer . ' ' . $workTitle),
                    $defaultFont,
                    $defaultParagraph
                );
            }
        }
//        blank line to provide space before next Project
        $this->pageSection->addText(' ', $defaultFont, $defaultParagraph);
    }
}
