<?php

namespace Customer\obf\reports\RehearsalSchedule;

use App\Reports\Report;
use App\Reports\ReportWord;

use Cake\Core\Configure;

use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\AddressQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;

use Customer\obf\reports\OnWord\OnWord;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use ReflectionException;

/* Report produces a rehearsal schedule for each WEEK
*  Larger font is used as the report is typically posted backstage and should be
 * visible from a few feet away
*/

class RehearsalSchedule extends ReportWord
{

    /**
     * Helper class for the date query of the dates.
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var AddressQueryHelper
     */
    private $addressHelper;

    /**
     * Helper for works on the rehersal
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function initialize()
    {
        parent::initialize();

        $this->dateQueryHelper = new DateQueryHelper();
        $this->addressHelper = new AddressQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();

        $this->reportStyles = new RehearsalScheduleStyles();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * Get the EVENTS selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    private function renderReport()
    {
        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => "portrait",
                'marginLeft' => Converter::inchToTwip(0.5),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.5),
                'marginBottom' => Converter::inchToTwip(0.5),
                'breakType' => 'continuous',
            ]
        );

//        +++++ STYLES AND FORMATS +++++
//      default font and paragraph styles created in RehearsalScheduleStyles class
        $defaultFont = $this->reportStyles->reportFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

//        date and time formats sent to the week program section and main rehearsal section
        $programDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $programTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');

        if (!empty(Configure::read('Formats.programHeaderTime'))) {
            $timeFormat = Configure::read('Formats.programHeaderTime');
        } else {
            $timeFormat = Configure::read('Formats.time');
        }
        $repFile = $this->getRepFileParams();

//        +++++++ START DATA FETCH AND MAIN OUTPUT ++++++++
//        identify each week in the selected dates; page break between weeks
        $weeks = [];
        foreach ($this->datesResult as $dateResult) {
            $weeks[] = $dateResult['pweek'];
        }
        $weeks = array_unique($weeks);
        $w = 0;

//        loop through each week
        foreach ($weeks as $week) {
            if ($w > 0) {
                $pageSection->addPageBreak();
            }

//          +++++  HEADER +++++
//        7.25 inch table; no border
            $titleTable = $pageSection->addTable($this->reportStyles->titleTableStyle);
            $titleTable->addRow();
            $titleTable->addCell(Converter::inchToTwip(3.5), $this->reportStyles->defaultCell)
                ->addText(
                    htmlspecialchars(
                        Configure::read('CustomerSettings.name')
                    ),
                    $this->reportStyles->reportTitleFont,
                    $defaultParagraph
                );
            $titleTable->addCell(Converter::inchToTwip(3.75), $this->reportStyles->defaultCell)
                ->addText(
                    $repFile['report_title'] . ' - ' . $repFile['week'] . ' ' . $week,
                    $this->reportStyles->reportTitleFont,
                    $this->reportStyles->defaultParagraphRight
                );
            $titleTable->addRow();
            $titleTable->addCell(Converter::inchToTwip(3.5), $this->reportStyles->defaultCell);
            $titleTable->addCell(Converter::inchToTwip(3.75), $this->reportStyles->defaultCell)
                ->addText(
                    $repFile['posted'] . ' ' . date($programDateFormat),
                    $this->reportStyles->reportSmallFont,
                    $this->reportStyles->defaultParagraphRight
                );

            $pageSection->addText(' ', $defaultFont, $defaultParagraph);
            $firstRehearsal = '';
            $programArray = [];
            $weekPrograms = [];

            foreach ($this->datesResult as $rehearsal) {
//                create an array of Performances to send to the RehearsalScheduleProgram class
                if ($rehearsal['pweek'] === $week && $rehearsal['seventtype']['l_performance'] == 1) {
                    $programArray['dateID'] = $rehearsal['id'];
                    $programArray['date'] = $rehearsal['date_'];
                    $programArray['start'] = $rehearsal['start_'];
                    $programArray['project'] = $rehearsal['sproject']['name'];
                    $programArray['pgmNo'] = $rehearsal['programno'];
                    $programArray['pgmTitle'] = $rehearsal['programtitle'];
                    $programArray['condID'] = $rehearsal['conductor_id'];
                    $programArray['orchID'] = $rehearsal['orchestra_id'];
                    $programArray['venueID'] = $rehearsal['location_id'];
                    $programArray['dress'] = $rehearsal['sdress']['name'];
                }
                $weekPrograms[] = $programArray;

                if ($rehearsal['pweek'] === $week && $rehearsal['seventtype']['l_performance'] == 0) {
//                    Elements of the Event Row - date/time/project/conductor/ activity, etc
//                    This information is fetched separately for ease of customizations
                    $rehDate = $rehearsal['date_']->format($repFile['date_format']);
                    $rehTime = '';
                    if (!empty($rehearsal['start_'])) {
                        $rehTime .= $rehearsal['start_']->format($timeFormat);
                    }
                    if (!empty($rehearsal['end_'])) {
                        $rehTime .= ' - ' . $rehearsal['end_']->format($timeFormat);
                    }

                    $rehProject = $rehearsal['sproject']['code'] ?? $rehearsal['sproject']['name'];
//                    conductor is a single-time array
                    $rehConductor = '';
                    if (!is_null($rehearsal['conductor_id'])) {
                        $conductors = $this->addressHelper->getAddresses($rehearsal['conductor_id'])->getQuery(
                        )->toArray(0);
                        foreach ($conductors as $conductor) {
                            $rehConductor = __('adates.conductor_id') . ': ' . trim(
                                    $conductor['name2'] . ' ' . $conductor['name1']
                                );
                        }
                    }
                    $eventInfo = $rehearsal['seventtype']['name'];

//                    Main Table is 3 columns - Formatting and output is different for the FIRST
//                    Rehearsal on each DAY
                    $pageSection->addText('', $this->reportStyles->reportTitleFont, $defaultParagraph);
                    $rehDateTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    $rehDateTable->addRow();

                    if ($rehearsal['date_'] != $firstRehearsal) {
                        $pageSection->addText('', $this->reportStyles->reportTitleFont, $defaultParagraph);
                        $rehDateTable->addCell(Converter::inchToTwip(1.50), $this->reportStyles->topBorderCell)
                            ->addText($rehDate, $this->reportStyles->reportTitleFont, $defaultParagraph);
                        $rehDateTable->addCell(Converter::inchToTwip(1.90), $this->reportStyles->topBorderCell)
                            ->addText($rehTime, $this->reportStyles->reportTitleFont, $defaultParagraph);
                        $rehDateTable->addCell(Converter::inchToTwip(4), $this->reportStyles->topBorderCell)
                            ->addFormattedText(
                                $rehProject . ' ' . $eventInfo,
                                $this->reportStyles->reportTitleFont,
                                $defaultParagraph
                            );
                    } else {
                        $rehDateTable->addCell(Converter::inchToTwip(1.50), $this->reportStyles->defaultCell)
                            ->addText('', $this->reportStyles->reportTitleFont, $defaultParagraph);
                        $rehDateTable->addCell(Converter::inchToTwip(1.90), $this->reportStyles->defaultCell)
                            ->addText($rehTime, $this->reportStyles->reportTitleFont, $defaultParagraph);
                        $rehDateTable->addCell(Converter::inchToTwip(4), $this->reportStyles->defaultCell)
                            ->addFormattedText(
                                $rehProject . ' ' . $eventInfo,
                                $this->reportStyles->reportTitleFont,
                                $defaultParagraph
                            );
                    }

//                    This is rendered as a separate row for easy removal or other customizations.
//                    Some clients will want this to be removed, others may want to include notes or other info
                    $rehDateTable->addRow();
                    $rehDateTable->addCell(Converter::inchToTwip(1.50), $this->reportStyles->defaultCell)
                        ->addText('', $this->reportStyles->reportSmallFont, $defaultParagraph);
                    $rehDateTable->addCell(Converter::inchToTwip(1.90), $this->reportStyles->defaultCell)
                        ->addText('', $this->reportStyles->reportSmallFont, $defaultParagraph);
                    $rehDateTable->addCell(Converter::inchToTwip(4), $this->reportStyles->defaultCell)
                        ->addFormattedText($rehearsal['text'], $this->reportStyles->reportSmallFont, $defaultParagraph);

//                    Fetch works on the rehearsal
                    $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($rehearsal['id'])
                        ->getQuery()
                        ->toArray();
                    $rehProgramTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);

//                   render conductor above the program table (some clients may want soloist here as well)
                    $rehProgramTable->addRow();
                    $rehProgramTable->addCell(Converter::inchToTwip(1.50), $this->reportStyles->defaultCell)
                        ->addText('', $defaultFont, $defaultParagraph);
                    $rehProgramTable->addCell(Converter::inchToTwip(5.90), $this->reportStyles->defaultCell)
                        ->addFormattedText($rehConductor, $defaultFont, $defaultParagraph);

                    foreach ($dateWorks as $dateWork) {
//                  allow for possibility of 'break' (intermission) in rehearsal
                        if ($dateWork['swork']['l_intermission'] != 1) {
                            $composer = mb_strtoupper($dateWork['swork']['scomposer']['lastname']);
                        }
                        if (trim($dateWork['title2']) !== '') {
                            $title = $dateWork['title2'];
                        } else {
                            $title = $dateWork['swork']['title1'];
                        }
//            if there is at least one soloist on the work, render soloist tag
                        $rehearsalWorkSoloist = $this->dateWorkQueryHelper->getSoloistsForDateWorkID($dateWork['id'])
                            ->getQuery()
                            ->toArray(0);
                        if (!empty($rehearsalWorkSoloist)) {
                            $title .= ' ' . $repFile['with_soloist'];
                        }

                        $rehProgramTable->addRow();
                        $rehProgramTable->addCell(Converter::inchToTwip(1.50), $this->reportStyles->defaultCell)
                            ->addText('', $defaultFont, $defaultParagraph);
                        $rehProgramTable->addCell(Converter::inchToTwip(5.90), $this->reportStyles->defaultCell)
                            ->addFormattedText(
                                $composer . ': ' . $title,
                                $defaultFont,
                                $this->reportStyles->hangingParagraph
                            );
                    }

                    $firstRehearsal = $rehearsal['date_'];
                }
            }

//            if the user selected concerts, add page break before program section
            if (!empty(array_filter($weekPrograms))) {
                $pageSection->addText('<w:p><w:r><w:br w:type="page"/></w:r></w:p>', $defaultFont, $defaultParagraph);
                $pageSection->addText('', $defaultFont, $defaultParagraph);

                $pageSection->addText(
                    $repFile['program_header'],
                    $this->reportStyles->reportTitleFont,
                    $this->reportStyles->sectionHeadingParagraph
                );
                $pageSection->addText('', $defaultFont, $defaultParagraph);

                $weekPrograms = (array_filter(array_unique($weekPrograms, SORT_REGULAR)));
                $programs = new RehearsalSchedulePrograms(
                    $weekPrograms,
                    $pageSection,
                    $programDateFormat,
                    $programTimeFormat
                );
                $programs->renderPrograms();
            }


            $w++;
        }

    }

}
