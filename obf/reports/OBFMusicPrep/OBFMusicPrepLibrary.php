<?php

namespace Customer\obf\reports\OBFMusicPrep;

use Cake\Core\Configure;
use Customer\obf\reports\utility\ComposerQueryHelper;
use Customer\obf\reports\utility\WorkQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

/*
 * NOTE - this version renders any/all Library records attached to the main Work record.
 *    This is incorrect and it should instead link aDateWork_Library records (that is, library records
 *       manually linked by the user to the Date-Work record). as the user does not know how to link library
 *         records to date-work records, this alternate method is used and can be refactored to use the proper
 *          date-work library link at a later time
 */

class OBFMusicPrepLibrary
{
    const LIBRARY_HEADER = 'Acquisitions';

    const LIBRARY_COLUMN_SMALL = 0.75;
    const LIBRARY_COLUMN_MEDIUM = 1.50;
    const LIBRARY_COLUMN_LARGE = 2.25;

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct($dateWork, $repFileText, Section $pageSection)
    {
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->workQueryHelper = new WorkQueryHelper();

        $this->reportStyles = new OBFMusicPrepStyles();

        $this->dateWork = $dateWork;
        $this->repFileText = $repFileText;
        $this->pageSection = $pageSection;
    }

    public function renderLibrary()
    {
//        most often-used font/cell/paragraph styles
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultCell = $this->reportStyles->defaultCell;
        $bottomBorderCell = $this->reportStyles->bottomBorderCell;


        $this->pageSection->addText(
            self::LIBRARY_HEADER,
            $this->reportStyles->sectionHeaderFont,
            $this->reportStyles->sectionHeaderParagraph
        );

        $libraries = $this->workQueryHelper->getLibrariesForWork($this->dateWork['work_id'])->getQuery()->toArray();

//        If no library record attached to the work, print an empty table so user can manually record rentals
        if (is_null($libraries) || empty($libraries)) {
            $libraryHeaderTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
            $this->renderHeaderTable('', $libraryHeaderTable, $defaultFont, $defaultCell, $defaultParagraph);
            $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $libraryTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
            $this->renderLibraryTable(
                '',
                $libraryTable,
                $defaultFont,
                $defaultCell,
                $bottomBorderCell,
                $defaultParagraph
            );
            $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        }

//        Render Library Table for each Library record linked to main work (see Note above)
        foreach ($libraries as $library) {
            $libraryHeaderTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
            $this->renderHeaderTable(
                $library['publisheraddress']['name1'],
                $libraryHeaderTable,
                $defaultFont,
                $defaultCell,
                $defaultParagraph
            );
            $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $libraryTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
            $this->renderLibraryTable(
                $library['catalog'],
                $libraryTable,
                $defaultFont,
                $defaultCell,
                $bottomBorderCell,
                $defaultParagraph
            );

            $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        }
    }

    protected function renderHeaderTable($publisher, $libraryHeaderTable, $defaultFont, $defaultCell, $defaultParagraph)
    {
        $libraryHeaderTable->addRow();
        $libraryHeaderTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $defaultCell)
            ->addText($this->repFileText['publisher'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryHeaderTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_LARGE), $defaultCell)
            ->addFormattedText($publisher, $defaultFont, $defaultParagraph);
        $libraryHeaderTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $defaultCell)
            ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryHeaderTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_LARGE), $defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
    }

    protected function renderLibraryTable(
        $catalog,
        $libraryTable,
        $defaultFont,
        $defaultCell,
        $bottomBorderCell,
        $defaultParagraph
    ) {
        $libraryTable->addRow();
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['library'], $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $defaultCell)
            ->addFormattedText($catalog, $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['scores'], $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);

        $libraryTable->addRow();
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['purchase'], $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['ordered'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['rental'], $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['ordered'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);

        $libraryTable->addRow();
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['due'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['due'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);

        $libraryTable->addRow();
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['received'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['received'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);

        $libraryTable->addRow();
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText('', $this->reportStyles->defaultFontBoldItalic, $defaultParagraph);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_SMALL), $defaultCell)
            ->addText($this->repFileText['contract'], $defaultFont, $this->reportStyles->defaultParagraphRight);
        $libraryTable->addCell(Converter::inchToTwip(self::LIBRARY_COLUMN_MEDIUM), $bottomBorderCell)
            ->addText('', $defaultFont, $defaultParagraph);
    }
}
