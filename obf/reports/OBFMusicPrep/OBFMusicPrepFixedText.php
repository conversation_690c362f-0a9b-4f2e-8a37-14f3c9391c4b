<?php

namespace Customer\obf\reports\OBFMusicPrep;

use PhpOffice\PhpWord\Shared\Converter;

/*
 * Render the fixed text, primarily at the bottom of the document.
 * Text is stored in this file rather than the .rep file as it is the text most likley to change
 *   when this report is customized for other clients
 */


class OBFMusicPrepFixedText
{
    const PREP_TABLE_COL_XLARGE = 1.25;
    const PREP_TABLE_COL_LARGE = 1.20;
    const PREP_TABLE_COL_MEDIUM = 0.72;
    const PREP_TABLE_COL_SMALL = 0.30;
    const PREP_TABLE_COL_SPACER = 0.43;

    const TABLE_2COL_WIDTH = 3.75;

    private $reportStyles;
    private $pageSection;


    public function __construct($pageSection)
    {
        $this->pageSection = $pageSection;
        $this->reportStyles = new OBFMusicPrepStyles();

        $this->defaultFont = $this->reportStyles->defaultFont;
        $this->smallFont = $this->reportStyles->smallFont;

        $this->defaultParagraph = $this->reportStyles->defaultParagraph;
        $this->defaultParagraphRight = $this->reportStyles->defaultParagraphRight;

        $this->defaultCell = $this->reportStyles->defaultCell;
        $this->borderCell = $this->reportStyles->borderCell;
    }

    public function renderPreparation()
    {
        $this->pageSection->addText(
            'Part Preparation',
            $this->reportStyles->sectionHeaderFont,
            $this->reportStyles->sectionHeaderParagraph
        );

        $this->pageSection->addTextBreak(1, $this->defaultFont, $this->defaultParagraph);

        $bowingTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $bowingTable->addRow();
        $bowingTable->addCell(Converter::inchToTwip(self::TABLE_2COL_WIDTH), $this->defaultCell)
            ->addText('', $this->reportStyles->defaultFontBold, $this->defaultParagraph);
        $bowingTable->addCell(Converter::inchToTwip(self::TABLE_2COL_WIDTH), $this->defaultCell)
            ->addText('Bowing Cycle', $this->reportStyles->defaultFontBold, $this->defaultParagraph);

        $this->pageSection->addTextBreak(1, $this->defaultFont, $this->defaultParagraph);

        $prepTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);



        for ($row = 0; $row < 5; $row++) {
            $prepTable->addRow();
            $prepCol1 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_LARGE), $this->defaultCell);
            $prepCol2 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SMALL), $this->borderCell);
            $prepCol3 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_LARGE), $this->defaultCell);
            $prepCol4 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SMALL), $this->borderCell);
            $prepCol5 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SPACER), $this->defaultCell);
            $prepCol6 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_XLARGE), $this->defaultCell);
            $prepCol7 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SMALL), $this->borderCell);
            $prepCol8 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_LARGE), $this->defaultCell);
            $prepCol9 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SMALL), $this->borderCell);
            $prepCol10 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_MEDIUM), $this->defaultCell);
            $prepCol11 = $prepTable->addCell(Converter::inchToTwip(self::PREP_TABLE_COL_SMALL), $this->borderCell);
            switch ($row) {
                case 0:
                    $prepCol1->addText('Stamped', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol2->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol3->addText('Cuts', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol4->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol5->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol6->addText('VL1: to principal', $this->defaultFont, $this->defaultParagraphRight);
                    break;
                case 1:
                    $prepCol1->addText('Numbered', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol2->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol3->addText('Start/Stop', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol4->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol5->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol6->addText('VL2: to principal', $this->defaultFont, $this->defaultParagraphRight);
                    break;
                case 2:
                    $prepCol1->addText('Errata', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol2->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol3->addText('Practice Parts', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol4->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol5->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol6->addText('VLA: to principal', $this->defaultFont, $this->defaultParagraphRight);
                    break;
                case 3:
                    $prepCol1->addText('Repair', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol2->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol3->addText('Scan', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol4->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol5->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol6->addText('VC: to principal', $this->defaultFont, $this->defaultParagraphRight);
                    break;
                case 4:
                    $prepCol1->addText('Page Turns', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol2->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol3->addText('Additional Parts', $this->defaultFont, $this->defaultParagraphRight);
                    $prepCol4->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol5->addText('', $this->defaultFont, $this->defaultParagraph);
                    $prepCol6->addText('CB: to principal', $this->defaultFont, $this->defaultParagraphRight);
                    break;

            }
            $prepCol7->addText('', $this->defaultFont, $this->defaultParagraph);
            $prepCol8->addText('returned', $this->defaultFont, $this->defaultParagraphRight);
            $prepCol9->addText('', $this->defaultFont, $this->defaultParagraph);
            $prepCol10->addText('done', $this->defaultFont, $this->defaultParagraphRight);
            $prepCol11->addText('', $this->defaultFont, $this->defaultParagraph);
        }
        $this->pageSection->addTextBreak(1, $this->defaultFont, $this->defaultParagraph);

        $this->pageSection->addText(
            'Notes',
            $this->reportStyles->sectionHeaderFont,
            $this->reportStyles->sectionHeaderParagraph
        );
    }
}
