<?php

namespace Customer\obf\reports\OBFMusicPrep;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;

use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\ComposerQueryHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Shared\Converter;

use ReflectionException;

/*  For event selected by user, print program with detailed instrumentation and Library info
*   Report is used to track preparation of Music and that information is hand-entered on the paper report
 *
 * TODO: refine data collection so if the user selects multiple dates, the report logically assembles them
 *  into Season and Project
 */

class OBFMusicPrep extends ReportWord
{

//    Top concert info table-two columns
    const TOP_TABLE_COL1_WIDTH = 1.5;
    const TOP_TABLE_COL2_WIDTH = 6.0;

//    Four-column table
    const FOUR_COL_SMALL_WIDTH = 1.5;
    const FOUR_COL_LARGE_WIDTH = 2.0;


    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Helper class for date-work soloist formatting
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    private $reportStyles;

    /**
     * Initialize
     * Set the query helper for this report container and define the styles
     */
    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();

        // Set report font, table and paragraph styles
        $this->reportStyles = new OBFMusicPrepStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file.
     */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Work records selected by user to workQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }


    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

//    CREATE File and SAVE REPORT

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


//    +++ RENDER REPORT +++

//  Loop through each date record selected by user

    private function renderReport()
    {
//        Set formats based on client location and other settings.php values
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');

//        headers/labels from the report .rep file
        $repFileText = $this->getRepFileParams();

//        most frequently used Fonts and Paragraph Styles
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $smallFont = $this->reportStyles->smallFont;
        $smallFontBold = $this->reportStyles->smallFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultParagraphRight = $this->reportStyles->defaultParagraphRight;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => 'portrait',
                'marginLeft' => Converter::inchToTwip(0.50),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );


//          +++  START RENDERING ++
//        Group all dates selected by user into unique season:project:programNumber. Loop through these and put a page break between them
//        $fixedText = new OBFMusicPrepFixedText($pageSection);
        $dateLoop = 0;
        $dateWorkLoop = 0;
        foreach ($this->datesResult as $dateResult) {
            if ($dateLoop > 0) {
                $pageSection->addPageBreak();
            }

//            elements at the top of the report
            $ensembleName = $dateResult['orchestraaddress']['name1'];
            $conductorName = trim(
                $dateResult['conductoraddress']['name2'] . ' ' . $dateResult['conductoraddress']['name1']
            );
            $venueName = trim($dateResult['locationaddress']['name1'] . ' ' . $dateResult['locationaddress']['name2']);
            $printDate = $dateResult['date_']->format($topDateFormat) . ' ' . __(
                    'at'
                ) . ' ' . $dateResult['start_']->format($topTimeFormat);

//          Fetch all events in the Season and Project so Rehearsal and deadlines can be calculated
            $projectDates = $this->dateQueryHelper->getDatesforSeasonProject(
                $dateResult['season_id'],
                $dateResult['project_id']
            )->getQuery()->toArray();
            $firstRehearsal = $this->getFirstRehearsal($projectDates);
//            If there is no first rehearsal (either no rehearsal or bad data entry by user) then use the
//            selected event as the rehearsal
            if (is_null($firstRehearsal) || (string)trim($firstRehearsal) == '') {
                $firstRehearsal = $dateResult['date_'];
            }

            $dueDate = date($repFileText['due_date_format'], strtotime('-7 days', strtotime($firstRehearsal)));

//            START DATE-WORK LOOP
            $dateWorks = $this->dateQueryHelper->getDateWorksForDateID($dateResult['id'])
                ->withDateWorkInstrumentationGrids()
                ->getQuery()
                ->toArray();
            foreach ($dateWorks as $dateWork) {
                if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                    if ($dateWorkLoop > 0) {
                        $pageSection->addPageBreak();
                    }

                    $composerName = $this->composerQueryHelper->getComposerName(
                        $dateWork['swork']['scomposer']['lastname'],
                        $dateWork['swork']['scomposer']['firstname'],
                        1
                    );
                    if (!is_null($dateWork['title2']) && (string)trim($dateWork['title2']) !== '') {
                        $workTitle = $dateWork['title2'];
                    } else {
                        $workTitle = $dateWork['swork']['title1'];
                    }
                    if (!is_null($dateWork['arrangement']) && (string)trim($dateWork['arrangement']) !== '') {
                        $workTitle .= ' (' . $dateWork['arrangement'] . ')';
                    }
                    $dateWorkSoloists = $this->dateQueryHelper->getSoloistsForDateWorkID($dateWork['id'])->getQuery(
                    )->toArray();
                    $dateWorkSoloistOutput = $this->dateWorkQueryHelper->getDateWorkSoloistsAsString(
                        $dateWorkSoloists,
                        $dateWork['conductor_id'] ?? 0,
                        '; ',
                        0,
                        0,
                        ', '
                    );


//                RENDER COMPOSER AND TITLE
                    $pageSection->addFormattedText($composerName, $this->reportStyles->titleFont, $defaultParagraph);
                    $pageSection->addFormattedText($workTitle, $this->reportStyles->titleFont, $defaultParagraph);
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                RENDER TOP TABLE - concert information
                    $topTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    for ($row = 0; $row < 5; $row++) {
                        $topTable->addRow();
                        $leftCell = $topTable->addCell(
                            Converter::inchToTwip(self::TOP_TABLE_COL1_WIDTH),
                            $this->reportStyles->defaultCell
                        );
                        $rightCell = $topTable->addCell(
                            Converter::inchToTwip(self::TOP_TABLE_COL2_WIDTH),
                            $this->reportStyles->defaultCell
                        );
                        switch ($row) {
                            case 0:
                                $leftCell->addText($repFileText['ensemble'], $defaultFont, $defaultParagraphRight);
                                $rightCell->addText($ensembleName, $defaultFontBold, $defaultParagraph);
                                break;
                            case 1:
                                $leftCell->addText($repFileText['conductor'], $defaultFont, $defaultParagraphRight);
                                $rightCell->addText($conductorName, $defaultFontBold, $defaultParagraph);
                                break;
                            case 2:
                                $leftCell->addText($repFileText['soloist'], $defaultFont, $defaultParagraphRight);
                                $rightCell->addText($dateWorkSoloistOutput, $defaultFontBold, $defaultParagraph);
                                break;
                            case 3:
                                $leftCell->addText($repFileText['perfdate'], $defaultFont, $defaultParagraphRight);
                                $rightCell->addText($printDate, $defaultFontBold, $defaultParagraph);
                                break;
                            case 4:
                                $leftCell->addText($repFileText['venue'], $defaultFont, $defaultParagraphRight);
                                $rightCell->addText($venueName, $defaultFontBold, $defaultParagraph);
                        }
                    }

                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//              +++ RENDER DUE DATE TABLE +++
                    $dueDateTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    $dueDateTable->addRow();
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_SMALL_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText($repFileText['first_reh'], $defaultFont, $defaultParagraphRight);
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_LARGE_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText(
                            $firstRehearsal->format($repFileText[due_date_format]),
                            $defaultFont,
                            $defaultParagraph
                        );
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_SMALL_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText($repFileText['due_date'], $defaultFont, $defaultParagraphRight);
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_LARGE_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText($dueDate, $defaultFont, $defaultParagraph);

                    $dueDateTable->addRow();
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_SMALL_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText($repFileText['date_ready'], $defaultFont, $defaultParagraphRight);
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_LARGE_WIDTH),
                        $this->reportStyles->bottomBorderCell
                    )
                        ->addText('', $defaultFont, $defaultParagraph);
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_SMALL_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText('', $defaultFont, $defaultParagraphRight);
                    $dueDateTable->addCell(
                        Converter::inchToTwip(self::FOUR_COL_LARGE_WIDTH),
                        $this->reportStyles->defaultCell
                    )
                        ->addText('', $defaultFont, $defaultParagraph);

                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//              +++ RENDER INSTRUMENTATION TABLE +++
                    $instrumentationTable = new OBFMusicPrepInstrumentation($dateWork, $repFileText, $pageSection);
                    $instrumentationTable->renderInstrumentationTable();
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                +++ RENDER LIBRARY TABLE +++
                    $libraryTable = new OBFMusicPrepLibrary($dateWork, $repFileText, $pageSection);
                    $libraryTable->renderLibrary();

//                +++ RENDER FIXED TEXT - Music Prep and notes +++
                    $fixedText = new OBFMusicPrepFixedText($pageSection);
                    $fixedText->renderPreparation();
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


                    $dateWorkLoop++;
                }
            }

            $dateLoop++;
        }
    }

    private function getFirstRehearsal($projectDates)
    {
//        dates in the array are returned in chron order so grab first non-perf with orchestra as first reh
        $rehearsals = [];
        foreach ($projectDates as $projectDate) {
            if ($projectDate['seventtype']['l_performance'] == 0 && $projectDate['orchestra_id'] > 0) {
                $rehearsals[] = $projectDate['date_'];
            }
        }

        return $rehearsals[0];
    }

}
