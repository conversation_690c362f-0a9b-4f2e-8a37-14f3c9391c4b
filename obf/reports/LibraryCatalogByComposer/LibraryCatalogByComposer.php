<?php

namespace Customer\obf\reports\LibraryCatalogByComposer;

use Customer\fasutilities\reports\utility\WorkQueryHelper;
use Customer\fasutilities\reports\utility\LibraryQueryHelper;
use Customer\fasutilities\reports\utility\ComposerQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentationWork;

use App\Reports\Report;
use App\Reports\ReportWord;

use Cake\Core\Configure;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use ReflectionException;

/*
 * Based on OPAS Classic reports; produces a catalog of Library
 * holdings, organized by composer
 */

class LibraryCatalogByComposer extends ReportWord
{
    const COL_1_WIDTH = 0.80;
    const COL_2_WIDTH = 2.52;
    const COL_3_WIDTH = 0.31;
    const COL_4_WIDTH = 2.12;
    const COL_5_WIDTH = 1.02;
    const COL_6_WIDTH = 0.21;
    const COL_7_WIDTH = 0.21;
    const COL_8_WIDTH = 0.23;

    /**
     * Helper class for Library entries selected by user
     * @var LibraryQueryHelper
     */
    private $libraryQueryHelper;

    /**
     * Helper class for Works for duration
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * Helper class for the composer details
     *
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * Helper class for Instrumentation
     * @var CustomerInstrumentationWork
     */
    private $instrumentation;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    /**
     * Prepared array for the rendering
     * @var array
     */
    private $libraryResult;

    /**
     * Set the query helper
     *
     * @throws Exception
     */
    public function initialize()
    {
        parent::initialize();

        $this->libraryQueryHelper = new LibraryQueryHelper();
        $this->workQueryHelper = new WorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentationWork();

        $this->reportStyles = new LibraryCatalogByComposerStyles();
    }

    /*********************************************/

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the Library records selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $libraryQuery = $this->libraryQueryHelper
            ->getLibraries($this->getRequest()->getData()['dataItemIds'])
            ->withWorkKeyboardGrid()
            ->withWorkExtraGrid()
            ->withWorkChorusGrid()
            ->getQuery();

        $this->libraryResult = $libraryQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    /**
     *
     * @param null $view
     * @param null $layout
     * @return mixed|string
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    private function renderReport()
    {
        //        report title and column headings from .rep file
        $repFileText = $this->getRepFileParams();

        // default font and paragraph styles created in WorkListStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $customerName = Configure::read('CustomerSettings.name');
        $footerDateFormat = Configure::read('Formats.date');

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.50),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.55),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        Render Header and Footer
        $this->renderHeader($pageSection, $repFileText);
        $this->renderFooter($pageSection, $repFileText, $footerDateFormat);

// Sort selected records by Composer and Title 1
        $libraries = $this->libraryResult;

        foreach ($libraries as $key => $value) {
            $composerLast[$key] = $value['swork']['scomposer']['lastname'];
            $composerFirst[$key] = $value['swork']['scomposer']['firstname'];
            $title[$key] = $value['swork']['title1'];
        }
        array_multisort($composerLast, SORT_ASC, $composerLast, SORT_ASC, $title, SORT_ASC, $libraries);

        $composerId = 0;
        $rowNo = 1;
        foreach ($libraries as $library) {
//            Render composer at his/her first work
            if ($library['swork']['composer_id'] != $composerId) {
                $composerName = $this->composerQueryHelper->getComposerName(
                    $library['swork']['scomposer']['lastname'],
                    $library['swork']['scomposer']['firstname'],
                    2
                );
                $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                $pageSection->addText($composerName, $this->reportStyles->headerFont, $defaultParagraph);
            }
            $composerId = $library['swork']['composer_id'];


            if ($library['swork']['composer_id'] === $composerId) {
                if ($rowNo % 2 == 0) {
                    $libraryRowStyle = $this->reportStyles->shadedCell;
                } else {
                    $libraryRowStyle = $this->reportStyles->defaultCell;
                }

//              Get instrumentation (with kybd/extra/chorus grids) from helper
                $work = $library['swork'];
                $this->instrumentation->addInstrumentationStringToWork($work);
                $workInstrumentation = $work['instrumentation_standard'];

                $libraryTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                $libraryTable->addRow();

                $libraryTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $libraryRowStyle)
                    ->addText($library['catalog'], $defaultFont, $this->reportStyles->defaultParagraphRight);

                $libraryTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $libraryRowStyle)
                    ->addText($library['swork']['title1'], $defaultFont, $defaultParagraph);

                if (!empty($library['swork']['duration'])) {
                    $workDuration = $this->workQueryHelper->getDurationAsInt($library['swork']['duration']);
                } else {
                    $workDuration = '';
                }
                $libraryTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $libraryRowStyle)
                    ->addText(
                        $workDuration,
                        $defaultFont,
                        $this->reportStyles->defaultParagraphRight
                    );

                $libraryTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $libraryRowStyle)
                    ->addFormattedText($workInstrumentation, $defaultFont, $defaultParagraph);

                $publisher = trim($library['publisheraddress']['name2'] . ' ' . $library['publisheraddress']['name1']);
                $publication =  trim($library['spublication']['publisheraddress']['code']) ?? $library['spublication']['publisheraddress']['name1'];
                $publisherOutput = $publisher;
                if ($library['spublication']['publisheraddress']['id'] != $library['publisheraddress']['id']
                    && $library['spublication']['publisheraddress']['id'] > 0) {
                    $publisherOutput .= ' [' .  $publication . ']';
                }
                if (is_null(
                        $library['spublication']['publisheraddress']['id']
                    ) && $library['spublication']['publisheraddress']['id'] > 0) {
                    $publisherOutput .= $publication;
                }
                $renderPublisher = trim($publisherOutput);

                $libraryTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $libraryRowStyle)
                    ->addText(htmlspecialchars($renderPublisher), $defaultFont, $defaultParagraph);

                $checkMark = html_entity_decode('&#10003;', 0, 'UTF-8');
                if ($library['l_parts'] == 1) {
                    $parts = $checkMark;
                } else {
                    $parts = '';
                }
                if ($library['l_stringmasters'] == 1) {
                    $stringMaster = $checkMark;
                } else {
                    $stringMaster = '';
                }
                if ($library['l_permanentloan'] == 1) {
                    $loan = $checkMark;
                } else {
                    $loan = '';
                }

                $libraryTable->addCell(Converter::inchToTwip(self::COL_6_WIDTH), $libraryRowStyle)
                    ->addText($parts, $defaultFont, $this->reportStyles->defaultParagraphCenter);
                $libraryTable->addCell(Converter::inchToTwip(self::COL_7_WIDTH), $libraryRowStyle)
                    ->addText($stringMaster, $defaultFont, $this->reportStyles->defaultParagraphCenter);
                $libraryTable->addCell(Converter::inchToTwip(self::COL_8_WIDTH), $libraryRowStyle)
                    ->addText($loan, $defaultFont, $this->reportStyles->defaultParagraphCenter);

                $rowNo++;
            }
        }
    }

    private function renderHeader($pageSection, $repFileText)
    {
        $header = $pageSection->createHeader();
        $header->addText(
            $repFileText['report_title'],
            $this->reportStyles->titleFont,
            $this->reportStyles->defaultParagraphCenter
        );

        $headerTable = $header->addTable($this->reportStyles->defaultTableStyle);
        $headerTable->addRow();
        $headerTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_1'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphRight
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_2'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_3'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphRight
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_4'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_5'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraph
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_6_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_6'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphCenter
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_7_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_7'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphRight
            );
        $headerTable->addCell(Converter::inchToTwip(self::COL_8_WIDTH), $this->reportStyles->noBorderCell)
            ->addText(
                $repFileText['Col_8'],
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphRight
            );
    }

    private function renderFooter($pageSection, $repFileText, $footerDateFormat)
    {
        $footer = $pageSection->createFooter();
        //tab character following date and legend
        $footer->addPreserveText(
            $repFileText['printed'] . ' ' . date($footerDateFormat) . '	' . $repFileText['legend']. '	' . 'Page {PAGE} of {NUMPAGES}',
            $this->reportStyles->defaultFont,
            $this->reportStyles->footerParagraphStyle
        );

    }
}
