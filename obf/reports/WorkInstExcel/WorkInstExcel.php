<?php

namespace Customer\obf\reports\WorkInstExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\fasutilities\reports\utility\WorkQueryHelper;
use Customer\fasutilities\reports\utility\ComposerQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentationWork;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;


class WorkInstExcel extends Report
{
//    Change these values to add a title or other heading
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 1;
    const DATA_ROW = 2;

    /**
     * Helper class for the selected Works.
     *
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * Helper class for formatting Composer name and dates
     */
    private $composerQueryHelper;

    /**
     * Helper class for rendering Instrumentation
     * @var CustomerInstrumentationWork
     */
    private $instrumentation;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $worksResult;

    // SET COLUMN ADDRESSES
    private $col_Composer = 'A';
    private $col_Title1 = 'B';
    private $col_Title2 = 'C';
    private $col_Arrangement = 'D';
    private $col_Date = 'E';
    private $col_Duration = 'F';
    private $col_DurMin = 'G';
    private $col_InstSummary = 'H';
    private $col_InstDetail = 'I';
    private $col_Genre = 'J';
    private $col_Library = 'K';
    private $col_LibraryHoldings = 'L';

    /**
     *
     */
    public function initialize()
    {
        $this->workQueryHelper = new WorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentationWork();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = [])
    {
        $workQuery = $this->workQueryHelper
            ->getWorks($this->getRequest()->getData()['dataItemIds'])
            ->withLibrary()
            ->withGenre()
            ->getQuery();

        $this->worksResult = $workQuery->toArray();

        return $this;
    }


    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();


//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

//      +++++ Render DATA HEADER ROW as single-item array in row 1
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

//        There is no doubt a more efficient way to do this but this sorts the work array by
//          Composer Last, Composer First, Work Title which is the most common method.
//        This procedure has the advantage that it is very easy to edit for report customizations
        $workArray = $this->worksResult;
        foreach ($workArray as $key => $value) {
            $lastName[$key] = $value['scomposer']['lastname'];
            $firstName[$key] = $value['scomposer']['firstname'];
            $title[$key] = $value['title1'];
        }
        array_multisort($lastName, SORT_ASC, $firstName, SORT_ASC, $title, SORT_ASC, $workArray);

//        +++++ FETCH WORK DATA AND RENDER by Individual Cell +++++
//        render by individual cell so format text can be used

        $row = self::DATA_ROW;
        $upperLeftCell = $this->col_Composer . $row;

        foreach ($workArray as $work) {
            $this->instrumentation->formatInstrumentationString($work);

            $composerName = $this->composerQueryHelper->getComposerName(
                $work['scomposer']['lastname'],
                $work['scomposer']['firstname'],
                2
            );
            $workYear = $work['compyearstatus'] . $work['compyear'];
            $workYear .= $work['compyear2'] ? '-' . $work['compyear2'] : '';

            $workDur = $this->workQueryHelper->getDurationStringForWork($work['duration'], ':', '');
            $workDurMin = $this->workQueryHelper->getDurationAsInt($work['duration']);

            $workGenre = $work['Sworkgenres']['name'];

//            LIBRARY INFO - $swork['alibraries'] is an array with data from the alibraries table.
//            It it used to determine if the work exists in the Library (yes/no in the report).
//            The $libraries array contains linked tables to the Library record and so is used for output
            if (sizeof($work['alibraries']) > 0) {
                $inLibrary = __('yes');
            } else {
                $inLibrary = __('no');
            }

            $libraries = $this->workQueryHelper->getLibrariesForWork($work['id'])->getQuery()->toArray();
            $libraryArray = [];
            foreach ($libraries as $library) {
                $libraryElement = $library['catalog'] ? $library['catalog'] . ' - ' : '';
                $libraryElement .= $library['publisheraddress']['code'] ?? $library['publisheraddress']['name1'];
                if ($library['l_parts'] == 1) {
                    $libraryElement .= ' (' . __('alibraries.l_parts') . ')';
                }
                $libraryArray[] = $libraryElement;
            }

            $libraryDetail = implode("\n", $libraryArray);

//            RENDER CELLS
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Composer . $row, htmlspecialchars($composerName));
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted($this->col_Title1 . $row, $work['title1']);
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted($this->col_Title2 . $row, $work['title2']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Arrangement . $row, htmlspecialchars($work['arranger']));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Date . $row, $workYear);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Duration . $row, $workDur);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_DurMin . $row, $workDurMin);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_InstSummary . $row, $work['instrumentation_standard']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_InstDetail . $row, $work['instrumentation_detail']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Genre . $row, $workGenre);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Library . $row, $inLibrary);
//            Text Wrap must be immediately applied to this cell because "\n" line break is used
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_LibraryHoldings . $row, $libraryDetail)
                ->getStyle($this->col_LibraryHoldings . $row)
                ->getAlignment()->setWrapText(true);

            $row++;
        }

//          ++++++ FORMAT SPREADSHEET ++++++
        $finalRow = $row - 1;
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;

//        BOLD Header (row 1) and turn on text wrap
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':L' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//        SET COLUMN WIDTHS
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Composer)
            ->setWidth(25);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Title1)
            ->setWidth(35);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Title2)
            ->setWidth(35);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Arrangement)
            ->setWidth(20);

//        format the next 3 columns using auto-width
        foreach (range($this->col_Date, $this->col_DurMin) as $col) {
            $spreadsheet->getActiveSheet()->getColumnDimension($col)
                ->setAutoSize(true);
        }

        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_InstSummary)
            ->setWidth(35);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_InstDetail)
            ->setWidth(35);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Library)
            ->setAutoSize(true);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_LibraryHoldings)
            ->setWidth(30);

//      SET FONT AND ALIGNMENT USING FORMAT ARRAY
//        Set font for the entire data table
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $this->col_LibraryHoldings . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

//         Columns E F G are right-aligned (composition year and duration columns)
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Date . self::HEADER_ROW . ':' . $this->col_DurMin . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

//        Column K is centered (work in library)
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Library . self::HEADER_ROW . ':' . $this->col_Library . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

//        Freeze Panes after composer and master title
        $spreadsheet->getActiveSheet()->freezePane('C' . self::DATA_ROW);


        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['Col_Composer'] ?? __('sworks.composer_id');
        $columnB = $repFileText['Col_Title1'] ?? __('sworks.title1');
        $columnC = $repFileText['Col_Title2'] ?? __('sworks.title2');
        $columnD = $repFileText['Col_Arr'] ?? __('sworks.arrangement');
        $columnE = $repFileText['Col_Year'] ?? __('sworks.compyear');
        $columnF = $repFileText['Col_Dur'] ?? __('sworks.duration');
        $columnG = $repFileText['Col_DurMin'] ?? __('sworks.duration');
        $columnH = $repFileText['Col_Inst1'] ?? __('instrumentation');
        $columnI = $repFileText['Col_Inst2'] ?? __('instrumentation');
        $columnJ = $repFileText['Col_Genre'] ?? __('sworks.genre_id');
        $columnK = $repFileText['Col_Lib1'] ?? __('alibraries.id');
        $columnL = $repFileText['Col_Lib2'] ?? __('alibraries.id');

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK,
            $columnL
        );
    }

    function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => '29465B') // Dark Blue Grey
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),

            'alignment' => [
                'setWrapText' => true
            ],


        );
    }

}
