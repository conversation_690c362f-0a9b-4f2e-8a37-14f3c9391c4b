<?php

namespace Customer\obf\reports\AddressBookExcelExport;

use App\Reports\Report;
use App\Reports\ReportsInterface;
use Cake\Core\Configure;

use Customer\obf\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\obf\reports\utility\AddressQueryHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders basic Address Book information to Excel.
 *   Designed to easily expand for client customizations
 *   This version renders Name|Address|Phone|Email|Group|Instrument|Notes
 */

class AddressBookExcelExport extends Report
{
    /**
     * Helper class for Address Book entries selected by user
     * @var AddressQueryHelper
     */
    private $addressQueryHelper;
    private $addressResult;

//   place various elements of the report
    const CLIENT_NAME = 'A1';
    const SHEET_TITLE = 'A2';
    const HEADER_ROW = 3;
    const DATA_ROW = 4;

    // SET COLUMN ADDRESSES
    private $col_LastName = 'A';
    private $col_FirstName = 'B';
    private $col_Address = 'C';
    private $col_City = 'D';
    private $col_State = 'E';
    private $col_Zip = 'F';
    private $col_Phones = 'G';
    private $col_Emails = 'H';
    private $col_Groups = 'I';
    private $col_Instruments = 'J';
    private $col_Notes = 'K';

    public function initialize()
    {
         parent::initialize();
        $this->addressQueryHelper = new AddressQueryHelper();
    }

    /**
     * @param array $where
     * @return $this|Report
     *
     */
    public function collect(array $where = [])
    {
        $addressQuery = $this->addressQueryHelper
            ->getAddresses($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->addressResult = $addressQuery->toArray();

        return $this;
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so print properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

        $row = self::DATA_ROW;
        $upperLeftCell = 'A' . $row;

//        TOP SECTION AND HEADER ROW
        $spreadsheet->getActiveSheet()->setCellValue(self::CLIENT_NAME, htmlspecialchars(Configure::read('CustomerSettings.name')));
        $spreadsheet->getActiveSheet()->setCellValue(self::SHEET_TITLE, $repFileText['report_title']);
        $spreadsheet->getActiveSheet()->fromArray($this->getHeaderRow($repFileText), null, 'A' . self::HEADER_ROW);


//        +++++ FETCH EACH ADDRESS BOOK RECORD AND RENDER by Individual Cell +++++

        foreach ($this->addressResult as $address) {
//            RENDER LAST AND FIRST NAME
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_LastName . $row, htmlspecialchars($address['name1']));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_FirstName . $row, htmlspecialchars($address['name2']));


//            RENDER ADDRESS
            $streetAddress = $address['street'];
            if (!is_null($address['pobox']) && (string)trim($address['pobox']) !== '') {
                $streetAddress .= ', ' . $address['pobox'];
            }
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Address . $row, htmlspecialchars($streetAddress));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_City . $row, $address['place']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_State . $row, $address['state']);
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Zip . $row, $address['zipcode']);

//            RENDER PHONES AND EMAILS
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Phones . $row, $this->getPhoneNumbers($address));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Emails . $row, $this->getEmailAddresses($address));

//            RENDER ADDRESS GROUPS and INSTRUMENTS
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Groups . $row, $this->getAddressGroups($address));
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Instruments . $row, $this->getInstruments($address));

//            RENDER NOTES
            $spreadsheet->getActiveSheet()
                ->setCellValue($this->col_Notes . $row, $address['notes']);

            $row++;
        }

//        ++++++ FORMAT SPREADSHEET ++++++
        $this->getFormatting($upperLeftCell, $row, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    protected function getPhoneNumbers($address)
    {
        $phoneArray = [];
        $phoneNumberGrid = $address['saddress_numbers'];
        // sort by phone number 'number order'
        usort(
            $phoneNumberGrid,
            function ($a, $b) {
                if ($a->number_order == $b->number_order) {
                    return 0;
                }
                return ($a->number_order < $b->number_order) ? -1 : 1;
            }
        );
        foreach ($phoneNumberGrid as $phone) {
            $phoneNumber = '';
            if ($phone['snumbertype']['l_mobile'] == 1 || $phone['snumbertype']['l_phone'] == 1) {
                $phoneNumber .= $phone['snumbertype']['name'] . ': ';
                $phoneNumber .= $phone['number_'] . ' ';
                if (!is_null($phone['text']) && (string)trim($phone['text']) !== '') {
                    $phoneNumber .= ' - ' . htmlspecialchars($phone['text']);
                }
            }
            $phoneArray[] = $phoneNumber;
        }

        return implode("\n", array_filter($phoneArray));
    }

    protected function getEmailAddresses($address)
    {
        $emailArray = [];
        $phoneNumberGrid = $address['saddress_numbers'];
        // sort by phone number 'number order'
        usort(
            $phoneNumberGrid,
            function ($a, $b) {
                if ($a->number_order == $b->number_order) {
                    return 0;
                }
                return ($a->number_order < $b->number_order) ? -1 : 1;
            }
        );
        foreach ($phoneNumberGrid as $phone) {
            $phoneNumber = '';
            if (strpos($phone['number_'], '@') === true || $phone['snumbertype']['l_email'] == 1) {
                $phoneNumber .= $phone['number_'] . ' ';
                if (!is_null($phone['text']) && (string)trim($phone['text']) !== '') {
                    $phoneNumber .= ' - ' . htmlspecialchars($phone['text']);
                }
            }


            $emailArray[] = $phoneNumber;
        }

        return implode("\n", array_filter($emailArray));
    }

    protected function getAddressGroups($addressEntry)
    {
        // define address group array
        $addressGroupGrid = $addressEntry['saddress_addressgroups'];
        // sort by l_main descending (main group first)
        usort(
            $addressGroupGrid,
            function ($a, $b) {
                if ($b->l_main == $a->l_main) {
                    return 0;
                }
                return ($b->l_main < $a->l_main) ? -1 : 1;
            }
        );
        // assigned address groups are put into an array so they can be output in a single line
        $tempGroupGrid = [];
        foreach ($addressGroupGrid as $addressGroup) {
            $groupName = $addressGroup['saddressgroup']['name'];
            $tempGroupGrid[] = $groupName;
        }
        return implode('; ', $tempGroupGrid);
    }

    protected function getInstruments($addressEntry)
    {
        usort(
            $addressEntry['saddress_instruments'],
            function ($a, $b) {
                if ($b->l_main == $a->l_main) {
                    return 0;
                }
                return ($b->l_main < $a->l_main) ? -1 : 1;
            }
        );

        $tempInstrumentGrid = [];
        foreach ($addressEntry['saddress_instruments'] as $subInstrument) {
            $instrumentName = $subInstrument['sinstrinstrument']['name'];
            $instrumentName .= $subInstrument['notes'] ? ' - ' . htmlspecialchars($subInstrument['notes']) : '';
            $tempInstrumentGrid[] = trim($instrumentName);
        }
        return implode('; ', array_filter($tempInstrumentGrid));
    }

//    ++++ HEADERS AND FORMATS ++++
    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['col_LastName'] ?? __('saddresses.name1');
        $columnB = $repFileText['col_FirstName'] ?? __('saddresses.name2');
        $columnC = $repFileText['col_Address'] ?? __('saddresses.street');
        $columnD = $repFileText['col_City'] ?? __('saddresses.place');
        $columnE = $repFileText['col_State'] ?? __('saddresses.state');
        $columnF = $repFileText['col_Zip'] ?? __('saddresses.zipcode');
        $columnG = $repFileText['col_Phones'] ?? __('snumbertypes.l_phone');
        $columnH = $repFileText['col_Emails'] ?? __('snumbertypes.l_email');
        $columnI = $repFileText['col_Groups'] ?? __('saddress_addressgroups.addressgroup_id');
        $columnJ = $repFileText['col_Instruments'] ?? __('saddress_instruments.instrument_id');
        $columnK = $repFileText['col_Notes'] ?? __('saddresses.notes');

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK
        );
    }

    protected function getFormatting($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row - 1;
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;

//        BOLD client name in Cell A1
        $styleForClientName = [
            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'size' => 12
            )
        ];
        $spreadsheet->getActiveSheet()->getStyle('A1')->applyFromArray($styleForClientName);

//        BOLD Header Row and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':K' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//        +++++ SET FONT ++++++
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $this->col_Notes . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);


//        +++++ SET COLUMN WIDTH +++++

//        WIDER COLUMNS
        $widerColumns = array($this->col_LastName, $this->col_Address, $this->col_Phones, $this->col_Emails);
        foreach ($widerColumns as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setWidth(25);
        }

//        MEDIUM COLUMNS
        $mediumColumns = array($this->col_FirstName, $this->col_City, $this->col_Groups, $this->col_Instruments);
        foreach ($mediumColumns as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setWidth(15);
        }

//        State, Zip and Notes set individually
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_State)
            ->setWidth(5);
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Zip)
            ->setWidth(12);
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Zip . self::DATA_ROW . ':' . $this->col_Zip . $row
        )->getAlignment()->setHorizontal(
            'left'
        );
        $spreadsheet->getActiveSheet()->getColumnDimension($this->col_Notes)
            ->setWidth(55);

//        Set TEXT WRAP on phone and email so line returns will appear
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Phones . self::DATA_ROW . ':' . $this->col_Emails . $row
        )->getAlignment()->setWrapText(true);

//       +++++ Freeze Panes +++++ after first Name
        $spreadsheet->getActiveSheet()->freezePane('C' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => 'D6D9C4') // Light Tan
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => '961616'), // Dark Red
//              'italic' => true,
                'size' => $defaultFontSize
            ),

//            'alignment' => [
//                'setWrapText' => true
//            ],

        );
    }
}
