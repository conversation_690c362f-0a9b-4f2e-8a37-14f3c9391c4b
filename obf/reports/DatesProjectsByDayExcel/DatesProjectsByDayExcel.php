<?php

namespace Customer\obf\reports\DatesProjectsByDayExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DaysQueryHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders a schedule in the style of a Gantt chart. Each Project as a Row
 *   and each Day in a column
 */

class DatesProjectsByDayExcel extends Report
{

//    Header Row is for title, text.
// DAY ROWS are typically 3: weekday/date/holiday
// DATA typically starts directly after the last Day Row
    const CLIENT_NAME_CELL = 'A1';
    const REPORT_TITILE_CELL = 'A2';
    const HEADER_ROW = 1;
    const DAY_ROW = 4;
    const DATA_ROW = 7;

//    Change these if inserting info between the Project column
//   and the first Day (i.e. project type)
    const PROJECT_COL = 'A';
    const DAY_COL = 'B';

//    Change these to the preferred color for the client
    const TOP_BORDER_COLOR =  'a29068';  // OBF TAN. Original = '58101b';  // OPAS Next Darkest Red
    const BORDER_COLOR_AGRB = 'FFA29068';  // OBF TAN
    const SATURDAY_SHADE = 'e7e1d4'; //OBF Light tan. original = 'eee7e8';
    const SUNDAY_SHADE = 'e1d9c7';  // OBF Light tan. original = 'ddcfd1';
    /**
     * Helper class for selected date records
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for calendar days / columns
     */
    private $dayQueryHelper;


    public function initialize()
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dayQueryHelper = new DaysQueryHelper();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withPersons()
            ->withStatus()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so print properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

        $row = self::DATA_ROW;
        $firstDayOfWeek = Configure::read('Calendar.Weeks.FirstDayOfWeek');
//        asssume region/paper size for date format and time format
        if (Configure::read('opasReports.paperFormat') == 'Letter') {
            $dateFormat = 'M d';
            $timeFormat = 'g:i';
        } else {
            $dateFormat = 'd M';
            $timeFormat = 'H:i';
        }

//        FIND NUMBER OF DAYS - this is the NUMBER OF COLUMNS
        $calendarDays = $this->getCalendarDays($this->datesResult);
        $numberOfDays = sizeof($calendarDays);
//        FIND NUMBER OF PROJECTS - this is the NUMBER OF DATA ROWS
        $projects = $this->getProjects($this->datesResult);
        $numberOfProject = sizeof($projects);

//        RENDER TOP OF REPORT - Client Name and Title
        $spreadsheet->getActiveSheet()->setCellValue(self::CLIENT_NAME_CELL, Configure::read('CustomerSettings.name'));
        $spreadsheet->getActiveSheet()->setCellValue(self::REPORT_TITILE_CELL, $repFileText['report_title']);


//        RENDER ROWS with Day Information
//        Weekday |  Date |  Holiday
        foreach ($calendarDays as $day) {
            if ($day['weekday'] == $firstDayOfWeek) {
                $week = $repFileText['week_prefix'] . ' ' . $day['pweek'];
            } else {
                $week = '';
            }
            $holiday = implode('/', array_filter(array($week, $day['sholyday']['name'])));
//            Mark Cells that are on Weekend Days so those columns can be shaded
            if ($day['date_']->format('w') == 0) {
                $shading = 'SUN';
            } elseif ($day['date_']->format('w') == 6) {
                $shading = 'SAT';
            } else {
                $shading = '';
            }

            $shadingArray[] = $shading;
            $weekDayArray[] = $day['date_']->format('l');
            $dateArray[] = $day['date_']->format($dateFormat);
            $holidayArray[] = $holiday;
        }
//        shading array goes above the first day row and puts a TEMPORARY Mark there.
//        mark is removed when shading applied
        $spreadsheet->getActiveSheet()
            ->fromArray($shadingArray, null, self::DAY_COL . (self::DAY_ROW - 1));
        $spreadsheet->getActiveSheet()
            ->fromArray($weekDayArray, null, self::DAY_COL . self::DAY_ROW);
        $spreadsheet->getActiveSheet()
            ->fromArray($dateArray, null, self::DAY_COL . (self::DAY_ROW + 1));
        $spreadsheet->getActiveSheet()
            ->fromArray($holidayArray, null, self::DAY_COL . (self::DAY_ROW + 2));

//  +++++ RENDER PROJECTS / EVENTS +++++
//        Loop through projects and for each, put the events on the proper day.
//        This section is filled cell-by-cell to allow for FormattedText

        foreach ($projects as $project) {
//      RENDER NAME in first column
            $spreadsheet->getActiveSheet()
                ->setCellValueFormatted(self::PROJECT_COL . $row, $project['name']);

//            LOOP through events. if of the same project, put into proper calendar day cell
//            Each individual activity entry is an imploded array so we can more easily manage customizations
//            Each Cell Output is an imploded array of activity arrays to allow for many events on one day
            $column = self::DAY_COL;
            foreach ($calendarDays as $day) {
                $cellOutputArray = [];
                foreach ($this->datesResult as $event) {
                    if ($event['date_'] == $day['date_'] && $event['project_id'] === $project['id']) {
//                       Define the elements of the activity entry.
                        if (!is_null($event['start_']) && (string)trim($event['start_']) !== '') {
                            $eventTime = $event['start_']->format($timeFormat);
                        } else {
                            $eventTime = '';
                        }
                        if (!is_null($event['end_']) && (string)trim(
                                $event['end_']
                            ) !== '' && $event['seventtype']['l_performance'] == 0) {
                            $eventTime .= '-' . $event['end_']->format($timeFormat);
                        }
//                        Cannot easily mix font styles inside a cell (i.e. bold), so performances in ALL CAPS
                        if ($event['seventtype']['l_performance'] == 1) {
                            $activity = strtoupper($event['seventtype']['name']);
                        } else {
                            $activity = $event['seventtype']['name'];
                        }
                        if ($event['location_id'] > 0) {
                            if (!is_null($event['locationaddress']['code']) && trim(
                                    $event['locationaddress']['code']
                                ) !== '') {
                                $venue = $event['locationaddress']['code'];
                            } else {
                                $venue = $event['locationaddress']['name1'];
                            }
                        }

//                        Assemble the items that constitute an activity; customize this for different clients
                        $eventOutputArray = array(
                            $eventTime,
                            $activity . ($venue ? ' [' . $venue . ']' : ''),
                            $event['text'],
                            $event['programtitle']
                        );
                        $eventOutput = implode("\n", array_filter($eventOutputArray));
                        $cellOutputArray[] = $eventOutput;
                    }
                }
//                Render all events on this calendar day
                $spreadsheet->getActiveSheet()
                    ->setCellValue(
                        $column . $row,
                        implode("\n" . $repFileText['event_divider'] . "\n", $cellOutputArray)
                    );

                $column++;
            }
            $row++;
        }


        //        ++++++ FORMAT SPREADSHEET ++++++
        $this->getFormatting($row, $column, $numberOfDays, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    private function getCalendarDays($datesResult): array
    {
        $calendarDays = [];
        $firstDay = reset($datesResult);
        $lastDay = end($datesResult);

//         use adays table to fetch holidays
        $allCalendarDays = $this->dayQueryHelper->getDaysForDateRange($firstDay['date_'], $lastDay['date_'])->getQuery(
        )->toArray();
        //   reduce to Level One days
        foreach ($allCalendarDays as $day) {
            if ($day['planninglevel'] == 1) {
                $calendarDays[] = $day;
            }
        }

        return $calendarDays;
    }

    private function getProjects($datesResult): array
    {
//  Create array of unique projects; then sort by criteria client wants.
//        Default sort is project order, then name
        $projectArray = [];
        foreach ($datesResult as $dateResult) {
            $projectArray[] = array(
                'id' => $dateResult['project_id'],
                'order' => $datesResult['sproject']['project_order'],
                'name' => $dateResult['sproject']['name']
            );
        }
        $projectArray = array_unique($projectArray, SORT_REGULAR);

        /**
         * @see https://stackoverflow.com/a/2699159
         */
        usort(
            $projectArray,
            function ($a, $b) {
                return $a['order'] . $a['name'] <=> $b['order'] . $b['name'];
            }
        );

        return $projectArray;
    }

    private function getFormatting($row, $finalColumn, $numberOfDays, $spreadsheet)
    {
        $finalRow = $row - 1;
//        because the formatting stops at column !== last column, we need to move the 'final column'
//        one column to the right to include the actual last column with data in it
        $finalColumn = ++$finalColumn;
        $defaultFont = 'Calibri';
        $defaultFontSize = 9;
        $dayHeaderRange = self::DAY_COL . self::DAY_ROW . ':' . $finalColumn . (self::DATA_ROW - 2);
        $holidayRange = self::DAY_COL . (self::DATA_ROW - 1) . ':' . $finalColumn . self::DATA_ROW;
        $dataRange = self::DAY_COL . self::DATA_ROW . ':' . $finalColumn . $finalRow;
        $topBorderRange = self::PROJECT_COL . (self::DATA_ROW - 1) . ':' . $finalColumn . (self::DATA_ROW - 1);

//  FORMAT DATA COLUMN WIDTHS (need to iterate over each column as php spreadsheet won't accept a range for col dimension)
        for ($col = self::DAY_COL; $col != $finalColumn; $col++) {
            $spreadsheet->getActiveSheet()->getColumnDimension($col)->setWidth(20);
        }

//        FORMAT TITLE CELLS
        $spreadsheet->getActiveSheet()
            ->getStyle(self::CLIENT_NAME_CELL)
            ->getFont()
            ->setName($defaultFont)
            ->setSize(14)
            ->setBold(true)
            ->getColor()->setRGB(self::TOP_BORDER_COLOR);
        $spreadsheet->getActiveSheet()
            ->getStyle(self::REPORT_TITILE_CELL)
            ->getFont()
            ->setName($defaultFont)
            ->setSize(12)
            ->setBold(true);

//        FORMAT FONTS in DAY HEADER ROWS - Do Not BOLD the holiday/week numbers
        $spreadsheet->getActiveSheet()
            ->getStyle($dayHeaderRange)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_TOP)
            ->setWrapText(true);
        $spreadsheet->getActiveSheet()
            ->getStyle($dayHeaderRange)
            ->getFont()
            ->setName($defaultFont)
            ->setSize(11)
            ->setBold(true);

        $spreadsheet->getActiveSheet()
            ->getStyle($holidayRange)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_TOP)
            ->setWrapText(true);
        $spreadsheet->getActiveSheet()
            ->getStyle($holidayRange)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

//        SET BORDER along bottom of day name header (right under holidays)
        $spreadsheet->getActiveSheet()->getStyle($topBorderRange)
            ->applyFromArray($this->getTopBorderStyle());


//       FORMAT PROJECT COLUMN(s)
        $projectRange = self::PROJECT_COL . self::DAY_ROW . ':' . self::PROJECT_COL . $finalRow;
        $spreadsheet->getActiveSheet()
            ->getColumnDimension(self::PROJECT_COL)
            ->setWidth(30);
        $spreadsheet->getActiveSheet()
            ->getStyle($projectRange)
            ->getFont()
            ->setName($defaultFont)
            ->setSize(11)
            ->setBold(true);

//       FORMAT ACTIVITY CELL Contents
        $activityRange = self::DAY_COL . self::DATA_ROW . ':' . $finalColumn . $finalRow;
        $spreadsheet->getActiveSheet()
            ->getStyle($activityRange)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_LEFT)
            ->setVertical(Alignment::VERTICAL_TOP)
            ->setWrapText(true);
        $spreadsheet->getActiveSheet()
            ->getStyle($activityRange)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

//        SHADE WEEKEND COLUMNS and DELETE MARKER
        for ($col = self::DAY_COL; $col != $finalColumn; $col++) {
//            SATURDAY column Shade
            if ($spreadsheet->getActiveSheet()->getCell($col . (self::DAY_ROW - 1))->getValue() === 'SAT') {
                $spreadsheet->getActiveSheet()->getStyle($col . self::DAY_ROW . ':' . $col . $finalRow)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setARGB(self::SATURDAY_SHADE);
                $spreadsheet->getActiveSheet()->setCellValue($col . (self::DAY_ROW - 1), '');
            }
            //           SUNDAY column Shade
            if ($spreadsheet->getActiveSheet()->getCell($col . (self::DAY_ROW - 1))->getValue() === 'SUN') {
                $spreadsheet->getActiveSheet()->getStyle($col . self::DAY_ROW . ':' . $col . $finalRow)->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()->setARGB(self::SUNDAY_SHADE);
                $spreadsheet->getActiveSheet()->setCellValue($col . (self::DAY_ROW - 1), '');
            }
        }

//        SET INTERIOR BORDERS
        $spreadsheet->getActiveSheet()->getStyle($dataRange)
            ->applyFromArray($this->getBorderStyle());

        //       +++++ Freeze Panes +++++
        $spreadsheet->getActiveSheet()->freezePane(self::DAY_COL . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    protected function getBorderStyle()
    {
        return array(
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => array('argb' => self::BORDER_COLOR_AGRB)
                ]
            ]
        );
    }

    protected function getTopBorderStyle()
    {
        return array(
            'borders' => [

                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                    'color' => array('argb' => self::BORDER_COLOR_AGRB)
                ]
            ]
        );
    }
}
