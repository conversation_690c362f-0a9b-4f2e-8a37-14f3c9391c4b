<?php

namespace Customer\obf\reports\OnSpreadsheet;

use Customer\obf\reports\OnSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Calculation\Calculation;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Style;
//use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use ReflectionClass;

class OnSpreadsheet extends Spreadsheet
{

    /**
     * Create a new PhpSpreadsheet with one Worksheet.
     * @throws \ReflectionException
     */
    public function __construct()
    {

        parent::__construct();

        $reflection = new ReflectionClass($this);
        $spreadSheet = $reflection->getParentClass();
        $workSheetCollectionProperty = $spreadSheet->getProperty('workSheetCollection');
        $workSheetCollectionProperty->setAccessible(true);

        $workSheetCollectionValue = [];
        $workSheetCollectionValue[] = new Worksheet($this);

        $workSheetCollectionProperty->setValue($this, $workSheetCollectionValue);

    }

}
