<?php

namespace Customer\obf\reports\OnSpreadsheet\Worksheet\FormattedText;

use PhpOffice\PhpSpreadsheet\RichText\RichText;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\Exception\Exception;

/**
 * Class FormattedTextAbstract
 *
 * Abstract class for the single formatting classes.
 *
 * @package Customer\obf\reports\OnWord\Element\FormattedText
 */
abstract class FormattedTextAbstract
{

    public $start = '';
    public $end = '';

    /**
     * FormattedTextAbstract constructor.
     *
     * Just check if a start and a end value is set.
     *
     * @throws Exception
     */
    public function __construct()
    {
        if ($this->start === '' || $this->end === '') {
            throw new Exception('The properties start and end must be set in \\' . get_called_class());
        }
    }

    public function render($text, RichText $richText)
    {
        throw new Exception('No render function defined in \\' . get_called_class());
    }

}
