<?php

namespace Customer\obf\reports\MusicianDirName;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Fill;
use ReflectionException;

class MusicianDirNameStyles
{
    const HEADER_COLOR = '8A0808';  // DARK RED
    const ADDRESS_SHADING = 'F1F1F1';  // Light grey
    const PHONE_SHADING = 'eaeae1'; // Light tan

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->nameFont = clone($this->defaultFontBold);
        $this->nameFont->setSize(12);

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(14);

        $this->titleFont = clone($this->headerFont);
        $this->titleFont->setColor(self::HEADER_COLOR);

        $this->letterHeaderFont = clone($this->headerFont);
        $this->letterHeaderFont->setColor('FFFFFF');
    }

    /**
     * Define the styles for the report: Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0  // space between lines in twips
        ];

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->letterHeadingParagraph = array_merge(
            $this->defaultParagraphCenter,
            [
                'shading' => (array('fill' => '8A0808')),
                'alignment'=> 'center',
            ]
        );

        $this->addressParagraph = array_merge(
            $this->defaultParagraph,
            [
                'shading' => (array('fill' => self::ADDRESS_SHADING)),
            ]
        );

        $this->phoneParagraph = array_merge(
            $this->defaultParagraph,
            [
                'shading' => (array('fill' => self::PHONE_SHADING)),
            ]

        );

        $this->footerParagraph = [
            'alignment' => 'left',  // left; Right; Center; other options available
            'spaceBefore' => 100,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
            'borderTopSize' => 6,
            'borderColor' => self::PHONE_SHADING,
            'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
                new \PhpOffice\PhpWord\Style\Tab('center', 5040),
                new \PhpOffice\PhpWord\Style\Tab('right', 10400),
            ),
        ];
    }


}
