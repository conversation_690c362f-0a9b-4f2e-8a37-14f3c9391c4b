<?php

namespace Customer\obf\reports\MusicianDirName;

use Cake\Core\Configure;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Customer\obf\reports\utility\AddressQueryHelper;
use Customer\obf\reports\utility\NameFormatHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;


/* AUTHOR NOTES
 *  Report produces a 2-column directory of musicians, by name
 *  CONTENT NOTES:  expand to include: title, classification grid (functions), middle name, other
 *    personal information as requested by clients
 */

class MusicianDirName extends ReportWord
{

    /**
     * Prepared array for the rendering
     * @var addressQueryHelper
     */
    private $addressQueryHelper;
    private $addressesResult;

    /**
     * Name formatter
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function initialize()
    {
        parent::initialize();
        $this->addressQueryHelper = new AddressQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->reportStyles = new MusicianDirNameStyles();
    }


    public function collect(array $where = []): ReportsInterface
    {
        $addressQuery = $this->addressQueryHelper
            ->getAddresses($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->addressesResult = $addressQuery->toArray();

        return $this;
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
the <text></text> section of the .rep file. This allows for headings and other report text to output in a
variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    public function renderReport()
    {
//        Fetch regional and client settings from settings.php
        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');
        $repFileText = $this->getRepFileParams();

//        +++++ STYLES AND FORMATS +++++
//      default font and paragraph styles created in RehearsalScheduleStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $topSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => "portrait",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//      Render Report Title and Footer
        $topSection->addText(
            $repFileText['report_title'],
            $this->reportStyles->titleFont,
            $this->reportStyles->defaultParagraphCenter
        );
        $topSection->addTextBreak(1);

        $footer = $topSection->addFooter();
        $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $defaultFont,
            $this->reportStyles->footerParagraph,
        );


        // Switch to a TWO-COLUMN Layout
        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => "portrait",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
                'colsNum' => (2),
                // "colsSpace" => () not known if this is twips or ?
            ]
        );

        // sort the array of names by Last Name, First Name
        usort(
            $this->addressesResult,
            function ($a, $b) {
                if ($cmp = strnatcasecmp($a['name1'], $b['name1'])) {
                    return $cmp;
                }
                return strnatcasecmp($a['name2'], $b['name2']);
            }
        );


        // Alphabet loop
        foreach (range('A', 'Z') as $letter) {
            $i = 0;
            foreach ($this->addressesResult as $addressEntry) {
                if (substr(mb_strtoupper($addressEntry['name1']), 0, 1) === $letter) {
//                    print Letter Heading at first musician
                    if ($i === 0) {
                        $pageSection->addText(
                            $letter,
                            $this->reportStyles->letterHeaderFont,
                            $this->reportStyles->letterHeadingParagraph
                        );
                    }

//                        MUSICIAN NAME
                    $musicianName = $this->nameFormatHelper->getEntityName(
                        $addressEntry['name1'],
                        $addressEntry['name2'],
                        0
                    );
                    $pageSection->addFormattedText($musicianName, $this->reportStyles->nameFont, $defaultParagraph);

//                   ADDRESS GROUP(S)
                    $addressGroups = $this->getAddressGroups($addressEntry);
                    if (!is_null($addressGroups) && (string)trim($addressGroups) !== '') {
                        $pageSection->addText($addressGroups, $defaultFont, $defaultParagraph);
                    }

//                   INSTRUMENT(S)
                    $instruments = $this->getInstruments($addressEntry);
                    if (!is_null($instruments && (string)trim($instruments) !== '')) {
                        $pageSection->addText($instruments, $this->reportStyles->defaultFontBold, $defaultParagraph);
                    }

//                        ADDRESS
                    $addressOutput = [];
                    $addressOutput[] = htmlspecialchars($addressEntry['street']);
                    $addressOutput[] = $addressEntry['pobox'];
                    $cityState = $addressEntry['place'];
                    $cityState .= $addressEntry['place'] && $addressEntry['state'] ? ', ' : ' ';
                    $cityState .= $addressEntry['state'] . ' ' . $addressEntry['zipcode'];
                    $addressOutput[] = $cityState;

                    $pageSection->addText(
                        implode('</w:t><w:br/><w:t>', array_filter($addressOutput)),
                        $defaultFont,
                        $this->reportStyles->addressParagraph
                    );

//                  PHONE NUMBERS
                    $phoneNumbers = $this->getPhoneNumbers($addressEntry);
                    if (!is_null($phoneNumbers) && (string)trim($phoneNumbers) !== '') {
                        $pageSection->addText($phoneNumbers, $defaultFont, $this->reportStyles->phoneParagraph);
                    }

                    $pageSection->addTextBreak(1);

                    $i++;
                }
            }
        }
    }

    protected function getAddressGroups($addressEntry)
    {
        // define address group array
        $addressGroupGrid = $addressEntry['saddress_addressgroups'];
        // sort by l_main descending (main group first)
        usort(
            $addressGroupGrid,
            function ($a, $b) {
                if ($b->l_main == $a->l_main) {
                    return 0;
                }
                return ($b->l_main < $a->l_main) ? -1 : 1;
            }
        );
        // assigned address groups are put into an array so they can be output in a single line
        $tempGroupGrid = [];
        foreach ($addressGroupGrid as $addressGroup) {
            $groupName = $addressGroup['saddressgroup']['name'];
            $tempGroupGrid[] = $groupName;
        }
        return implode('; ', $tempGroupGrid);
    }

    protected function getInstruments($addressEntry)
    {
        usort(
            $addressEntry['saddress_instruments'],
            function ($a, $b) {
                if ($b->l_main == $a->l_main) {
                    return 0;
                }
                return ($b->l_main < $a->l_main) ? -1 : 1;
            }
        );

        $tempInstrumentGrid = [];
        foreach ($addressEntry['saddress_instruments'] as $subInstrument) {
            $instrumentName = $subInstrument['sinstrinstrument']['name'];
            $instrumentName .= $subInstrument['notes'] ? ' - ' . htmlspecialchars($subInstrument['notes']) : '';
            $tempInstrumentGrid[] = trim($instrumentName);
        }
        return implode('; ', array_filter($tempInstrumentGrid));
    }

    protected function getPhoneNumbers($addressEntry)
    {
        $phoneArray = [];
        $phoneNumberGrid = $addressEntry['saddress_numbers'];
        // sort by phone number 'number order'
        usort(
            $phoneNumberGrid,
            function ($a, $b) {
                if ($a->number_order == $b->number_order) {
                    return 0;
                }
                return ($a->number_order < $b->number_order) ? -1 : 1;
            }
        );
        foreach ($phoneNumberGrid as $phone) {
            $phoneNumber = '';
            if (strpos($phone['number_'], '@') === false) {
                $phoneNumber .= trim($phone['snumbertype']['name']) . ': ';
            }
            $phoneNumber .= $phone['number_'] . ' ';
            if (!is_null($phone['text']) && (string)trim($phone['text']) !== '') {
                $phoneNumber .= ' - ' . htmlspecialchars($phone['text']);
            }


            $phoneArray[] = $phoneNumber;
        }

        return implode('</w:t><w:br/><w:t>', array_filter($phoneArray));
    }
}

