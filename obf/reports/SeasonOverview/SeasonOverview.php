<?php

use App\Model\Entity\Sproject;
use App\Model\Entity\Adate; // required for date-specific info like the Program Title
use Customer\obf\PHPWordHelper;
use Customer\obf\reports\ReportWordFas;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph; // required to create ad-hoc paragraph styles
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter; // required to put margins in Inches or cm and convert to twip
use PhpOffice\PhpWord\PhpWord;

/* AUTHOR NOTES
*  Report produces a single 6-columh table layout of season programs; alternate to Program Schedule reports
*  FORMAT NOTES:    $this->reportParagraphDefault,  defined in ReportWordFas. Standard left-
*      aligned paragraph with no extra spacing
*      Class //use PhpOffice\PhpWord\Style\Paper;  not used
*  CONTENT NOTES:  does not yet include date-work conductor or soloist notes or conductr-as-soloist
*/

class SeasonOverview extends ReportWordFas
{
    public function render($view = null, $layout = null): string
    {
        $allSeasons = [];
        foreach ($this->projects as $project) {
            $seasons = $this->reportHelper->getSeasonsFromProjectRows($project);

            /* TWG - this gets all distinct/unique seasons
            */
            $allSeasons = array_unique(array_merge($seasons, $allSeasons));
        }
        // this report is typically run for a single season, but if user selects more, sort the seasons by datestart
        usort($allSeasons, function ($a, $b) {
            if ($a->datestart == $b->datestart) {
                return 0;
            }
            return ($a->datestart < $b->datestart) ? -1 : 1;
        });

        $this->renderToWord($this->projects, $allSeasons, $this->wordHelper);

        $fileName = $this->_createFileName() . $this->getFileExtension();  // Create the file and return the filename

        ob_end_clean();  // Clean (erase) the output buffer and turn off output buffering

        $this->wordHelper->write($fileName);

        return $fileName;
    }


    /* TWG
    *  Set the DOCUMENT PROPERTIES in the first addSection created by the report file. While almost
    *   all reports will have the same properites, this gives the option to alter the paper size,
    *   font, orientation, borders or other document-level properties. Deviations from these must
    *   be done on individual 'addSection' found after this one
    */
    public function renderToWord($allProjects, array $allSeasons, PHPWordHelper $wordHelper)
    {
        foreach ($allSeasons as $season) {
            // get all projects by season
            $projectsForSeason = [];
            foreach ($allProjects as $project) {
                if ($project['adates'][0]->season_id === $season->id) {
                    $projectsForSeason[] = $project;
                }
            }
            $section = $wordHelper->addSection([  // adding a section REQUIRES $phpWord
                'paperSize' => "Letter" ,   // "Letter" , "A4" , others in PHPWord folder "Paper.php"
  //            "orientation" => "Landscape" //  portrait is default - no need to indicate unless switching to landsacpe
                'marginTop' => Converter::inchToTwip(.5),
                'marginBottom' => Converter::inchToTwip(.75),
                'marginLeft' => Converter::inchToTwip(.75),
                'marginRight' => Converter::inchToTwip(.5),
                'breakType' => 'continuous', // prevents page break between sections as this report uses multiple sections
               ]);

            $wordHelper->addSectionText(
                $section,
                'Season Programs' . '<br/>' ,
                (new Font())->setSize(16)->setName('Calibri'),
                $this->reportParagraphCenter
            );

  // OUTPUT name of season
            $wordHelper->addSectionText(
                $section,
                $season->name ,
                (new Font())->setSize(12)->setBold(true)->setName('Calibri'), // over-rides reportFontDefault
                (new Paragraph())->setborderBottomColor('777777')->setborderBottomSize(12)
            );

// OUTPUT HEADER ROW - top heading table in different style with fixed text

            $table = $section->addTable([
            'cellMarginRight' => 114,  // interior margin of 0.08 in
            'cellMarginLeft' => 114,  // interior margin of 0.08 in
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word

            'borderColor' => '#696b64' , // darker grey
            'borderSize' => 8 ,
            ]);

            $row = $wordHelper->addRow($table, PHPWordHelper::ROW_COLS_SIX);
            $wordHelper->addTableText(
                $row,  0,  '<b>Performance</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,  1,  '<b>Project</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,  2,  '<b>Program</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,  3,  '<b>Conductor</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,  4,  '<b>Soloist</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,  5,  '<b>Venue</b>',  $this->reportFontDefault,  $this->reportParagraphDefault
            );
            // for each season, loop through the renderProjects functiuon below
           $this->renderProjects($section, $projectsForSeason, $wordHelper);
        }
    } // END of Season Loop


    public function renderProjects(Section $section, $projects, PHPWordHelper $wordHelper)
    {

        foreach ($projects as $projectInfo) {
            /** @var Sproject $project */
            $project = $projectInfo['sproject'];
            $projectHeader = '<b>' . $project->name . '</b>';

  //   Normally we call to the table functions in PHPWordHelper like this: $table = $wordHelper->addTableFullBorder($section);
  //     but thius creates an ad-hoc custom table for this report
            $table = $section->addTable([
            'cellMarginRight' => 114,  // interior margin of 0.08 in
            'cellMarginLeft' => 114,  // interior margin of 0.08 in
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word

            'borderColor' => '#c2c0ba' , // light grey
            'borderSize' => 5 ,
          ]);

            $row = $wordHelper->addRow($table, PHPWordHelper::ROW_COLS_SIX);
            // OUTPUT Project Name
            $wordHelper->addTableText(
                $row,
                1,
                $projectHeader,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );

            $dates = $this->reportHelper->getDatesForProject($projectInfo['adates']);
            $dateRangeString = $this->reportHelper->formatDateRange($dates);

            // OUTPUT date range
            $wordHelper->addTableText(
                $row,
                0,
                $dateRangeString,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );

            $programTitle = [];
            // loop through each date in the Project and return each program title into an array. format the title in italics
            //    and test to make sure no empty/deleted titles are included (no blank lines)
            foreach ($projectInfo['adates'] as $dateRecord) {  // $projectInfo declared at the top of this function
                  $programTitle[] =  $dateRecord->programtitle ? '<i>'. $dateRecord->programtitle . '</i>' : '' ;
            }
            // Program title output; each unique program title (ususally just one) with break between & after
            $programTitleOutput = implode("<br/>",  array_unique($programTitle)) ? implode("<br/>",  array_unique( $programTitle )) . "<br/>" : '' ;

            $venue = [];
            // loop through each date in the Project and return each venue into an array
            foreach ($projectInfo['adates'] as $dateRecord) {  // $projectInfo declared at the top of this function
                  $venue[] =  $dateRecord->locationaddress->code ?? $dateRecord->locationaddress->name1;
            }
            // OUTPUT venue
            // the string that outputs is each unique venue separated by a line break
            $venueName = implode("<br/>", array_unique($venue));

            $wordHelper->addTableText(
                $row,
                5,
                $venueName,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );

/* the conductor, soloist and programs are fetched and formatted in a separate function: getWorksConductorSoloistInfo below (line 197)
*   (this public function is used in a variety of program-based reports)
*      because the rendering of the table has to be entirely done within the confines of a single function [renderProjects in this case],
*         the values from a separate function are gathered using the statement below.
*  The list() function is used to assign values to a list of variables in one operation. since the function getWorksConductorSoloistInfo
*    returns an array, the elements of that array can be represented by the 3 entries within the list()
*  See line 114 for $projectInfo
*/
            list($workString, $conductorName, $soloistString) = $this->getWorksConductorSoloistInfo($projectInfo);

            $wordHelper->addTableText(
                $row,
                2,
                $programTitleOutput . $workString,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );

            $wordHelper->addTableText(
                $row,
                3,
                str_replace(', Conductor' , '' , $conductorName),
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );
            $wordHelper->addTableText(
                $row,
                4,
                $soloistString,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );
        }  // End of ($projects as $projectInfo)
    } // End of renderProjects Loop



    /**
     * @param $projectInfo
     * @return array
     */
    protected function getWorksConductorSoloistInfo($projectInfo): array
    {
        // get more info
        $Adate = $this->reportHelper->getAdateInfoFromProjectRow($projectInfo); // allows fetching of data for a single date
        $works = $this->reportHelper->getWorksForAdateId($Adate->id);

        //  @var \App\Model\Entity\AdateWork $work //
        $workString = '';
        foreach ($works as $work) {
            if ($work->l_encore != 0 || $work->swork->l_intermission == 1) {
                continue;
            }

            $workString .= '<b>' . trim($work->swork->scomposer->lastname) . '</b>: ';
            $workString .= trim($work->title2) !== '' ? $work->title2 : $work->swork->title1;

            if ($work->arrangement) {
                $workString .= ' (' . $work->arrangement . ')';
            }

            if ($work->premiere_id) {
                $info = $this->reportHelper->getPremierInfo($work->premiere_id);
                $workString .= " <i>- " . $info->name . ' premiere</i>';
            }

            $workString .= "<br/>";
        }

        [
            'conductorName' =>  $conductorName,
            'soloists' => $soloists
        ] = $this->reportHelper->getConductorAndSoloistsForProjectRow($projectInfo);

        $soloistString = '';
        foreach ($soloists as $soloist) {
            $string = $soloist['data']->name2Name1;
            $ins = [];
            foreach ($soloist['instruments'] as $instrument) {
                $ins[$instrument->name] = $instrument->name;
            }
            $string .= ", " . mb_strtolower(implode(", ", array_keys($ins)));
            // OUTPUT soloist list
            $soloistString .= $string . '<br/>';
        }
/* since the elements of the array are not being output, they are returned so
*  they can be used in line 166
*/
        return array($workString, $conductorName, $soloistString);  // end of Soloist loop
    }
}
