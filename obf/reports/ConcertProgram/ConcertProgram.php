<?php

namespace Customer\obf\reports\ConcertProgram;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;
use Customer\obf\reports\utility\ComposerQueryHelper;
use Customer\obf\utility\Instrumentation\CustomerInstrumentation;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Standard Concert Program
 * Each project + program number composition
 * Conductor, date-work conductor, soloists, works and movements
 */

class ConcertProgram extends ReportWord
{
    const COL_1_WIDTH = 2.45;
    const COL_2_WIDTH = 3.85;
    const COL_3_WIDTH = 1.00;

    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for the compositions on each concert
     *
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        // Set report font, table and paragraph styles
        $this->reportStyles = new ConcertProgramStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Work records selected by user to workQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//    Typically the use case is to run this report for one project at a time. However, the user could run it for
//      more than one. this function breaks the selected dates down into each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concer Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the Header and program
    private function getAnchor(string $project): int
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult['id'];
            }
        }
    }


    private function renderReport()
    {
//        Set report formats based on client region and preferences
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        for Dates selected by User, loop through Season+Project+ProgramNumber, putting a page break between each
        $p = 0;
        foreach ($this->getProjects() as $project) {
            if ($p > 0) {
                $pageSection->addPageBreak();
            }

//            Top section of report prints all concert dates in the Season+Project+ProgramNumber set.
//            If all concerts take place in the same venue, venue name is below concert dates, otherwise after concert date
            foreach ($this->datesResult as $headerDate) {
                if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                    && $headerDate['seventtype']['l_performance'] == 1) {
                    $printConcertDate = $headerDate['date_']->format($topDateFormat)
                        . ' ' . __('at') . ' ' . $headerDate['start_']->format($topTimeFormat);

                    if ($headerDate['location_id'] > 0) {
                        $headerVenue = $this->getHeaderVenues($project);
//                   if $headerVenue is empty, then there is more than one concert venue so append venue to each concert date
                        if (strlen(trim($headerVenue)) == 0) {
                            $headerVenueName = $headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1'];
                            $printConcertDate .= ' - ' . trim(htmlspecialchars($headerVenueName));
                        }
                    } else {
                        $headerVenue = '';
                    }

//              render Header Dates
                    $pageSection->addText(
                        $printConcertDate,
                        $this->reportStyles->dateHeaderFont,
                        $this->reportStyles->defaultParagraphCenter
                    );
                }
            }
//              If all concerts take place in the same venue, print that venue under the final concert date
            if (strlen(trim($headerVenue)) != 0) {
                $pageSection->addText(
                    $headerVenue,
                    $this->reportStyles->dateHeaderFont,
                    $this->reportStyles->defaultParagraphCenter
                );
            }
            $pageSection->addTextBreak(1, $defaultFont, $this->reportStyles->defaultParagraphCenter);

//               +++ If the concert date is the Anchor Date for the Season+Project+ProgramNumber,
//            render all other information +++
            foreach ($this->datesResult as $concertDate) {
                if ($concertDate['id'] === $this->getAnchor($project)) {
//                    orchestra is a single-item array; if no orchestra, output program title
                    if ($concertDate['orchestra_id'] > 0) {
                        $orchestraName = trim(
                            $concertDate['orchestraaddress']['name2'] . ' ' . $concertDate['orchestraaddress']['name1']
                        );
                    } else {
                        $orchestraName = $concertDate['programtitle'];
                    }
                    $pageSection->addFormattedText(
                        $orchestraName ?? ' ',
                        $this->reportStyles->orchestraFont,
                        $this->reportStyles->defaultParagraphCenter
                    );

                    $projectName = mb_strtoupper($concertDate['sproject']['name']);
                    if (!is_null($concertDate['programno']) && (string)trim($concertDate['programno']) !== '') {
                        $projectName .= ' ';
                        $projectName .= $repFileText['programNo_Prefix'] ?? __('adates.programno');
                        $projectName .= ' ' . mb_strtoupper($concertDate['programno']);
                    }
                    $pageSection->addFormattedText(
                        $projectName,
                        $this->reportStyles->projectFont,
                        $this->reportStyles->defaultParagraphCenter
                    );
                    $pageSection->addTextBreak(1, $defaultFont, $this->reportStyles->defaultParagraphCenter);

//                   +++ RENDER HEADER PERSONNEL and CONCERT TITLE
                    $this->renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph);
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

                    if ($concertDate['orchestra_id'] > 0 && (string)trim($concertDate['programtitle']) !== '') {
                        $pageSection->addFormattedText(
                            $concertDate['programtitle'],
                            $this->reportStyles->dateHeaderFont,
                            $this->reportStyles->defaultParagraphCenter
                        );
                        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    } else {
                        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    }


//          ++++  RENDER  CONCERT PROGRAM ++++
                    $programTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($concertDate['id'])
                        ->withMovements()
                        ->withDateWorkConductor()
                        ->getQuery()
                        ->toArray();
                    $composer = 0;
                    foreach ($dateWorks as $dateWork) {
                        if ($dateWork['l_encore'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                            $programTable->addRow();
                            $leftColumnCell = $programTable->addCell(
                                Converter::inchToTwip(self::COL_1_WIDTH),
                                $this->reportStyles->leftColumnCell
                            );
                            $centerColumnCell = $programTable->addCell(
                                Converter::inchToTwip(self::COL_2_WIDTH),
                                $this->reportStyles->centerColumnCell
                            );
                            $rightColumnCell = $programTable->addCell(
                                Converter::inchToTwip(self::COL_3_WIDTH),
                                $this->reportStyles->defaultCell
                            );

//                            RENDER COMPOSER and DATES - suppress composer name if same composer as previous work
                            if ($dateWork['swork']['l_intermission'] == 0
                                && $dateWork['swork']['composer_id'] != $composer) {
                                $composerName = $this->composerQueryHelper->getComposerName(
                                    $dateWork['swork']['scomposer']['lastname'],
                                    $dateWork['swork']['scomposer']['firstname'],
                                    1
                                );
                                $composerName .= $dateWork['arrangement'] ? ' (' . $dateWork['arrangement'] . ')' : '';
                                $composerDates = $this->composerQueryHelper->getComposerDates(
                                    $dateWork['swork']['scomposer']['birthyear'],
                                    $dateWork['swork']['scomposer']['deathyear']
                                );
                            } else {
                                $composerName = '';
                                $composerDates = '';
                            }

                            $leftColumnCell->addFormattedText(
                                mb_strtoupper($composerName),
                                $this->reportStyles->defaultFontBold,
                                $this->reportStyles->defaultParagraphRight
                            );
                            if (!empty($composerDates)) {
                                $leftColumnCell->addText(
                                    $composerDates,
                                    $defaultFont,
                                    $this->reportStyles->defaultParagraphRight
                                );
                            }


//                       RENDER WORK TITLE -- first of print title or master title. Text run needed to
//                            render different font style for premiere
                            $workTitle = $centerColumnCell->addTextRun($defaultParagraph);
                            if (!empty(trim($dateWork['title2']))) {
                                $dateWorkTitle = $dateWork['title2'];
                            } else {
                                $dateWorkTitle = $dateWork['swork']['title1'];
                            }
                            $workTitle->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);
                            $premiere = $dateWork['Sworkpremieres']['name'] ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                                    'premiere'
                                ) : ' ';
                            $workTitle->addText($premiere, $this->reportStyles->defaultFontItalic, $defaultParagraph);

//                            Work Composition Year
                            $workDates = $this->dateWorkQueryHelper->getWorkDates(
                                $dateWork['swork']['compyear'],
                                $dateWork['swork']['compyear2'],
                                $dateWork['swork']['compyearstatus']
                            );
                            $rightColumnCell->addText(
                                $workDates,
                                $defaultFont,
                                $this->reportStyles->defaultParagraphRight
                            );

                            /*
                            *                  Loop through MOVEMENTS - movements are an array within the date-works variable
                            *                       Since the movement name is a memo field, assume any character can be used in addition
                            *                          to the italics markers; treat as work title 2
                            */
                            foreach ($dateWork['adatework_movements'] as $datework_movement) {
                                if (strlen(trim($datework_movement['name'])) > 0) {
                                    $centerColumnCell->addFormattedText(
                                        trim($datework_movement['name']),
                                        $defaultFont,
                                        $this->reportStyles->movementParagraph
                                    );
                                }
                            }


//                            +++ RENDER DATE-WORK CONDUCTORS and SOLOISTS +++
//                            Fetch soloists (array)
                            $soloists = $this->dateWorkQueryHelper->getSoloistsForDateWorkID($dateWork['id'])->getQuery(
                            )->toArray();

//                          Fetch date-work conductor as string
                            if ($dateWork['conductor_id'] > 0) {
                                $dateWorkConductor = $this->dateWorkQueryHelper->getDateWorkConductorAsString(
                                    $soloists,
                                    $dateWork['conductor_id'],
                                    0,
                                    0,
                                    0,
                                    ', ',
                                    ';'
                                );
                                $centerColumnCell->addText(
                                    $dateWorkConductor,
                                    $this->reportStyles->defaultFontBold,
                                    $this->reportStyles->defaultParagraphCenter
                                );
                            }

                            $dateWorkSoloists = $this->dateWorkQueryHelper->getDateWorkSoloistsAsString(
                                $soloists,
                                $dateWork['conductor_id'] ?? 0,
                                '</w:t><w:br/><w:t>',
                                0,
                                0,
                                ', '
                            );
                            $centerColumnCell->addText(
                                $dateWorkSoloists,
                                $this->reportStyles->defaultFontBold,
                                $this->reportStyles->defaultParagraphCenter
                            );


//                          RENDER EMPTY ROW between works
                            $programTable->addRow();
                            $programTable->addCell(
                                Converter::inchToTwip(self::COL_1_WIDTH),
                                $this->reportStyles->leftColumnCell
                            )
                                ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
                            $programTable->addCell(
                                Converter::inchToTwip(self::COL_2_WIDTH),
                                $this->reportStyles->centerColumnCell
                            )
                                ->addText('', $defaultFont, $this->reportStyles->defaultParagraphCenter);
                            $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH))
                                ->addText('', $defaultFont, $defaultParagraph);
                        }

                        $composer = $dateWork['swork']['composer_id'];
                    }
                }
            }
            $p++;
        }
    }

    public function getHeaderVenues($seasonProjectCombo)
    {
        $headerVenues = [];
        foreach ($this->datesResult as $venueDate) {
            if ($venueDate['season_id'] . ':' . $venueDate['project_id'] . ':' . $venueDate['programno'] == $seasonProjectCombo
                && $venueDate['seventtype']['l_performance'] == 1) {
//            entire address array is fetched in case the venue city, address or other info is needed
                $headerVenues[] = trim(
                    htmlspecialchars(
                        $venueDate['locationaddress']['name2']
                        . ' ' . $venueDate['locationaddress']['name1']
                    )
                );
            }
        }
        $venueCount = sizeof(array_unique($headerVenues));
//        if there is only one venue, return that one name as a string and it will be displayed UNDER the concert dates
        if ($venueCount == 1) {
            return implode('', array_unique($headerVenues));
        }
    }

    public function renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph)
    {
        $mainConductor = $this->datePerformerHelper->getConductor(
            $concertDate['id'],
            $concertDate['conductor_id'],
            0,
            0,
            ', ',
            '; '
        );
        if (!empty($mainConductor)) {
            $pageSection->addText($mainConductor, $this->reportStyles->defaultFontBold, $defaultParagraph);
        }

//      function returns date-work conductors in a single text string
        $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
            $concertDate['id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            0,
            ', ',
            '; '
        );
        if (!empty($dateWorkConductors)) {
            $pageSection->addText(
                $dateWorkConductors,
                $this->reportStyles->defaultFontBold,
                $defaultParagraph
            );
        }

//                    function returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
        $mainSoloist = $this->datePerformerHelper->getSoloists(
            $concertDate['id'],
            $concertDate['conductor_id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            ', '
        );
        if (!empty($mainSoloist)) {
            $pageSection->addText($mainSoloist, $defaultFont, $defaultParagraph);
        }
    }
}
