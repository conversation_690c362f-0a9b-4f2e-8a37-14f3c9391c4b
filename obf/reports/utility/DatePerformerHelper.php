<?php

namespace Customer\obf\reports\utility;

class DatePerformerHelper
{
    /**
     * Format the Main Conductor, Date-Work Conductor and Soloists for any given Date Record.
     * Name format, Instrument format and separators
     */

    /**
     * Helper class for the date
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Helper class for the compositions on each concert
     *
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * Helper class for the addresses.
     * @var AddressQueryHelper
     */
    private $addressHelper;

    public function __construct()
    {
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->dateQueryHelper = new DateQueryHelper();
        $this->addressHelper = new AddressQueryHelper();
    }

    public function getConductor(
        $dateID,
        $conductorID = null,
        $nameFormat = 0,
        $instrumentFormat = 0,
        $nameLabelSeparator = ', ',
        $instrumentSeparator = '; '
    ): string {
        if ($conductorID > 0) {
            $conductors = $this->addressHelper->getAddresses($conductorID)->getQuery()->toArray();
            foreach ($conductors as $conductor) {
                $name1 = trim($conductor['name1']);
                $name2 = trim($conductor['name2']);
                switch ($nameFormat) {
//                      format = Jane Smith, conductor - default format
                    case 0:
                        $conductorName = trim($name2 . ' ' . $name1);
                        $conductorName .= mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                        break;
//                      format = Smith, Jane, conductor
                    case 1:
                        $conductorName = $name1;
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $conductorName .= $nameLabelSeparator . $name2;
                        }
                        $conductorName .= mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                        break;
//                      format = J. Smith, conductor
                    case 2:
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $firstNames = explode(' ', $name2);
                            $initals = '';

                            foreach ($firstNames as $firstName) {
                                $initals .= $firstName[0] . ".";
                            }

                            $firstNameInitials = preg_split(
                                "/[\s,_-]+/",
                                $initals
                            ); // split $initials by any number of commas or space characters
                        }
                        $conductorName = trim(implode("", $firstNameInitials) . ' ' . $name1);
                        $conductorName .= mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                        break;
//                      format = Name only: Jane Smith
                    case 3:
                        $conductorName = trim($name2 . ' ' . $name1);
                        break;
//                      format = name with Title Case label: Jane Smith, Conductor
                    case 4:
                        $conductorName = trim($name2 . ' ' . $name1);
                        $conductorName .= $nameLabelSeparator . __('adates.conductor_id');
                        break;
//                      format = JANE SMITH, conductor - name in upper case
                    case 5:
                        $conductorName = trim(mb_strtoupper($name2 . ' ' . $name1));
                        $conductorName .= mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                        break;

                    default:
                        $conductorName = trim($name2 . ' ' . $name1);
                        $conductorName .= mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                }
            }
//          fetch any Instruments the conductor also plays as a Soloist
            $conductorInstArray = [];
            $conductorInstruments = $this->dateQueryHelper->getSoloistsForDateID($dateID)->getQuery()->toArray();
            foreach ($conductorInstruments as $conductorInstrument) {
                if ($conductorInstrument['artist_id'] == $conductorID) {
                    switch ($instrumentFormat) {
                        case 0:
                            $instrumentName = mb_strtolower($conductorInstrument['sinstrinstrument']['name']);
                            break;
                        case 1:
                            $instrumentName = $conductorInstrument['sinstrinstrument']['name'];
                            break;
                        default:
                            $instrumentName = mb_strtolower($conductorInstrument['sinstrinstrument']['name']);
                    }
                    $conductorInstArray[] = $instrumentName;
                }
            }
            $conductorInstArray = array_filter(array_unique($conductorInstArray));
            $conductorSoloInstrument = implode($instrumentSeparator, $conductorInstArray);
        }
        return htmlspecialchars(
            $conductorName . ($conductorSoloInstrument ? ' ' . __('and') . ' ' . $conductorSoloInstrument : '')
        );
    }


    public function getSoloists(
        $dateID,
        $conductorID = null,
        $separator = ' ',
        $nameFormat = 0,
        $instrumentFormat = 0,
        $nameInstrumentSeparator = ', '
    ): string {
//        Fetch all soloists who are NOT date-work conductors; Date-Work Conductors should be rendered separately.
//        If the musician plays more than one instrument, return the name once followed by each instrument.

        $dateWorkConductors = [];
        $concertDateWorks = $this->dateQueryHelper->getDateWorksForDateID($dateID)->getQuery()->toArray();
//      fetch all DATE-WORK CONDUCTORS on the Concert
        foreach ($concertDateWorks as $concertDateWork) {
            $dateWorkConductors[] = $concertDateWork['conductor_id'];
        }
        $dateWorkConductors = array_filter(array_unique($dateWorkConductors));

        $dateSoloistArray = [];
        $dateSoloists = $this->dateQueryHelper->getSoloistsForDateID($dateID)->getQuery()->toArray();

        foreach ($dateSoloists as $dateSoloist) {
//        only fetch soloist if NOT conductor or date-work conductor - these are rendered separately
            if ($conductorID !== $dateSoloist['artist_id'] && !in_array(
                    $dateSoloist['artist_id'],
                    $dateWorkConductors
                )) {
                $name1 = trim($dateSoloist['saddress']['name1']);
                $name2 = trim($dateSoloist['saddress']['name2']);
                switch ($nameFormat) {
//                  nameFormat = Jane Smith - default nameFormat
                    case 0:
                        $soloistName = trim($name2 . ' ' . $name1);
                        break;

//                   nameFormat = Smith, Jane
                    case 1:
                        $soloistName = $name1;
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $soloistName .= ', ' . $name2;
                        }
                        break;

//                  nameFormat = J. Smith
                    case 2:
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $firstNames = explode(' ', $name2);
                            $initals = '';

                            foreach ($firstNames as $firstName) {
                                $initals .= $firstName[0] . ".";
                            }

                            $firstNameInitials = preg_split(
                                "/[\s,_-]+/",
                                $initals
                            ); // split $initials by any number of commas or space characters
                        }
                        $soloistName = trim(implode("", $firstNameInitials) . ' ' . $name1);
                        break;

//                  nameFormat = JANE SMITH - default nameFormat in upper case
                    case 3:
                        $soloistName = trim(mb_strtoupper($name2 . ' ' . $name1));
                        break;


                    default:
                        $soloistName = trim($name2 . ' ' . $name1);
                }

//                 fetch all instruments the soloist plays on that date record - this is typically one but can easily be more
                $dateSoloistInstArray = [];
                foreach ($dateSoloists as $dateSoloistInstrument) {
//                  instrument name is traditionally not printed for chorus
                    if ($dateSoloist['artist_id'] == $dateSoloistInstrument['artist_id'] &&
                        $dateSoloistInstrument['sinstrinstrument']['sinstrsection']['syssection_id'] != 19) {
                        $soloistInstrument = $dateSoloistInstrument['sinstrinstrument']['name'];
                    } else {
                        $soloistInstrument = '';
                    }

                    switch ($instrumentFormat) {
                        case 0:
                            $instrumentName = mb_strtolower($soloistInstrument);
                            break;
                        case 1:
                            $instrumentName = $soloistInstrument;
                            break;
                        default:
                            $instrumentName = mb_strtolower($soloistInstrument);
                    }
                    $dateSoloistInstArray[] = $instrumentName;
                    $dateSoloistInstArray = array_filter(array_unique($dateSoloistInstArray));
                }
                $soloistInstrumentString = implode(' ' . __('and') . ' ', $dateSoloistInstArray);

//                Fetch All Date-Work Soloist TEXT
                $dateSoloistTextArray = [];
                foreach ($dateSoloists as $dateSoloistText) {
                    if ($dateSoloist['artist_id'] === $dateSoloistInstrument['artist_id']) {
                        $dateSoloistTextArray[] = $dateSoloistText['notes'];
                    }
                    $dateSoloistTextArray = array_filter(array_unique($dateSoloistTextArray));
                }
                $dateSoloistNotes = implode(' / ', $dateSoloistTextArray);
                if (!is_null($dateSoloistNotes) && (string)trim($dateSoloistNotes) !== '') {
                    $soloistNoteString = ' - ' . $dateSoloistNotes;
                } else {
                    $soloistNoteString = '';
                }


                $dateSoloistArray[] = $soloistInstrumentString ? trim(
                    htmlspecialchars(
                        $soloistName . $nameInstrumentSeparator . $soloistInstrumentString . $soloistNoteString
                    )
                ) : trim(htmlspecialchars($soloistName) . $soloistNoteString);
                $dateSoloistArray = array_unique($dateSoloistArray);
            }
        }

        if (!empty($dateSoloistArray)) {
            return implode($separator, $dateSoloistArray);
        } else {
            return '';
        }
    }


    public function getDateWorkConductors(
        $dateID,
        $separator = ' ',
        $nameFormat = 0,
        $labelFormat = 0,
        $instrumentFormat = 0,
        $nameLabelSeparator = ', ',
        $instrumentSeparator = '; '
    ): string {
        if (empty($separator)) {
            $separator = '; ';
        }

        switch ($labelFormat) {
            case 0:
                $conductorLabel = mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                break;
            case 1:
                $conductorLabel = $nameLabelSeparator . __('adates.conductor_id');
                break;
            default:
                $conductorLabel = mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
        }

        $dateWorkConductors = [];
        $concertDateWorks = $this->dateQueryHelper->getDateWorksForDateID($dateID)->getQuery()->toArray();

        $dateSoloistIDs = [];
        $dateSoloistInstruments = [];
        $concertSoloists = $this->dateQueryHelper->getSoloistsForDateID($dateID)->getQuery()->toArray();
        foreach ($concertSoloists as $concertSoloist) {
            $dateSoloistIDs[] = $concertSoloist['artist_id'];
        }
        $dateSoloistIDs = array_filter(array_unique($dateSoloistIDs));

//      fetch all date-work CONDUCTORS on the Concert
        foreach ($concertDateWorks as $concertDateWork) {
            $dateWorkConductors[] = $concertDateWork['conductor_id'];
        }
        $dateWorkConductors = array_filter(array_unique($dateWorkConductors));

        foreach ($dateWorkConductors as $dateWorkConductor) {
            $dateWorkCondNames = $this->addressHelper->getAddresses($dateWorkConductor)->getQuery()->toArray();

            foreach ($dateWorkCondNames as $dateWorkCondName) {
                $name1 = trim($dateWorkCondName['name1']);
                $name2 = trim($dateWorkCondName['name2']);
                switch ($nameFormat) {
//                    format = Jane Smith, conductor - default format
                    case 0:
                        $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                        $dateWorkConductorOutput .= $conductorLabel;
                        break;
//                   format = Smith, Jane, conductor
                    case 1:
                        $dateWorkConductorOutput = $name1;
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $dateWorkConductorOutput .= ', ' . $name2;
                        }
                        $dateWorkConductorOutput .= $conductorLabel;
                        break;
//                   format = J. Smith, conductor
                    case 2:
                        if (!is_null($name2) && (string)$name2 !== '') {
                            $firstNames = explode(' ', $name2);
                            $initals = '';

                            foreach ($firstNames as $firstName) {
                                $initals .= $firstName[0] . ".";
                            }

                            $firstNameInitials = preg_split(
                                "/[\s,_-]+/",
                                $initals
                            ); // split $initials by any number of commas or space characters
                        }
                        $dateWorkConductorOutput = trim(implode("", $firstNameInitials) . ' ' . $name1);
                        $dateWorkConductorOutput .= $conductorLabel;
                        break;

//                  Name only: Jane Smith
                    case 3:
                        $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                        break;

//                    format = JANE SMITH, conductor - default format upper case
                    case 4:
                        $dateWorkConductorOutput = trim(mb_strtoupper($name2 . ' ' . $name1));
                        $dateWorkConductorOutput .= $conductorLabel;
                        break;

                    default:
                        $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                        $dateWorkConductorOutput .= $conductorLabel;
                }

//              if the date-work conductor is also a soloist on the concert, fetch all instruments played
                if (!empty($dateSoloistIDs) && in_array($dateWorkCondName['id'], $dateSoloistIDs)) {
                    foreach ($concertSoloists as $concertSoloist) {
                        if ($dateWorkCondName['id'] === $concertSoloist['artist_id']) {
                            switch ($instrumentFormat) {
                                case 0:
                                    $instrumentName = mb_strtolower($concertSoloist['sinstrinstrument']['name']);
                                    break;
                                case 1:
                                    $instrumentName = $concertSoloist['sinstrinstrument']['name'];
                                    break;
                                default:
                                    $instrumentName = mb_strtolower($concertSoloist['sinstrinstrument']['name']);
                            }
                            $dateSoloistInstruments[] = $instrumentName;
                        }
                        $dateSoloInstString = implode($instrumentSeparator, array_unique($dateSoloistInstruments));
                    }
                    $dateWorkConductorOutput .= ' ' . __('and') . ' ' . $dateSoloInstString;
                }
                $dateWorkConductorOutputArray[] = htmlspecialchars($dateWorkConductorOutput);
            }
        }

        if (!empty($dateWorkConductorOutputArray)) {
            return implode($separator, $dateWorkConductorOutputArray);
        } else {
            return '';
        }
    }

}
