<?php

namespace Customer\obf\reports\utility;

use Cake\I18n\Time;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Element\TextRun;

/*
 * Helper for Date-Work elements EXCEPT FOR Instrumentation
 * functions for common title, composer and duration output in reports
 * use the utility\Instrumentation\CustomerInstrumentation file for instrumentation output
 */

class DateWorkQueryHelper
{
    /** get Address Book entries; primarily for date-work conductor
     * @var AddressQueryHelper
     */
    protected $addressQueryHelper;
    protected $dateWorksTable = null;
    protected $dateWorkSoloistsTable = null;
    protected $dateWorkMovementsTable = null;
    protected $report = null;
    protected $dateWorkQuery;


    public function __construct()
    {
        $this->dateWorksTable = TableRegistry::getTableLocator()->get('AdateWorks');
        $this->dateWorkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');
        $this->dateWorkMovementsTable = TableRegistry::getTableLocator()->get('AdateworkMovements');
        $this->addressQueryHelper = new AddressQueryHelper();
    }

    public function getQuery()
    {
        return $this->dateWorkQuery;
    }

//    fetch date-work information for a given DATE ID
    public function getDateWorksForDateID($dateID)
    {
        $this->dateWorkQuery = $this
            ->dateWorksTable
            ->find('all')
            ->contain(
                [
                    'Sworks' => ['Scomposers'],
                    'SworkPremieres'
                ]
            )
            ->where(['AdateWorks.date_id' => $dateID])
            ->order(['AdateWorks.work_order' => 'ASC']);

        return $this;
    }

//    fetch date-work information for a given DATE-WORK ID
    public function getDateWorksForDateWorkID($dateWorkID)
    {
        $this->dateWorkQuery = $this
            ->dateWorksTable
            ->find('all')
            ->contain(
                [
                    'Sworks' => ['Scomposers'],
                    'SworkPremieres'
                ]
            )
            ->where(['AdateWorks.id' => $dateWorkID]);

        return $this;
    }


    public function withMovements()
    {
        $this->dateWorkQuery->contain(
            [
                'AdateworkMovements' => function ($q) {
                    return $q->order(['movement_order' => 'ASC']);
                }
            ]
        );
        return $this;
    }

    public function withInstrumentation()
    {
        $this->dateWorkQuery->contain(
            [
                'AdateworkKeyboards' => 'Sinstrinstruments',
                'AdateworkExtras' => 'Sinstrinstruments',
                'AdateworkVocals' => 'Sinstrinstruments',
                'AdateworkPercussions' => 'Sinstrinstruments',
                'AdateworkSoloinstr' => 'Sinstrinstruments',
                'AdateworkSoloists' => 'Sinstrinstruments'
            ]
        );
        return $this;
    }

    public function withDateWorkSoloists()
    {
        $this->dateWorkQuery->contain(
            [
                'AdateworkSoloists'=> [
                    'Saddresses',
                    'Sinstrinstruments' => ['Sinstrsections'],
                    'Sdatestatuses'
                ]
            ]
        );

        return $this;
    }

    public function withLibraries()
    {
        $this->dateWorkQuery->contain(['AdateworkLibraries']);
        return $this;
    }

    public function withDateWorkConductor()
    {
        $this->dateWorkQuery->contain(['Conductoraddresses']);
        return $this;
    }

    public function withToDo()
    {
        $this->dateWorkQuery->contain(['Atodos']);
        return $this;
    }

    public function withAdditionalComposers()
    {
        $this->dateWorkQuery
            ->contain(
                [
                    'Sworks' => ['SworksComposers' => ['Scomposers']]
                ],
            );
        return $this;
    }


//    ++++++  DATE-WORK SOLOISTS ++++++
    public function getSoloistsForDateWorkID($dateWorkID)
    {
        $this->dateWorkQuery = $this
            ->dateWorkSoloistsTable
            ->find('all')
            ->contain(
                [
                    'Saddresses',
                    'Sinstrinstruments' => ['Sinstrsections'],
                    'Sdatestatuses'
                ]
            )
            ->where(['AdateworkSoloists.datework_id' => $dateWorkID])
            ->order(['AdateworkSoloists.artist_order' => 'ASC']);

        return $this;
    }

//    +++ DATE-WORK CONDUCTOR and DATE-WORK SOLOISTS as STRING +++
//    Returns the Date-Work Conductor along with any instrument(s) the conductor may also play
//      as a soloist. Allows for name, instrument and separator formatting

    public function getDateWorkConductorAsString(
        array $soloists,
        int $dateWorkConductorId = 0,
        $nameFormat = 0,
        $labelFormat = 0,
        $instrumentFormat = 0,
        $nameLabelSeparator = ', ',
        $instrumentSeparator = '; '
    ): string {
//        single-element array
        $dateWorkConductor = $this->addressQueryHelper->getAddresses($dateWorkConductorId)->getQuery()->toArray()[0];
        $name1 = trim($dateWorkConductor['name1']);
        $name2 = trim($dateWorkConductor['name2']);
        $dateSoloInstString = [];

//        'Conductor' label after the date-work conductor's name
        switch ($labelFormat) {
            case 0:
                $conductorLabel = mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
                break;
            case 1:
                $conductorLabel = $nameLabelSeparator . __('adates.conductor_id');
                break;
            default:
                $conductorLabel = mb_strtolower($nameLabelSeparator . __('adates.conductor_id'));
        }

//        Name format for the date-work conductor
        switch ($nameFormat) {
//                    format = Jane Smith, conductor - default format
            case 0:
                $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                $dateWorkConductorOutput .= $conductorLabel;
                break;
//                   format = Smith, Jane, conductor
            case 1:
                $dateWorkConductorOutput = trim($name1);
                if (!is_null($name2) && (string)trim($name2) !== '') {
                    $dateWorkConductorOutput .= ', ' . trim($name2);
                }
                $dateWorkConductorOutput .= $conductorLabel;
                break;
//                  format = J. Smith, conductor
            case 2:
                if (!is_null($name2) && (string)trim($name2) !== '') {
                    $firstNames = explode(' ', $name2);
                    $initals = '';

                    foreach ($firstNames as $firstName) {
                        $initals .= $firstName[0] . ".";
                    }

                    $firstNameInitials = preg_split(
                        "/[\s,_-]+/",
                        $initals
                    ); // split $initials by any number of commas or space characters
                }
                $dateWorkConductorOutput = trim(
                    implode("", $firstNameInitials) . ' ' . $name1
                );
                $dateWorkConductorOutput .= $conductorLabel;
                break;

//                  format = Name only: Jane Smith
            case 3:
                $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                break;

            default:
                $dateWorkConductorOutput = trim($name2 . ' ' . $name1);
                $dateWorkConductorOutput .= $conductorLabel;
        }

//      If the date-work conductor is also a soloist, fetch any instruments played
        foreach ($soloists as $dateWorkSoloist) {
            if ($dateWorkSoloist['artist_id'] === $dateWorkConductorId) {
                switch ($instrumentFormat) {
                    case 0:
                        $instrumentName = mb_strtolower($dateWorkSoloist['sinstrinstrument']['name']);
                        break;
                    case 1:
                        $instrumentName = $dateWorkSoloist['sinstrinstrument']['name'];
                        break;
                    default:
                        $instrumentName = mb_strtolower($dateWorkSoloist['sinstrinstrument']['name']);
                }
                $dateSoloistInstruments[] = $instrumentName;
            }
            $dateSoloInstString = implode($instrumentSeparator, array_unique($dateSoloistInstruments));
        }
        if (!empty($dateSoloInstString)) {
            $dateWorkConductorOutput .= ' ' . __('and') . ' ' . $dateSoloInstString;
        }

        return $dateWorkConductorOutput;
    }

//  Returns soloists for the Date-Work entry as a single string
    public function getDateWorkSoloistsAsString(
        array $soloists,
        $dateWorkConductorId = 0,
        $recordSeparator = '; ',
        $nameFormat = 0,
        $instrumentFormat = 0,
        $nameInstrumentSeparator = ', '
    ): string {
        $dateWorkSoloistArray = [];
        foreach ($soloists as $dateWorkSoloist) {
            if ($dateWorkSoloist['artist_id'] !== $dateWorkConductorId) {
                $name1 = trim($dateWorkSoloist['saddress']['name1']);
                $name2 = trim($dateWorkSoloist['saddress']['name2']);

                switch ($nameFormat) {
//                  nameFormat = Jane Smith - default nameFormat
                    case 0:
                        $soloistName = trim($name2 . ' ' . $name1);
                        break;

//                   nameFormat = Smith, Jane
                    case 1:
                        $soloistName = $name1;
                        if (!is_null($name2) && (string)trim($name2) !== '') {
                            $soloistName .= ', ' . $name2;
                        }
                        break;

//                  nameFormat = J. Smith
                    case 2:
                        if (!is_null($name2) && (string)trim($name2) !== '') {
                            $firstNames = explode(' ', $name2);
                            $initals = '';

                            foreach ($firstNames as $firstName) {
                                $initals .= $firstName[0] . ".";
                            }

                            $firstNameInitials = preg_split(
                                "/[\s,_-]+/",
                                $initals
                            ); // split $initials by any number of commas or space characters
                        }
                        $soloistName = trim(
                            implode("", $firstNameInitials) . ' ' . $name1
                        );
                        break;

                    default:
                        $soloistName = $soloistName = trim($name2 . ' ' . $name1);
                }

//          allow for instance where the soloist plays more than one instrument on the Date-Work
//                This would imply very bad data entry by the user but it is not technically impossible
                $dateWorkSoloistInstArray = [];
                foreach ($soloists as $dateWorkSoloistInstrument) {
//                  instrument name is traditionally not printed for chorus
                    if ($dateWorkSoloist['artist_id'] == $dateWorkSoloistInstrument['artist_id'] &&
                        $dateWorkSoloistInstrument['sinstrinstrument']['sinstrsection']['syssection_id'] != 19) {
                        $soloistInstrument = $dateWorkSoloistInstrument['sinstrinstrument']['name'];
                    } else {
                        $soloistInstrument = '';
                    }

                    switch ($instrumentFormat) {
                        case 0:
                            $instrumentName = mb_strtolower($soloistInstrument);
                            break;
                        case 1:
                            $instrumentName = $soloistInstrument;
                            break;
                        default:
                            $instrumentName = mb_strtolower($soloistInstrument);
                    }
                    $dateWorkSoloistInstArray[] = $instrumentName;
                    $dateWorkSoloistInstArray = array_filter(array_unique($dateWorkSoloistInstArray));
                }
                $soloistInstrumentString = implode(' ' . __('and') . ' ', $dateWorkSoloistInstArray);
                $dateWorkSoloistArray[] = $soloistInstrumentString ? htmlspecialchars(
                    $soloistName . $nameInstrumentSeparator . $soloistInstrumentString
                ) : htmlspecialchars($soloistName);
                $dateWorkSoloistArray = array_unique($dateWorkSoloistArray);
            }
        }
        if (!empty($dateWorkSoloistArray)) {
            return implode($recordSeparator, $dateWorkSoloistArray);
        } else {
            return '';
        }
    }


    /*   ++++++  DATE-WORK MOVEMENT LINKED DATA ++++++
    *   Sometimes (rarely) - linked composer and sWork information is needed for date-work Movements
    */
    public function getDateWorkMovementDetails($dateWorkMovementID)
    {
        $this->dateWorkQuery = $this
            ->dateWorkMovementsTable
            ->find('all')
            ->contain(
                [
                    'Sworks' => ['Scomposers'],
                ]
            )
            ->where(['AdateworkMovements.id' => $dateWorkMovementID])
            ->order(['AdateworkMovements.movement_order' => 'ASC']);

        return $this;
    }


    /*    ++++++ COMMON DATE-WORK OUTPUT: Titles, Dates, Durations, etc ++++++
     *      allow for any possible character (or bad data entry) in composer names and work titles;
     *      add Arranger credit in the report PHP file, because Arranger credit can come after the
     *      composer or after the work title (too many variations to program into these functions)
    */

//    COMPOSITION DATES
    public function getWorkDates($compYear = null, $compYear2 = null, $compYearStatus = null)
    {
        $workCompYear = $compYearStatus ? $compYearStatus . ' ' : '';
        $workCompYear .= $compYear;
        if (strlen(trim($compYear) > 0) && strlen(trim($compYear2)) > 0) {
            $workCompYear .= '-' . $compYear2;
        }
        return $workCompYear;
    }


//    ++++++  DATE-WORK and DATE-WORK MOVEMENT DURATIONS ++++++
//    fetch duration in minutes for a given date-work duration string
    public function getDateWorkDurationAsInt($durationString)
    {
        if (empty($durationString) || $durationString == '00:00:00') {
            return '';
        } else {
            $durationTime = new Time($durationString);
            $durationHour = ((int)$durationTime->format('H') * 60);
            $durationMinutes = (int)$durationTime->format('i');
            $durationSeconds = ((int)$durationTime->format('s') / 60);

            $durationInteger = round($durationHour + $durationMinutes + $durationSeconds);

            return $durationInteger;
        }
    }

    /**
     * Get the duration for a DATE-WORK object
     *
     * Based on the duration this function returned it in the form m's''.
     *
     * @param $duration
     * @return string
     */
    public function getDateWorkDuration($duration, int &$accumulator = 0): string
    {
        if (empty($duration) || $duration == '00:00:00') {
            return '';
        } else {
            $dwDurationString = '';
            if (empty($this->report->text['minutes_suffix'])) {
                $minuteSuffix = ':';
            } else {
                $minuteSuffix = $this->report->text['minutes_suffix'];
            }
            if (empty($this->report->text['seconds_suffix'])) {
                $secondSuffix = '';
            } else {
                $secondSuffix = $this->report->text['seconds_suffix'];
            }

            $durationTime = new Time($duration);
            $durationMinutes = (int)$durationTime->format('i') + ((int)$durationTime->format('H') * 60);
            $durationSeconds = $durationTime->format('s');

            if ($durationMinutes > 0) {
                $dwDurationString .= $durationMinutes . $minuteSuffix;
                $accumulator += (int)($durationMinutes * 60);
            }

            if ($durationSeconds > 0) {
                $accumulator += (int)$durationSeconds;
                $dwDurationString .= $durationSeconds . $secondSuffix;
            }

            if ($durationSeconds == 0 && $secondSuffix == '') {
                $dwDurationString .= '00';
            }

            return $dwDurationString;
        }
    }

//    fetch duration in minutes for a given date-work movement duration string
    public function getDateWorkMvtDurationAsInt($durationString)
    {
        if (empty($durationString) || $durationString == '00:00:00') {
            return '';
        } else {
            $durationTime = new Time($durationString);
            $durationHour = ((int)$durationTime->format('H') * 60);
            $durationMinutes = (int)$durationTime->format('i');
            $durationSeconds = ((int)$durationTime->format('s') / 60);

            $durationInteger = round($durationHour + $durationMinutes + $durationSeconds);

            return $durationInteger;
        }
    }

    /**
     * Get the duration for a DATE-WORK MOVEMENT object
     *
     * Based on the duration this function returned it in the form m's'' or m:s.
     *
     * @param $duration
     * @return string
     */
    public function getDateWorkMvtDuration($duration): string
    {
        if (empty($duration) || $duration == '00:00:00') {
            return '';
        } else {
            $dwDurationString = '';
            if (empty($this->report->text['minutes_suffix'])) {
                $minuteSuffix = ':';
            } else {
                $minuteSuffix = $this->report->text['minutes_suffix'];
            }
            if (empty($this->report->text['seconds_suffix'])) {
                $secondSuffix = '';
            } else {
                $secondSuffix = $this->report->text['seconds_suffix'];
            }

            $durationTime = new Time($duration);
            $durationMinutes = (int)$durationTime->format('i') + ((int)$durationTime->format('H') * 60);
            $durationSeconds = $durationTime->format('s');

            if ($durationMinutes > 0) {
                $dwDurationString .= $durationMinutes . $minuteSuffix;
            }

            if ($durationSeconds > 0) {
                $dwDurationString .= $durationSeconds . $secondSuffix;
            }

            if ($durationSeconds == 0 && $secondSuffix == '') {
                $dwDurationString .= '00';
            }

            return $dwDurationString;
        }
    }
}
