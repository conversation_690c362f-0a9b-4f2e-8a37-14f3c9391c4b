<?php

namespace Customer\obf\reports\utility;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;


class DateQueryHelper
{

    protected $dateTable = null;
    protected $dateActivitiesTable = null;
    protected $datePersonsTable = null;
    protected $dateWorksTable = null;
    protected $dateSoloistsTable = null;
    protected $daysTable = null;
    protected $dateQuery;

    public function __construct()
    {
        $this->dateTable = TableRegistry::getTableLocator()->get('adates');
        $this->dateActivitiesTable = TableRegistry::getTableLocator()->get('AdateActivities');
        $this->datePersonsTable = TableRegistry::getTableLocator()->get('AdatePersons');
        $this->dateWorksTable = TableRegistry::getTableLocator()->get('AdateWorks');
        $this->dateSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');
        $this->daysTable = TableRegistry::getTableLocator()->get('Adays');
    }

    public function getQuery()
    {
        return $this->dateQuery;
    }

    public function getDates($datesIds)
    {
        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                [
                    'Sprojects',
                    'Seventtypes',
                    'Sdresses',
                    'Sseasons',
                    'AdateWorks' => [
                        'Sworks'
                    ],
                ]
            )
            ->where(['adates.id IN ' => $datesIds])
            ->order(['adates.date_' => 'ASC', 'adates.start_' => 'ASC', 'adates.end_' => 'ASC']);

        return $this;
    }

//    include Conductor, Venue, Orchestra other 1:1 address relations are uncommon
//     and so covered under additional data
    public function withAddresses()
    {
        $this->dateQuery->contain(
            [
                'Locationaddresses',
                'Conductoraddresses',
                'Orchestraaddresses'
            ]
        );

        return $this;
    }

//    include Instrumentation Grids in Date fetch
    public function withInstrumentationGrids()
    {
        $this->dateQuery->contain(
            [
                'AdateWorks' => [
                    'AdateworkPercussions' => ['Sinstrinstruments'],
                    'AdateworkSoloinstr' => ['Sinstrinstruments'],
                    'AdateworkVocals' => ['Sinstrinstruments'],
                    'AdateworkKeyboards' => ['Sinstrinstruments'],
                    'AdateworkExtras' => ['Sinstrinstruments'],
                    'AdateworkSoloists' => ['Sinstrinstruments']
                ]
            ]
        );
        return $this;
    }


//    include Status in Date fetch
    public function withStatus()
    {
        $this->dateQuery->contain(
            ['Sdatestatuses']
        );

        return $this;
    }


//    include the date-persons table. use getDatePersons function
//      to fetch names, address groups, instrument, etc.
    public function withPersons()
    {
        $this->getQuery()->contain(
            [
                'AdatePersons' => [
                    'Saddresses',
                    'Sinstrinstruments',
                    'Saddressgroups',
                    'SaddressFunctionItems'
                ]
            ]
        );
        return $this;
    }

    public function withChecklist()
    {
        $this->getQuery()->contain(
            [
                'Atodos' => ['Stodostatus', 'Stodotypes', 'OpasUsers']
            ]
        );
        return $this;
    }

    public function withSeries()
    {
        $this->getQuery()->contain(
            [
                'AdateSeries' => 'Sseries'
            ]
        );
        return $this;
    }

    public function withMarketing()
    {
        $this->getQuery()->contain(
            [
                'AdatesMarketing'
            ]
        );
        return $this;
    }

//    fetch all Dates for a given project and season. primarily used to print a date-range
//    where the report does not originate in the Dates area. For example, given a work or conductor, show the
//    performance dates as: Aug. 29 - Sep. 3, 2014; or count the number of events in a project+season
    public function getDatesforSeasonProject($seasonId, $projectID)
    {
        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                [
                    'Seventtypes',
                    'Locationaddresses',
                    'Conductoraddresses',
                    'Orchestraaddresses'
                ]
            )
            ->where(
                ['adates.season_id' => $seasonId, 'adates.project_id' => $projectID],
            )
            ->order(['adates.date_' => 'ASC', 'adates.start_' => 'ASC', 'adates.end_' => 'ASC']);

        return $this;
    }

//    fetch all dress/attire information for a given project and season. Allows for easy output of
//      dress/attire across an entire project (especially when more than one dress type is used
    public function getDressforSeasonProject($seasonId, $projectID)
    {
        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                [
                    'Sdresses'
                ]
            )
            ->where(
                ['adates.season_id' => $seasonId, 'adates.project_id' => $projectID],
            )
            ->order(['adates.date_' => 'ASC', 'adates.start_' => 'ASC', 'adates.end_' => 'ASC']);

        return $this;
    }

// fetch the date-work records for a given date record; this query fetches more detail than the main date query above
    public function getDateWorksForDateID($dateID)
    {
        $this->dateQuery = $this
            ->dateWorksTable
            ->find('all')
            ->contain(
                [
                    'Sworks' => ['Scomposers'],
                    'AdateworkMovements',
                    'SworkPremieres',
                    'AdateworkSoloists' => ['Saddresses', 'Sinstrinstruments' => ['Sinstrsections']],

                ]
            )
            ->where(['AdateWorks.date_id' => $dateID])
            ->order(['AdateWorks.work_order' => 'ASC']);

        return $this;
    }

//    include Instrumentation Grids in Date-Work fetch
    public function withDateWorkInstrumentationGrids()
    {
        $this->dateQuery->contain(
            [
                'AdateworkPercussions' => ['Sinstrinstruments'],
                'AdateworkSoloinstr' => ['Sinstrinstruments'],
                'AdateworkVocals' => ['Sinstrinstruments'],
                'AdateworkKeyboards' => ['Sinstrinstruments'],
                'AdateworkExtras' => ['Sinstrinstruments'],
                'AdateworkSoloists' => ['Sinstrinstruments']
            ]
        );
        return $this;
    }

//    fetch duration in minutes for a given date-work duration string
    public function getDurationAsInt($durationString)
    {
        $durationTime = new Time($durationString);
        $durationHour = ((int)$durationTime->format('H') * 60);
        $durationMinutes = (int)$durationTime->format('i');
        $durationSeconds = ((int)$durationTime->format('s') / 60);

        $durationInteger = round($durationHour + $durationMinutes + $durationSeconds);

        return $durationInteger;
    }

//    fetch soloists for the passed Date Record
    public function getSoloistsForDateID($dateID)
    {
        $this->dateQuery = $this
            ->dateSoloistsTable
            ->find('all')
            ->contain(
                [
                    'AdateWorks',
                    'Saddresses',
                    'Sinstrinstruments' => ['Sinstrsections'],
                    'Sdatestatuses'
                ]
            )
            ->where(['AdateWorks.date_id' => $dateID])
            ->where(
                function (QueryExpression $exp, Query $q) {
                    return $exp->isNotNull('AdateworkSoloists.artist_order2');
                }
            )
            ->order(['AdateworkSoloists.artist_order2' => 'ASC']);

        return $this;
    }


    public function getDatePersons($dateId)
    {
        $this->dateQuery = $this
            ->datePersonsTable
            ->find('all')
            ->contain(
                [
                    'Saddresses',
                    'Sinstrinstruments',
                    'Saddressgroups',
                    'SaddressFunctionItems'
                ]
            )
            ->where(['AdatePersons.date_id' => $dateId])
            ->order(['AdatePersons.person_order' => 'ASC']);

        return $this;
    }

    public function getDateActivities($dateId)
    {
        $this->dateQuery = $this
            ->dateActivitiesTable
            ->find('all')
            ->contain(['SeventTypes'])
            ->where(['AdateActivities.date_id' => $dateId]);

        return $this;
    }

    public function getDatesOnSameDay($dateId)
    {
        $date = $this->dateTable->find('all')->where(['adates.id' => $dateId])->first();

        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                ['SeventTypes', 'sProjects']
            )
            ->where(['adates.date_' => $date['date_']->format('Y-m-d H:i:s')])
            ->order(['adates.start_' => 'ASC', 'adates.end_' => 'ASC']);

        return $this;
    }


//    fetch all dates associated with a given CONDUCTOR ID (from saddresses).
//      Used to get the Conductor's Performance History.
//      Use the function getDateWorksForDateID($dateID) to produce the program
    public function getDatesForConductorID($conductorId)
    {
        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                [
                    'Sprojects',
                    'Seventtypes',
                    'Sseasons',
                    'AdateWorks' => [
                        'AdateworkSoloists' => ['Saddresses', 'Sinstrinstruments' => ['Sinstrsections']],
                    ],
                ]
            )
            ->where(['adates.conductor_id' => $conductorId])
            ->order(['adates.date_' => 'DESC', 'adates.start_' => 'DESC', 'adates.end_' => 'DESC']);

        return $this;
    }

//    fetch all dates associated with a given SOLOIST ID (from saddresses).
//      Used to get the Soloist (or ensemble) Performance History.
    public function getDateWorksForSoloistID($soloistId)
    {
        $this->dateQuery = $this
            ->dateSoloistsTable
            ->find('all')
            ->contain(
                [
                    'AdateWorks' => [
                        /*
                         * PW: Line 345 works fine
                         * When we try to fetch joined tables in line 346, we get the Date record
                         * but none of the joined tables
                         */
//                     'aDates',
                        'Adates' => ['Sseasons', 'Sprojects', 'Seventtypes', 'Conductoraddresses'],
                        'SworkPremieres',
                        'Sworks'
                    ]

                ]
            )
            ->where(['AdateworkSoloists.artist_id' => $soloistId])
            ->order(['Adates.date_' => 'DESC', 'Adates.start_' => 'DESC', 'Adates.end_' => 'DESC']);

        return $this;
    }

//    fetch all dates associated with a given WORK ID (from sworks).
//      Used to produce a Composition Performance History.
//      Event Types is the only linked Date table so it is easy to grab 'only performances'
//      Use the function getDateWorksForDateID($dateID) to produce the full program and getDates for event detail.

    public function getDatesforWorkID($workId = null)
    {
        $this->dateQuery = $this
            ->dateWorksTable
            ->find('all')
            ->contain(
                [
                    'aDates' => [
                        'Seventtypes',
                        'Locationaddresses',
                        'Conductoraddresses',
                        'Orchestraaddresses',
                        'Sprojects'
                    ],
                    'SworkPremieres',
                    'AdateworkSoloists' => ['Saddresses', 'Sinstrinstruments' => ['Sinstrsections']],
                    'Conductoraddresses'
                ]
            )
            ->where(['AdateWorks.work_id' => $workId])
            ->order(['Adates.date_' => 'DESC', 'Adates.start_' => 'DESC', 'Adates.end_' => 'DESC']);

        return $this;
    }


//    fetch all dates associated with a given VENUE ID (from saddresses).
//      Used to get the Venue Performance History (rare, but used).
//      Use the function getDateWorksForDateID($dateID) to produce the program
    public function getDatesForVenueID($venueId)
    {
        $this->dateQuery = $this
            ->dateTable
            ->find('all')
            ->contain(
                [
                    'Sprojects',
                    'Seventtypes',
                    'Sseasons',
                    'AdateWorks' => [
                        'AdateworkSoloists' => ['Saddresses', 'Sinstrinstruments' => ['Sinstrsections']],
                    ],
                ]
            )
            ->where(['adates.location_id' => $venueId])
            ->order(['adates.date_' => 'DESC', 'adates.start_' => 'DESC', 'adates.end_' => 'DESC']);

        return $this;
    }


    //    fetch soloists for the passed Date-Work Record; move to Program Helper and
    //    deprecate this one in existing reports
    public function getSoloistsForDateWorkID($dateWorkID)
    {
        $this->dateQuery = $this
            ->dateSoloistsTable
            ->find('all')
            ->contain(
                [
                    'AdateWorks',
                    'Saddresses',
                    'Sinstrinstruments' => ['Sinstrsections'],
                    'Sdatestatuses'
                ]
            )
            ->where(['AdateWorks.id' => $dateWorkID])
            ->order(['AdateworkSoloists.artist_order' => 'ASC']);

        return $this;
    }

//  given two Dates, return date range as string in format needed for report

    public function getFormattedDateRange($startDate, $endDate, $dateRangeFormat = null): string
    {
        /*        standard format for date ranges; add to this list as required by region
            1 = Oct. 30 - Nov. 1, 2015 or Oct. 3-5, 2015
            2 = October 30 - November 1, 2015 or October 3-5, 2015
            3 = 30 Oct. - 1 Nov. 2015  or 3-5 Oct. 2015
            4 = 30 October-1 November 2015

        If format is not specified use Date format from settings.php
        */

        if ($startDate == $endDate) {
            $sameDate = true;
        }

        if ($startDate->format('m') === $endDate->format('m')) {
            $sameMonth = true;
        }

        switch ($dateRangeFormat) {
            case 1:
                if ($sameDate == true) {
                    $dateRange = $startDate->format('M. j, Y');
                } else {
                    $dateRange = $startDate->format('M. j');
                    if ($sameMonth == true) {
                        $dateRange .= '-' . $endDate->format('j, Y');
                    } else {
                        $dateRange .= ' - ' . $endDate->format('M. j, Y');
                    }
                }
                break;

            case 2:
                if ($sameDate == true) {
                    $dateRange = $startDate->format('F j, Y');
                } else {
                    $dateRange = $startDate->format('F j');
                    if ($sameMonth == true) {
                        $dateRange .= '-' . $endDate->format('j, Y');
                    } else {
                        $dateRange .= ' - ' . $endDate->format('F j, Y');
                    }
                }
                break;

            case 3:
                if ($sameDate == true) {
                    $dateRange = $startDate->format('j M Y');
                } else {
                    $dateRange = $startDate->format('j');
                    if ($sameMonth == true) {
                        $dateRange .= '-' . $endDate->format('j M Y');
                    } else {
                        $dateRange .= $startDate->format(' M') . ' - ';
                        $dateRange .= $endDate->format('j M Y');
                    }
                }
                break;

            case 4:
                if ($sameDate == true) {
                    $dateRange = $startDate->format('j F Y');
                } else {
                    $dateRange = $startDate->format('j');
                    if ($sameMonth == true) {
                        $dateRange .= '-' . $endDate->format('j F Y');
                    } else {
                        $dateRange .= $startDate->format('F') . ' - ';
                        $dateRange .= $endDate->format('j F Y');
                    }
                }
                break;

            default:
                $dateRange = $startDate->format(Configure::read('Formats.date'));
                $dateRange .= '-';
                $dateRange .= $endDate->format(Configure::read('Formats.date'));
        }

        return $dateRange;
    }


// +++++ CALENDAR DAY FUNCTIONS +++++
    public function getDaysInfoForDate($dateId)
    {
        $date = $this->dateTable->find('all')->where(['adates.id' => $dateId])->first();

        $this->dateQuery = $this
            ->daysTable
            ->find('all')
            ->contain(
                [
                    'Sholydays',
                    'Sseasons',
                    'Sdaystatuses'
                ]
            )
            ->where(['Adays.date_' => $date['date_']->format('Y-m-d H:i:s')]);

        return $this;
    }

    public function getDaysInfoForCalDay($calDay)
    {
        $this->dateQuery = $this
            ->daysTable
            ->find('all')
            ->contain(
                [
                    'Sholydays',
                    'Sseasons',
                    'Sdaystatuses'
                ]
            )
            ->where(
                [
                    'Adays.date_' => $calDay->format('Y-m-d H:i:s'),
                    'Adays.planninglevel' => 1
                ]
            );
        return $this;
    }

//    all Calendar Days for a given Season and Week (chron week)
    public function getDaysforSeasonWeek($seasonID, $week)
    {
        $this->dateQuery = $this
            ->daysTable
            ->find('all')
            ->contain(
                [
                    'Sholydays',
                    'Sseasons',
                    'Sdaystatuses'
                ]
            )
            ->where(
                [
                    'Adays.season_id' => $seasonID,
                    'Adays.week' => $week,
                    'Adays.planninglevel' => 1
                ]
            )
            ->order(['Adays.date_' => 'ASC']);

        return $this;
    }

//    this function is required to print 'wall calendar' or month grid reports
//      where a season may start or end in the middle of a month. YEAR is a necessary
//      variable becuase many client databases span more than a century of activities
    public function getDaysforYearWeek($year, $week)
    {
        $this->dateQuery = $this
            ->daysTable
            ->find('all')
            ->contain(
                [
                    'Sholydays',
                    'Sseasons',
                    'Sdaystatuses'
                ]
            )
            ->where(
                [
                    'Adays.year' => $year,
                    'Adays.week' => $week,
                    'Adays.planninglevel' => 1
                ]
            )
            ->order(['Adays.date_' => 'ASC']);

        return $this;
    }

    public function getDaysFromRange($startDate, $endDate)
    {
        $this->dateQuery = $this
            ->daysTable
            ->find('all')
            ->contain(
                [
                    'Sholydays',
                    'Sseasons',
                    'Sdaystatuses'
                ]
            )
            ->where(
                [
                    'Adays.date_ >= ' => $startDate,
                    'Adays.date_ <= ' => $endDate,
                    'Adays.planninglevel' => 1
                ]
            )
            ->order(['Adays.date_' => 'ASC']);

        return $this;
    }


}
