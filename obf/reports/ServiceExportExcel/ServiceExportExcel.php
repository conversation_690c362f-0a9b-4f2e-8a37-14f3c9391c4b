<?php

namespace Customer\obf\reports\ServiceExportExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\fasutilities\reports\utility\DutyQueryHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report renders basic service information to Excel. This version omits repertoire.
 *
 */

class ServiceExportExcel extends Report
{
//    Change these values to add a title or other heading
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 3;
    const DATA_ROW = 4;

    /**
     * Helper class for the selected Service records
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;
    private $dutiesResult;

// SET COLUMN ADDRESSES
    private $col_Week = 'A';
    private $col_Date = 'B';
    private $col_Start = 'C';
    private $col_Project = 'D';
    private $col_Event = 'E';
    private $col_Musician = 'F';
    private $col_Instrument = 'G';
    private $col_Group = 'H';
    private $col_Classification = 'I';
    private $col_Attendance = 'J';
    private $col_LPresent = 'K';
    private $col_Notes = 'L';
    private $col_Order1 = 'M';
    private $col_Seat = 'N';
    private $col_AccountCat = 'O';
    private $col_SvcVal = 'P';
    private $col_Hours = 'Q';

    public function initialize()
    {
        $this->dutyQueryHelper = new DutyQueryHelper();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dutyQuery = $this->dutyQueryHelper
            ->getDuties($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->dutiesResult = $dutyQuery->toArray();

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

//        Render Title
        $spreadsheet->getActiveSheet()
            ->setCellValue('A1' , $repFileText['report_title']);

//      +++++ Render DATA HEADER ROW as single-item array in designated row
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow($repFileText),
                null,
                'A' . self::HEADER_ROW
            );

        $rowCount = sizeof($this->dutiesResult);
        $upperLeftCell = $this->col_Week . self::DATA_ROW;



//        +++++ FETCH EACH DUTY RECORD AND RENDER by array +++++

//        There is no doubt a more efficient way to do this but this sorts the service array by
//          Musician Last, Musician First, Date and Start Time
//        This procedure has the advantage that it is easy to edit for report customizations
        $dutyArray = $this->dutiesResult;
        /**
         * @see https://stackoverflow.com/a/2699159
         */
        usort($dutyArray, function($a, $b) {
            return $a['artistaddress']['name1'] . $a['artistaddress']['name2']. $a['adate']['date_'] . $a['adate']['start_']
                <=> $b['artistaddress']['name1'] . $b['artistaddress']['name2']. $b['adate']['date_'] . $b['adate']['start_'];
        });


        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getServiceData($dutyArray),
                null,
                'A' . self::DATA_ROW
            );

//        ++++++ FORMAT SPREADSHEET ++++++
       $this->formatSheet($upperLeftCell, $rowCount, $spreadsheet);

        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;

        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    protected function getServiceData($dutyArray)
    {
        $dutyOutput = [];
        foreach ($dutyArray as $duty) {
            if (Configure::read('Formats.region') === 'North America') {
                $week = $duty['adate']['pweek'];
            } else {
                $week = $duty['adate']['week'];
            };
            $date = $duty['adate']['date_']->format(Configure::read('Formats.date'));
            if (!is_null($duty['adate']['start_']) && (string)trim($duty['adate']['start_']) !== '') {
                $startTime = $duty['adate']['start_']->format(Configure::read('Formats.time_short'));
            } else {
                $startTime = '';
            }


            $project = $duty['adate']['sproject']['name'];
            $activity = $duty['adate']['seventtype']['name'];

            $musician = $duty['artistaddress']['name1'];
            $musician .= $duty['artistaddress']['name2'] ? ', ' . $duty['artistaddress']['name2'] : '';
            $instrument = $duty['sinstrinstrument']['name'];
            $group = $duty['saddressgroup']['name'];
            if ($duty['function_id'] > 0) {
                $classification = $duty['saddressfunctionitem']['name'];
            } else {
                $classification = '';
            }
            $attendance = $duty['sdutytype']['name'];
            if ($duty['sdutytype']['l_present'] == 1) {
                $present = __('yes');
            } else {
                $present = __('no');
            }
            $notes = htmlspecialchars($duty['notes']);
            $order1 = $duty['order_1'];
            $seat = $duty['seat'];

            $acctCategoryName = $this->dutyQueryHelper->getAcctCatNameFoDutyAcctCat(
                $duty['accounting_category']
            )->getQuery()->toArray()[0];

            if (!empty($duty['accounting_category'])) {
                $acctCategory = $duty['accounting_category'] . ' - ';
                $acctCategory .= $acctCategoryName['name'];
            } else {
                $acctCategory = '';
            }

            $svcVal = $duty['duties'];
            if (!empty($duty['duration']) || !is_null($duty['duration'])) {
                $hours = $duty['duration'];
            } else {
                $hours = 0;
//                $hours = round(abs(strtotime($duty['adate']['end_'] ?? "now") - strtotime($duty['adate']['start_']) ?? "now") / 3600, 2);
            }

            $dutyOutput[] =
                [
                    $week,
                    $date,
                    $startTime,
                    $project,
                    $activity,
                    $musician,
                    $instrument,
                    $group,
                    $classification,
                    $attendance,
                    $present,
                    $notes,
                    $order1,
                    $seat,
                    $acctCategory,
                    $svcVal,
                    $hours
                ];
        }

        return $dutyOutput;
    }

    public function getHeaderRow($repFileText)
    {
        $columnA = $repFileText['col_Week'] ?? __('adates.pweek');
        $columnB = $repFileText['col_Date'] ?? __('adates.date_');
        $columnC = $repFileText['col_Start'] ?? __('adates.start_');
        $columnD = $repFileText['col_Project'] ?? __('adates.project_id');
        $columnE = $repFileText['col_Event'] ?? __('adates.eventtype_id');
        $columnF = $repFileText['col_Musician'] ?? __('aduties.artist_id');
        $columnG = $repFileText['col_Instrument'] ?? __('aduties.instrument_id');
        $columnH = $repFileText['col_Group'] ?? __('aduties.addressgroup_id');
        $columnI = $repFileText['col_Classification'] ?? __('aduties.function_id');
        $columnJ = $repFileText['col_Attendance'] ?? __('aduties.dutytype_id');
        $columnK = $repFileText['col_LPresent'] ?? __('present');
        $columnL = $repFileText['col_Notes'] ?? __('aduties.notes');
        $columnM = $repFileText['col_Order1'] ?? __('aduties.order_1');
        $columnN = $repFileText['col_Seat'] ?? __('aduties.seat');
        $columnO = $repFileText['col_AccountCat'] ?? __('aduties.accounting_category');
        $columnP = $repFileText['col_SvcVal'] ?? __('aduties.duties');
        $columnQ = $repFileText['col_Hours'] ?? __('aduties.duration');

        return array(
            $columnA,
            $columnB,
            $columnC,
            $columnD,
            $columnE,
            $columnF,
            $columnG,
            $columnH,
            $columnI,
            $columnJ,
            $columnK,
            $columnL,
            $columnM,
            $columnN,
            $columnO,
            $columnP,
            $columnQ
        );
    }

    protected function formatSheet($upperLeftCell, $rowCount, $spreadsheet)
    {
        $finalRow = $rowCount + 4; // take header rows into account
        $defaultFont = 'Calibri';
        $defaultFontSize = 10;

//        BOLD Header (row 1) and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':Q' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));

//        +++++ SET FONT ++++++
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $this->col_Hours . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

        $spreadsheet->getActiveSheet()->getStyle('A1')
            ->getFont()
            ->setName($defaultFont)
            ->setBold(true)
            ->setSize(12);

//        +++++ SET COLUMN WIDTH +++++
//       first column 6, Auto-size all other columns
        foreach (range($this->col_Date, $this->col_Hours) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setAutoSize(true);
        }

//        +++++ SET COLUMN ALIGNMENT +++++
//       Set week, date and start time to right align.
//        Present, order and seat columns centered
        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Week . self::HEADER_ROW . ':' . $this->col_Start . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_LPresent . self::HEADER_ROW . ':' . $this->col_LPresent . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $spreadsheet->getActiveSheet()->getStyle(
            $this->col_Order1 . self::HEADER_ROW . ':' . $this->col_Seat . $finalRow
        )
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //       +++++ Freeze Panes +++++ after header row
        $spreadsheet->getActiveSheet()->freezePane('A' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }


    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => '3E5634') // Green House
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),

//            'alignment' => [
//                'setWrapText' => true
//            ],

        );
    }
}
