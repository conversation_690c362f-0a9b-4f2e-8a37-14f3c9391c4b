<?php

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\obf\reports\utility\WorkQueryHelper;
use Customer\obf\reports\BaseConcertProgram;
use Customer\obf\PHPWordHelper;

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;
use PhpOffice\PhpWord\Shared\Converter;


/**
 * AUTHOR NOTE
 *   Report produces a simple list of compositions, sorted by Master Title / Title1. Typically used to output the results
 *     of a pops music search where compositions are refered to more by title than composer
 *  FORMAT NOTES:   Standard left-aligned paragraph with no extra spacing.
 *          BaseConcertProgram extends all the necessary classes to italicize text in < >
 *  CONTENT NOTES:
 */
class WorkListTitle1Sort extends BaseConcertProgram
{

    /**
     * Helper class for the date query of the date works.
     *
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * Set the query helper and the instrumentation class
     *
     * @throws Exception
     */
    public function initialize()
    {
        $this->workQueryHelper = new WorkQueryHelper();
    }

    /**
     * Get the works selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $dateQuery = $this->workQueryHelper
            ->getWorks($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     *
     * @param null $view
     * @param null $layout
     * @return mixed|string
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $this->phpWord = new PhpWord();
        $section = $this->wordHelper->addSection(
            [  // adding a section REQUIRES $phpWord
                'paperSize' => "Letter",
                // "Letter" , "A4" , others in PHPWord folder "Paper.php"
                // 'orientation' => "Portrait" ,   portrait is default - no need to indicate unless switching to landsacpe
                'marginTop' => Converter::inchToTwip(.5),
                'marginBottom' => Converter::inchToTwip(.75),
                'marginLeft' => Converter::inchToTwip(.75),
                'marginRight' => Converter::inchToTwip(.5),
                'breakType' => 'continuous',
                // prevents page break between sections when a report uses multiple sections
            ]
        );


        // insert footer
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');
        $runningTimeFormat = Configure::read(
            'Formats.programHeaderTime'
        );

        $footer = $section->addFooter();
        $footerOutput = $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $this->reportFontDefault,
            $this->reportParagraphFooter // ReportWordFas - standard footer layout
        );

        // insert title and table header
        $this->wordHelper->addSectionText(
            $section,
            'Work List' . '<br/>',
            (new Font())->setSize(14)->setName('Calibri')->setColor('901414')->setBold(true),
            $this->reportParagraphCenter
        );

        $table = $this->wordHelper->addTable($section);
        $row = $this->wordHelper->addRow(
            $table,
            PHPWordHelper::ROW_COLS_FIFTHS,
            $widths = [
                $this->wordHelper->getWidthTwpByPct(33),
                $this->wordHelper->getWidthTwpByPct(20),
                $this->wordHelper->getWidthTwpByPct(25),
                $this->wordHelper->getWidthTwpByPct(15),
                $this->wordHelper->getWidthTwpByPct(7),
            ],
        );

        $this->wordHelper->addTableText(
            $row,
            0,
            'Master Title',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            1,
            'Composer',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            2,
            'Print Title',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            3,
            'Arranger',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphDefault
        );
        $this->wordHelper->addTableText(
            $row,
            4,
            'Dur.',
            (new Font())->setSize(10)->setName('Calibri')->setBold(true),
            $this->reportParagraphRight
        );


// fetch all works as a single array then sort Master Title then Composer
        $workArray = $this->datesResult;
        function compareByTitle($a, $b)
        {
            return strnatcasecmp(
                $a['title1'] . $a['scomposer']['lastname'] . $a['somposer']['firstname'],
                $b['title1'] . $b['scomposer']['lastname'] . $b['somposer']['firstname']
            );
        }

        usort(
            $workArray,
            'comparebyTitle'
        );

        $table = $this->wordHelper->addTableFullBorder($section);

        // iterate through the sorted array of all work data
        foreach ($workArray as $work) {
            $row = $this->wordHelper->addRow(
                $table,
                PHPWordHelper::ROW_COLS_FIFTHS,
                $widths = [
                    $this->wordHelper->getWidthTwpByPct(33),
                    $this->wordHelper->getWidthTwpByPct(20),
                    $this->wordHelper->getWidthTwpByPct(25),
                    $this->wordHelper->getWidthTwpByPct(15),
                    $this->wordHelper->getWidthTwpByPct(7),
                ],
            );

            $compYear = '';
            $compYear .= $work['compyearstatus'];
            $compYear .= $work['compyear'];
            if (trim($work['compyear2']) !== '') {
                $compYear .= " - " . $work['compyear2'];
            }

            $this->wordHelper->addTableText(
                $row,
                0,
                $work['title1'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                1,
                trim($work['scomposer']['firstname'] . ' ' . $work['scomposer']['lastname']),
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                2,
                $work['title2'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                3,
                $work['arrangement'],
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphDefault
            );

            $this->wordHelper->addTableText(
                $row,
                4,
                $this->workQueryHelper->getDurationStringForWork($work['duration'], ':', ''),
                (new Font())->setSize(9)->setName('Calibri'),
                $this->reportParagraphRight
            );
        }


        // Create the file and return the filename
        $fileName = $this->_createFileName() . $this->getFileExtension();

        ob_end_clean();

        $this->wordHelper->write($fileName);

        return $fileName;
    }

}
