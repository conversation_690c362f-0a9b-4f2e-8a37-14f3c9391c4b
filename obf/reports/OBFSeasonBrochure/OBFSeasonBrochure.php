<?php

namespace Customer\obf\reports\OBFSeasonBrochure;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Standard Concert Program in brochure format
 * Each project + program number composition
 * Conductor, date-work conductor, soloists, works and movements; also some Dates Marketing fields. Print title is Title 3 (alt. title).
 * UNIQUE TO THIS REPORT - the anchor date is NOT the first performance; it is the first event belonging to one of the
 * determined Event Type GROUPS (array below). Client uses this report to include classes, workshops and other non-performances
 *
 */

/*
 * TG: 2025-FEB - customizations per client request; use alt. conductor/soloist name
 */

class OBFSeasonBrochure extends ReportWord
{
    const SPONSOR = 'Sponsor: ';
    const MEDIA = 'Media Partner: ';

    const PERF_GROUP_ID = 2;
    const EDU_GROUP_ID = 10;
    const PHIL_GROUP_ID = 7;
    const EVENT_GROUP_ARRAY = array(self::PERF_GROUP_ID, self::EDU_GROUP_ID, self::PHIL_GROUP_ID);

    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();

        // Set report font, table and paragraph styles
        $this->reportStyles = new OBFSeasonBrochureStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file. This allows for headings and other report text to output in a
    variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withMarketing()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    //    Use case is to run this report for all projects in the Season. This function
    //  identifies each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concert Programs can be repeated within each Season:Project:ProgramNumber. Unique to this report: the anchor date
//      is not the First Performance, but the First event of the Project where the event is in the proper Group.
    private function getAnchorDateObject(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                in_array($dateResult['seventtype']['group_id'], self::EVENT_GROUP_ARRAY)) {
                return $dateResult;
            }
        }
        return 0;
    }

    private function renderReport()
    {
        //        Set report formats based on client region and preferences
        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(1.00),
                'marginRight' => Converter::inchToTwip(1.00),
                'marginTop' => Converter::inchToTwip(1.00),
                'marginBottom' => Converter::inchToTwip(1.00),
                'breakType' => 'continuous',
            ]
        );

        foreach ($this->getProjects() as $project) {
            $anchorDate = $this->getAnchorDateObject($project);

//      Program Title is section heading for each project
            if (!is_null($anchorDate['programtitle']) && trim($anchorDate['programtitle']) !== '') {
                $pageSection->addFormattedText(
                    $anchorDate['programtitle'],
                    $this->reportStyles->titleFont,
                    $defaultParagraph
                );
            }

//            Render all performance dates at the top of the section
            $this->renderPerformanceDates($project, $pageSection, $defaultFontBold, $defaultParagraph);

            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            if (!is_null($anchorDate['adates_marketing']['notes']) && trim(
                    $anchorDate['adates_marketing']['notes']
                ) !== '') {
                $pageSection->addFormattedText($anchorDate['adates_marketing']['notes'], $defaultFont);
                $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            }

            /** ensure program is in order
             * @see https://stackoverflow.com/a/2699159
             */
            $dateWorkArray = $anchorDate['adate_works'];
            usort(
                $dateWorkArray,
                function ($a, $b) {
                    return $a['work_order'] <=> $b['work_order'];
                }
            );
            foreach ($dateWorkArray as $dateWork) {
                if ($dateWork['l_encore'] == 0 && $dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                    $dateWorkTextRun = $pageSection->addTextRun($defaultParagraph);

                    if (!is_null($dateWork['swork']['scomposer']['name2']) && trim(
                            !empty($dateWork['swork']['scomposer']['name2'])
                        )) {
                        $composer = $dateWork['swork']['scomposer']['name2'];
                    } else {
                        $composer = $dateWork['swork']['scomposer']['lastname'];
                    }
                    $dateWorkTextRun->addFormattedText($composer, $defaultFontBold);
                    $dateWorkTextRun->addText(':  ', $defaultFont, $defaultParagraph);
                    if (!is_null($dateWork['title3']) && trim($dateWork['title3']) !== '') {
                        $workTitle = $dateWork['title3'];
                    } elseif (!is_null($dateWork['title2']) && trim($dateWork['title2']) !== '') {
                        $workTitle = $dateWork['title2'];
                    } else {
                        $workTitle = $dateWork['swork']['title1'];
                    }
                    $dateWorkTextRun->addFormattedText($workTitle, $defaultFont);
                }
            }

//            Orchestra, conductors, soloists after program
            $conductor = $this->datePerformerHelper->getConductor(
                $anchorDate['id'],
                $anchorDate['conductor_id'],
                0,
                0,
                ', ',
                '; ',
                0,
                true
            );
            $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
                $anchorDate['id'],
                '~~',
                0,
                0,
                0,
                ', ',
                '; ',
                null,
                null,
                false
            );
            $soloists = $this->datePerformerHelper->getSoloists(
                $anchorDate['id'],
                $anchorDate['conductor_id'],
                '~~',
                0,
                0,
                ', ',
                false,
                ' - ',
                '/',
                null
            );

            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
//            Render and suppress empty lines
//            Report makes a low-effort but mostly effective attempt to italicize the instrument name for
//            soloists and also italicize "conductor" for conductors. It will not work if the soloist does not have an instrument (i.e. a choir)
//              or if by chance the artist name or instrument contains a comma
            if ($anchorDate['orchestra_id'] > 0) {
                $pageSection->addFormattedText(
                    trim($anchorDate['orchestraaddress']['name2'] . ' ' . $anchorDate['orchestraaddress']['name1']),
                    $defaultFont,
                    $defaultParagraph
                );
            }


//            Soloists on Separate Lines
            if (!empty($soloists)) {
                $soloistArray = explode('~~', $soloists);
                foreach ($soloistArray as $soloist) {
                    $soloistTextRun = $pageSection->addTextRun($defaultParagraph);
                    $commaLocation = stripos($soloist, ',');
                    $soloistName = substr($soloist, 0, $commaLocation + 1);
                    $instrument = substr($soloist, $commaLocation + 1);
                    $soloistTextRun->addFormattedText($soloistName, $defaultFont);
                    $soloistTextRun->addFormattedText($instrument, $this->reportStyles->defaultFontItalic);
                }
            }
            if ($anchorDate['conductor_id'] > 0) {
                $conductorTextRun = $pageSection->addTextRun($defaultParagraph);
                $commaLocation = stripos($conductor, ',');
                $conductorName = substr($conductor, 0, $commaLocation + 1);
                $conductorTag = substr($conductor, $commaLocation + 1);
                $conductorTextRun->addFormattedText($conductorName, $defaultFont);
                $conductorTextRun->addFormattedText($conductorTag, $this->reportStyles->defaultFontItalic);
            }
//            Date-Work Conductors on separate lines
            if (!empty($dateWorkConductors)) {
                $dateWorkConductorArray = explode('~~', $dateWorkConductors);
                foreach ($dateWorkConductorArray as $dateWorkConductor) {
                    $dateWorkCondTextRun = $pageSection->addTextRun($defaultParagraph);
                    $commaLocation = stripos($dateWorkConductor, ',');
                    $dateWorkCondName = substr($dateWorkConductor, 0, $commaLocation + 1);
                    $dateWorkCondTag = substr($dateWorkConductor, $commaLocation + 1);
                    $dateWorkCondTextRun->addFormattedText($dateWorkCondName, $defaultFont);
                    $dateWorkCondTextRun->addFormattedText($dateWorkCondTag, $this->reportStyles->defaultFontItalic);
                }
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            //            TG: 2025-FEB - customization - saddresses.name3 is the "professional" or
//             program name for conductors, soloists, etc. Render these so client can decide to use them in the report
//            via copy/paste
            if (!is_null($anchorDate['conductoraddress']['name3']) && trim(
                    $anchorDate['conductoraddress']['name3']
                ) !== '') {
                $pageSection->addText(
                    'conductor professional name: ' . $anchorDate['conductoraddress']['name3'],
                    $defaultFont,
                    $defaultParagraph
                );
            }
            $soloistAltNames = $this->dateQueryHelper->getSoloistsForDateID($anchorDate['id'])->getQuery()->toArray();
            foreach ($soloistAltNames as $soloistAltName) {
                if (!is_null($soloistAltName['saddress']['name3']) && trim(
                        $soloistAltName['saddress']['name3']
                    ) !== '') {
                    $pageSection->addText(
                        'soloist professional name: ' . trim(
                            $soloistAltName['saddress']['name2'] . ' ' . $soloistAltName['saddress']['name1']
                        ) . ' to ' . $soloistAltName['saddress']['name3'],
                        $defaultFont,
                        $defaultParagraph
                    );
                }
            }

//            DATES MARKETING FIELDS
            if (!is_null($anchorDate['adates_marketing']['text_1']) && trim(
                    $anchorDate['adates_marketing']['text_1'] !== ''
                )) {
                $pageSection->addText(
                    self::SPONSOR . ' ' . $anchorDate['adates_marketing']['text_1'],
                    $defaultFont,
                    $defaultParagraph
                );
            }
            if (!is_null($anchorDate['adates_marketing']['text_2']) && trim(
                    $anchorDate['adates_marketing']['text_2'] !== ''
                )) {
                $pageSection->addText(
                    self::MEDIA . ' ' . $anchorDate['adates_marketing']['text_2'],
                    $defaultFont,
                    $defaultParagraph
                );
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        }
    }

    private function renderPerformanceDates($project, $pageSection, $defaultFontBold, $defaultParagraph)
    {
        $unicodeCircle = html_entity_decode('&#9679;', 0, 'UTF-8');
        foreach ($this->datesResult as $headerDate) {
            if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                && in_array($headerDate['seventtype']['group_id'], self::EVENT_GROUP_ARRAY)) {
                $headerDateTextRun = $pageSection->addTextRun($defaultParagraph);
                $headerDateTextRun->addText($headerDate['date_']->format('l, F j'), $defaultFontBold);
                $headerDateTextRun->addText(' ' . $unicodeCircle . ' ', $defaultFontBold);
                if (!is_null($headerDate['start_']) && (string)trim($headerDate['start_']) != '') {
                    $headerDateTextRun->addText($headerDate['start_']->format('g:i A'), $defaultFontBold);
                }
                $headerDateTextRun->addText(' ' . $unicodeCircle . ' ', $defaultFontBold);
                if (!is_null($headerDate['locationaddress']['text_1']) && trim(
                        $headerDate['locationaddress']['text_1']
                    ) !== '') {
                    $headerDateTextRun->addFormattedText($headerDate['locationaddress']['text_1'], $defaultFontBold);
                } else {
                    $headerDateTextRun->addFormattedText(
                        trim($headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1']),
                        $defaultFontBold
                    );
                }
            }
        }
    }
}
