<?php

namespace Customer\obf\reports\MonthCalendar;

use App\Reports\Report;
use Cake\Core\Configure;
use App\Reports\ReportWord;
use App\Reports\ReportsInterface;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\AddressQueryHelper;

/* This class formats each event / activity in the Calendar (each Calendar entry).
*   It returns the entry as a single string so formatting can be done in the main report
*  It is a separate class to make future edits and adaptations to other calendars easier
 *  to implement.
 */

class MonthCalendarEvents
{
// these are typically english-only projects that do not output in the report
    const SUPPRESSED_PROJECT_ADMIN = 'admin';
    const SUPPRESSED_PROJECT_MISC = 'misc';
    const SUPPRESSED_PROJECT_HOLD = 'hold';

    private $suppressedProject = [
        self::SUPPRESSED_PROJECT_ADMIN,
        self::SUPPRESSED_PROJECT_MISC,
        self::SUPPRESSED_PROJECT_HOLD
    ];

    /**
     * Helper class for the Events selected by user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Prepared SINGLE-ELEMENT array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * Helper class for Conductor, Venue and other addresses
     * @var AddressQueryHelper
     */
    private $addressQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;

    public function __construct($datesResult )
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->addressQueryHelper = new AddressQueryHelper();
        // Set the passed data
        $this->datesResult = $datesResult;
//        $this->repFileText = $repFileText;
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file. This allows for headings and other report text to output in a
    variety of languages

    public function getRepFileParams() {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }
*/

    public function getEventOutput($datesResult,  $repFileText)
    {
//        report titles and separators from REP file. if empty use constants
//        $repFileText = $this->getRepFileParams();
        if(!empty($repFileText['time_format'])) {
            $timeFormat = $repFileText['time_format'];
        } else {
            $timeFormat = Configure::read('Formats.time_short');
        }
//        Optional Start Time Format - this allows 12-hour time format to fit on one line
//        so the report renders: 10:00 - 12:30 PM
        $startTimeFormat = $repFileText['start_time_format'];
        $programNoPrefix = $repFileText['pgm_prefix'];
        $conductorPrefix = $repFileText['cond_prefix'];
        $conductorSuffix = $repFileText['cond_suffix'];
        $venuePrefix = $repFileText['venue_prefix'];
        $venueSuffix = $repFileText['venue_suffix'];
//        optional event separator; typically just a space
        $eventSeparator = $repFileText['event_separator'];


        $lPerf = $datesResult['seventtype']['l_performance'];

//        for non-performances, print start time with dedicated format so
//        12-hour systems can fit the entire time string on one line: 10:00 - 12:30 PM
        if ($lPerf == 1 && !empty($datesResult['start_'])) {
            $activityTime = $datesResult['start_']->format($timeFormat);
        } else {
            if ($lPerf == 0 && !empty($datesResult['start_'])) {
                $activityTime = $datesResult['start_']->format($startTimeFormat);
            }
        }
        if ($lPerf == 0 && !empty($datesResult['end_'])) {
            $activityTime .= ' - ' . $datesResult['end_']->format($timeFormat);
        }

        if(!is_null($datesResult['eventtype_id'])) {
            if (!is_null($datesResult['seventtype']['abbreviation']) && trim($datesResult['seventtype']['abbreviation'] != '')) {
                $activityName = $datesResult['seventtype']['abbreviation'];
            } elseif (!is_null($datesResult['seventtype']['code']) && trim($datesResult['seventtype']['code'] != '')) {
                $activityName = $datesResult['seventtype']['code'];
            } else {
                $activityName = $datesResult['seventtype']['name'];
            }
        }
        $activityName .= $datesResult['programno'] ? ' ' . $programNoPrefix . ' '. $datesResult['programno'] : '';

        if (!is_null($datesResult['project_id'])) {
            if (!is_null($datesResult['sproject']['code']) && $datesResult['sproject']['code'] !== '') {
                $projectOutput = $datesResult['sproject']['code'];
            } else {
                $projectOutput = $datesResult['sproject']['name1'];
            }

            if (in_array($projectOutput, $this->suppressedProject)) {
                $projectOutput = '';
            }
        } else {
            $projectOutput = ' ';
        }

//            single-item array for Conductor. Render either CODE or if empty, last name
        if (!is_null($datesResult['conductor_id'])) {
            $conductors = $this->addressQueryHelper->getAddresses($datesResult['conductor_id'])->getQuery()->toArray();
            foreach ($conductors as $conductor) {
                if(!empty($conductor['code'])) {
                $conductorName = trim($conductor['code']) ;
            } else {
                    $conductorName = $conductor['name1'];
                }
            }
        }

//            single-item array for Venue. Render either CODE or if empty, Name.
        if ($datesResult['location_id'] > 0) {
            $venues = $this->addressQueryHelper->getAddresses($datesResult['location_id'])->getQuery()->toArray(0);
            foreach ($venues as $venue) {
                    $venueName = trim($venue['code']) ?? trim($venue['name2'] . ' ' .  $venue['name1']);
                }
            }

//            assemble elements into string using standard output.
//          This arrangement of elements will change for other clients

        $activityOutput = $activityTime;
        $activityOutput .= $activityName ? ' ' . $activityName  : '';
        if(!empty($activityName) && !empty($projectOutput)) {
            $activityOutput .= $eventSeparator ;
        }
        $activityOutput .= ' ' . $projectOutput;
        $activityOutput .= $venueName ? ' ' . $venuePrefix . ' ' . $venueName . $venueSuffix : '';
        $activityOutput .= $conductorName ? ' ' .  $conductorPrefix . $conductorName . $conductorSuffix . ' ' : '';

        return trim($activityOutput);
    }


}
