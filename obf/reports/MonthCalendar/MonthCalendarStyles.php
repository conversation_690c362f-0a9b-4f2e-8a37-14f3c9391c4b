<?php

namespace Customer\obf\reports\MonthCalendar;

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Tab;

class MonthCalendarStyles
{

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(9);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->defaultFontBoldUnderline = clone($this->defaultFontBold);
        $this->defaultFontBoldUnderline->setUnderline('single');

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setBold(true);
        $this->titleFont->setSize(18);

        $this->programFont = clone($this->defaultFont);
        $this->programFont->setSize(8);

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(10);

        $this->performanceFont = clone($this->defaultFontBold);
        $this->performanceFont->setColor('ff1a1a');  // red

    }

    /**
     * Define the styles for the report - Paragraph | Table | Cell
     * this particular report has a crazy number of styles, required primarily to format the main Calendar table
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

//        used only in the one line under the main Calendar table
        $this->footerParagraph = array_merge(
            $this->defaultParagraph,
            [
                'tabs' => array(
                    new Tab('right', 13507, ' ') // 9.38 in
                )
            ]
        );


        $this->calendarTableStyle = array(
            'borderSize' => 6,
            'borderColor' => '000000',
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115,
        );

        $this->programTableStyle = array(
            'borderTopSize' => 12,
            'borderTopColor' => 'c7cfa3',
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115,
        );



        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

        $this->shadedCell = array_merge(
            $this->defaultCell,
            ['bgColor' => 'ECEFE0']
        );

        $this->leftColumnCell = array_merge(
            $this->shadedCell,
            [
                'borderLeftSize' => 6,
                'borderLeftColor' => '000000',
                'borderLeftStyle' => 'double',
                'valign' => 'center'
            ]
        );
        $this->rightColumnCell = array_merge(
            $this->shadedCell,
            [
                'borderRightSize' => 6,
                'borderRightColor' => '000000',
                'borderRightStyle' => 'double',
                'valign' => 'center'
            ]
        );
        $this->topRowCell = array_merge(
            $this->defaultCell,
            [
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
            ]
        );

//        these two cells are used only One Time Each - in the upper right and left
//        corners of the Calendar Table to make the borders run around the top corners
        $this->upperLeftCell = array_merge(
            $this->shadedCell,
            [
                'borderLeftSize' => 6,
                'borderLeftColor' => '000000',
                'borderLeftStyle' => 'double',
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
            ]
        );
        $this->upperRightCell = array_merge(
            $this->shadedCell,
            [
                'borderRightSize' => 6,
                'borderRightColor' => '000000',
                'borderRightStyle' => 'double',
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
            ]
        );

        $this->titleCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderBottomColor' => 'b3b6ba',
            'borderBottomSize' => 4
        );
    }

}
