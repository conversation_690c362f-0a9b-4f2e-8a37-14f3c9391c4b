<?php

namespace Customer\obf\reports\MonthCalendar;

use App\Reports\Report;
use App\Reports\ReportWord;

use Cake\Core\Configure;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;

use Customer\fasutilities\reports\utility\DateQueryHelper;

use Customer\fasutilities\reports\OnWord\OnWord;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;

use ReflectionException;


/* Report produces a month calendar by week - each week in the month is fully shown.
*  Left-most column contains the PWeek number, right-most the number of services in the week
*/

class MonthCalendar extends ReportWord
{

    /**
     * Helper class for the Events selected by user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;


    public function initialize()
    {
        parent::initialize();

        $this->dateQueryHelper = new DateQueryHelper();

        $this->reportStyles = new MonthCalendarStyles();
    }

    /*********************************************/

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * Get the EVENTS selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }


    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    private function renderReport()
    {
//        report title and column headings from .rep file
        $repFileText = $this->getRepFileParams();
        $weekColHeader = $repFileText['week_column'];
        $svcColHeader = $repFileText['service_column'];
        $programNoPrefix = $repFileText['pgm_prefix'];

//          default font and paragraph styles created in MonthCalendarStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        if (Configure::read('Formats.region') == 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
//          The left-most column or start of each week row
        if(Configure::read('Calendar.Weeks.FirstDayOfWeek') === 'Mo') {
            $firstDayOfWeek = 'Monday';
        } else {
            $firstDayOfWeek = 'Sunday';
        }

//        All events selected by user
        $datesResult = $this->datesResult;

//        grab the first Season ID to pass to the getDaysForSeasonWeek function below
        $firstDate = reset($datesResult);
        $seasonID = $firstDate['season_id'];

//      fetch each year.month combination among all dates selected by user
        foreach ($datesResult as $month) {
            $Yearmonth = $month['year'] . str_pad(
                    $month['month'],
                    2,
                    '0',
                    STR_PAD_LEFT
                ); // define the year-month combination
            $allMonths[] = $Yearmonth; // set those into a unique array;
            $allMonths = array_filter(array_unique($allMonths));
        }

//       loop through each unique month
        $m = 0;
        foreach ($allMonths as $eventMonth) {
            $programArray = [];
            $monthPrograms = [];

//            addSection has to be inside the Month Loop so two different section styles can be incorporated on the same page
            $pageSection = $this->onWord->addSection(
                [
                    'breakType' => 'continuous',
                    'paperSize' => $paperSize,
                    'orientation' => "landscape",
                    'marginLeft' => Converter::inchToTwip(1.00),
                    'marginRight' => Converter::inchToTwip(0.46),
                    'marginTop' => Converter::inchToTwip(0.25),
                    'marginBottom' => Converter::inchToTwip(0.34)
                ]
            );
//            output a page break between months, print the month header, establish the table for the calendar
            if ($m > 0) {
                $pageSection->addText('<w:p><w:r><w:br w:type="page"/></w:r></w:p>', $defaultFont, $defaultParagraph);
            }
            $dateTime = new \DateTime();
            $monthHeader = $dateTime->createFromFormat('Ymd', $eventMonth . '01');

            $pageSection->addText(
                $monthHeader->format('F Y'),
                $this->reportStyles->titleFont,
                $this->reportStyles->defaultParagraphCenter
            );
            $pageSection->addText(' ', $defaultFont, $defaultParagraph);
            $calendarTable = $pageSection->addTable($this->reportStyles->calendarTableStyle);

            $month = substr($eventMonth, -2);
            $calYear = substr($eventMonth, 0, 4);

//           Fetch the first day of the FIRST Week of the Month, given the client's start day (Sunday or Monday)
//           Fetch the first day of the LAST Week of the Month, given the client's start day (Sunday or Monday)
//            Carbon parses (for example): First Sunday of December 2021
//            First Day of the Week is taken from Settings.php
            $firstDayOfFirstWeek = Carbon::parse('First ' . $firstDayOfWeek . ' of ' . $monthHeader->format('F') . ' ' . $calYear);
            $firstDayOfLastWeek = Carbon::parse('Last ' . $firstDayOfWeek . ' of ' . $monthHeader->format('F') . ' ' . $calYear);
//            Given those dates, create a period consisting of each Sunday or each Monday in the month
//            This period will define each row or week within the month. It ensures that weeks that span more
//            than one season or one year or one adays.week value will still print completely
            $startOfEachWeekInTheMonth = CarbonPeriod::create($firstDayOfFirstWeek, CarbonInterval::weeks(1), $firstDayOfLastWeek);

            $w = 1;
//            loop through each start day / row in the Month
            foreach ($startOfEachWeekInTheMonth as $weekStartDay) {

//                Define the 7 days in the row: Start day of the week + 6 days.
//                Set the time to startOfDay to zero out the time portion of the date. This ensures we always add
//                or subtract a full day
                $weekEndDay = clone $weekStartDay->startOfDay();
                $weekEndDay = $weekEndDay->addDays(6);
                $daysOfTheWeek = $this->dateQueryHelper->getDaysFromRange($weekStartDay, $weekEndDay)->getQuery()->toArray();

//            on the First Week of the Month, render Header Row with days of the week
                if ($w === 1) {

                    $calendarTable->addRow();
                    $calendarTable->addCell(Converter::inchToTwip(0.51), $this->reportStyles->upperLeftCell)
                        ->addText($weekColHeader, $this->reportStyles->headerFont, $this->reportStyles->defaultParagraphCenter);
                    foreach ($daysOfTheWeek as $headerDay) {
                        $calendarTable->addCell(Converter::inchToTwip(1.21), $this->reportStyles->topRowCell)
                            ->addText(
                                strtoupper($headerDay['date_']->format('l')),
                                $this->reportStyles->headerFont,
                                $this->reportStyles->defaultParagraphCenter
                            );
                    }
                    $calendarTable->addCell(Converter::inchToTwip(0.44), $this->reportStyles->upperRightCell)
                        ->addText($svcColHeader, $this->reportStyles->headerFont, $this->reportStyles->defaultParagraphCenter);
                }

                $calendarTable->addRow(Converter::inchToTwip(0.85), array('atLeastHeight' => true));
//                PWeek number in the far left column and used below for service calculation
                $leftBorderDate = $this->dateQueryHelper->getDaysInfoForCalDay($weekStartDay)->getQuery()->toArray()[0];
                $pWeek = $leftBorderDate['pweek'];
                $calendarTable->addCell(Converter::inchToTwip(0.51), $this->reportStyles->leftColumnCell)
                    ->addText(
                        $pWeek,
                        $this->reportStyles->titleFont,
                        $this->reportStyles->defaultParagraphCenter
                    );

                $svcSum = 0;
                foreach ($daysOfTheWeek as $weekDay) {
                    $dayCell = $calendarTable->addCell(Converter::inchToTwip(1.21), $this->reportStyles->defaultCell);

//                    Calendar Day info - day of month, holiday, day text
                    if ($weekDay['month'] != intval($month)) {
                        $printDay = $weekDay['date_']->format('M j');
                    } else {
                        $printDay = $weekDay['date_']->format('j');
                    }
                    $dayCell->addText(
                        trim($printDay),
                        $this->reportStyles->defaultFontBold,
                        $this->reportStyles->defaultParagraphRight
                    );
                    if(!empty($weekDay['sholyday']['name'])) {
                        $dayCell->addFormattedText(
                            $weekDay['sholyday']['name'],
                            $this->reportStyles->defaultFontItalic,
                            $this->reportStyles->defaultParagraphRight
                    );
                    }
                    if (!empty($weekDay['text_'])) {
                        $dayCell->addFormattedText(
                            htmlspecialchars($weekDay['text_']),
                            $defaultFont,
                            $this->reportStyles->defaultParagraphRight
                        );
                    }

//                    Events on that Day via the MonthCalendarEvents class
                    foreach ($this->datesResult as $event) {

                        if ($event['date_']->format('Ymd') === $weekDay['date_']->format('Ymd')) {

                            $svcSum += $event['duties'];

                            $activity = new MonthCalendarEvents($event);
                            $activityOutput = $activity->getEventOutput($event, $repFileText);
                            if ($event['seventtype']['l_performance'] == 1) {
                                $dayCell->addFormattedText(
                                    $activityOutput,
                                    $this->reportStyles->defaultFontBold,
                                    $defaultParagraph
                                );
                            } else {
                                $dayCell->addFormattedText(
                                    $activityOutput,
                                    $defaultFont,
                                    $defaultParagraph
                                );
                            }

//                      if the event is a Performance, create an ad-hoc array with the info required to print programs for each month
//                      this array is sent to the MonthCalendarPrograms class
                            if ($event['seventtype']['l_performance'] == 1) {
                                $programArray['dateID'] = $event['id'];
                                $programArray['project'] = $event['sproject']['name'];
                                $programArray['pgmNo'] = $event['programno'];
                                $programArray['pgmTitle'] = $event['programtitle'];
                                $programArray['condID'] = $event['conductor_id'];
                                $programArray['orchID'] = $event['orchestra_id'];
                                $programArray['pweek'] = $event['pweek'];
                            }
                            $monthPrograms[] = $programArray;

                            // blank line between events
                            $dayCell->addText('', $defaultFont, $defaultParagraph);
                        }
                    }
                }

                $weekSvc = $svcSum > 0 ? $svcSum : '';
                $calendarTable->addCell(Converter::inchToTwip(0.44), $this->reportStyles->rightColumnCell)
                    ->addText($weekSvc, $this->reportStyles->headerFont, $this->reportStyles->defaultParagraphCenter);

                $w++;
            }


//            add page break before program  section
            $pageSection->addText('<w:p><w:r><w:br w:type="page"/></w:r></w:p>', $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);

            $programTable = new MonthCalendarPrograms(
                array_unique(array_filter($monthPrograms), SORT_REGULAR),
                $pageSection
            );

//            send the Program Number prefix from the report .rep file for rendering in project header
            $programTable->render($programNoPrefix);

            $m++;
        }
    }

}
