<?php

namespace Customer\obf\reports\AdatesPianoTuningSchedule;

use App\Reports\ReportsInterface;
use App\Reports\ReportWord;
use Cake\Datasource\ConnectionManager;
use Customer\fasutilities\reports\OnWord\OnWord;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use ReflectionException;

/*
 *  JH 20240312 ONCUST-1998
 * TG: 20240401 - modified to become Standard North American report -
 *    change cell margin, font, add keyboard instruments to include
 *
 */

class AdatesPianoTuningSchedule extends ReportWord
{

const INSTRUMENTS_TO_OUTPUT = "('piano','harpsichord','continuo')";
    /**
     * Helper class for each event selected by the user
     * @var DateQueryHelper
     */
    private $dateTable;
    private $dbConn;


    // projects filled in .Collect()
    private $monthsResult;

        /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;

    private $repFileText;

    private $dateIDs;

    public function initialize()
    {
        parent::initialize();

        $this->dbConn = ConnectionManager::get("default");
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = []): ReportsInterface
    {

        $this->dateIDs = $this->getRequest()->getData()['dataItemIds'];
        $dateIDsList = implode(',', $this->dateIDs);

        $collectQuery = <<<SQL
            SELECT DISTINCT 
                adates.year, adates.month, adates.planninglevel
            FROM adates
            WHERE adates.id IN ($dateIDsList)
            ORDER BY adates.year, adates.month
            SQL;

        $this->monthsResult = $this->dbConn
            ->Query($collectQuery)
            ->fetchAll('assoc');

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    public function renderReport()
    {

        //  headers/labels from rep file
        $this->repFileText = $this->getRepFileParams();
//    Type the instruments to be included in the search/output. Use lower-case
        $instrumentsToOutput = "('piano','harpsichord','continuo')";

        $fontName = 'Calibri';

        $this->onWord->setDefaultParagraphStyle(
            [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0
            ]
        );
        $this->onWord->setDefaultFontName($fontName);
        $this->onWord->setDefaultFontSize(11);

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => 'Letter',
                'orientation' => 'portrait',
                'marginLeft' => Converter::inchToTwip(0.50),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::cmToTwip(0.50),
                'breakType' => 'continuous',
            ]
        );
        $printWidthInch = 8.5 - 0.5 - 0.5;

        $defaultFont = new Font($fontName);
        $defaultFont->setName();
        $defaultFont->setSize(11);

        $fontTitle = clone($defaultFont);
        $fontTitle->setSize(18);
        $fontTitle->setBold();

        $fontHeader = clone($defaultFont);
        $fontHeader->setBold();

        $fontPrint = clone($defaultFont);
        $fontPrint->setSize(9);

        $fontInfo = clone($defaultFont);
        $fontInfo->setSize(9);

        $fontMonth = clone($defaultFont);
        $fontMonth->setSize(14);
        $fontMonth->setBold();

        $monthFormat = 'F, Y';
        $dateFormat = 'l, M j';
        $timeFormat = 'g:i a';

        $paragraphLeft = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,
            'alignment' => 'left'
        ];
        $paragraphRight = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,
            'alignment' => 'right'
        ];

        $tableStyle = [
            'unit' => TblWidth::TWIP,
            'cellMarginLeft' => 115.20, // Word default 0.08 inches in twips
            'cellMarginRight' => 115.20, // Word default 0.08 inches in twips
            'cellMarginTop' => 20,
            'cellMarginBottom' => 20
        ];

        $eventCellStyle = [
            'borderTopColor' => '000000',
            'borderTopSize' => 1,
            'borderBottomColor' => '000000',
            'borderBottomSize' => 1
        ];

        //  column widths in inches
        $colWidthDate = 1.39;
        $colWidthStart = 0.94;
        $colWidthActivity = 1.13;
        $colWidthVenue = 1.50;
        $colWidthInstrument = $printWidthInch - $colWidthDate - $colWidthStart - $colWidthActivity - $colWidthVenue;

        //  page header
        $header = $pageSection->addheader();
        $table = $header->addTable(
            array_merge($tableStyle, ['width' => Converter::inchToTwip($printWidthInch)])
        );
        $table->addRow();
        $cell = $table->addCell(Converter::inchToTwip($printWidthInch * 2 / 3));
        $cell->addText(
            $this->repFileText['report_title'],
            $fontTitle,
            $paragraphLeft
        );
        $cell = $table->addCell(Converter::inchToTwip($printWidthInch / 3));
        $cell->addText(
            'Printed ' .  date('j F Y'),
            $fontPrint,
            $paragraphRight
        );
        $header->addTextBreak(1);
        $table = $header->addTable(
            array_merge($tableStyle, ['width' => Converter::inchToTwip($printWidthInch)])
        );
        $table->addRow();
        $cell = $table->addCell(Converter::inchToTwip($colWidthDate),
            ['borderBottomColor' => '000000', 'borderBottomSize' => 10]
        );
        $cell->addText(
            'Date',
            $fontHeader,
            $paragraphLeft
        );
        $cell = $table->addCell(Converter::inchToTwip($colWidthStart),
            ['borderBottomColor' => '000000', 'borderBottomSize' => 10]
        );
        $cell->addText(
            'Time*',
            $fontHeader,
            $paragraphLeft
        );
        $cell = $table->addCell(Converter::inchToTwip($colWidthActivity),
            ['borderBottomColor' => '000000', 'borderBottomSize' => 10]
        );
        $cell->addText(
            'Activity',
            $fontHeader,
            $paragraphLeft
        );
        $cell = $table->addCell(Converter::inchToTwip($colWidthVenue),
            ['borderBottomColor' => '000000', 'borderBottomSize' => 10]
        );
        $cell->addText(
            'Venue',
            $fontHeader,
            $paragraphLeft
        );
        $cell = $table->addCell(Converter::inchToTwip($colWidthInstrument),
            ['borderBottomColor' => '000000', 'borderBottomSize' => 10]
        );
        $cell->addText(
            'Instrument Used',
            $fontHeader,
            $paragraphLeft
        );
        $header->addTextBreak(1);

        //  page footer
        $footer = $pageSection->addFooter();
        $table = $footer->addTable(
            array_merge($tableStyle, ['width' => Converter::inchToTwip($printWidthInch)])
        );
        $table->addRow();
        $cell = $table->addCell(Converter::inchToTwip($printWidthInch * 2 / 3), ['valign' => 'bottom']);
        if ($this->repFileText['orchestra_info1']) {
            $cell->addText(
                $this->repFileText['orchestra_info1'],
                $fontInfo,
                $paragraphLeft
            );
        }
        if ($this->repFileText['orchestra_info2']) {
            $cell->addText(
                $this->repFileText['orchestra_info2'],
                $fontInfo,
                $paragraphLeft
            );
        }
        if ($this->repFileText['orchestra_info3']) {
            $cell->addText(
                $this->repFileText['orchestra_info3'],
                $fontInfo,
                $paragraphLeft
            );
        }
        $cell = $table->addCell(Converter::inchToTwip($printWidthInch / 3));
//        To use client logo:  [1] put the jpg file into the same folder as the php file
//         [2] put the name of the jpg file into the report .rep file using the tag below
//         [3] uncomment this line and adjust the width
//        $cell->addImage(
//            dirname(__FILE__) . DS . $this->repFileText['orchestra_logo'],
//            ['width' => Converter::inchToPoint($printWidthInch / 3)]
//        );
        $cell->addText('', $fontInfo, $paragraphLeft);

        $firstMonth = true;

        foreach ($this->monthsResult as $monthYear) {

            $year = $monthYear['year'];
            $month = $monthYear['month'];
            $planninglevel = $monthYear['planninglevel'];

            $collectQuery = <<<SQL
                SELECT
                    adates.id, adates.date_, adates.start_,
                    seventtypes.name as activity,
                    LTRIM(concat_ws(' ', locations.name2, locations.name1)) as venue
                FROM adates
                    LEFT JOIN seventtypes on adates.eventtype_id = seventtypes.id
                    LEFT JOIN saddresses locations ON adates.location_id = locations.id
                WHERE adates.year = $year
                    AND adates.month = $month
                    AND adates.planninglevel = $planninglevel
                    AND (
                        adates.id IN
                            (SELECT adate_works.date_id
                                FROM adate_works
                                    INNER JOIN adatework_keyboards ON adate_works.id = adatework_keyboards.datework_id 
                                INNER JOIN sinstrinstruments ON adatework_keyboards.instrument_id = sinstrinstruments.id
                                WHERE LOWER(sinstrinstruments.name) IN $instrumentsToOutput
                            )
                        OR adates.id IN
                            (SELECT adate_works.date_id
                                FROM adate_works
                                    INNER JOIN adatework_soloists ON adate_works.id = adatework_soloists.datework_id
                                    INNER JOIN sinstrinstruments ON adatework_soloists.instrument_id = sinstrinstruments.id
                                    INNER JOIN sinstrsections ON sinstrinstruments.section_id = sinstrsections.id
                                WHERE sinstrsections.syssection_id = 17 
                                AND LOWER(sinstrinstruments.name) IN $instrumentsToOutput
                            )
                        )
                ORDER BY adates.date_, adates.start_
                SQL;

            $events = $this->dbConn
                ->Query($collectQuery)
                ->fetchAll('assoc');

            if (! $firstMonth) {
                $pageSection->addTextBreak(1);
            } else {
                $firstMonth = false;
            }

            $pageSection->addText(
                date($monthFormat, strtotime($events[0]['date_'])),
                $fontMonth,
                $paragraphLeft
            );
            $pageSection->addTextBreak(1);

            $table = $pageSection->addTable(
                array_merge($tableStyle, ['width' => Converter::inchToTwip($printWidthInch)])
            );

            foreach ($events as $event) {
                
                $table->addRow(null,  ['cantSplit' => true]);

                $cell = $table->addCell(Converter::inchToTwip($colWidthDate), $eventCellStyle);
                $cell->addText(
                    date($dateFormat, strtotime($event['date_'])),
                    $defaultFont,
                    $paragraphLeft
                );

                $cell = $table->addCell(Converter::inchToTwip($colWidthStart), $eventCellStyle);
                $cell->addText(
                    date($timeFormat, strtotime($event['start_'])),
                    $defaultFont,
                    $paragraphLeft
                );

                $cell = $table->addCell(Converter::inchToTwip($colWidthActivity), $eventCellStyle);
                $cell->addText(
                    $event['activity'],
                    $defaultFont,
                    $paragraphLeft
                );

                $cell = $table->addCell(Converter::inchToTwip($colWidthVenue), $eventCellStyle);
                $cell->addText(
                    $event['venue'],
                    $defaultFont,
                    $paragraphLeft
                );

                $cell = $table->addCell(Converter::inchToTwip($colWidthInstrument), $eventCellStyle);

                $dateID = $event['id'];

                $collectQuery = <<<SQL
                SELECT DISTINCT 
                    adatework_keyboards.number_,
                    sinstrinstruments.name as instrument
                FROM adate_works
                    INNER JOIN adatework_keyboards ON adate_works.id = adatework_keyboards.datework_id
                    INNER JOIN sinstrinstruments ON adatework_keyboards.instrument_id = sinstrinstruments.id
                WHERE LOWER(sinstrinstruments.name) IN $instrumentsToOutput AND adate_works.date_id = $dateID 
                SQL;
                $instruments = $this->dbConn
                    ->Query($collectQuery)
                    ->fetchAll('assoc');
                foreach ($instruments as $instr) {
                    $text = ($instr['number_'] == 1 ? '' : $instr['number_'].' ')
                            . 'Orchestral '
                            . $instr['instrument'];
                    $cell->addText(
                        $text,
                        $defaultFont,
                        $paragraphLeft
                    );
                }

                $collectQuery = <<<SQL
                SELECT DISTINCT 
                    ltrim(concat_ws(' ', soloists.name2, soloists.name1)) as soloist,
                    sinstrinstruments.name as instrument
                FROM adate_works
                    INNER JOIN adatework_soloists ON adate_works.id = adatework_soloists.datework_id
                    INNER JOIN sinstrinstruments ON adatework_soloists.instrument_id = sinstrinstruments.id
                    INNER JOIN sinstrsections ON sinstrinstruments.section_id = sinstrsections.id
                    INNER JOIN saddresses soloists ON adatework_soloists.artist_id = soloists.id 
                WHERE adate_works.date_id = $dateID
                    AND sinstrsections.syssection_id = 17
                SQL;
                $instruments = $this->dbConn
                    ->Query($collectQuery)
                    ->fetchAll('assoc');
                foreach ($instruments as $instr) {
                    $text = 'Solo '
                        . $instr['instrument']
                        . ' (' . $instr['soloist'] . ')';
                    $cell->addText(
                        $text,
                        $defaultFont,
                        $paragraphLeft
                    );
                }


            }   //  $events loop



        } //    monthsResult loop

        $pageSection->addTextBreak(1);
        $pageSection->addText(
            '* Note that start times are those of the Activity, and not those of the tuning',
            $defaultFont,
            $paragraphLeft
        );

    }

}
