<?php

namespace Customer\obf\reports\OBFContractArtistOffer;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;
use Customer\fasutilities\reports\utility\ContractSoloQueryHelper;
use Customer\fasutilities\reports\utility\DateQueryHelper; 
use Customer\fasutilities\reports\utility\NameFormatHelper;
use Customer\fasutilities\reports\utility\ExpenseQueryHelper;

use Cake\Core\Configure;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Recreation of OPAS Classic Report. Custom OBF Version
 * Artist Offer letter sent prior to the actual formal contract
 * Includes contract info, clauses and fixed text
 *
 * Update 2023 Dec: Combine Project and Programs into one section
 * Update 2024 Sept: re-order to: Project - clauses - schedule - fee
 */

class OBFContractArtistOffer extends ReportWord
{
    const PGM_COL_1_WIDTH = 0.50;
    const PGM_COL_2_WIDTH = 1.86;
    const PGM_COL_3_WIDTH = 4.64;

    const VENUE_COL_2_WIDTH = 3.00;

    const SCHEDULE_COL_1_WIDTH = 0.50;
    const SCHEDULE_COL_2_WIDTH = 1.86;
    const SCHEDULE_COL_3_WIDTH = 1.50;
    const SCHEDULE_COL_4_WIDTH = 1.86;
    const SCHEDULE_COL_5_WIDTH = 1.28;

    const FEE_COL_1_WIDTH = 0.50;
    const FEE_COL_2_WIDTH = 1.50;
    const FEE_COL_3_WIDTH = 2.50;
    const FEE_COL_4_WIDTH = 2.50;


    const TBD = 'TBD';
    const CAPACITY = 'Capacity:';

//    Event Type GROUPS and Event Types for all ancillary events
    const EDUCATION_GROUP_ID = 10;
    const PHILANTHROPY_GROUP_ID = 7;
    const OPERATIONS_GROUP_ID = 5;
    const RECEPTION_ID = 8;

    const US_VISA_CLAUSE_TYPE = 1;
    const WORKSCOPE_CLAUSE_TYPE = 13;


    /**
     * helper class for contracts selected by user
     * @var ContractSoloQueryHelper
     */
    private $contractSoloQueryHelper;

    /**
     * helper class for event details and date range formatting
     * @var DateQueryHelper
     */
    private $dateQueryHelper;


    /**
     * Prepared array for the rendering
     * @var array
     */
    private $contractsResult;
    /**
     * Helper for name formats
     * @var NameFormatHelper
     */
    private $nameFormatHelper;
    /**
     * Helper for the Expenses
     * @var ExpenseQueryHelper
     */
    private $expenseQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();
        // Set query helpers
        $this->contractSoloQueryHelper = new ContractSoloQueryHelper();
        $this->dateQueryHelper = new DateQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->expenseQueryHelper = new ExpenseQueryHelper();

        $this->reportStyles = new OBFContractArtistOfferStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file. This allows for headings and other report text to output in a
    variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the CONTRACT RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface
    {
        $contractQuery = $this->contractSoloQueryHelper
            ->getContracts($this->getRequest()->getData()['dataItemIds'])
            ->withDateDetail()
            ->withAgentContact()
            ->withItinerary()
            ->withClauses()
            ->withExpenses()
            ->getQuery();

        $this->contractsResult = $contractQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    public function renderReport()
    {
        foreach ($this->contractsResult as $contract) {
//        report title and column headings from .rep file
            $repFileText = $this->getRepFileParams();
            $customerName = Configure::read('CustomerSettings.name');
            $scheduleDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read(
                    'Formats.date'
                );
            $scheduleTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read(
                    'Formats.time'
                );


            // default font and paragraph styles created in WorkListStyles class
            $defaultFont = $this->reportStyles->defaultFont;
            $defaultParagraph = $this->reportStyles->defaultParagraph;
            $borderCell = $this->reportStyles->borderCell;
            $unicodeCircle = html_entity_decode('&#9679;', 0, 'UTF-8');

            if (Configure::read('Formats.region') == 'North America') {
                $paperSize = "Letter";
            } else {
                $paperSize = "A4";
            }

            $pageSection = $this->onWord->addSection(
                [
                    'paperSize' => $paperSize,
                    'marginLeft' => Converter::inchToTwip(1.00),
                    'marginRight' => Converter::inchToTwip(0.75),
                    'marginTop' => Converter::inchToTwip(0.50),
                    'marginBottom' => Converter::inchToTwip(0.50),
                    'breakType' => 'continuous',
                ]
            );

//      +++ RENDER REPORT TITLE +++
            $pageSection->addImage(
                dirname(__FILE__) . DS . 'assets' . DS . 'obf_logo.jpg',
                array(
                    'width' => Converter::inchToPixel(2.10),
                    'alignment' => 'center'
                )
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            $pageSection->addText(
                $contract['sseason']['name'] . ' | ' . $repFileText['report_title'],
                $this->reportStyles->titleFont,
                $this->reportStyles->defaultParagraphCenter
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//          +++ RENDER TOP SECTION OF REPORT +++
//            Use Text Runs to mix fonts
            $artistSection = $pageSection->addTextRun($defaultParagraph);
            $artistSection->addText($repFileText['artist'], $this->reportStyles->defaultFontBold);
            $artistSection->addText('  ', $defaultFont);
            $artistName = $this->nameFormatHelper->getEntityName(
                $contract['artistaddress']['name1'],
                $contract['artistaddress']['name2'],
                1
            );
            $artistSection->addText($artistName, $defaultFont);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $roleSection = $pageSection->addTextRun($defaultParagraph);
            $roleSection->addText($repFileText['role'], $this->reportStyles->defaultFontBold);
            $roleSection->addText('  ', $defaultFont);
            $roleSection->addText($contract['sinstrinstrument']['name'], $defaultFont);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//            TG: 2024-06-03 - client adds "Scope of Work" contract clause between "Medium" and Projects section
            foreach ($contract['acontract_clauses'] as $clause) {
                if ($clause['scontractclause']['clausetype_id'] === self::WORKSCOPE_CLAUSE_TYPE) {
                    if (!is_null($clause['text']) && (string)trim($clause['text']) !== '') {
                        $clauseContents = $clause['text'];
                    } else {
                        $clauseContents = $clause['scontractclause']['text'];
                    }

                    $workScopeTextRun = $pageSection->addTextRun($defaultParagraph);
                    $workScopeTextRun->addText($repFileText['scope'], $this->reportStyles->defaultFontBold);
                    $workScopeTextRun->addText('  ', $defaultFont);
                    $workScopeTextRun->addFormattedText($clauseContents, $defaultFont);
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                }
            }

//            Render ALL Projects within the Contract
//            REMOVED December 2023; this section merged with programs as per client request
//            REINSTATED September 2024 - output project names BEFORE clauses
//            REMOVED AGAIN November 2024
//            $this->renderProjects(
//                $contract,
//                $repFileText,
//                $pageSection,
//                $defaultFont,
//                $defaultParagraph
//            );
//            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//            +++ RENDER PROJECT + PROGRAM SECTION +++
//              fetch all anchor dates (contract may contain multiple projects
            $pageSection->addText($repFileText['program'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $concerts = $this->getAnchorDates($contract);
            if (!empty($concerts)) {
                foreach ($concerts as $concert) {  // each $concert is a date object

//                    ensure date-works are in the proper order
                    $dateWorkArray = $concert['adate_works'];
                    usort(
                        $dateWorkArray,
                        function ($a, $b) {
                            return $a['work_order'] <=> $b['work_order'];
                        }
                    );

//                    Render Project Name and Program Title above program
                    $projectName = $concert['sproject']['name'];
                    if (!is_null($concert['programtitle']) && trim($concert['programtitle']) !== '') {
                        $projectName .= ' - ' . $concert['programtitle'];
                    }

                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $pageSection->addFormattedText("\t" . $projectName, $defaultFont, $defaultParagraph);
//                  Render program table
//                    TG - 2024-Nov. change program output from table to bullet list. Keep code intact because client
//                    changes their mind frequently
//                    $programTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    foreach ($dateWorkArray as $dateWork) {
                        if ($dateWork['swork']['l_intermission'] == 0) {
                            $composerName = $dateWork['swork']['scomposer']['lastname'];
                            $composerName .= $dateWork['arrangement'] ? ' (' . $dateWork['arrangement'] . ')' : '';
                            $composerName .= ': ';
                        } else {
                            $composerName = '';
                        }
                        if (strlen(trim($dateWork['title2'])) > 0) {
                            $workTitle = $dateWork['title2'];
                        } else {
                            $workTitle = $dateWork['swork']['title1'];
                        }
                        $pageSection->addFormattedText(
                            "\t" . "  " . $unicodeCircle . "  " . trim($composerName . $workTitle),
                            $defaultFont,
                            $defaultParagraph
                        );
                    }
                }
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//           +++ RENDER PERFORMANCE AND REHEARSAL SCHEDULE +++
            $pageSection->addText($repFileText['performance'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $this->renderPerformances(
                $contract,
                $pageSection,
                $defaultFont,
                $defaultParagraph,
                $borderCell,
                $scheduleDateFormat,
                $scheduleTimeFormat
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $pageSection->addText($repFileText['rehearsal'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $this->renderRehearsals(
                $contract,
                $pageSection,
                $defaultFont,
                $defaultParagraph,
                $borderCell,
                $scheduleDateFormat,
                $scheduleTimeFormat
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            RENDER OTHER TYPES OF EVENTS IN THE CONTRACT
            $pageSection->addText($repFileText['education'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $this->renderEducationEvents(
                $contract,
                $pageSection,
                $defaultFont,
                $defaultParagraph,
                $borderCell,
                $scheduleDateFormat,
                $scheduleTimeFormat
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $pageSection->addText(
                $repFileText['patron_events'],
                $this->reportStyles->defaultFontBold,
                $defaultParagraph
            );
            $this->renderPhilanthropyEvents(
                $contract,
                $pageSection,
                $defaultFont,
                $defaultParagraph,
                $borderCell,
                $scheduleDateFormat,
                $scheduleTimeFormat
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            RENDER VENUE INFORMATION
            $pageSection->addText($repFileText['location'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $this->renderVenues($contract, $pageSection, $defaultFont, $defaultParagraph);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


            //            RENDER CONTRACT CLAUSES
//            Several clauses may belong to the TYPE: US Work Visa. That heading should print only one time
//            TG: 2024-06-03 - client removes clauses of TYPE = "Scope of Work" from this section. they now print above

            $printedVisaHeading = false;
            foreach ($contract['acontract_clauses'] as $clause) {
                if ($clause['scontractclause']['clausetype_id'] !== self::WORKSCOPE_CLAUSE_TYPE) {
                    if (!is_null($clause['scontractclause']['name2']) && (string)trim(
                            $clause['scontractclause']['name2']
                        ) !== '') {
                        $clauseName = $clause['scontractclause']['name2'];
                    } else {
                        $clauseName = $clause['scontractclause']['name'];
                    }

                    $clauseSectionTextRun = $pageSection->addTextRun($defaultParagraph);
                    $clauseSectionTextRun->addText($clauseName, $this->reportStyles->defaultFontBold);
                    $clauseSectionTextRun->addText(':  ', $defaultFont);

//            TG: 2024-Sept - comment out part that suppresses clause name printing
//                    if ($clause['scontractclause']['clausetype_id'] === self::US_VISA_CLAUSE_TYPE && $printedVisaHeading === true) {
//                        $clauseSectionTextRun->addText('', $defaultFont);
//                    } elseif ($clause['scontractclause']['clausetype_id'] === self::US_VISA_CLAUSE_TYPE && $printedVisaHeading === false) {
//                        $clauseSectionTextRun->addText($clauseName, $this->reportStyles->defaultFontBold);
//                        $clauseSectionTextRun->addText(':  ', $defaultFont);
//                    } else {
//                        $clauseSectionTextRun->addText($clauseName, $this->reportStyles->defaultFontBold);
//                        $clauseSectionTextRun->addText(':  ', $defaultFont);
//                    }

                    if (!is_null($clause['text']) && (string)trim($clause['text']) !== '') {
                        $clauseSectionTextRun->addFormattedText($clause['text'], $defaultFont);
                    } else {
                        $clauseSectionTextRun->addFormattedText($clause['scontractclause']['text'], $defaultFont);
                    }
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                If the Clause is of the type "U.S. Work Visa" then set the flag to TRUE so that
//                heading does not print again
//                    if ($clause['scontractclause']['clausetype_id'] === self::US_VISA_CLAUSE_TYPE) {
//                        $printedVisaHeading = true;
//                    }
                }
            }

//            RENDER FEE in table
            $pageSection->addText($repFileText['fee'], $this->reportStyles->defaultFontBold, $defaultParagraph);
            $this->renderFee($contract, $pageSection, $repFileText, $defaultFont, $defaultParagraph, $borderCell);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


            /*            REMOVED - client seems to have this info in clauses
                        $visaSection = $pageSection->addTextRun($defaultParagraph);
                        $visaSection->addText($repFileText['visa'], $this->reportStyles->defaultFontBold, $defaultParagraph);
                        $visaSection->addText(' ', $defaultFont, $defaultParagraph);
                        $visaSection->addText($contract['text_2'], $defaultFont, $defaultParagraph);
                        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            */


//            RENDER FIXED TEXT
            $fixedText = new OBFContractArtistOfferFixedText($pageSection);
            $fixedText->renderClauses();
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//            RENDER DUE DATES
            $createdSection = $pageSection->addTextRun($defaultParagraph);
            $createdSection->addText($repFileText['created'], $this->reportStyles->defaultFontBold);
            $createdSection->addText(' ', $defaultFont);
            $createdSection->addText(date("l, F j, Y"), $defaultFont);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $dueSection = $pageSection->addTextRun($defaultParagraph);
            $dueSection->addText($repFileText['due'], $this->reportStyles->defaultFontBold);
            $dueSection->addText(' ', $defaultFont);
            $dueSection->addText(
                date("l, F j, Y", strtotime("+7 days")),
                $defaultFont
            );
            $dueSection->addTextBreak(2, $defaultFont);
            $dueSection->addText($repFileText['accept'], $this->reportStyles->defaultFontBold);
            $dueSection->addText(' ', $defaultFont);
            if (!is_null($contract['date_1']) && (string)trim($contract['date_1']) !== '') {
                $dueSection->addText($contract['date_1']->format("l, F j, Y"), $defaultFont);
            };
        }
    }

    private function renderProjects(
        $contract,
        $repFileText,
        $pageSection,
        $defaultFont,
        $defaultParagraph
    ) {
        $projects = [];
        $pageSection->addText($repFileText['program'], $this->reportStyles->defaultFontBold, $defaultParagraph);
        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        //        make sure contract dates are in chron order
        $contractDates = $contract['acontract_dates'];
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'];
            }
        );
        foreach ($contractDates as $contractDate) {
            $projectName = $contractDate['adate']['sproject']['name'];
            if (!is_null($contractDate['adate']['programtitle']) && trim(
                    $contractDate['adate']['programtitle']
                ) !== '') {
                $projectName .= ' - ' . $contractDate['adate']['programtitle'];
            }
            $projects[] = $projectName ?? ' ';
        }
        $projects = array_unique($projects);
        foreach ($projects as $project) {
            $pageSection->addText("\t" . htmlspecialchars($project), $defaultFont, $defaultParagraph);
        }
    }

    private function getAnchorDates($contract): array
    {
//        return FIRST PERFORMANCE in the Contract - FOR EACH PROJECT
//            this is the anchor date used to render the program
        $contractDates = $contract['acontract_dates'];
        $peformanceDateArray = [];
        $projectId = 0;

//        make sure contract dates are in chron order
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'];
            }
        );

//    Extract the first performance for each Project
        foreach ($contractDates as $perf) {
            if ($perf['adate']['seventtype']['l_performance'] == 1 && $perf['adate']['project_id'] !== $projectId) {
                $peformanceDateArray[] = $perf['adate'];
                $projectId = $perf['adate']['project_id'];
            }
        }

        return $peformanceDateArray;
    }


    private function renderPerformances(
        $contract,
        $pageSection,
        $defaultFont,
        $defaultParagraph,
        $borderCell,
        $scheduleDateFormat,
        $scheduleTimeFormat
    ) {
        $contractDates = $contract['acontract_dates'];
//        get dates in proper order
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'] . $b['adate']['start_'];
            }
        );


        $performanceTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($contractDates as $contractDate) {
            if ($contractDate['adate']['seventtype']['l_performance'] == 1
                && $contractDate['adate']['seventtype']['id'] !== self::RECEPTION_ID
                && $contractDate['adate']['seventtype']['group_id'] !== self::EDUCATION_GROUP_ID
                && $contractDate['adate']['seventtype']['group_id'] !== self::PHILANTHROPY_GROUP_ID) {
                $performanceTable->addRow();

//                COLUMN 1
                $performanceTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                )
                    ->addText('', $defaultFont, $defaultParagraph);
//                COLUMN 2
                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_2_WIDTH), $borderCell)
                    ->addText(
                        $contractDate['adate']['date_']->format($scheduleDateFormat),
                        $defaultFont,
                        $defaultParagraph
                    );
//                COLUMN 3
                if (!is_null(
                    $contractDate['adate']['start_'] && (string)trim($contractDate['adate']['start_']) !== ''
                )) {
                    $concertStart = $contractDate['adate']['start_']->format($scheduleTimeFormat);
                    if (!is_null(
                        $contractDate['adate']['end_'] && (string)trim($contractDate['adate']['end_']) !== ''
                    )) {
                        $concertStart .= ' - ' . $contractDate['adate']['end_']->format($scheduleTimeFormat);
                    }
                } else {
                    $concertStart = '';
                }

                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_3_WIDTH), $borderCell)
                    ->addText(
                        $concertStart,
                        $defaultFont,
                        $defaultParagraph
                    );

//                COLUMN 4
                $projectCell = $performanceTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_4_WIDTH),
                    $borderCell
                );
                $projectCellTextRun = $projectCell->addTextRun($defaultParagraph);
                $projectCellTextRun->addText($contractDate['adate']['sproject']['name'], $defaultFont);
                $projectCellTextRun->addTextBreak(1, $defaultFont);
                $projectCellTextRun->addFormattedText(
                    $contractDate['adate']['seventtype']['name'],
                    $defaultFont,
                    $defaultParagraph
                );


//                COLUMN 5
                if ($contractDate['adate']['locationaddress']['id'] > 0) {
                    $venueName = trim($contractDate['adate']['locationaddress']['code']) ?? trim(
                            $contractDate['adate']['locationaddress']['name1']
                        );
                } else {
                    $venueName = self::TBD;
                }
                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_5_WIDTH), $borderCell)
                    ->addText(
                        $venueName,
                        $defaultFont,
                        $defaultParagraph
                    );
            }
        }
    }

    private function renderRehearsals(
        $contract,
        $pageSection,
        $defaultFont,
        $defaultParagraph,
        $borderCell,
        $scheduleDateFormat,
        $scheduleTimeFormat
    ) {
        $contractDates = $contract['acontract_dates'];
        //        get dates in proper order
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'] . $b['adate']['start_'];
            }
        );
//        OMIT education and philanthropy event type Groups and Reception Events
        $performanceTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($contractDates as $contractDate) {
            if ($contractDate['adate']['seventtype']['l_performance'] == 0
                && $contractDate['adate']['seventtype']['id'] !== self::RECEPTION_ID
                && $contractDate['adate']['seventtype']['group_id'] !== self::EDUCATION_GROUP_ID
                && $contractDate['adate']['seventtype']['group_id'] !== self::PHILANTHROPY_GROUP_ID) {
                $performanceTable->addRow();
//                COLUMN 1
                $performanceTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                )
                    ->addText('', $defaultFont, $defaultParagraph);
//                COLUMN 2
                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_2_WIDTH), $borderCell)
                    ->addText(
                        $contractDate['adate']['date_']->format($scheduleDateFormat),
                        $defaultFont,
                        $defaultParagraph
                    );
//                COLUMN 3
                if (!is_null(
                    $contractDate['adate']['start_'] && (string)trim($contractDate['adate']['start_']) !== ''
                )) {
                    $concertStart = $contractDate['adate']['start_']->format($scheduleTimeFormat);
                    if (!is_null(
                        $contractDate['adate']['end_'] && (string)trim($contractDate['adate']['end_']) !== ''
                    )) {
                        $concertStart .= ' - ' . $contractDate['adate']['end_']->format($scheduleTimeFormat);
                    }
                } else {
                    $concertStart = '';
                }

                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_3_WIDTH), $borderCell)
                    ->addText(
                        $concertStart,
                        $defaultFont,
                        $defaultParagraph
                    );
//                COLUMN 4
                $projectCell = $performanceTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_4_WIDTH),
                    $borderCell
                );
                $projectCellTextRun = $projectCell->addTextRun($defaultParagraph);
                $projectCellTextRun->addText($contractDate['adate']['sproject']['name'], $defaultFont);
                $projectCellTextRun->addTextBreak(1, $defaultFont);
                $projectCellTextRun->addFormattedText(
                    $contractDate['adate']['seventtype']['name'],
                    $defaultFont,
                    $defaultParagraph
                );


//                COLUMN 5
                if ($contractDate['adate']['locationaddress']['id'] > 0) {
                    $venueName = trim($contractDate['adate']['locationaddress']['code']) ?? trim(
                            $contractDate['adate']['locationaddress']['name1']
                        );
                } else {
                    $venueName = self::TBD;
                }
                $performanceTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_5_WIDTH), $borderCell)
                    ->addText(
                        $venueName,
                        $defaultFont,
                        $defaultParagraph
                    );
            }
        }
    }

    private function renderEducationEvents(
        $contract,
        $pageSection,
        $defaultFont,
        $defaultParagraph,
        $borderCell,
        $scheduleDateFormat,
        $scheduleTimeFormat
    ) {
        $contractDates = $contract['acontract_dates'];
        //        get dates in proper order
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'] . $b['adate']['start_'];
            }
        );
//        ONLY education  event type Groups
        $eduTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($contractDates as $contractDate) {
            if ($contractDate['adate']['seventtype']['group_id'] === self::EDUCATION_GROUP_ID) {
                $eduTable->addRow();
//                COLUMN 1
                $eduTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                )
                    ->addText('', $defaultFont, $defaultParagraph);
//                COLUMN 2
                $eduTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_2_WIDTH), $borderCell)
                    ->addText(
                        $contractDate['adate']['date_']->format($scheduleDateFormat),
                        $defaultFont,
                        $defaultParagraph
                    );
//                COLUMN 3
                if (!is_null(
                    $contractDate['adate']['start_'] && (string)trim($contractDate['adate']['start_']) !== ''
                )) {
                    $concertStart = $contractDate['adate']['start_']->format($scheduleTimeFormat);
                    if (!is_null(
                        $contractDate['adate']['end_'] && (string)trim($contractDate['adate']['end_']) !== ''
                    )) {
                        $concertStart .= ' - ' . $contractDate['adate']['end_']->format($scheduleTimeFormat);
                    }
                } else {
                    $concertStart = '';
                }

                $eduTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_3_WIDTH), $borderCell)
                    ->addText(
                        $concertStart,
                        $defaultFont,
                        $defaultParagraph
                    );

//                COLUMN 4
                $projectCell = $eduTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_4_WIDTH), $borderCell);
                $projectCellTextRun = $projectCell->addTextRun($defaultParagraph);
                $projectCellTextRun->addText($contractDate['adate']['sproject']['name'], $defaultFont);
                $projectCellTextRun->addTextBreak(1, $defaultFont);
                $projectCellTextRun->addFormattedText(
                    $contractDate['adate']['seventtype']['name'],
                    $defaultFont,
                    $defaultParagraph
                );


//                COLUMN 5
                if ($contractDate['adate']['locationaddress']['id'] > 0) {
                    $venueName = trim($contractDate['adate']['locationaddress']['code']) ?? trim(
                            $contractDate['adate']['locationaddress']['name1']
                        );
                } else {
                    $venueName = self::TBD;
                }
                $eduTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_5_WIDTH), $borderCell)
                    ->addText(
                        $venueName,
                        $defaultFont,
                        $defaultParagraph
                    );
            }
        }
    }

    private function renderPhilanthropyEvents(
        $contract,
        $pageSection,
        $defaultFont,
        $defaultParagraph,
        $borderCell,
        $scheduleDateFormat,
        $scheduleTimeFormat
    ) {
        $contractDates = $contract['acontract_dates'];
        //        get dates in proper order
        usort(
            $contractDates,
            function ($a, $b) {
                return $a['adate']['date_'] . $a['adate']['start_'] <=> $b['adate']['date_'] . $b['adate']['start_'];
            }
        );
//        ONLY Philanthropy event type Groups  or Reception Event
        $philTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($contract['acontract_dates'] as $contractDate) {
            if ($contractDate['adate']['seventtype']['group_id'] === self::PHILANTHROPY_GROUP_ID
                || $contractDate['adate']['seventtype']['id'] === self::RECEPTION_ID) {
                $philTable->addRow();
//                COLUMN 1
                $philTable->addCell(
                    Converter::inchToTwip(self::SCHEDULE_COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                )
                    ->addText('', $defaultFont, $defaultParagraph);
//                COLUMN 2
                $philTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_2_WIDTH), $borderCell)
                    ->addText(
                        $contractDate['adate']['date_']->format($scheduleDateFormat),
                        $defaultFont,
                        $defaultParagraph
                    );
//                COLUMN 3
                if (!is_null(
                    $contractDate['adate']['start_'] && (string)trim($contractDate['adate']['start_']) !== ''
                )) {
                    $concertStart = $contractDate['adate']['start_']->format($scheduleTimeFormat);
                    if (!is_null(
                        $contractDate['adate']['end_'] && (string)trim($contractDate['adate']['end_']) !== ''
                    )) {
                        $concertStart .= ' - ' . $contractDate['adate']['end_']->format($scheduleTimeFormat);
                    }
                } else {
                    $concertStart = '';
                }

                $philTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_3_WIDTH), $borderCell)
                    ->addText(
                        $concertStart,
                        $defaultFont,
                        $defaultParagraph
                    );

//                COLUMN 4
                $projectCell = $philTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_4_WIDTH), $borderCell);
                $projectCellTextRun = $projectCell->addTextRun($defaultParagraph);
                $projectCellTextRun->addText($contractDate['adate']['sproject']['name'], $defaultFont);
                $projectCellTextRun->addTextBreak(1, $defaultFont);
                $projectCellTextRun->addFormattedText(
                    $contractDate['adate']['seventtype']['name'],
                    $defaultFont,
                    $defaultParagraph
                );


//                COLUMN 5
                if ($contractDate['adate']['locationaddress']['id'] > 0) {
                    $venueName = trim($contractDate['adate']['locationaddress']['code']) ?? trim(
                            $contractDate['adate']['locationaddress']['name1']
                        );
                } else {
                    $venueName = self::TBD;
                }
                $philTable->addCell(Converter::inchToTwip(self::SCHEDULE_COL_5_WIDTH), $borderCell)
                    ->addText(
                        $venueName,
                        $defaultFont,
                        $defaultParagraph
                    );
            }
        }
    }

    private function renderVenues($contract, $pageSection, $defaultFont, $defaultParagraph)
    {
//        Only Performance Venues are rendered
        $venueId = 0;
        $venueTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($contract['acontract_dates'] as $contractDate) {
            if ($contractDate['adate']['seventtype']['l_performance'] == 1 &&
                $contractDate['adate']['location_id'] > 0 &&
                $contractDate['adate']['location_id'] !== $venueId) {
//                $venueName = $this->nameFormatHelper->getEntityName(
//                    $contractDate['adate']['locationaddress']['name1'],
//                    $contractDate['adate']['locationaddress']['name2'],
//                    1
//                );
                $venueAddressArray = [];
                $venueName = $contractDate['adate']['locationaddress']['name1'];
                $venueAddressArray[] = $contractDate['adate']['locationaddress']['street'];
                $venueAddressArray[] = $contractDate['adate']['locationaddress']['pobox'];
                $venueAddressArray[] = $contractDate['adate']['locationaddress']['place'] . ', '
                    . $contractDate['adate']['locationaddress']['state'] . ' '
                    . $contractDate['adate']['locationaddress']['zipcode'];

//                Render in 3-column table; first column blank
                $venueTable->addRow();
                $venueTable->addCell(
                    Converter::inchToTwip(self::PGM_COL_1_WIDTH),
                    $this->reportStyles->defaultCell
                )
                    ->addText('', $defaultFont, $defaultParagraph);
                $middleCell = $venueTable->addCell(
                    Converter::inchToTwip(self::VENUE_COL_2_WIDTH),
                    $this->reportStyles->defaultCell
                );
                $middleCell->addText(htmlspecialchars($venueName), $defaultFont, $defaultParagraph);


//                THIRD Column - currently blank
                $rightCell = $venueTable->addCell(
                    Converter::inchToTwip(self::VENUE_COL_2_WIDTH),
                    $this->reportStyles->defaultCell
                );
                $rightCell->addText(
                    implode('</w:t><w:br/><w:t>', array_filter($venueAddressArray)),
                    $defaultFont,
                    $defaultParagraph
                );
//                VENUE CAPACITY on Additional Data screen
                if ($contractDate['adate']['locationaddress']['number_1'] > 0) {
                    $rightCell->addText(
                        self::CAPACITY . ' ' . $contractDate['adate']['locationaddress']['number_1'],
                        $defaultFont,
                        $defaultParagraph
                    );
                }

//                Place a blank line between venues
                $rightCell->addTextBreak(1, $defaultFont, $defaultParagraph);

                $venueId = $contractDate['adate']['location_id'];
            }
        }
    }

    public function renderFee($contract, $pageSection, $repFileText, $defaultFont, $defaultParagraph, $borderCell)
    {
//       REVISED by client - output all expense types and names
//        THEN REVISED AGAIN by client to just ouput sum of all fees
//        THEN REVISED AGAIN (october 2023) to output all expense types and names

//        Sort expenses by expense order
        $contractExpenses = $contract['aexpenses'];
        usort(
            $contractExpenses,
            function ($a, $b) {
                return $a['expense_order'] <=> $b['expense_order'];
            }
        );

        $contractFee = 0;
        $feeTotal = 0;
        $feeTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);

        foreach ($contractExpenses as $expense) {
            $contractFee = ($expense['number_'] * $expense['amount']);
            $feeTotal += $contractFee;
            /*
             *  This is very inefficient, but it is a rare use case. Because the client can include multiple Projects
             * in the same contract, they want each expense item to reflect the Project to which the Expense is attached,
             * not the Project to which the Contract is attached. So we fetch a whole new expense object
             */
            $expenseObject = $this->expenseQueryHelper->getExpenses($expense['id'])->getQuery()->toArray()[0];
            $expenseProject = $expenseObject['sproject']['name'];

            $feeTable->addRow();
//          Column 1 - empty
            $feeTable->addCell(Converter::inchToTwip(self::FEE_COL_1_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);
//                Column 2 - amount
            $feeTable->addCell(Converter::inchToTwip(self::FEE_COL_2_WIDTH), $borderCell)
                ->addText(
                    $expense->scurrency->code . number_format($contractFee, 2, '.', ','),
                    $defaultFont,
                    $this->reportStyles->defaultParagraphRight
                );
//                Column 3 - expense type
            $feeTable->addCell(Converter::inchToTwip(self::FEE_COL_3_WIDTH), $borderCell)
                ->addText($expense->sexpensetype->name, $defaultFont, $defaultParagraph);
//                Column 4 - Project Name
            $feeTable->addCell(Converter::inchToTwip(self::FEE_COL_4_WIDTH), $borderCell)
                ->addText($expenseProject, $defaultFont, $defaultParagraph);
        }

//        RENDER TOTAL TABLE
        $totalTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $totalTable->addRow();
        $totalTable->addCell(Converter::inchToTwip(self::FEE_COL_1_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $totalTable->addCell(Converter::inchToTwip(self::FEE_COL_2_WIDTH), $this->reportStyles->defaultCell)
            ->addText(
                '$' . number_format(round($feeTotal, 0), 2, '.', ','),
                $this->reportStyles->defaultFontBold,
                $this->reportStyles->defaultParagraphRight
            );
        $totalTable->addCell(Converter::inchToTwip(self::FEE_COL_3_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $totalTable->addCell(Converter::inchToTwip(self::FEE_COL_4_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
    }
}
