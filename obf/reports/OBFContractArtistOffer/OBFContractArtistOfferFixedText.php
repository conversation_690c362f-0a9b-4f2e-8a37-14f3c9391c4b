<?php

namespace Customer\obf\reports\OBFContractArtistOffer;

use PhpOffice\PhpWord\Shared\Converter;

// Fixed Text at the bottom of the report

class OBFContractArtistOfferFixedText
{

    const COL_1_WIDTH = 1.00;
    const COL_2_WIDTH = 6.00;

    const MERCH = 'Merchandise:';
    const EXCLUSIVE = 'Exclusivity:';
    const RIDER = 'Contract Rider:';
    const PAYMENT = 'Payment:';

    const ADDRESS = 'Address_____________________________________________________________________';
    const CITY = 'City/State/Zip______________________________________________________________';
    const EMAIL = 'Email________________________________________________________________________';
    const PHONE = 'Phone________________________________________________________________________';

    private $reportStyles;
    private $pageSection;

    public function __construct($pageSection)
    {
        $this->pageSection = $pageSection;
        $this->reportStyles = new OBFContractArtistOfferStyles();
    }

    public function renderClauses()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;


//  TG: 2024-Sept - remove this per client request
//        $merchandiseClause = $this->pageSection->addTextRun($defaultParagraph);
//        $merchandiseClause->addText(self::MERCH, $this->reportStyles->defaultFontBold);
//        $merchandiseClause->addText(' ', $defaultFont, $defaultParagraph);
//        $merchandiseClause->addText('N/A', $defaultFont, $defaultParagraph);
//        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

/*      November 2023, client removes Exclusivity as Fixed Text and moves it to Clauses
        $exclusiveClause = $this->pageSection->addTextRun($defaultParagraph);
        $exclusiveClause->addText(self::EXCLUSIVE, $this->reportStyles->defaultFontBold);
        $exclusiveClause->addText(' ', $defaultFont, $defaultParagraph);
        $exclusiveClause->addText(
            'Artist not to perform within 150 miles of Eugene, OR 180 days prior to and 180 days ',
            $defaultFont
        );
        $exclusiveClause->addText(
            'after the final performance with Oregon Bach Festival (OBF) without expressed written permission.',
            $defaultFont
        );
        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
 *
 */

        /*
         * TG:  2024-Okt - client requests these two sections are removed from fixed text as they are now rendered
         * dynamically with contract clauses. The code is kept here because the client changes their mind frequently
         *
         *        $riderClause = $this->pageSection->addTextRun($defaultParagraph);
        $riderClause->addText(self::RIDER, $this->reportStyles->defaultFontBold);
        $riderClause->addText(' ', $defaultFont, $defaultParagraph);
        $riderClause->addText(
            'Artist agrees to submit any pertinent riders or technical requests with the response to this offer such that they can be reviewed by ',
            $defaultFont
        );
        $riderClause->addText(
            'OBF as part of the negotiation process .  OBF is not liable for expenses included in technical riders that are not negotiated in advance of issuing a formal contract.',
            $defaultFont
        );
        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

        $payClause = $this->pageSection->addTextrun($defaultParagraph);
        $payClause->addText(self::PAYMENT, $this->reportStyles->defaultFontBold);
        $payClause->addText(' ', $defaultFont, $defaultParagraph);
        $payClause->addText(
            "Project fees and all stipends will be paid within 30 days of OBF’s acceptance of all work completed under contract. ",
            $defaultFont
        );
        $payClause->addText(
            "If Artist is not a US citizen, Artist must provide OBF with a copy of their passport and most recent I-94.  OBF will also withhold 30% of payment for taxes.  ",
            $defaultFont
        );
        $payClause->addText(
            "All taxes withheld will be paid to the IRS in Artist's name.  At the end of the tax year, OBF will send a 1099 or 1042-S to Artist.  ",
            $defaultFont
        );
        $payClause->addText(
            "Artist may file a U.S. tax return requesting a refund from the IRS.  Please indicate if Artist’s payment will be made directly to Artist or via an Agent on Artist’s behalf:",
            $defaultFont
        );
        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

         */

//       TG:  NOVEMBER 2024 - client wants the signature section removed

//        $signatureTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
//        $signatureTable->addRow();
//        $signatureTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCellStyle)
//            ->addText('___ Artist', $this->reportStyles->defaultFontBold);
//
//        $rightCell = $signatureTable->addCell(
//            Converter::inchToTwip(self::COL_2_WIDTH),
//            $this->reportStyles->defaultCellStyle
//        );
//        $rightCell->addText(self::ADDRESS, $defaultFont, $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::CITY, $defaultFont,  $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::EMAIL, $defaultFont,  $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::PHONE, $defaultFont,  $this->reportStyles->headerParagraph);
//
//
//        $signatureTable->addRow();
//        $signatureTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),  $this->reportStyles->defaultCellStyle)
//                             ->addText('', $this->reportStyles->defaultFontBold);
//        $signatureTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH),  $this->reportStyles->defaultCellStyle)
//            ->addText('', $this->reportStyles->defaultFontBold);
//
//        $signatureTable->addRow();
//
//        $signatureTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCellStyle)
//            ->addText('___ Agent', $this->reportStyles->defaultFontBold);
//
//        $rightCell = $signatureTable->addCell(
//            Converter::inchToTwip(self::COL_2_WIDTH),
//            $this->reportStyles->defaultCellStyle
//        );
//        $rightCell->addText(self::ADDRESS, $defaultFont, $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::CITY, $defaultFont,  $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::EMAIL, $defaultFont,  $this->reportStyles->headerParagraph);
//        $rightCell->addText(self::PHONE, $defaultFont,  $this->reportStyles->headerParagraph);

    }

}
