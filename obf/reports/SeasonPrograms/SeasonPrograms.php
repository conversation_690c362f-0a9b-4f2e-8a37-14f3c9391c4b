<?php

use App\Model\Entity\Sproject;
use Customer\obf\PHPWordHelper;
use Customer\obf\reports\ReportWordFas;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Element\Section;

/* TWG
*  FORMAT NOTES:    $this->reportParagraphDefault,  defined in ReportWordFas. Standard left-
*      aligned paragraph with no extra spacing
*/

class SeasonPrograms extends ReportWordFas
{
    public function render($view = null, $layout = null): string
    {
        /* wordHelper is a public function in ReportWordFas that
        * creates a new PHPWOrdHelper class:   $this->wordHelper = new PHPWordHelper();
        */

        $allSeasons = [];
        foreach ($this->projects as $project) {
            $seasons = $this->reportHelper->getSeasonsFromProjectRows($project);

            $allSeasons = array_unique(array_merge($seasons, $allSeasons));
        }

        // sort the seasons by datestart
        usort(
            $allSeasons,
            function ($a, $b) {
                if ($a->datestart == $b->datestart) {
                    return 0;
                }
                return ($a->datestart < $b->datestart) ? -1 : 1;
            }
        );


        $this->renderToWord($this->projects, $allSeasons, $this->wordHelper);

// Create the file and return the filename
        $fileName = $this->_createFileName() . $this->getFileExtension();

// Clean (erase) the output buffer and turn off output buffering
        ob_end_clean();

        $this->wordHelper->write($fileName);

        return $fileName;
    }

    /* TWG
    *  Set the DOCUMENT PROPERTIES in the first addSection created by the report file. While almost
    *   all reports will have the same properites, this gives the option to alter the paper size,
    *   font, orientation, borders or other document-level properties. Deviations from these must
    *   be done on individual 'addSection' found after this one
    */
    public function renderToWord($allProjects, array $allSeasons, PHPWordHelper $wordHelper)
    {
        foreach ($allSeasons as $season) {
            // get all projects by season
            $projectsForSeason = [];
            foreach ($allProjects as $project) {
                if ($project['adates'][0]->season_id === $season->id) {
                    $projectsForSeason[] = $project;
                }
            }
            $section = $wordHelper->addSection();
            $this->renderHeader($section, $season, $wordHelper);
            $this->renderProjects($section, $projectsForSeason, $wordHelper);
        }
    }

    public function renderHeader(Section $section, $season, PHPWordHelper $wordHelper)
    {
        $section->setStyle($this->borderBottom);
        $section->addText(
            "Season Programs",
            (new Font())->setSize(14)->setName('Calibri'), // over-rides reportFontDefault
            $this->reportParagraphCenter
        );
        $section->addText(
            $season->name,
            (new Font())->setSize(12)->setBold(true)->setName('Calibri'), // over-rides reportFontDefault
            $this->reportParagraphCenter
        );
    }

    public function renderProjects(Section $section, $projects, PHPWordHelper $wordHelper)
    {
        foreach ($projects as $projectInfo) {
            /** @var Sproject $project */
            $project = $projectInfo['sproject'];

            // top spacer before grey project banner
            $section->addText(
                '  ',
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );

            $section->addText(
                $project->name,
                (new Font())->setSize(12)->setBold(true)->setName('Calibri'), // over-rides reportFontDefault
                /* over-ride reportParagraphDefault to get shading
                *  EAF1DD is Word light green default
                */
                ['shading' => ['fill' => 'EAF1DD'], 'spaceAfter' => 0, 'spacing' => 0]
            );

            // 5-point bottom spacer after grey project banner
            $section->addText(
                'x',
                (new Font())->setSize(5)->setName('Calibri')->setColor('white'),
                $this->reportParagraphDefault,
            );

            $dates = $this->reportHelper->getDatesForProject($projectInfo['adates']);
            $dateRangeString = $this->reportHelper->formatDateRange($dates);

            $this->renderConductorAndWorks($section, $dateRangeString, $projectInfo, $wordHelper);
        }
    }

    public function renderConductorAndWorks(
        Section $section,
        string $dateRangeString,
        $projectRow,
        PHPWordHelper $wordHelper
    ) {
        [
            'conductor' => $conductor,
            'conductorName' => $conductorName,
            'soloists' => $soloists
        ] = $this->reportHelper->getConductorAndSoloistsForProjectRow($projectRow);

// if no conductor, remove any blank lines; soloists start right nuder date range
        $dateAndName = '<b>' . $dateRangeString . '</b>';
        $dateAndName .= $conductorName ? '<br/><b>' . $conductorName . '</b>' : '';

        $Adate = $this->reportHelper->getAdateInfoFromProjectRow(
            $projectRow
        ); // allows fetching of data for a single date
        $works = $this->reportHelper->getWorksForAdateId($Adate->id);

        /* program title is fetched here, but output before FIRST WORK on the program
        *   so as to better manage empty line within the cell if no program title but compositions on the program (happens frequently)
        *     Still need to manage the reverse: program title is there but no compositiions (rare but possible)
        */
        $programTitle = $Adate->programtitle ? '<i>' . $Adate->programtitle . '</i><br/>' : '';  // see Adate just above

        $table = $wordHelper->addTable($section);
        $row = $wordHelper->addRow($table, PHPWordHelper::ROW_COLS_2575);

        $wordHelper->addTableText(
            $row,
            0,
            $dateAndName,
            $this->reportFontDefault,
            $this->reportParagraphDefault
        );

        foreach ($soloists as $soloist) {
            $string = $soloist['data']->name2Name1;
            $ins = [];
            foreach ($soloist['instruments'] as $instrument) {
                $ins[$instrument->name] = $instrument->name;
            }
            $string .= ", " . mb_strtolower(implode(", ", array_keys($ins)));
            $wordHelper->addTableText(
                $row,
                0,
                $string,
                $this->reportFontDefault,
                $this->reportParagraphDefault
            );
        }

        /*
         * for each work, show the composer: work title
         * skip encores and intermission
         */
        /** @var \App\Model\Entity\AdateWork $work */
        $workString = '';
        foreach ($works as $work) {
            if ($work->l_encore != 0 || $work->swork->l_intermission == 1) {
                continue;
            }

            if ($work->work_order == 1) {
                $workString .= $programTitle;  // program title fetched in line 149
            }

            $workString .= trim($this->reportHelper->getComposerNameFirstLast($work->swork->scomposer)) . ': ';
            $workString .= trim($work->title2) !== '' ? $work->title2 : $work->swork->title1;

            if ($work->arrangement) {
                $workString .= ' (' . $work->arrangement . ')';
            }

            if ($work->premiere_id) {
                $info = $this->reportHelper->getPremierInfo($work->premiere_id);
                $workString .= " <i>- " . $info->name . ' premiere</i>';
            }

            $workString .= "<br/>";
        }
        $wordHelper->addTableText(
            $row,
            1,
            $workString,
            $this->reportFontDefault,
            $this->reportParagraphDefault
        );
    }

}
