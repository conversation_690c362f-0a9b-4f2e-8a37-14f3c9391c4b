<?php

namespace Customer\obf\reports\OBFProjectDetailsLandscape;

use Cake\Core\Configure;

use Customer\fasutilities\reports\utility\DateQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

/**
 * Render the bottom, schedule table - each event in the Project + Season
 * Date | Time | Event + text | Venue | Program, Notes, etc
 */
class OBFProjectDetailsLandscapeSchedule
{
    const COL_1_WIDTH = 1.50;
    const COL_2_WIDTH = 1.50;
    const COL_3_WIDTH = 2.15;
    const COL_4_WIDTH = 1.80;
    const COL_5_WIDTH = 2.55;

    /**
     * Date soloist output
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(
        array $scheduleDateArray,
        array $dateWorkOrderLetters,
        Section $pageSection,
        $repFileText
    ) {
        $this->dateQueryHelper = new DateQueryHelper();

        $this->scheduleDateArray = $scheduleDateArray;
        $this->pageSection = $pageSection;
        $this->dateWorkOrderLetters = $dateWorkOrderLetters;
//        labels from the report .rep file
        $this->repFileText = $repFileText;

        $this->reportStyles = new OBFProjectDetailsLandscapeStyles();
    }

    public function renderSchedule()
    {
//        date and time formats from rep file or settings.php
        $scheduleDateFormat = $this->repFileText['schedDateFormat'] ?? Configure::read('Formats.date', '');
        $scheduleTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');

        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultCell = $this->reportStyles->defaultCell;
        $scheduleCell = $this->reportStyles->scheduleCell;

        $scheduleTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $this->renderHeaderTable($scheduleTable, $defaultFontBold, $defaultParagraph, $this->repFileText);

        $calendarDay = '';
        foreach ($this->scheduleDateArray as $scheduleDate) {
            $scheduleTable->addRow();

//            DATE AND TIME
//            Print Date only once per calendar day, and change cell style
            if ($scheduleDate['date_']->format('Ymd') !== $calendarDay) {
                $eventDate = $scheduleDate['date_']->format($scheduleDateFormat);
                $dateCellStyle = $scheduleCell;
            } else {
                $eventDate = '';
                $dateCellStyle = $defaultCell;
            }
            $scheduleTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $dateCellStyle)
                ->addText($eventDate, $defaultFontBold, $defaultParagraph);

            if (!is_null($scheduleDate['start_']) && (string)trim($scheduleDate['start_']) !== '') {
                $eventTime = $scheduleDate['start_']->format($scheduleTimeFormat);
                if (!is_null($scheduleDate['end_']) && (string)trim(
                        $scheduleDate['end_']
                    ) !== '' && $scheduleDate['seventtype']['l_performance'] == 0) {
                    $eventTime .= ' - ' . $scheduleDate['end_']->format($scheduleTimeFormat);
                }
            } else {
                $eventTime = '';
            }
            $scheduleTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $scheduleCell)
                ->addText($eventTime, $defaultFontBold, $defaultParagraph);

//            EVENT TYPE, DATE TEXT and DRESS
            $eventCell = $scheduleTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $scheduleCell);
            if ($scheduleDate['seventtype']['l_performance'] == 1) {
                $eventName = mb_strtoupper($scheduleDate['seventtype']['name']);
            } else {
                $eventName = $scheduleDate['seventtype']['name'];
            }
            $eventCell->addFormattedText($eventName, $defaultFont, $defaultParagraph);
            if (!is_null($scheduleDate['text']) && (string)trim($scheduleDate['text']) !== '') {
                $eventCell->addFormattedText($scheduleDate['text'], $defaultFont, $defaultParagraph);
            }
            if ($scheduleDate['seventtype']['l_performance'] == 1) {
                $eventCell->addFormattedText($scheduleDate['sdress']['name'], $defaultFont, $defaultParagraph);
            }

//            VENUE - render code or name if code not given
            if (!is_null($scheduleDate['locationaddress']['code']) && (string)trim(
                    $scheduleDate['locationaddress']['code']
                ) !== '') {
                $venueName = $scheduleDate['locationaddress']['code'];
            } else {
                $venueName = $scheduleDate['locationaddress']['name'];
            }
            $scheduleTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $scheduleCell)
                ->addText(htmlspecialchars($venueName), $defaultFont, $defaultParagraph);

//            REPERTOIRE, DATE NOTES
            $repertoireCell = $scheduleTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $scheduleCell);
            $repertoireTextRun = $repertoireCell->addTextRun($defaultParagraph);

            if ($scheduleDate['seventtype']['l_performance'] == 0) {
//        sort date-works by work_order
                $dateWorkArray = $scheduleDate['adate_works'];
                /**
                 * @see https://stackoverflow.com/a/2699159
                 */
                usort($dateWorkArray, function($a, $b) {
                    return $a['work_order'] <=> $b['work_order'];
                });

                $rehWorkArray = [];
                foreach ($dateWorkArray as $dateWork) {
//            extract the work order number as a letter
                    $dateWorkOrderLetter = array_search($dateWork['work_id'], $this->dateWorkOrderLetters);
                    $rehWorkArray[] = $dateWork['swork']['scomposer']['lastname'] . ' (' . $dateWorkOrderLetter . ')';
                }
                $repertoireTextRun->addFormattedText(implode(' / ', $rehWorkArray), $defaultFont);

            }
//          render line breaks within the date note field
            $textlines = explode("\n", $scheduleDate['notes']);
            foreach ($textlines as $line) {
                $repertoireTextRun->addText(htmlspecialchars($line), $defaultFont);
            }

            $calendarDay = $scheduleDate['date_']->format('Ymd');
        }
    }

    protected function renderHeaderTable($scheduleTable, $defaultFontBold, $defaultParagraph, $repFileText)
    {
        $scheduleTable->addRow();
        $scheduleTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->scheduleHeaderCell)
            ->addText($repFileText['date'], $defaultFontBold, $defaultParagraph);
        $scheduleTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->scheduleHeaderCell)
            ->addText($repFileText['time'], $defaultFontBold, $defaultParagraph);
        $scheduleTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->scheduleHeaderCell)
            ->addText($repFileText['event'], $defaultFontBold, $defaultParagraph);
        $scheduleTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $this->reportStyles->scheduleHeaderCell)
            ->addText($repFileText['venue'], $defaultFontBold, $defaultParagraph);
        $scheduleTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $this->reportStyles->scheduleHeaderCell)
            ->addText($repFileText['repertoire'], $defaultFontBold, $defaultParagraph);
    }

}
