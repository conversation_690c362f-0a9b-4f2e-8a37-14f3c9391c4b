<?php

namespace Customer\obf\reports\OBFProjectDetailsLandscape;

use Cake\Core\Configure;

use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;


/**
 * Render the Instrumentation Table - Instrumentation for each work followed
 *   by total for the concert
 */
class OBFProjectDetailsLandscapeInstrumentation
{
//    work, percussion, strings, extras columns
    const COMP_COL_WIDTH = 1.09;
    const WIND_COL_WIDTH = 0.50;
//    brass and harp
    const BRASS_COL_WIDTH = 0.42;
    const PERC_COL_WIDTH = 1.09;
    const KYBD_COL_WIDTH = 0.62;
    const MAXGRID_COL_WIDTH = 8.41;

    const WIND_ARRAY = ['flute', 'oboe', 'clarinet', 'bassoon'];
    const BRASS_ARRAY = ['horn', 'trumpet', 'trombone', 'tuba'];

    /**
     * Date soloist output
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Instrumentation string output
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct($concert, array $dateWorkOrderLetters, Section $pageSection, $repFileText)
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->concert = $concert;
        $this->pageSection = $pageSection;
        $this->dateWorkOrderLetters = $dateWorkOrderLetters;
//        labels from the report .rep file
        $this->repFileText = $repFileText;

        $this->reportStyles = new OBFProjectDetailsLandscapeStyles();
    }

    public function renderInstrumentation()
    {
//        Instrumentation labels and grid prefix/suffix from settings.php
        $stringLabel = Configure::read('Instrumentation.stringLabel', '');

        $defaultFont = $this->reportStyles->defaultFont;
        $instFont = $this->reportStyles->instFont;

        $instFontBold = $this->reportStyles->instFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultCell = $this->reportStyles->defaultCell;
        $instGridCell = $this->reportStyles->instGridCell;
        $totalCell = $this->reportStyles->topBorderCell;


//        sort date-works by work_order
        $dateWorkArray = $this->concert['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

        /**
         * Instrumentation is rendered in one table -
         * Header row, then one work per row, Then a row for Totals
         */

        $instrumentationTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $this->renderTableHeader(
            $instrumentationTable,
            $this->repFileText,
            $defaultCell,
            $instFontBold,
            $defaultParagraph
        );
        $dateWorkNoteArray = [];

        foreach ($dateWorkArray as $dateWork) {
            if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {

//            this adds the methods from CustomerInstrumentation to the $dateWork entry
                $this->instrumentation->addInstrumentationStringToWork($dateWork);

                $instrumentationTable->addRow();
//            extract the work order number as a letter
                $dateWorkOrderLetter = array_search($dateWork['work_id'], $this->dateWorkOrderLetters);
                $instrumentationTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
                    ->addText(
                        $dateWork['swork']['scomposer']['lastname'] . ' (' . $dateWorkOrderLetter . ')',
                        $instFont,
                        $defaultParagraph
                    );

                foreach (self::WIND_ARRAY as $windInstrument) {
                    if (!is_null($dateWork[$windInstrument . '_text']) && (string)trim(
                            $dateWork[$windInstrument . '_text']
                        ) !== '') {
                        $windOutput = $dateWork[$windInstrument . '_text'];
                    } else {
                        $windOutput = $dateWork[$windInstrument];
                    }
                    $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $instGridCell)
                        ->addText($windOutput, $instFont, $defaultParagraph);
                }

                foreach (self::BRASS_ARRAY as $brassInstrument) {
                    if (!is_null($dateWork[$brassInstrument . '_text']) && (string)trim(
                            $dateWork[$brassInstrument . '_text']
                        ) !== '') {
                        $brassOutput = $dateWork[$brassInstrument . '_text'];
                    } else {
                        $brassOutput = $dateWork[$brassInstrument];
                    }
                    $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
                        ->addText($brassOutput, $instFont, $defaultParagraph);
                }

                $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
                    ->addText(
                        trim($dateWork['timpani'] . ' ' . $dateWork['timpani_text']),
                        $instFont,
                        $defaultParagraph
                    );

                $percussion = trim($dateWork['percussion']);
                $percussion .= trim($dateWork['percussion_text']) ? ' (' . $dateWork['percussion_text'] . ')' : '';
                $percussiongrid = $this->getInstrumentGridCode($dateWork['adatework_percussions']);
                if (!is_null($percussiongrid) && (string)trim($percussiongrid) !== '') {
                    $percussion .= ' - ' . $percussiongrid;
                }
                $instrumentationTable->addCell(Converter::inchToTwip(self::PERC_COL_WIDTH), $instGridCell)
                    ->addText($percussion, $instFont, $defaultParagraph);

                if (!is_null($dateWork['harp_text']) && (string)trim($dateWork['harp_text']) !== '') {
                    $harp = $dateWork['harp_text'];
                } else {
                    $harp = $dateWork['harp'];
                }
                $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
                    ->addText($harp, $instFont, $defaultParagraph);

                $keyboard = trim($dateWork['keyboard']);
                $keyboard .= trim($dateWork['keyboard_text'] ? ' (' . $dateWork['keyboard_text'] . ')' : '');
                $keyboardgrid = $this->getInstrumentGridCode($dateWork['adatework_keyboards']);
                if (!is_null($keyboardgrid) && (string)trim($keyboardgrid) !== '') {
                    $keyboard .= ' - ' . $keyboardgrid;
                }
                $instrumentationTable->addCell(Converter::inchToTwip(self::KYBD_COL_WIDTH),$instGridCell)
                    ->addText($keyboard, $instFont, $defaultParagraph);

//          remove the string prefix
                $stringCount = str_replace(
                        $stringLabel,
                        '',
                        $dateWork['instrumentation_strings']
                    ) ?? $dateWork['strings_text'];

                $instrumentationTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $instGridCell)
                    ->addText($stringCount, $instFont, $defaultParagraph);

                $extra = trim($dateWork['extra']);
                $extra .= trim($dateWork['extra_text'] ? ' (' . $dateWork['extra_text'] . ')' : '');
                $extragrid = $this->getInstrumentGridCode($dateWork['adatework_extras']);
                if (!is_null($extragrid) && (string)trim($extragrid) !== '') {
                    $extra .= ' - ' . $extragrid;
                }
                $instrumentationTable->addCell(Converter::inchToTwip(self::KYBD_COL_WIDTH), $instGridCell)
                    ->addText($extra, $instFont, $defaultParagraph);

                $dateWorkNoteArray[] = array(
                    $dateWork['swork']['scomposer']['lastname'] . ' (' . $dateWorkOrderLetter . ')',
                    $dateWork['details']
                );
            }
        }
        $this->renderMaxInstrumentation(
            $this->concert,
            $this->pageSection,
            $this->repFileText,
            $defaultCell,
            $instGridCell,
            $instFontBold,
            $defaultParagraph
        );

//        Render the Date-Work Notes for each work on the program. This is not a common use case;
//        most often the notes are rendered along with the instrumentation. This is set here as a separate
//        value[0] is the composer name, value[1] is the date-work details field

        $dateWorkNoteTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        foreach ($dateWorkNoteArray as $key => $value) {
            if (!is_null($value[1]) && (string)trim($value[1]) !== '') {
                $dateWorkNoteTable->addRow();
                $dateWorkNoteTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
                    ->addText($value[0], $instFont, $defaultParagraph);
                $dateWorkNoteTable->addCell(Converter::inchToTwip(self::MAXGRID_COL_WIDTH), $defaultCell)
                    ->addFormattedText($value[1], $instFont, $defaultParagraph);
            }
        }
    }

    protected function renderTableHeader(
        $instrumentationTable,
        $repFileText,
        $defaultCell,
        $boldFont,
        $defaultParagraph
    ) {
        $instrumentationTable->addRow();
        $instrumentationTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
            ->addText($repFileText['work'], $boldFont, $defaultParagraph);

        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $defaultCell)
            ->addText($repFileText['flute'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $defaultCell)
            ->addText($repFileText['oboe'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $defaultCell)
            ->addText($repFileText['clarinet'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $defaultCell)
            ->addText($repFileText['bassoon'], $boldFont, $defaultParagraph);

        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['horn'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['trumpet'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['trombone'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['tuba'], $boldFont, $defaultParagraph);

        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['timpani'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::PERC_COL_WIDTH), $defaultCell)
            ->addText($repFileText['percussion'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $defaultCell)
            ->addText($repFileText['harp'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::KYBD_COL_WIDTH), $defaultCell)
            ->addText($repFileText['keyboard'], $boldFont, $defaultParagraph);

        $instrumentationTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
            ->addText($repFileText['strings'], $boldFont, $defaultParagraph);
        $instrumentationTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
            ->addText($repFileText['extra'], $boldFont, $defaultParagraph);
    }

    protected function getInstrumentGrid($gridName)
    {
        $instrumentArray = [];
        foreach ($gridName as $instrument) {
            $instrumentEntry = '';
            if ($instrument['number_'] > 1) {
                $instrumentEntry .= $instrument['number_'] . '-';
            }
            $instrumentEntry .= mb_strtolower($instrument['sinstrinstrument']['name']);
            $instrumentEntry .= $instrument['text'] ? ' (' . htmlspecialchars($instrument['text']) . ')' : '';
            $instrumentArray[] = $instrumentEntry;
        }
        return implode('; ', $instrumentArray);
    }

    protected function getInstrumentGridCode($gridName)
    {
        $instrumentArray = [];
        foreach ($gridName as $instrument) {
            $instrumentEntry = '';
            if ($instrument['number_'] > 1) {
                $instrumentEntry .= $instrument['number_'] . '-';
            }
            $instrumentEntry .= trim(mb_strtolower($instrument['sinstrinstrument']['code'])) ?? mb_strtolower(
                    $instrument['sinstrinstrument']['name']
                );
            $instrumentEntry .= $instrument['text'] ? ' (' . htmlspecialchars($instrument['text']) . ')' : '';
            $instrumentArray[] = $instrumentEntry;
        }
        return implode('; ', $instrumentArray);
    }

    /* this report outputs the max instrumentation in a table (unusual use case) so instead of using the
    built-in max instrumentation string we get the full max instrumentation array and output each
    element individually - one instrument per cell.
    */
    public function renderMaxInstrumentation(
        $perfDate,
        $pageSection,
        $repFileText,
        $defaultCell,
        $instGridCell,
        $instFontBold,
        $defaultParagraph
    ) {
        $maxInstFont = $this->reportStyles->reportFontBold;

//        FETCH DATA
        $this->instrumentation->addInstrumentationString($perfDate);
        $maxInst = $perfDate['max_array'];
        $dblMark = Configure::read('Instrumentation.dblMark', '*');
        $stringSeparator = Configure::read('Instrumentation.stringSeparator', '-');
        $perfDateId = $perfDate->toArray(0)['id'];

//        RENDER TABLE
        $maxInstTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $maxInstTable->addRow();

        $maxInstTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
            ->addText($repFileText['total'], $instFontBold, $defaultParagraph);

        foreach (self::WIND_ARRAY as $maxWind) {
            $maxWindOutput = $maxInst[$maxWind]['text'] ? $dblMark . $maxInst[$maxWind]['number'] : $maxInst[$maxWind]['number'];
            $maxInstTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $instGridCell)
                ->addText($maxWindOutput, $instFontBold, $defaultParagraph);
        }
        foreach (self::BRASS_ARRAY as $maxBrass) {
            $maxBrassOutput = $maxInst[$maxBrass]['text'] ? $dblMark . $maxInst[$maxBrass]['number'] : $maxInst[$maxBrass]['number'];
            $maxInstTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
                ->addText($maxBrassOutput, $instFontBold, $defaultParagraph);
        }

        $maxInstTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
            ->addText($maxInst['timpani']['number'], $instFontBold, $defaultParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::PERC_COL_WIDTH), $instGridCell)
            ->addText($maxInst['percussion']['number'], $instFontBold, $defaultParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::BRASS_COL_WIDTH), $instGridCell)
            ->addText($maxInst['harp']['number'], $instFontBold, $defaultParagraph);
        $maxInstTable->addCell(Converter::inchToTwip(self::KYBD_COL_WIDTH), $instGridCell)
            ->addText($maxInst['keyboard']['number'], $instFontBold, $defaultParagraph);

        if ($maxInst['violin1']['number'] + $maxInst['violin2']['number']
            + $maxInst['viola']['number'] + $maxInst['cello']['number'] + $maxInst['bass']['number'] > 0) {
            $maxStrings = $maxInst['violin1']['number']
                . $stringSeparator . $maxInst['violin2']['number']
                . $stringSeparator . $maxInst['viola']['number']
                . $stringSeparator . $maxInst['cello']['number']
                . $stringSeparator . $maxInst['bass']['number'];
        } else {
            $maxStrings = '';
        }
        $maxInstTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $instGridCell)
            ->addText($maxStrings, $instFontBold, $defaultParagraph);

        $maxInstTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $instGridCell)
            ->addText($maxInst['extra']['number'], $instFontBold, $defaultParagraph);

        $pageSection->addText('', $maxInstFont, $defaultParagraph);

//                    max instrumentation grids are output in a separate 2-column table
        $maxGridLabel = array(
            mb_strtoupper(__('keyboard')),
            mb_strtoupper(__('percussion')),
            mb_strtoupper(__('extra')),
            mb_strtoupper(__('vocal')),
            mb_strtoupper(__('solo'))
        );
        $maxGridContents = array(
            $perfDate['max_instrumentation_grid_name']['keyboards'],
            $perfDate['max_instrumentation_grid_name']['percussions'],
            $perfDate['max_instrumentation_grid_name']['extras'],
            $perfDate['max_instrumentation_grid_name']['vocals'],
            $this->getSoloistInstruments($perfDateId)
        );
        $maxGridTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        for ($g = 0; $g < 5; $g++) {
//            only output row if there is content
            if (!is_null($maxGridContents[$g]) && (string)trim($maxGridContents[$g]) != '') {
                $maxGridTable->addRow();

                $maxGridTable->addCell(Converter::inchToTwip(self::COMP_COL_WIDTH), $defaultCell)
                    ->addText($maxGridLabel[$g], $instFontBold, $defaultParagraph);
                $maxGridTable->addCell(Converter::inchToTwip(self::MAXGRID_COL_WIDTH), $defaultCell)
                    ->addText(
                        $maxGridContents[$g],
                        $this->reportStyles->instFont,
                        $defaultParagraph
                    );
            }

        }
    }

    public function getSoloistInstruments($dateID)
    {
        $dateSoloistInstruments = [];
        $concertSoloists = $this->dateQueryHelper->getSoloistsForDateID($dateID)->getQuery()->toArray();
        foreach ($concertSoloists as $concertSoloist) {
            $dateSoloistInstruments[] = mb_strtolower($concertSoloist['sinstrinstrument']['name']);
        }
        return implode('; ', $dateSoloistInstruments);
    }
}
