<?php

namespace Customer\obf\reports\OBFServiceMusicianContractInfoGuarTotal;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;
use Customer\fasutilities\reports\utility\DutyQueryHelper;
use Customer\fasutilities\reports\utility\NameFormatHelper;

use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

/*  Report exports musician info and pay rates to Excel\
Identical to the Preliminary version but with different titles
TODO: Tidy up the Duty Accounting/Pay Adjustment methods; correct amounts and expense types are
returned but not the correct associated amount
TODO:  When client defines expenses/payments subject to work dues, factor those in
Change/Add Log
July 2023 - add column S which is a total of the gross pay minus work dues (Col O - Col R),
   and a grand total for this column

TG - 2023-12-13:  refactor to use the new accItemAmountValues table in OPAS Next 1.16. This
  includes refactoring DutyQueryHelper
*/

class OBFServiceMusicianContractInfoGuarTotal extends Report
{
//    Change these values to add a title or other heading
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 1;
    const DATA_ROW = 2;

    const DUES_AMOUNT = -0.03;
    const TRAVEL_TYPE_ID = 40; // travel event
    const ORCH_MEMBER_IDS = array(17, 36, 37 );

    const HEADER_ROW_SHADING = 'F9F8F1'; // tan

    const REPORT_TITLE = 'OBF Fee Sheet';

    const MUSICIAN = 'Musician';
    const GROUP = 'Musician Group';
    const PROJECT = 'Project';
    const CHAIR = 'Stand/Chair';
    const INSTRUMENT = 'Instrument';
    const PAY_TYPE = 'Attend/ Overage';
    const DATE = 'Date';
    const START = 'Start';
    const END = 'End';
    const ACTIVITY = 'Activity';
    const DUES = 'Dues Eligible';
    const SERVICES = 'Attend. Pay %';
    const RATE = 'Rate';
    const FEE_TOTAL = 'Gross Fee';
    const DEDUCTION = 'Work Dues Deduction';
    const GRAND_TOTAL = 'Total (Net)';
    const ACCOUNT_NO = 'Budget Code';
    const CLASSIFICATION = 'Classification';
    const PERSONNEL = 'Personnel No.';
    const ACCOUNT_NO2 = 'Pjct. Acct. No.';
    const PGMT_TITLE = 'Program Title';

    const ADMIN_ACTIVITY = 'Admin/Accounting';
    const BERWICK_PROJECT = 'Berwick Academy - General';

    /**
     * Helper class for the selected Service records
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;
    private $dutiesResult;

    /**
     * Helper class for all name formatting
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    // SET COLUMN ADDRESSES
    private $col_Musician = 'A';
    private $col_Group = 'B';
    private $col_Instrument = 'C';
    private $col_Chair = 'D';

    private $col_Date = 'F';
    private $col_Start = 'G';
    private $col_End = 'H';
    private $col_Project = 'I';
    private $col_PgmTitle = 'J';
    private $col_Activity = 'K';

    private $col_Rate = 'M';
    private $col_Value = 'N';
    private $col_Fee = 'O';

    private $col_DuesYN = 'P';
    private $col_DuesRate = 'Q';
    private $col_DuesAmount = 'R';
    private $col_GrandTotal = 'S';


    public function initialize()
    {
        $this->dutyQueryHelper = new DutyQueryHelper();
        $this->nameFormatHelper = new NameFormatHelper();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file. This allows for headings and other report text to output in a
    variety of languages
*/
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = [])
    {
        $dutyQuery = $this->dutyQueryHelper
            ->getDuties($this->getRequest()->getData()['dataItemIds'])
            ->withEventExpenseType()
            ->getQuery();

        $this->dutiesResult = $dutyQuery->toArray();

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);

        $spreadsheet->getActiveSheet()->getHeaderFooter()
            ->setOddHeader('Attachment G: Guaranteed Service Schedule');
        $spreadsheet->getActiveSheet()->setPrintGridlines(true);
        $spreadsheet->getActiveSheet()->getPageSetup()
            ->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $spreadsheet->getActiveSheet()->getPageSetup()->setFitToWidth(1);

//        RENDER TOP/HEADER ROW and Title
        $spreadsheet->getActiveSheet()
            ->fromArray(
                $this->getHeaderRow(),
                null,
                'A' . self::HEADER_ROW
            );

        $upperLeftCell = $this->col_Musician . self::DATA_ROW;
//        TEMPORARILY REMOVED
//        $spreadsheet->getActiveSheet()->setCellValue('A1', self::REPORT_TITLE);
//        $spreadsheet->getActiveSheet()->setCellValue('A2', 'Printed: ' . date(Configure::read('Formats.programHeaderDate')));

//        +++++ PREPARE DUTY ARRAY - sort by name and date ++++
        $dutyArray = $this->dutiesResult;
        foreach ($dutyArray as $key => $value) {
            $lastName[$key] = $value['artistaddress']['name1'];
            $firstName[$key] = $value['artistaddress']['name2'];
            $eventDate[$key] = $value['adate']['date_'];
            $eventStart[$key] = $value['adate']['start_'];
        }
        array_multisort(
            $lastName,
            SORT_ASC,
            $firstName,
            SORT_ASC,
            $eventDate,
            SORT_ASC,
            $eventStart,
            SORT_ASC,
            $dutyArray
        );

        $row = self::DATA_ROW;

//        Loop through Each SERVICE / DUTY RECORD - one row per duty
//        Client revision: 10-Okt-2023: Event Type = Travel not used in dues calculation
//        Client revision: 10-Okt-2023: Only orchestra members should be dues eligible (not chorus)
        $grandTotal = 0;
        foreach ($dutyArray as $duty) {

            $musicianName = $this->nameFormatHelper->getEntityName(
                $duty['artistaddress']['name1'],
                $duty['artistaddress']['name2'],
                2
            );
            $instrument = $duty['sinstrinstrument']['name'];
            $group = $duty['saddressgroup']['name'];
            $chair = $duty['seat'];
            $personnelNo = $duty['artistaddress']['saddress_persdata']['personnelno'];
            $attendance = $duty['sdutytype']['name'];
            $refPayDate = $duty['adate']['date_']->format('Ymd');
            $servicePay = $this->getServiceFixedAmount($duty, $refPayDate);
            $payPercent = $duty['sdutytype']['percentcount'];
            if ($duty['artistaddress']['l_logic_1'] == 1
                && $duty['adate']['eventtype_id'] !== self::TRAVEL_TYPE_ID
                && in_array(  $duty['saddressgroup']['id'] , self::ORCH_MEMBER_IDS) ) {
                $dues = 'Y';
            } else {
                $dues = '';
            }
            $printDuesRate = $dues === 'Y' ? self::DUES_AMOUNT : '';
            $accountNo = $duty['Sexpensetypes']['accountno'];
            $serviceTotalPay = $servicePay * ($payPercent / 100);
            $serviceDuesAmount = $serviceTotalPay * self::DUES_AMOUNT;
            $serviceNetPay = $serviceTotalPay + $serviceDuesAmount;
            $grandTotal += $dues === 'Y' ? $serviceNetPay : $serviceTotalPay;

            $date = $duty['adate']['date_']->format('m/j/Y');
            $startTime = $duty['adate']['start_']->format('g:i A');
            $endTime = $duty['adate']['end_']->format('g:i A');
            $project = $duty['adate']['sproject']['name'];
            $projectAccountNo = $duty['adate']['sproject']['accountno'];
            $programTitle = $duty['adate']['programtitle'];
            $activity = $duty['adate']['seventtype']['name'];

            $outputArray = [
                $musicianName,
                $group,
                $instrument,
                $chair,
                $attendance,
                $date,
                $startTime,
                $endTime,
                $project,
                $programTitle,
                $activity,
                '',
                $servicePay,
                $payPercent,
                $serviceTotalPay,
                $dues,
                $printDuesRate,
                $dues === 'Y' ? $serviceDuesAmount : '',
                $dues === 'Y' ? $serviceNetPay : $serviceTotalPay
            ];
//                    RENDER ROW AS ARRAY - re-arrange items to suit client needs
            $spreadsheet->getActiveSheet()->fromArray($outputArray, null, 'A' . $row);

//                    IF there are duty accountings for the musician, they will be returned here in an array:
//                    expense type | dues | rate | services used | total amount | expense type account
//                     Incorporate these items in the output Array
            //                FETCH ALL DUTY ACCOUNTINGS for the MUSICIAN + PROJECT, then Render them after the
//                  base salary row
            $musicianProjectDutyAccountings = $this->getDutyAccountings($duty);
            if (!is_null($musicianProjectDutyAccountings) && !empty($musicianProjectDutyAccountings)) {

                foreach ($musicianProjectDutyAccountings as $musicianProjectDutyAccounting) {
                    $row++;

//              For Duty Accountings, Work Dues do NOT apply to travel stipends
//                    UPDATED 24-July 2023. Client now decides that any "travel", "housing" or "stipend" expense types
//                      are EXEMPT from work dues
//                    UPDATED Oktober 2023. Client now decides that anything attached to the activity "Admin/Accounting"
//                       is EXEMPT from work dues
                    if ($dues === 'Y' && strpos(strtolower($musicianProjectDutyAccounting[0]), 'travel') === false
                        && strpos(strtolower($musicianProjectDutyAccounting[0]), 'housing') === false
                        && strpos(strtolower($musicianProjectDutyAccounting[0]), 'stipend') === false
                        && $activity !== self::ADMIN_ACTIVITY
                        && $project !== self::BERWICK_PROJECT) {
                        $dutyAccountingDuesAmount = ($musicianProjectDutyAccounting[2] * ($payPercent / 100)) * self::DUES_AMOUNT;
                    } else {
                        $dues = '';
                        $printDuesRate = '';
                        $dutyAccountingDuesAmount = 0;
                    }
                    $dutyAccountingNetPay = ($musicianProjectDutyAccounting[2] * ($payPercent / 100)) + $dutyAccountingDuesAmount;
                    $grandTotal += $dutyAccountingNetPay;

                    $outputArray = [
                        $musicianName,
                        $group,
                        $instrument,
                        $chair,
                        $musicianProjectDutyAccounting[0],
                        $date,
                        $startTime,
                        $endTime,
                        $project,
                        $programTitle,
                        $activity,
                        '',
                        $musicianProjectDutyAccounting[2],
                        $payPercent,
                        $musicianProjectDutyAccounting[2] * ($payPercent / 100),
                        $dues,
                        $printDuesRate,
                        $dutyAccountingDuesAmount,
                        $dutyAccountingNetPay
                    ];
                    $spreadsheet->getActiveSheet()->fromArray($outputArray, null, 'A' . $row);
                }
            }

            $row++;

        }

//        Added by client July 2023 - include a Grand Total for the column that calculates the Line totals.
        $spreadsheet->getActiveSheet()->setCellValue($this->col_GrandTotal . $row, $grandTotal);

        //        ++++++ FORMAT SPREADSHEET ++++++
        $this->formatSheet($upperLeftCell, $row, $spreadsheet);

//        $fileName = $this->_createFileName();
//        Client requests Custom Document Name
        $ts = date('Ymd');
        $fileName = "OBFServiceSchedule_{$ts}";
        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;
        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    public function getMusicianIds(): array
    {
        $musicianIds = [];
        foreach ($this->dutiesResult as $duty) {
            $musicianIds[] = $duty['artist_id'];
        }
        return array_unique($musicianIds);
    }

    public function getMusicianProjectIds($musicianId): array
    {
        $musicianProjects = [];
        foreach ($this->dutiesResult as $duty) {
            if ($duty['artist_id'] === $musicianId) {
                $musicianProjects[] = $duty['adate']['project_id'];
            }
        }
        return array_unique($musicianProjects);
    }

    public function getMusicianEventFixedAmountIds($musicianId, $projectId): array
    {
        $eventExpenseTypes = [];
        foreach ($this->dutiesResult as $duty) {
            if ($duty['artist_id'] === $musicianId && $duty['adate']['project_id'] === $projectId) {
                $eventExpenseTypes[] = $duty['adate']['seventtype']['accitem_id'];
            }
        }
        return array_unique($eventExpenseTypes);
    }

    public function getServiceFixedAmount($duty, $refPayDate)
    {
//        isolate the LAST account item entry where the Effective Date is Before the Service Reference Date
        $accountItemArray = [];
        foreach ($duty['adate']['seventtype']['saccitem']['saccitem_amounts'] as $acctItem) {
            if ($refPayDate >= $acctItem['effective_date']->format(
                    'Ymd'
                )) {
                $accountItemArray[] = $acctItem;
            }
        };
        $accountItemForTheService = (end($accountItemArray));

//        Get the new Account Item VALUES for this Account Item Amount
        $payValues = $this->dutyQueryHelper->getFixedAmountValueForAccountItemAmount(
            $accountItemForTheService['id']
        )->getQuery()->toArray();

//        Find the Pay Value Category that matches the musician's account category
        foreach ($payValues as $payValue) {
            if ($payValue['category'] == $duty['accounting_category']) {
                $servicePay = $payValue['value'];
            }
        }

        return $servicePay;
    }

    public function getAdjustmentFixedAmount($payAdjustment, $refPayDate, $dutyAccountCat)
    {
//        isolate the LAST account item entry where the Effective Date is Before the Service Reference Date
        $accountItemArray = [];
        foreach ($payAdjustment['saccitem']['saccitem_amounts'] as $acctItem) {
            if ($refPayDate >= $acctItem['effective_date']->format(
                    'Ymd'
                )) {
                $accountItemArray[] = $acctItem;
            }
        };
        $accountItemForTheService = (end($accountItemArray));

        //        Get the new Account Item VALUES for this Account Item Amount
        $payValues = $this->dutyQueryHelper->getFixedAmountValueForAccountItemAmount(
            $accountItemForTheService['id']
        )->getQuery()->toArray();

//        Find the Pay Value Category that matches the musician's account category
        foreach ($payValues as $payValue) {
            if ($payValue['category'] == $dutyAccountCat) {
                $adjustmentPay = $payValue['value'];
            }
        }

        return $adjustmentPay;
    }

    public function getDutyAccountings($duty)
    {
//        Return the financial elements of all duty accounting items linked to the duty

                $payAdjustments = $this->dutyQueryHelper->getDutyAccountingsForDutyId($duty['id'])->getQuery()->toArray(
                );
                $refPayDate = $duty['adate']['date_']->format('Ymd');

                foreach ($payAdjustments as $payAdjustment) {
                    if ($payAdjustment['amount'] !== 0) {
                        $payAmount = $payAdjustment['amount'];
                    } else {
                        $payAmount = $this->getAdjustmentFixedAmount(
                            $payAdjustment,
                            $refPayDate,
                            $duty['accounting_category']
                        );
                    }
                    $payAdjustmentArray[] = array(
                        $payAdjustment['sexpensetype']['name'],
                        $payAdjustment['sexpensetype']['costcenter_id'],
                        $payAmount,
                        $payAdjustment['sexpensetype']['accountno']
                    );
                }

        return $payAdjustmentArray;

    }


    public function getHeaderRow()
    {
// Rearrange if elements are put into a different order
        return array(
            self::MUSICIAN,
            self::GROUP,
            self::INSTRUMENT,
            self::CHAIR,
            self::PAY_TYPE,
            self::DATE,
            self::START,
            self::END,
            self::PROJECT,
            self::PGMT_TITLE,
            self::ACTIVITY,
            '',
            self::RATE,
            self::SERVICES,
            self::FEE_TOTAL,
            self::DUES,
            '',
            self::DEDUCTION,
            self::GRAND_TOTAL
        );
    }

    protected function formatSheet($upperLeftCell, $row, $spreadsheet)
    {
        $finalRow = $row  + 1; // take header rows into account
        $defaultFont = 'Calibri';
        $defaultFontSize = 11;

//        BOLD Header (row 1) and set background color
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':S' . self::HEADER_ROW)
            ->applyFromArray($this->getHeaderFormat($defaultFont, $defaultFontSize));
        $spreadsheet->getActiveSheet()->getStyle('A' . self::HEADER_ROW . ':S' . self::HEADER_ROW)
            ->getAlignment()->setWrapText(true);
//        use this to shade headers for dues, account numbers and other auditing/non-essential data
//        $spreadsheet->getActiveSheet()->getStyle('K' . self::HEADER_ROW . ':O' . self::HEADER_ROW)
//            ->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB(self::HEADER_ROW_SHADING);


//        +++++ SET FONT ++++++
        $spreadsheet->getActiveSheet()->getStyle($upperLeftCell . ':' . $this->col_GrandTotal . $finalRow)
            ->getFont()
            ->setName($defaultFont)
            ->setSize($defaultFontSize);

        //        +++++ SET COLUMN WIDTH +++++
//     AUTO-SIZE name, project, instrument, classification, group
        foreach (range($this->col_Musician, $this->col_Activity) as $col) {
            $spreadsheet->getActiveSheet()
                ->getColumnDimension($col)
                ->setAutoSize(true);
        }

//        Center Work Dues y/n column
        $spreadsheet->getActiveSheet()->getStyle($this->col_DuesYN . '1:' . $this->col_DuesYN.$finalRow)
            ->getAlignment()->setHorizontal('center');

//        Set Number columns as 2-decimal
        $spreadsheet->getActiveSheet()->getStyle('M'. self::DATA_ROW . ':' . 'S' . $finalRow)->getNumberFormat()
            ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);

        //       +++++ Freeze Panes +++++ after header row
        $spreadsheet->getActiveSheet()->freezePane('C' . self::DATA_ROW);

        //        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
        }
    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

//            'fill' => array(
//                'fillType' => Fill::FILL_SOLID,
//                'startColor' => array('rgb' => '3E5634') // Green House
//            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
//                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            ),



        );
    }
}
