<?php

namespace Customer\obf\reports\WorkList;

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;


class WorkListStyles
{


    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(9);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setBold(true);
        $this->titleFont->setSize(14);
        $this->titleFont->setColor('901414');

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(12);
        $this->headerFont->setColor('901414');
    }

    /**
     * Define the styles for this section
     */
    protected function getStyles()
    {
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopColor' => 'b3b6ba',
            'borderTopSize' => 4
        );

        $this->shadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'ECEFE0'
        );

        $this->titleCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderBottomColor' => 'b3b6ba',
            'borderBottomSize' => 4
        );


        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );


        // Define paragraph style for footer
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        $this->footerParagraphStyle = $phpWord->addParagraphStyle(
            'footerParagraphStyle',
            array(
                'alignment' => 'left',  // left; Right; Center; other options available
                'spaceBefore' => 100,
                'spaceAfter' => 0,
                'spacing' => 0,  // space between lines in twips
                'borderTopSize' => 6,
                'borderColor' => "ded6d5",  // light grey top border
                'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
                    new \PhpOffice\PhpWord\Style\Tab('center', 5040),
                    new \PhpOffice\PhpWord\Style\Tab('right', 10400),
                ),
            )
        );
    }

}


