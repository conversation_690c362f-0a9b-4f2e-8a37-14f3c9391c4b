<?php

namespace Customer\obf\reports\ProjectDetailsLandscape;

use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DateWorkQueryHelper;
use Customer\obf\reports\utility\ComposerQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

class ProjectDetailsLandscapeProgram
{
    const COL_1_WIDTH = 0.25;
    const COL_2_WIDTH = 2.25;
    const COL_3_WIDTH = 3.75;
    const COL_4_WIDTH = 2.75;
    const COL_5_WIDTH = 0.50;

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\obf\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(int $dateId, array $dateWorkOrderLetters, Section $pageSection, string $encore)
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->reportStyles = new ProjectDetailsLandscapeStyles();

        $this->dateId = $dateId;
        $this->dateWorkOrderLetters = $dateWorkOrderLetters;
        $this->pageSection = $pageSection;
        $this->encore = $encore;
    }


    public function renderProgram()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $programTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

//        use separate method so movements, soloists and date-work conductors can be returned
        $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($this->dateId)
            ->withDateWorkSoloists()
            ->withDateWorkConductor()
            ->withMovements()
            ->getQuery()->toArray();

        $concertDuration = 0;

        foreach ($dateWorks as $dateWork) {
            $programTable->addRow();
            $firstColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_1_WIDTH),
                $this->reportStyles->defaultCell
            );
            $secondColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_2_WIDTH),
                $this->reportStyles->defaultCell
            );
            $thirdColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->defaultCell
            );
            $fourthColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_4_WIDTH),
                $this->reportStyles->defaultCell
            );
            $fifthColumnCell = $programTable->addCell(
                Converter::inchToTwip(self::COL_5_WIDTH),
                $this->reportStyles->defaultCell
            );

//            FIRST COLUMN
//            Extract the work order number as a letter - this is the key in the dateWorkOrderLetters array
            $firstColumnCell->addText(
                array_search($dateWork['work_id'], $this->dateWorkOrderLetters),
                $defaultFont,
                $this->reportStyles->defaultParagraphRight
            );

//            SECOND COLUMN - composer
            if ($dateWork['swork']['l_intermission'] == 0) {
                $composerTextRun = $secondColumnCell->addTextRun($defaultParagraph);
                if ($dateWork['l_encore'] == 1) {
                    $composerTextRun->addText($this->encore, $defaultFont, $defaultParagraph);
                    $composerTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);
                }
                $composerName = $this->composerQueryHelper->getComposerName(
                    $dateWork['swork']['scomposer']['lastname'],
                    $dateWork['swork']['scomposer']['firstname'],
                    1
                );
                $composerName .= $dateWork['arrangement'] ? ' (' . $dateWork['arrangement'] . ')' : '';
            } else {
                $composerName = '';
            }
            $composerTextRun->addFormattedText($composerName, $defaultFont, $defaultParagraph);

//            THIRD COLUMN - work and movements
            $titleTextRun = $thirdColumnCell->addTextRun($defaultParagraph);
            if ($dateWork['l_encore'] == 1) {
                $titleTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);
            }
            if (!empty(trim($dateWork['title2']))) {
                $dateWorkTitle = $dateWork['title2'];
            } else {
                $dateWorkTitle = $dateWork['swork']['title1'];
            }
            $titleTextRun->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);
            $premiere = $dateWork['Sworkpremieres']['name'] ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                    'premiere'
                ) : ' ';
            $titleTextRun->addText($premiere, $this->reportStyles->defaultFontItalic, $defaultParagraph);
            $this->renderDateWorkMovements($dateWork, $thirdColumnCell, $defaultFont);

//            FOURTH COLUMN - date-work conductor and soloist
            $this->renderDateWorkConductor($dateWork, $fourthColumnCell, $defaultFont, $defaultParagraph);
            $this->renderDateWorkSoloists($dateWork, $fourthColumnCell, $defaultFont, $defaultParagraph);

//            FIFTH COLUMN - work duration
            $workDurTextRun = $fifthColumnCell->addTextRun($this->reportStyles->defaultParagraphRight);
            $workDur = $this->dateWorkQueryHelper->getDateWorkDuration($dateWork['duration'], $concertDuration);
            if ($dateWork['l_encore'] == 1) {
                $workDurTextRun->addTextBreak(1, $defaultFont, $defaultParagraph);
            }
            $workDurTextRun->addText($workDur, $defaultFont, $this->reportStyles->defaultParagraphRight);

//           EMPTY ROW between Works
            $programTable->addRow();
            $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $this->reportStyles->defaultParagraphRight)
                ->addText('', $defaultFont, $defaultParagraph);
        }

        $finalHours = (int)floor($concertDuration / 3600);
        $finalMinutes = (int)floor((($concertDuration - ($finalHours * 3600)) / 60));
        $finalSeconds = (int)floor(
            ((($concertDuration - ($finalHours * 3600))) - ($finalMinutes * 60))
        );
//        insert leading zero for minutes and seconds if less than 10
        if ($finalMinutes < 10) {
            $finalMinutes = '0' . $finalMinutes;
        }
        if ($finalSeconds < 10) {
            $finalSeconds = '0' . $finalSeconds;
        }
        $concertDurationString = $finalHours . ':' . $finalMinutes . ':' . $finalSeconds;
        $programTable->addRow();
        $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $programTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $this->reportStyles->defaultCell)
            ->addText(__('total'), $defaultFont, $this->reportStyles->defaultParagraphRight);
        $programTable->addCell(Converter::inchToTwip(self::COL_5_WIDTH), $this->reportStyles->topBorderCell)
            ->addText($concertDurationString, $defaultFont, $this->reportStyles->defaultParagraphRight);
    }

    protected function renderDateWorkSoloists($dateWork, $fourthColumnCell, $defaultFont, $defaultParagraph)
    {

        //        sort date-work soloists by artist order by work_order
        $soloists = $dateWork['adatework_soloists'];
        foreach ($soloists as $key => $value) {
            $programOrder[$key] = $value['artist_order'];
        }
        array_multisort($programOrder, SORT_ASC, $soloists);
        $soloistArtistArray = [];

        foreach ($soloists as $soloist) {
//                  soloist artist ID values to compare against date-work conductors
            $soloistArtistArray[] = $soloist['artist_id'];
            $soloistName = trim(
                $soloist['saddress']['name2'] . ' ' . $soloist['saddress']['name1']
            );

//                  if the soloist is also the DATE-WORK conductor (rare) then output 'conductor and' before the instrument
            if ($soloist['artist_id'] === $dateWork['conductor_id']) {
                $soloistName .= mb_strtolower(
                        ', ' . __('adates.conductor_id') . ' ' . __('and')
                    ) . ' ' . mb_strtolower(
                        $soloist['sinstrinstrument']['name']
                    );
            } else {
                if ($soloist['sinstrinstrument']['sinstrsection']['syssection_id'] != 19) {
                    $soloistName .= ', ' . mb_strtolower($soloist['sinstrinstrument']['name']);
                }
            }
            $fourthColumnCell->addFormattedText(
                $soloistName,
                $defaultFont,
                $defaultParagraph
            );
        }
    }

    protected function renderDateWorkConductor($dateWork, $fourthColumnCell, $defaultFont, $defaultParagraph)
    {
        /*
        *        IF the Date-Work conductor is ALSO a soloist, do nothing as the conductor indication is output with his/her
        *           soloist output. Otherwise, fetch the name
        */
        $soloistArtistIdArray = [];
        foreach ($dateWork['adatework_soloists'] as $dateWorkSoloist) {
            $soloistArtistIdArray[] = $dateWorkSoloist['artist_id'];
        }

        if ($dateWork['conductor_id'] > 0 && in_array(
                $dateWork['conductor_id'],
                $soloistArtistIdArray,
                true
            ) == false) {
            $dateWorkConductorOutput = trim(
                htmlspecialchars(
                    $dateWork['conductoraddress']['name2']
                    . ' ' . $dateWork['conductoraddress']['name1']
                    . ', ' . mb_strtolower(__('adates.conductor_id'))
                )
            );
            $fourthColumnCell->addText(
                $dateWorkConductorOutput,
                $defaultFont,
                $defaultParagraph
            );
        }
    }

    protected function renderDateWorkMovements($dateWork, $thirdColumnCell, $defaultFont)
    {
//        ensure movements are in proper order
        $dateWorkMovements = $dateWork['adatework_movements'];
        foreach ($dateWorkMovements as $key => $value) {
            $movementOrder[$key] = $value['movement_order'];
        }
        array_multisort($movementOrder, SORT_ASC, $dateWorkMovements);

        foreach ($dateWorkMovements as $dateWorkMovement) {
            $thirdColumnCell->addFormattedText(
                trim($dateWorkMovement['name']),
                $defaultFont,
                $this->reportStyles->movementParagraph
            );
        }
    }


}
