<?php

namespace Customer\obf\reports\ProjectDetailsLandscape;

use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;


class ProjectDetailsLandscapeStyles
{
    const OPAS_RED = '842020';

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Cal<PERSON>ri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(11);
        $this->headerFont->setColor(self::OPAS_RED); // dark red

        $this->headerFontBlack = clone($this->defaultFontBold);
        $this->headerFontBlack->setSize(11);

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setBold(true);
        $this->titleFont->setSize(11);
        $this->titleFont->setColor(self::OPAS_RED);

        $this->instFont = clone($this->defaultFont);
        $this->instFont->setSize(8);

        $this->instFontBold = clone($this->defaultFontBold);
        $this->instFontBold->setSize(8);
    }

//    Paragraph  |  Table  |  Cell styles
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->leftHeaderParagraph = array_merge(
            $this->defaultParagraph,
            ['indent' => -0.16]  // .08 inches
        );

//        section headers; background shading
        $this->sectionHeaderParagraph = array_merge(
            $this->defaultParagraph,
            ['shading' => array('fill' => 'E4E9DD')] // Catskill White
        );

        $this->movementParagraph = [
            'spaceAfter' => 0,
            'spacing' => 0,
            'indent' => 0.6  // indent is in HALF-INCHES
//            'tabs' => array(
//                new Tab('right', 6552, ' ') // 4.55 in
//            )
        ];

        $this->footerParagraph  = [
            'alignment' => 'left',
            'spaceBefore' => 100,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
            'borderTopSize' => 4,
            'borderTopColor' =>self::OPAS_RED,
            'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
                new \PhpOffice\PhpWord\Style\Tab('center', 6480),  // 4.5
                new \PhpOffice\PhpWord\Style\Tab('right', 13968),  // 9.5 in.
            )
        ];

        $this->headerParagraph  = [
            'alignment' => 'left',
            'spaceBefore' => 0,
            'spaceAfter' => 100,
            'spacing' => 0,  // space between lines in twips
            'borderBottomSize' => 4,
            'borderBottomColor' =>self::OPAS_RED,
            'tabs' => array(
//                new \PhpOffice\PhpWord\Style\Tab('left', 1550),
//                new \PhpOffice\PhpWord\Style\Tab('center', 6480),  // 4.5
                new \PhpOffice\PhpWord\Style\Tab('right', 13968),  // 9.5 in.
            )
        ];

//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 13680, // 9.50 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];




//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

//     instrumentation grid below the program has a thin top border
        $this->instGridCell =  array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopSize' => 4,
            'borderTopColor' => self::OPAS_RED
        );

        $this->topBorderCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopSize' => 6,
        );

        $this->scheduleCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopSize' => 6,
        );

    }


}
