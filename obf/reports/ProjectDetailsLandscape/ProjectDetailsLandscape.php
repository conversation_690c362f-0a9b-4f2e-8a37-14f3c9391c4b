<?php

namespace Customer\obf\reports\ProjectDetailsLandscape;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;

use Customer\obf\reports\utility\DateQueryHelper;
use Customer\obf\reports\utility\DatePerformerHelper;

use Customer\obf\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;

use ReflectionException;

/**
 *  Report groups by Project - header with performers, then program with instrumentation,
 *    followed by max instrumentation followed by schedule
 */
class ProjectDetailsLandscape extends ReportWord
{

//    Header table is a single-row 2-column table. The
//     column widths are set to line up with the program table, so if
//      changes are made in the program class, make them here as well
    const COL_1_WIDTH = 6.25;
    const COL_2_WIDTH = 3.25;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    private $reportStyles;

    /**
     * Initialize
     * Set the query helper for this report container and define the styles
     */
    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();

        // Set report font, table and paragraph styles
        $this->reportStyles = new ProjectDetailsLandscapeStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
    the <text></text> section of the .rep file.
     */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Work records selected by user to workQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    private function renderReport()
    {
//        Set formats based on client location and other settings.php values
        if (Configure::read('Formats.region') === 'North America') {
            $paperSize = "Letter";
        } else {
            $paperSize = "A4";
        }
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');

//        headers/labels from the report .rep file
        $repFileText = $this->getRepFileParams();

        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => 'landscape',
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

        // insert header and footer. take Season Name from first date record
        $firstDate = reset($this->datesResult);
        $seasonName = $firstDate['sseason']['name'];
        $header = $pageSection->addHeader();
        $header->addText(
            $repFileText['report_title'] . "\t" . $seasonName,
            $this->reportStyles->headerFont,
            $this->reportStyles->headerParagraph
        );
        $footer = $pageSection->addFooter();
        $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $defaultFont,
            $this->reportStyles->footerParagraph,
        );

//        Group all dates selected by user into unique season:project:programNumber. Loop through these and put a page break between them
        $p = 0;
        foreach ($this->getProjects() as $project) {
//            Identify the Anchor Date (first performance) for season:project and use it for title, conductor, etc
            $anchorDate = $this->getAnchor($project);

            foreach ($this->datesResult as $dateResult) {
                if ($dateResult['id'] === $anchorDate) {
                    if ($p > 0) {
                        $pageSection->addPageBreak();
                    }

//                    create the array of work order letters and work id so repertoire can be consistently
//                    labeled in the program, instrumentation and schedule sections
                    $dateWorkOrderLetters = $this->getDateWorkOrderMap($dateResult);

//                +++  RENDER PROJECT HEADER - Project Name, Program Title and Performance Dates +++
                    $headerProject = mb_strtoupper($dateResult['sproject']['name']);
                    if (!is_null($dateResult['programno']) && (string)trim($dateResult['programno']) !== '') {
                        $headerProject .= ' - ' . ($repFileText['programNo'] ?? __('adates.programno'));
                        $headerProject .= ' ' . $dateResult['programno'];
                    }
                    $pageSection->addFormattedText(
                        $headerProject,
                        $this->reportStyles->headerFont,
                        $defaultParagraph
                    );
                    if (!is_null($dateResult['programtitle']) && (string)trim($dateResult['programtitle']) !== '') {
                        $pageSection->addFormattedText(
                            $dateResult['programtitle'],
                            $this->reportStyles->headerFont,
                            $defaultParagraph
                        );
                    }

                    $headerTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
                    $headerTable->addRow();
                    $leftHeaderColumn = $headerTable->addCell(
                        Converter::inchToTwip(self::COL_1_WIDTH),
                        $this->reportStyles->defaultCell
                    );
                    $rightHeaderColumn = $headerTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->defaultCell
                    );

//            Render all Concert Dates in the Season+Project+ProgramNumber set.
//            If all concerts take place in the same venue, venue name is below concert dates, otherwise after concert date
                    foreach ($this->datesResult as $headerDate) {
                        if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                            && $headerDate['seventtype']['l_performance'] == 1) {
                            $printConcertDate = $headerDate['date_']->format($topDateFormat)
                                . ' ' . __('at') . ' ' . $headerDate['start_']->format($topTimeFormat);

                            if ($headerDate['location_id'] > 0) {
                                $headerVenue = $this->getHeaderVenues($project);
//                   if $headerVenue is empty, then there is more than one concert venue so append venue name to each concert date
                                if ((string)trim($headerVenue) == '') {
                                    $headerVenueName = $headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1'];
                                    $printConcertDate .= ' - ' . trim(htmlspecialchars($headerVenueName));
                                }
                            } else {
                                $headerVenue = '';
                            }

//                            the leftHeaderParagrpah indents the text .08 in to the LEFT so it lines up
//                            with the project name above; this eliminates the need for a whole new table style
//                            with zero margin sizes
                            $leftHeaderColumn->addText(
                                $printConcertDate,
                                $this->reportStyles->headerFontBlack,
                                $this->reportStyles->leftHeaderParagraph
                            );
                        }
                    }
                    //              If all concerts take place in the same venue, print that venue under the final concert date
                    if ((string)trim($headerVenue) !== '') {
                        $leftHeaderColumn->addText(
                            $headerVenue,
                            $this->reportStyles->headerFontBlack,
                            $this->reportStyles->leftHeaderParagraph
                        );
                    }

//                  +++  MAIN CONDUCTOR, DATE-WORK CONDUCTOR, SOLOISTS  ++++
//                   Use the separate class for conductor and soloist(s) and date-work conductors in the report header.
//                    Main conductor, dote-work conductor and soloist are output separately for greater control over formatting
                    $headerPersonnel = new DatePerformerHelper();
                    $mainConductor = $headerPersonnel->getConductor(
                        $dateResult['id'],
                        $dateResult['conductor_id'],
                        0
                    );
                    if (!is_null($mainConductor) && (string)trim($mainConductor) !== '') {
                        $rightHeaderColumn->addText(
                            $mainConductor,
                            $this->reportStyles->headerFontBlack,
                            $defaultParagraph
                        );
                    }
//                    function returns date-work conductors in a single text string
                    $dateWorkConductors = $headerPersonnel->getDateWorkConductors(
                        $dateResult['id'],
                        '; ',
                        0
                    );
                    if (!is_null($dateWorkConductors) && (string)trim($dateWorkConductors) !== '') {
                        $rightHeaderColumn->addText(
                            $dateWorkConductors,
                            $this->reportStyles->headerFontBlack,
                            $defaultParagraph
                        );
                    }

//                    function returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
                    $mainSoloist = $this->datePerformerHelper->getSoloists(
                        $dateResult['id'],
                        $dateResult['conductor_id'],
                        '; ',
                        0
                    );
                    if (!is_null($mainSoloist) && (string)trim($mainSoloist) !== '') {
                        $rightHeaderColumn->addText(
                            $mainSoloist,
                            $this->reportStyles->headerFontBlack,
                            $defaultParagraph
                        );
                    }

                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $pageSection->addText(
                        $repFileText['programHeader'],
                        $this->reportStyles->headerFontBlack,
                        $this->reportStyles->sectionHeaderParagraph
                    );
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                    +++ CONCERT PROGRAM +++
                    $concertProgram = new ProjectDetailsLandscapeProgram(
                        $dateResult['id'],
                        $dateWorkOrderLetters,
                        $pageSection,
                        'ENCORE'
                    );
                    $concertProgram->renderProgram();


//                    +++ INSTRUMENTATION TABLE +++
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $instrumentation = new ProjectDetailsLandscapeInstrumentation(
                        $dateResult,
                        $dateWorkOrderLetters,
                        $pageSection,
                        $repFileText
                    );
                    $instrumentation->renderInstrumentation();

//            all selected events in the season+project+program number are sent to the schedule class
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                    $pageSection->addText(
                        $repFileText['scheduleHeader'],
                        $this->reportStyles->headerFontBlack,
                        $this->reportStyles->sectionHeaderParagraph
                    );
                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

                    $scheduleArray = $this->getScheduleDates(
                        $project,
                        $dateResult['season_id'],
                        $dateResult['project_id']
                    );

                    $schedule = new ProjectDetailsLandscapeSchedule(
                        $scheduleArray,
                        $dateWorkOrderLetters,
                        $pageSection,
                        $repFileText
                    );
                    $schedule->renderSchedule();
                }
            }


            $p++;
        }
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//   Organize all selected events into season:project groups
    private function getProjects()
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                ) != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                        $dateResult['programno']
                    );
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                );
        }
        return array_unique(array_filter($projectArray));
    }


//    the Anchor Date is the first concert in the Season:Project. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the Header
    private function getAnchor($project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                    $dateResult['programno']
                ) === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult['id'];
            }
        }
    }


    /*
     *   For the SCHEDULE portion of the report we need all date records within the Season + Project, but IGNORING the Program Number
     *           This is because rehearsals within the Project often do NOT have a program number indication; if we use the $project
     *          variable, those rehearsals are omitted and the schedule is incomplete
     *   So we fetch all performances with the Program Number and all other events without the program number. This does result in some
     *          replication in schedule output but matches the use case
     */
    private function getScheduleDates($project, $seasonId, $projectId)
    {
        $scheduleDateArray = [];
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['seventtype']['l_performance'] == 1 && ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . trim(
                        $dateResult['programno']
                    ) === $project) || $dateResult['seventtype']['l_performance'] == 0 && $dateResult['season_id'] === $seasonId && $dateResult['project_id'] === $projectId) {
                $scheduleDateArray[] = $dateResult;
            }
        }
        return array_unique(array_filter($scheduleDateArray));
    }

    protected function getDateWorkOrderMap($dateResult)
    {
        /*          Create the array in which the KEY is the Program Order number as a letter, and
        *               and the VALUE is the WORK ID. This array is passsed to other functions so
         *              works can be referred to by "A", "B", "C" etc. instead of by composer and title
         *          TODO: figure a way to do this if the same work is performed twice on a program; very
         *              rare use-case but not impossible
         */
        $alphabet = range('A', 'Z');
        $dateWorkOrderMap = [];

//        First sort date-works by work_order
        $dateWorkArray = $dateResult['adate_works'];
        foreach ($dateWorkArray as $key => $value) {
            $programOrder[$key] = $value['work_order'];
        }
        array_multisort($programOrder, SORT_ASC, $dateWorkArray);

//        then map the work order to the proper letter
        foreach ($dateWorkArray as $dateWork) {
            $dateWorkOrderMap[$alphabet[$dateWork['work_order'] - 1]] = $dateWork['work_id'];
        }

        return $dateWorkOrderMap;
    }

    public function getHeaderVenues($seasonProjectCombo)
    {
        $headerVenues = [];
        foreach ($this->datesResult as $venueDate) {
            if ($venueDate['season_id'] . ':' . $venueDate['project_id'] . ':' . $venueDate['programno'] == $seasonProjectCombo
                && $venueDate['seventtype']['l_performance'] == 1) {
//            entire address array is fetched in case the venue city, address or other info is needed
                $headerVenues[] = trim(
                    htmlspecialchars(
                        $venueDate['locationaddress']['name2']
                        . ' ' . $venueDate['locationaddress']['name1']
                    )
                );
            }
        }
        $venueCount = sizeof(array_unique($headerVenues));
//        if there is only one venue, return that one name as a string and it will be displayed UNDER the concert dates
        if ($venueCount == 1) {
            return implode('', array_unique($headerVenues));
        }
    }

}
