<?php

namespace Customer\obf\reports\OBFArtistItinerary;

use PhpOffice\PhpWord\Shared\Converter;

// Fixed Text at the bottom of the report
class OBFArtistItineraryFixedText
{

    const COL_1_WIDTH = 1.75;
    const COL_2_WIDTH = 2.50;
    const COL_3_WIDTH = 3.00;



    private $reportStyles;
    private $pageSection;

    public function __construct($pageSection)
    {
        $this->pageSection = $pageSection;
        $this->reportStyles = new OBFArtistItineraryStyles();
    }

    public function renderContactTable()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $this->pageSection->addText('OBF Contacts', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $this->pageSection->addText(
            'Should you have questions, please do not hesitate to call or email:',
            $defaultFont,
            $defaultParagraph
        );

        $contactTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $contactTable->addRow();
        $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->borderCell)
            ->addText('Name', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->borderCell)
            ->addText('Title', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->borderCell)
            ->addText('Phone/Email', $this->reportStyles->defaultFontBold, $defaultParagraph);

        $contactTable->addRow();
        $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->borderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->borderCell)
            ->addText('', $defaultFont, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->borderCell)
            ->addText('', $defaultFont, $defaultParagraph);

    }

}
