<?php

namespace Customer\obf\reports\OBFArtistItinerary;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;

class OBFArtistItineraryStyles
{
    const BLACK = '000000';

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Californian FB';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(11);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->smallFont = clone($this->defaultFont);
        $this->smallFont->setSize(5);

        $this->headerFont = clone($this->defaultFontBold);
        $this->headerFont->setSmallCaps(true);
        $this->headerFont->setSize(11);
        $this->headerFont->setUnderline('single');

        $this->schedHeaderFont = clone($this->defaultFontBold);
        $this->schedHeaderFont->setSmallCaps(true);
        $this->schedHeaderFont->setSize(10);

        $this->titleFont = clone($this->defaultFontBold);
        $this->titleFont->setSize(18);
        $this->titleFont->setSmallCaps(true);
    }

    /**
     * Define the styles for the report
     * Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->headerParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 12,
            'spacing' => 120,  // space between lines in twips
        ];

//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10080, // 7.00 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];


//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0
        );


        $this->borderCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderColor' => self::BLACK,
            'borderSize' => 6
        );

        $this->topBorderCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopColor' => self::BLACK,
            'borderTopSize' => 6
        );

    }






}
