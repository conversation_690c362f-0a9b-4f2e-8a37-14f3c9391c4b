<?php

namespace Customer\obf\reports\OBFConcertProgram;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DateWorkQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\reports\utility\ComposerQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use Carbon\Carbon;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Client version of Standard Concert Program - changes in top and bottom of report; program table
 * essentially the same
 * Each project + program number composition
 * Conductor, date-work conductor, soloists, works and movements
 */

class OBFConcertProgram extends ReportWord
{
    const COL_1_WIDTH = 2.45;
    const COL_2_WIDTH = 3.85;
    const COL_3_WIDTH = 1.00;

    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for the compositions on each concert
     *
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        // Set report font, table and paragraph styles
        $this->reportStyles = new OBFConcertProgramStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Work records selected by user to workQueryHelper
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withMarketing()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//    Typically the use case is to run this report for one project at a time. However, the user could run it for
//      more than one. this function breaks the selected dates down into each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concert Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all
//      the representative conductor, soloist, title, etc. information so it is used for the Header and program
    private function getAnchorId(string $project): int
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult['id'];
            }
        }
        return 0;
    }

    private function getAnchorDate(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
        return '';
    }


    private function renderReport()
    {
//        Set report formats based on client region and preferences
        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
        $topDateFormat = 'l, F j';
        $topTimeFormat = 'g:i A';
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $unicodeCircle = html_entity_decode('&#9679;', 0, 'UTF-8');

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        for Dates selected by User, loop through Season+Project+ProgramNumber, putting a page break between each
        $p = 0;
        foreach ($this->getProjects() as $project) {
            if ($p > 0) {
                $pageSection->addPageBreak();
            }

//            Fetch Anchor Date as date object
            $concertDate = $this->getAnchorDate($project);

//            RENDER TITLE
//            TG: 2024-Apr. client adds title in all caps and subtitle form marketing page
            if (!is_null($concertDate['programtitle']) && trim($concertDate['programtitle']) !== '') {
                $pageSection->addFormattedText(
                    strtoupper($concertDate['programtitle']),
                    $this->reportStyles->titleFont,
                    $this->reportStyles->defaultParagraphCenter
                );
            }
            if (!is_null($concertDate['adates_marketing']['text_3']) && trim(
                    $concertDate['adates_marketing']['text_3']
                ) !== '') {
                $pageSection->addFormattedText(
                    strtoupper($concertDate['adates_marketing']['text_3']),
                    $this->reportStyles->subtitleFont,
                    $this->reportStyles->defaultParagraphCenter
                );
            }
            $pageSection->addText('', $this->reportStyles->dateHeaderFont, $defaultParagraph);

//            Top section of report prints all concert dates in the Season+Project+ProgramNumber set.
            foreach ($this->datesResult as $headerDate) {
                if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                    && $headerDate['seventtype']['l_performance'] == 1) {
                    $printConcertDate =  strtoupper($headerDate['date_']->format($topDateFormat))
                        . ' ' . $unicodeCircle . ' ' . $headerDate['start_']->format($topTimeFormat);

                    $pageSection->addText(
                        $printConcertDate,
                        $this->reportStyles->dateHeaderFont,
                        $this->reportStyles->defaultParagraphCenter
                    );

                    if (!is_null($headerDate['locationaddress']['text_1']) && trim(
                            $headerDate['locationaddress']['text_1']
                        ) !== '') {
                        $headerVenueName = htmlspecialchars(strtoupper($headerDate['locationaddress']['text_1']));
                    } else {
                        $headerVenueName = strtoupper(
                            $headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1']
                        );
                    }
                    $pageSection->addText(
                        $headerVenueName,
                        $this->reportStyles->dateHeaderFont,
                        $this->reportStyles->defaultParagraphCenter
                    );
                }
            }

            $pageSection->addText('', $this->reportStyles->dateHeaderFont, $defaultParagraph);
            $pageSection->addText('', $this->reportStyles->dateHeaderFont, $defaultParagraph);
            $pageSection->addText('', $this->reportStyles->dateHeaderFont, $this->reportStyles->bottomBorderParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);


//         +++ RENDER ALL OTHER INFO USING THE ANCHOR DATE +++
//          ++++  RENDER  CONCERT PROGRAM ++++
            $programTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($concertDate['id'])
                ->withMovements()
                ->withDateWorkConductor()
                ->getQuery()
                ->toArray();
            $composer = 0;
            foreach ($dateWorks as $dateWork) {
                if ($dateWork['l_encore'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                    $programTable->addRow();
                    $leftColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_1_WIDTH),
                        $this->reportStyles->leftColumnCell
                    );
                    $centerColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->centerColumnCell
                    );
                    $rightColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_3_WIDTH),
                        $this->reportStyles->defaultCell
                    );

//                            RENDER COMPOSER and DATES - suppress composer name if same composer as previous work
                    if ($dateWork['swork']['l_intermission'] == 0
                        && $dateWork['swork']['composer_id'] != $composer) {
                        $composerName = $this->composerQueryHelper->getComposerName(
                            $dateWork['swork']['scomposer']['lastname'],
                            $dateWork['swork']['scomposer']['firstname'],
                            1
                        );
                        $composerDates = $this->composerQueryHelper->getComposerDates(
                            $dateWork['swork']['scomposer']['birthyear'],
                            $dateWork['swork']['scomposer']['deathyear']
                        );
                    } else {
                        $composerName = '';
                        $composerDates = '';
                    }
                    if (!is_null($dateWork['arrangement']) && trim($dateWork['arrangement']) != '') {
                        $arranger =   ' (' . $dateWork['arrangement'] . ')'  ;
                    } else {
                        $arranger = '';
                    }


                    $leftColumnCell->addFormattedText(
                        mb_strtoupper($composerName),
                        $this->reportStyles->defaultFontBold,
                        $this->reportStyles->defaultParagraphRight
                    );
                    if (!empty($composerDates)) {
                        $leftColumnCell->addText(
                            $composerDates,
                            $defaultFont,
                            $this->reportStyles->defaultParagraphRight
                        );
                    }
                    if (!empty($arranger)) {
                        $leftColumnCell->addFormattedText(
                            $arranger,
                            $defaultFont,
                            $this->reportStyles->defaultParagraphRight
                        );
                    }

//                       RENDER WORK TITLE -- first of print title or master title. Text run needed to
//                            render different font style for premiere
                    $workTitle = $centerColumnCell->addTextRun($defaultParagraph);
                    if (!empty(trim($dateWork['title2']))) {
                        $dateWorkTitle = $dateWork['title2'];
                    } else {
                        $dateWorkTitle = $dateWork['swork']['title1'];
                    }
                    $workTitle->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);
                    $premiere = $dateWork['Sworkpremieres']['name'] ? ' (' . $dateWork['Sworkpremieres']['name'] . ') ' . __(
                            'premiere'
                        ) : ' ';
                    $workTitle->addText($premiere, $defaultFont, $defaultParagraph);
//                    TG: 2024-apr. add commission
                    if (!is_null($dateWork['swork']['commission']) && trim($dateWork['swork']['commission']) != '') {
                        $workTitle->addText( ' (' . $dateWork['swork']['commission']. ') ', $defaultFont, $defaultParagraph);
                    }

//                           DATE WORK DURATION replaces composition year
                    if (is_null($dateWork['duration']) || (string)trim($dateWork['duration']) == '') {
                        $dateWorkDuration = '00:00:00';
                    } else {
                        $dateWorkDuration = str_replace('_', '0', $dateWork['duration']);
                    }
                    $carbonWorkDur = Carbon::createFromFormat('H:i:s', $dateWorkDuration);
                    $workDurMinutes = round($carbonWorkDur->secondsSinceMidnight() / 60);
                    if ($workDurMinutes > 0) {
                        $dateWorkDur = $workDurMinutes . "'";
                    }
                    $rightColumnCell->addText($dateWorkDur, $defaultFont, $this->reportStyles->defaultParagraphRight);

                    /*
                    *                  Loop through MOVEMENTS - movements are an array within the date-works variable
                    */
                    foreach ($dateWork['adatework_movements'] as $datework_movement) {
                        if (strlen(trim($datework_movement['name'])) > 0) {
                            $centerColumnCell->addFormattedText(
                                trim($datework_movement['name']),
                                $defaultFont,
                                $this->reportStyles->movementParagraph
                            );
                        }
                    }


//                            +++ RENDER DATE-WORK CONDUCTORS and SOLOISTS +++
//                            Fetch soloists (array)
                    $soloists = $this->dateWorkQueryHelper->getSoloistsForDateWorkID($dateWork['id'])->getQuery(
                    )->toArray();

//                          Fetch date-work conductor as string
                    if ($dateWork['conductor_id'] > 0) {
                        $dateWorkConductor = $this->dateWorkQueryHelper->getDateWorkConductorAsString(
                            $soloists,
                            $dateWork['conductor_id'],
                            0,
                            0,
                            0,
                            ', ',
                            ';'
                        );
                        $centerColumnCell->addText(
                            $dateWorkConductor,
                            $this->reportStyles->defaultFontBold,
                            $this->reportStyles->defaultParagraphCenter
                        );
                    }

                    $dateWorkSoloists = $this->dateWorkQueryHelper->getDateWorkSoloistsAsString(
                        $soloists,
                        $dateWork['conductor_id'] ?? 0,
                        '</w:t><w:br/><w:t>',
                        0,
                        0,
                        ', '
                    );
                    $centerColumnCell->addText(
                        $dateWorkSoloists,
                        $this->reportStyles->defaultFontBold,
                        $this->reportStyles->defaultParagraphCenter
                    );


//                          RENDER EMPTY ROW between works
                    $programTable->addRow();
                    $programTable->addCell(
                        Converter::inchToTwip(self::COL_1_WIDTH),
                        $this->reportStyles->leftColumnCell
                    )
                        ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
                    $programTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->centerColumnCell
                    )
                        ->addText('', $defaultFont, $this->reportStyles->defaultParagraphCenter);
                    $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH))
                        ->addText('', $defaultFont, $defaultParagraph);
                }

                $composer = $dateWork['swork']['composer_id'];
            }


//                   +++ RENDER HEADER PERSONNEL and CONCERT TITLE
//            TG: 2024-Apr. add sponsor and media and dedication data from MARKETING additional data

            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $this->reportStyles->bottomBorderParagraph);
            $this->renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $mediaTextRun = $pageSection->addTextRun($defaultParagraph);
            $mediaTextRun->addText('Sponsor: ', $defaultFont);
            if (!is_null($concertDate['adates_marketing']['text_1']) && trim(
                    $concertDate['adates_marketing']['text_1']
                ) !== '') {
                $mediaTextRun->addFormattedText($concertDate['adates_marketing']['text_1'], $defaultFont);
                $mediaTextRun->addText("\t ", $defaultFont);
            } else {
                $mediaTextRun->addText('							', $defaultFont); // bunch of tabs
            }
            $mediaTextRun->addText('Media Partner: ', $defaultFont);
            if (!is_null($concertDate['adates_marketing']['text_2']) && trim(
                    $concertDate['adates_marketing']['text_2']
                ) !== '') {
                $mediaTextRun->addFormattedText($concertDate['adates_marketing']['text_2'], $defaultFont);
            }
//            TB: 2024-May; add blank line before dedication
            if (!is_null($concertDate['adates_marketing']['text_6']) && trim(
                    $concertDate['adates_marketing']['text_6']
                ) !== '') {
                $pageSection->addText('', $defaultFont, $defaultParagraph);
            }
            $pageSection->addFormattedText(
                $concertDate['adates_marketing']['text_6'],
                $defaultFont,
                $this->reportStyles->defaultParagraphCenter
            );
            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $this->reportStyles->bottomBorderParagraph);

            $p++;
        }
    }

    public function getHeaderVenues($seasonProjectCombo)
    {
        $headerVenues = [];
        foreach ($this->datesResult as $venueDate) {
            if ($venueDate['season_id'] . ':' . $venueDate['project_id'] . ':' . $venueDate['programno'] == $seasonProjectCombo
                && $venueDate['seventtype']['l_performance'] == 1) {
//            entire address array is fetched in case the venue city, address or other info is needed
//                CUSTOM - this client has an alternate print name as additional data text 1
                if (!is_null($venueDate['locationaddress']['text_1']) && trim(
                        $venueDate['locationaddress']['text_1']
                    ) !== '') {
                    $venueName = htmlspecialchars($venueDate['locationaddress']['text_1']);
                } else {
                    $venueName = trim(
                        htmlspecialchars(
                            $venueDate['locationaddress']['name2']
                            . ' ' . $venueDate['locationaddress']['name1']
                        )
                    );
                }
                $headerVenues[] = $venueName;
            }
        }
        $venueCount = sizeof(array_unique($headerVenues));
//        if there is only one venue, return that one name as a string and it will be displayed UNDER the concert dates
        if ($venueCount == 1) {
            return implode('', array_unique($headerVenues));
        }
        return '';
    }

    public function renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph)
    {
        $pageSection->addText('', $defaultFont, $defaultParagraph);
        $orchestraName = trim(
            $concertDate['orchestraaddress']['name2'] . ' ' . $concertDate['orchestraaddress']['name1']
        );
        $pageSection->addText($orchestraName, $defaultFont, $defaultParagraph);

        $mainConductor = $this->datePerformerHelper->getConductor(
            $concertDate['id'],
            $concertDate['conductor_id'],
            0,
            0,
            ', ',
            '; ',
            0,
            true
        );
        if (!empty($mainConductor)) {
            $pageSection->addText($mainConductor, $defaultFont, $defaultParagraph);
        }

//      function returns date-work conductors in a single text string
        $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
            $concertDate['id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            0,
            ', ',
            '; ',
            0,
            0,
            true
        );
        if (!empty($dateWorkConductors)) {
            $pageSection->addText(
                $dateWorkConductors,
                $defaultFont,
                $defaultParagraph
            );
        }

//                    function returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
        $mainSoloist = $this->datePerformerHelper->getSoloists(
            $concertDate['id'],
            $concertDate['conductor_id'],
            '~~',
            0,
            0,
            ', ',
            true,
            ' - ',
            ' / ',
            0
        );
//        if (!empty($mainSoloist)) {
//            $soloistArray = explode('~~', $mainSoloist);
//            foreach ($soloistArray as $soloist) {
//                $pageSection->addFormattedText($soloist, $defaultFont, $defaultParagraph);
//            }
//        }
    }
}
