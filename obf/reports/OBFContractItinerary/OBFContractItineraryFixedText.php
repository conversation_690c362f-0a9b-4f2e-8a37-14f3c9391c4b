<?php

namespace Customer\obf\reports\OBFContractItinerary;

use PhpOffice\PhpWord\Shared\Converter;

class OBFContractItineraryFixedText
{
    const FULL_COL_WIDTH = 6.75;

    const COL_1_WIDTH = 1.75;
    const COL_2_WIDTH = 2.50;
    const COL_3_WIDTH = 2.50;


    public function __construct($pageSection, $isConductor)
    {
        $this->pageSection = $pageSection;
        $this->isConductor = $isConductor;
        $this->reportStyles = new OBFContractItineraryStyles();
    }

    public function renderContacts()
    {
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $headerTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $headerTable->addRow();
        $headerTable->addCell(Converter::inchToTwip(self::FULL_COL_WIDTH), $this->reportStyles->scheduleCell)
            ->addText('OBF Contacts', $this->reportStyles->subTitleFont, $defaultParagraph);
        $this->pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//        RENDER CONTACT TABLE
        $this->pageSection->addText(
            'Should you have questions, please do not hesitate to call or email:',
            $defaultFont,
            $defaultParagraph
        );

        $contactTable = $this->pageSection->addTable($this->reportStyles->contactTableStyle);
        $contactTable->addRow();

        $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->shadedCell)
            ->addText('Name', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->shadedCell)
            ->addText('Title', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->shadedCell)
            ->addText('Phone/Email', $this->reportStyles->defaultFontBold, $defaultParagraph);

//        If contract is for a SOLOIST (not Conductor) then only the third row appears

        if ($this->isConductor === true) {
            $contactTable->addRow();
            $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->contactCell)
                ->addText('James Boyd', $defaultFont, $defaultParagraph);
            $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->contactCell)
                ->addText('Director of Programming and Administration', $defaultFont, $defaultParagraph);
            $phoneCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->contactCell
            );
            $phoneCell->addText('************ | office', $defaultFont, $defaultParagraph);
            $phoneCell->addText('************ | mobile', $defaultFont, $defaultParagraph);
            $phoneCell->addText('<EMAIL>', $defaultFont, $defaultParagraph);

            $contactTable->addRow();
            $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->contactCell)
                ->addText('Greg Hamilton', $defaultFont, $defaultParagraph);
            $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->contactCell)
                ->addText('Librarian', $defaultFont, $defaultParagraph);
            $phoneCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->contactCell
            );
            $phoneCell->addText('************ | mobile', $defaultFont, $defaultParagraph);
            $phoneCell->addText('<EMAIL>', $defaultFont, $defaultParagraph);

            $contactTable->addRow();
            $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->contactCell)
                ->addText('Thor Mikesell', $defaultFont, $defaultParagraph);
            $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->contactCell)
                ->addText('Producing Technical Director', $defaultFont, $defaultParagraph);
            $phoneCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->contactCell
            );
            $phoneCell->addText('************ | office', $defaultFont, $defaultParagraph);
            $phoneCell->addText('************ | mobile', $defaultFont, $defaultParagraph);
            $phoneCell->addText('<EMAIL>', $defaultFont, $defaultParagraph);

        }

        $contactTable->addRow();
        $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->contactCell)
            ->addText('Doriandra Smith', $defaultFont, $defaultParagraph);
        $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->contactCell)
            ->addText('Artist Liaison', $defaultFont, $defaultParagraph);
        $phoneCell = $contactTable->addCell(
            Converter::inchToTwip(self::COL_3_WIDTH),
            $this->reportStyles->contactCell
        );
        $phoneCell->addText(' ************ | mobile', $defaultFont, $defaultParagraph);
        $phoneCell->addText('<EMAIL>', $defaultFont, $defaultParagraph);

        if ($this->isConductor === true) {
            $contactTable->addRow();
            $contactTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->contactCell)
                ->addText('Kathy Saltzman Romey', $defaultFont, $defaultParagraph);
            $contactTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->contactCell)
                ->addText('OBF Chorus Master', $defaultFont, $defaultParagraph);
            $phoneCell = $contactTable->addCell(
                Converter::inchToTwip(self::COL_3_WIDTH),
                $this->reportStyles->contactCell
            );
            $phoneCell->addText('************ | office', $defaultFont, $defaultParagraph);
            $phoneCell->addText('************| mobile', $defaultFont, $defaultParagraph);
            $phoneCell->addText('<EMAIL>', $defaultFont, $defaultParagraph);

        }


    }


}
