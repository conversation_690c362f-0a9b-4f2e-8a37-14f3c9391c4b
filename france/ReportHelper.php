<?php


namespace Customer\france;

use App\Model\Entity\Adate;
use App\Model\Entity\Saddress;
use App\Model\Entity\Scomposer;
use App\Model\Entity\Sseason;
use App\Model\Entity\Swork;
use App\Model\Entity\Sworkpremiere;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateworkSoloistsTable;
use App\Model\Table\AdateWorksTable;
use App\Model\Table\SaddressesTable;
use App\Model\Table\SseasonsTable;
use App\Model\Table\SworkpremieresTable;
use App\Reports\Report;
use Cake\Collection\CollectionInterface;
use Cake\Database\Expression\QueryExpression;
use Cake\Datasource\ResultSetInterface;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Element\Cell;
use PhpOffice\PhpWord\Style\Font;

class ReportHelper
{

    /**
     * @var array
     */
    private $postData;
    /**
     * @var Report
     */
    private $report;
    private $model;

    public $allRows = [];

    public function __construct($report, array $postData, $model)
    {
        $model = TableRegistry::getTableLocator()->get($report->source);
        $this->postData = $postData;
        $this->report = $report;
        $this->model = $model;
    }

    public function setAllRows($rows)
    {
        $this->allRows = $rows;
    }

    public function getAllRows(): \Cake\Datasource\ResultSetInterface
    {
        if (count($this->allRows)===0) {
            $query = $this->getQueryForCollect($this->postData);
            $rows = $query->all();

            $this->setAllRows($rows);
        }

        return $this->allRows;
    }

    public function getInitialAdateRows(): array
    {
        $query = $this->model
            ->find('all')
            ->select([
                'Adates.id',
                'Adates.project_id',
                'Adates.season_id',
            ])
            ->where(['Adates.id IN' => $this->postData['dataItemIds']]);

        return $query->all()->toArray();
    }

    public function getQueryForCollect(): Query
    {
        $query = $this->model
            ->find('all')
            ->select([
                'Adates.id',
                'Adates.date_',
                'Adates.start_',
                'Adates.end_',
                'Adates.text',
                'Adates.project_id',
                'Adates.conductor_id',
                'Adates.orchestra_id',
                'Adates.location_id',
                'Adates.year',
                'Adates.week',
                'Adates.season_id',
                'Adates.programtitle',
            ])
            ->contain('Sdresses', function (Query $query) {
                return $query->select([
                    'Sdresses.id',
                    'Sdresses.name'
                ]);
            })
            ->contain('Sprojects', function (Query $query) {
                return $query->select([
                    'Sprojects.id',
                    'Sprojects.name'
                ]);
            })
            ->contain('Seventtypes', function (Query $query) {
                return $query->select([
                    'Seventtypes.id',
                    'Seventtypes.name',
                    'Seventtypes.l_performance'
                ]);
            })
            ->contain('Locationaddresses', function (Query $query) {
                return $query->select([
                    'Locationaddresses.id',
                    'Locationaddresses.name1',
                    'Locationaddresses.place',
                    'Locationaddresses.code',
                ]);
            })
            ->contain('Conductoraddresses', function (Query $query) {
                return $query->select([
                    'Conductoraddresses.id',
                    'Conductoraddresses.name1',
                    'Conductoraddresses.name2'
                ])->contain('SaddressAddressgroups', function (Query $query) {
                    return $query->select([
                        'SaddressAddressgroups.id',
                        'SaddressAddressgroups.address_id',
                        'SaddressAddressgroups.l_main'
                    ])
                        ->where(['SaddressAddressgroups.l_main' => 1])
                        ->contain('Saddressgroups', function (Query $query) {
                            return $query->select([
                                'Saddressgroups.id',
                                'Saddressgroups.name'
                            ]);
                        });
                });
            })
            ->contain('AdateWorks', function (Query $query) {
                return $query->select([
                    'AdateWorks.id',
                    'AdateWorks.date_id',
                    'AdateWorks.work_id',
                    'AdateWorks.duration',
                    'AdateWorks.violin1',
                    'AdateWorks.violin2',
                    'AdateWorks.viola',
                    'AdateWorks.cello',
                    'AdateWorks.bass',
                    'AdateWorks.flute',
                    'AdateWorks.oboe',
                    'AdateWorks.clarinet',
                    'AdateWorks.bassoon',
                    'AdateWorks.horn',
                    'AdateWorks.trumpet',
                    'AdateWorks.trombone',
                    'AdateWorks.tuba',
                    'AdateWorks.timpani',
                    'AdateWorks.harp',
                    'AdateWorks.percussion',
                    'AdateWorks.keyboard',
                    'AdateWorks.extra',
                    'AdateWorks.vocals',
                    'AdateWorks.title2',
                    'AdateWorks.arrangement',
                    'AdateWorks.work_order',
                ])->contain('Sworks', function (Query $query) {
                    return $query->select([
                        'Sworks.id',
                        'Sworks.title1',
                    ])->contain('Scomposers', function (Query $query) {
                        return $query->select([
                            'Scomposers.id',
                            'Scomposers.lastname',
                            'Scomposers.firstname'
                        ]);
                    });
                })->contain('AdateworkSoloists', function (Query $query) {
                    return $query->select([
                        'AdateworkSoloists.id',
                        'AdateworkSoloists.artist_id',
                        'AdateworkSoloists.datework_id',
                        'AdateworkSoloists.artist_order',
                        'AdateworkSoloists.artist_order2',
                    ])->contain('Sinstrinstruments', function (Query $query) {
                        return $query->select([
                            'Sinstrinstruments.id',
                            'Sinstrinstruments.name'
                        ]);
                    })->contain('Saddresses', function (Query $query) {
                        return $query->select([
                            'Saddresses.id',
                            'Saddresses.name1',
                            'Saddresses.name2'
                        ]);
                    });
                })->order(['AdateWorks.id' => 'ASC']);
            })
            ->where(['Adates.id IN' => $this->postData['dataItemIds']])
            ->where(function (QueryExpression $exp) {
                return $exp->isNotNull('project_id');
            })->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC']);

        return $query;
    }

    public function getProjectSeasonCombinationKeys(ResultSetInterface $allRows): array
    {
        $combinedKeys = [];
        foreach ($allRows as $row) {
            $key = $row->Adates['project_id'] . ':' . $row->Adates['season_id'];
            $combinedKeys[$key] = $key;
        }
        return array_keys($combinedKeys);
    }

    /**
     * Anchor ID is the ID of the selected dates
     * with the largest number of works.
     *
     * @param $projectId
     * @param $seasonId
     * @param $allRows
     * @return int
     */
    public function getAnchorIdByProjectAndSeason($projectId, $seasonId): int
    {
        $allRows = $this->getAllRows();
        $maxCount = 0;
        $key = 0;

        foreach ($allRows as $row) {
            $d = $row;
            if ($d['project_id'] === $projectId && $d['season_id'] === $seasonId) {
                $works = $this->getWorksForAdateId($d['id']);
                $count = count($works);
                if ($count>$maxCount) {
                    $maxCount = $count;
                    $key = $d['id'];
                }
            }
        }
        return $key;
    }

    /**
     * Anchor ID is the ID of the selected dates
     * with the largest number of works and where
     * the event is a performance
     *
     * @param $projectId
     * @param $seasonId
     * @param $allRows
     * @return int
     */
    public function getAnchorIdByProjectAndSeasonOfFirstPerformance($projectId, $seasonId): int
    {
        $allRows = $this->getAllRows();
        $maxCount = 0;
        $key = 0;

        foreach ($allRows as $row) {
            $d = $row;
            if ($d['project_id'] === $projectId && $d['season_id'] === $seasonId) {
                if ((int)$d['seventtype']['l_performance'] === 0) {
                    continue;
                }
                $works = $this->getWorksForAdateId($d['id']);
                $count = count($works);
                if ($count>$maxCount) {
                    $maxCount = $count;
                    $key = $d['id'];
                }
            }
        }
        return $key;
    }

    /**
     * @param int $adateWorkId
     */
    public function getMovementsForAdateWorkId(int $adateWorkId): ResultSetInterface
    {
        $table = new AdateworkMovementsTable();
        $query = $table
            ->find('all')
            ->where(['datework_id = ' => $adateWorkId]);
        return $query->all();
    }

    /**
     * @param int $adateWorkId
     */
    public function getSoloistsForAdateWork(int $adateWorkId, $sortBy = 1): ResultSetInterface
    {
        $table = new AdateworkSoloistsTable();
        $sort = ($sortBy===1) ? 'artist_order' : 'artist_order2';
        $query = $table
            ->find('all')
            ->orderAsc($sort)
            ->where(['datework_id = ' => $adateWorkId])
            ->contain('Sinstrinstruments', function (Query $query) {
                return $query->select([
                    'Sinstrinstruments.id',
                    'Sinstrinstruments.name'
                ]);
            })->contain('Saddresses', function (Query $query) {
                return $query->select([
                    'Saddresses.id',
                    'Saddresses.name1',
                    'Saddresses.name2'
                ]);
            });
        return $query->all();
    }


    public function getSoloist(int $id): Saddress
    {
        $table = new SaddressesTable();
        $query = $table
            ->find('all')
            ->where(['id = ' => $id]);
        return $query->first();
    }

    public function getWorksForAdate(Adate $adate): array
    {
        $table = new AdateWorksTable();
        $query = $table
            ->find('all')
            ->where(['date_id = ' => $adate->Adates['id']]);
        return $query->toArray();
    }

    public function getPremierInfo(int $premierId): Sworkpremiere
    {
        $table = new SworkpremieresTable();
        $query = $table
            ->find('all')
            ->where(['id = ' => $premierId]);
        return $query->toArray()[0];
    }

    /**
     * @param int $adateId
     */
    public function getWorksForAdateId(int $adateId): CollectionInterface
    {
        $table = new AdateWorksTable();
        $query = $table
            ->find('all')
            ->where(['date_id = ' => $adateId]);
        $all = $query->all();
        $sorted = $all->sortBy('work_order', SORT_ASC);
        return $sorted;
    }

    public function getOrchestraInfoById(?int $id): ?Saddress
    {
        $table = new SaddressesTable();
        $query = $table
            ->find('all')
            ->where(['id = ' => $id]);
        return $query->first();
    }

    public function getAddressById(?int $id): ?Saddress
    {
        $table = new SaddressesTable();
        $query = $table
            ->find('all')
            ->where(['id = ' => $id]);
        return $query->first();
    }

    public function getSeasonById(?int $id): ?Sseason
    {
        $table = new SseasonsTable();
        $query = $table
            ->find('all')
            ->where(['id = ' => $id]);
        return $query->first();
    }


    /**
     * This will sort soloists by artist_order2
     * @param array $projectRow
     * @return array
     */
    public function getConductorAndSoloistsForProjectRow(array $projectRow): array
    {
        $Adate = $this->getAdateInfoFromProjectRow($projectRow);
        $conductor = $Adate->conductoraddress;
        $conductorName = '';
        if (trim($conductor->name2Name1)!=='') {
            $conductorName = $conductor->name2Name1 . ", Conductor";
        }

        $soloists = [];
        foreach ($projectRow['soloists'] as $soloist) {
            if ($conductor->id !== $soloist['data']->id) {
                $soloists[$soloist['sorting']['order2']] = $soloist;
            }
        }
        // sort by the order2 (pulled in in queryForCollect)
        ksort($soloists);

        return [
            'conductor' => $conductor,
            'conductorName' => $conductorName,
            'soloists' => $soloists
        ];
    }


    public function getAdateInfoFromProjectRow($projectRow): Adate
    {
        $anchorDateId = $projectRow['anchorDateId'];
        /** @var Adate $found */
        $found = null;

        foreach ($projectRow['adates'] as $adate) {
            if ($adate->id === $anchorDateId) {
                $found = $adate;
            }
        }
        return $found;
    }

    public function getComposerNameLastFirst(Scomposer $composer): String
    {
        return mb_strtoupper($composer->lastname . ", " . $composer->firstname);
    }

    public function getComposerNameFirstLast(Scomposer $composer): String
    {
        return mb_strtoupper($composer->firstname . " " . $composer->lastname);
    }

    /**
     * @param Scomposer|null $composer
     * @return string
     */
    public function getComposerYears(Scomposer $composer = null): string
    {
        if (null === $composer) {
            return '';
        }
        if (strlen(trim($composer->birthyear) . trim($composer->deathyear)) == 0) {
            return '';
        }
        if (strlen(trim($composer->birthyear)) && strlen(trim($composer->deathyear)) == 0) {
            return '(b. ' . $composer->birthyear . ')';
        }
        return '(' . $composer->birthyear . ' - ' . $composer->deathyear . ')';
    }


    public function getSoloistNameLastFirst(Saddress $soloist): String
    {
        // virtual field on Saddress object (_getName1Name2)
        return trim($soloist->name1Name2);
    }

    public function getSoloistNameFirstLast(Saddress $soloist): String
    {
        // virtual field on Saddress object (_getName2Name1)
        return trim($soloist->name2Name1);
    }

    public function getCompositionYear(Swork $swork): ?String
    {
        $string = $swork->compyear;
        if (trim($swork->compyear2) !== '') {
            $string .= " - " . $swork->compyear2;
        }
        return $string;
    }

    /**
     * Get the duration
     *
     * Based on the duration this function returned it in the form m's''.
     *
     * @param $duration
     * @return string
     */
    public function getDurationStringForWork(Swork $swork, $version = 1, int &$accumulator = 0)
    {
        $duration = $swork->duration;

        if ($duration === '') {
            return $duration;
        } else {
            $durationString = '';

            $durationTime = new Time($duration);
            $durationMinutes = (int)$durationTime->format('i') + ((int)$durationTime->format('H') * 60);
            $durationSeconds = $durationTime->format('s');

            if ($durationMinutes > 0) {
                $durationString .= $durationMinutes . $this->report->text['work_table_duration_minutes_postfix'];
                $accumulator += (int)($durationMinutes * 60);
            }

            if ($durationSeconds > 0) {
                $accumulator += (int)$durationSeconds;
                $durationString .= $durationSeconds . $this->report->text['work_table_duration_seconds_postfix'];
            }

            return $durationString;
        }
    }

    public function getDurationFromString($durationString): String
    {
        $finalString = '';

        $durationTime = new Time($durationString);
        $durationMinutes = (int)$durationTime->format('i') + ((int)$durationTime->format('H') * 60);
        $durationSeconds = $durationTime->format('s');

        if ($durationMinutes > 0) {
            $finalString .= $durationMinutes . $this->report->text['work_table_duration_minutes_postfix'];
        }

        if ($durationSeconds > 0) {
            $finalString .= $durationSeconds . $this->report->text['work_table_duration_seconds_postfix'];
        }

        return $finalString;
    }

// change (int) to round() to get integer to the nearest minute?
    public function getDurationAsInt($durationString)
    {
        $durationTime = new Time($durationString);
        $durationHour = ((int)$durationTime->format('H') * 60);
        $durationMinutes = (int)$durationTime->format('i');
        $durationSeconds = ((int)$durationTime->format('s') / 60);

        $durationInteger = round($durationHour + $durationMinutes + $durationSeconds);

        return $durationInteger;
    }

    public function getDurationStringForMovement($movement, $version = 1, int &$accumulator = 0)
    {
        $duration = $movement->duration;

        if ($duration === '') {
            return $duration;
        } else {
            $durationString = '';

            $durationTime = new Time($duration);
            $durationMinutes = (int)$durationTime->format('i') + ((int)$durationTime->format('H') * 60);
            $durationSeconds = $durationTime->format('s');

            if ($durationMinutes > 0) {
                $durationString .= $durationMinutes . $this->report->text['work_table_duration_minutes_postfix'];
                $accumulator += (int)($durationMinutes * 60);
            }

            if ($durationSeconds > 0) {
                $accumulator += (int)$durationSeconds;
                $durationString .= $durationSeconds . $this->report->text['work_table_duration_seconds_postfix'];
            }

            return $durationString;
        }
    }
    /**
     * @param array $rows
     * @return array<Sseason>
     */
    public function getSeasonsFromProjectRows($project): array
    {
        $seasons = [];
        foreach ($project['adates'] as $adate) {
            $seasonId = $adate->season_id;
            $seasons[$seasonId] = $seasonId;
        }
        foreach ($seasons as $seasonId) {
            $seasons[$seasonId] = $this->getSeasonById($seasonId);
        }
        return $seasons;
    }


    /**
     *
     * The 'collect' gives us all dates retrieved for a project/season
     * repeated on each 'project row' ('adate' entity?)
     *
     * A bit misleading method name
     *
     * @param array $projectRow
     * @return array Array of dates
     */
    public function getPerformanceDatesForAnchorProject(array $projectRow): array
    {
        $seasonId = $projectRow['adates']->season_id;
        $projectId = $projectRow['adates']['project_id'];
        $allRows = $this->getAllRows();

        $allDates = [];

        foreach ($allRows as $row) {
            if ($row->project_id === $projectId && $row->season_id === $seasonId) {
                $allDates[] = $row;
            }
        }

        return $allDates;
    }

    public function getDatesForProject(array $adates): array
    {
        $dates = [];
        foreach ($adates as $adate) {
            $dates[] = $adate->date_;
        }
        return $dates;
    }

    /**
     * Given array of dates, return something like
     *
     * "Dec 1-3, 2020" or "Nov 30 - Dec 3, 2020"
     *
     * Get lowest and highest date range and return string
     *
     * @param array $dates
     * @return string
     */
    public function formatDateRange(array $dates): string
    {
        $string = '';
        sort($dates);
        if (count($dates) === 1) {
            $low = $dates[0];
            $high = $dates[0];
        } else {
            $low = array_shift($dates);
            $high = array_pop($dates);
        }
        $lowMonth = $low->format("M");
        $highMonth = $high->format("M");
        $lowDay = $low->format("d");
        $highDay = $high->format("d");
        $string .= $lowMonth . ' ' . $lowDay;
        if ($lowMonth != $highMonth) {
            $string .= ' - ';
            $string .= $highMonth . ' '. $highDay;
        } else {
            if ($lowDay != $highDay) {
                $string .= ' - ' . $highDay;
            }
        }
        $string .= ', ' . $high->format('Y');
        return $string;
    }

    /**
     * Return array of words, each with a style (plain or italic)
     * to indicate how to format each word in a textRun
     *
     * @param $string
     * @return array
     */
    public function parseForWordsAndStyles($string): array
    {
        $words = explode(" ", $string);
        $finalWords = [];
        $style = 'plain';
        foreach ($words as $word) {
            $array = [];
            if (substr($word, 0, 1)==='<') {
                $word = str_replace("<", "", $word);
                $style = 'italic';
            }
            $array['style'] = $style;
            // ideally the word would *end* with > for closing
            // italic, but some end with , now, so this
            // attempts to support those existing scenarios
            if (strpos($word, '>')>-1) {
                $word = str_replace(">", "", $word);
                $style = 'plain';
            }
            $array['word'] = $word;

            $finalWords[] = $array;
        }
        return $finalWords;
    }

    /**
     * Support internal string formatting for styles
     * currently only 'italics' or 'plain'
     * italics/plain flags from splitWords
     *
     * @param $string
     * @param Cell $section
     * @param Font $defaultStyle
     */
    public function addTextWithStyles($string, Cell $cell, Font $defaultStyle, $textRunStyle = '')
    {
        $textRun = $cell->addTextRun($textRunStyle);
        $wordsAndStyles = $this->parseForWordsAndStyles($string);
        foreach ($wordsAndStyles as $position=>$item) {
            $word = $item['word'];
            $style = $item['style'];
            if ($position>0) {
                $word = " ".$word;
            }
            $styleToUse = clone $defaultStyle;
            if (!$defaultStyle->isItalic()) {
                if ($style === 'italic') {
                    $styleToUse->setItalic(true);
                }
            }
            $textRun->addText($word, $styleToUse);
        }
    }
}
