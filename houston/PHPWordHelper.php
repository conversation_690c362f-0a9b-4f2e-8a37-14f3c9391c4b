<?php

namespace Customer\houston;

use PhpOffice\PhpWord\Element\Row;
use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Shared\Html;
use PhpOffice\PhpWord\SimpleType\Jc;
use PhpOffice\PhpWord\SimpleType\VerticalJc;
use PhpOffice\PhpWord\Style\Paper;
use PhpOffice\PhpWord\Style\Cell;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;
use PhpOffice\PhpWord\Writer\Word2007\Part\Styles;

class PHPWordHelper
{

    const PAGE_MARGIN_TOP = 1.27;  // Page margin top in cm 1.27 = 0.5 inch
    const PAGE_MARGIN_LEFT = 1.905; // Page margin left in cm 1.905 = 0.75 inch
    const PAGE_MARGIN_RIGHT = 1.27; // Page margin right in cm 1.27 = 0.5 inch
    const PAGE_MARGIN_BOTTOM = 1.905; // Page margin bottom in cm 1.905 = 0.75 inch (2.54 = 1 inch)

/*   Width of the Printable Area; used primarily to set a new table to take
*    up the entire print area. 17.78 cm = 7 in; 18.41 = 7.25 in; (8.5 paper size minus l/r margins of .75 and .5 in)
*/
    const PAGE_WIDTH_CM = 18.41;

    public const PAPER_SIZE = 'Letter'; // or 'Legal', 'A4' ...

    public const ORIENTATION_PORTRAIT = 'portrait';
    public const ORIENTATION_LANDSCAPE = 'landscape';

    public const ROW_COLS_FULL = 1;
    public const ROW_COLS_5050 = 2;
    public const ROW_COLS_FOURTHS = 9;
    public const ROW_COLS_2575 = 3;
    public const ROW_COLS_7525 = 4;
    public const ROW_COLS_THIRDS = 5;
    public const ROW_COLS_25705 = 6;
    public const ROW_COLS_256015 = 7;  // 3-column table with wider right col
    public const ROW_COLS_SIX = 8; // six column table
    public const ROW_COLS_SEVEN = 10;  // seven column table
    public const ROW_COLS_FIFTHS = 11;  // five column table

    const STYLE_P_RIGHT = 'RIGHT';
    const STYLE_P_LEFT = 'LEFT';
    const STYLE_P_CENTER = 'CENTER';

/* TWG:  static methods to define a table border (1pt) and color (dark grey)
* maybe use this to create a second addTable public function like that around line 100?
*   this way we have 2 default tables; one withOUT border (more common) and one with borders ?
*/
    const TABLE_BORDER_COLOR = '#777777' ;
    const TABLE_BORDER_WIDTH = 6 ;


    /** @var PhpWord $wordDoc */
    protected $wordDoc = null;

    public $paragraphStyles = [];
    public $cellStyles = [];
    public $fontStyles = [];
    public $pageStyle = [];

    public function __construct()
    {
        $this->pageStyle = array(
/* TWG: can/should $this->pageStyle be expanded to include other attributes
* like page width, default font, default spacing? Is that what lines 73-75 are for?
*  $this->pageStyle does not seem to have any effect when referenced in report files
*   see also: ReportWordFas line 337
*/
            'marginTop' => Converter::cmToTwip(self::PAGE_MARGIN_TOP),
            'marginLeft' => Converter::cmToTwip(self::PAGE_MARGIN_LEFT),
            'marginBottom' => Converter::cmToTwip(self::PAGE_MARGIN_BOTTOM),
            'marginRight' => Converter::cmToTwip(self::PAGE_MARGIN_RIGHT)
        );

        $this->defineParagraphStyles();  // defined in line 427
        $this->defineCellStyles();   // defined in line 409
        $this->defineFontStyles();  // defined in line 381
    }


/* TWG - if document-level properties like paper size, orientation and margins are
*   set in a Helper, I assume it would be here as this is where the document is created ?
*/
    public function createDoc(): void
    {
        $this->wordDoc = new PhpWord();

    }

    public function write($name)
    {
        $fileName = $name;
        $path = ROOT_REP_IN_DIR . $fileName;
        $objWriter = IOFactory::createWriter($this->wordDoc, 'Word2007');
        $objWriter->save($path);
        return $path;
    }

// TWG: pageStyle is defined above in line 61
    public function addSection($style = null): Section
    {
        $style = $style ?? $this->pageStyle;

        $paper = new Paper();
        $paper->setSize(self::PAPER_SIZE);
// comment out the size properties so 'landsacpe' can be invoked when a section is added
//        $style['pageSizeW'] = $paper->getWidth();
//        $style['pageSizeH'] = $paper->getHeight();

        $section = $this->wordDoc->addSection($style);
        return $section;
    }

// TWG: default properties of any table - 114 twips = standard 0.08 interior margin
    public function addTable(Section $section): Table
    {
        $table = $section->addTable([
            'cellMarginRight' => 114,  // interior margin of 0.08 in
            'cellMarginLeft' => 114,  // interior margin of 0.08 in
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word

        ]);
        return $table;
    }

    public function addTableFullBorder(Section $section): Table
    {
        $table = $section->addTable([
            'cellMarginRight' => 114,  // interior margin of 0.08 in
            'cellMarginLeft' => 114,  // interior margin of 0.08 in
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0,  // zero is standard for top/bottom in Word

            'borderColor' => self::TABLE_BORDER_COLOR,
            'borderSize' => self::TABLE_BORDER_WIDTH,
        ]);
        return $table;
    }


// TWG: $columnLayout is defined above; gives a convenient way to assign/recognize a table by the number of columns
    public function addRow(Table $table, int $columnLayout = self::ROW_COLS_FULL, array $widths = null, array $styles = null)
    {
        if (count($table->getRows())!==0) {
            $columnLayout = $table->getRows()[0]->__LAYOUT;
        }
        if ($widths === null) {
            $widths = $this->getDefaultWidthsForColumns($columnLayout);  // defined below
        }
        if ($styles === null) {
            $styles = $this->getDefaultStylesForColumns($columnLayout);  // defined below
        }

        $row = $table->addRow();
        $row->__LAYOUT = $columnLayout;

        foreach ($styles as $index=>$style) {
            $width = $widths[$index] ?? $this->getWidthTwpByPct();
            $row->addCell($width, $this->paragraphStyles[$style]);
        }

        return $row;
    }

// TWG: individual column widths are set as a percentage of the total table width, based on the percentage
        public function getDefaultWidthsForColumns(int $rowStyle = self::ROW_COLS_FULL): array
        {
            switch ($rowStyle) {
                case self::ROW_COLS_5050:
                    return [$this->getWidthTwpByPct(50), $this->getWidthTwpByPct(50)];
                case self::ROW_COLS_2575:
                    return [$this->getWidthTwpByPct(25), $this->getWidthTwpByPct(75)];
                case self::ROW_COLS_25705:
                    return [
                        $this->getWidthTwpByPct(25),
                        $this->getWidthTwpByPct(70),
                        $this->getWidthTwpByPct(5),
                    ];
                case self::ROW_COLS_7525:
                    return [$this->getWidthTwpByPct(75), $this->getWidthTwpByPct(25)];
                case self::ROW_COLS_THIRDS:
                    return [
                        $this->getWidthTwpByPct(33.33),
                        $this->getWidthTwpByPct(33.34),
                        $this->getWidthTwpByPct(33.33),
                    ];
                case self::ROW_COLS_FOURTHS:
                    return [
                        $this->getWidthTwpByPct(20),
                        $this->getWidthTwpByPct(20),
                        $this->getWidthTwpByPct(35),
                        $this->getWidthTwpByPct(25),
                    ];
                case self::ROW_COLS_FIFTHS:
                    return [
                        $this->getWidthTwpByPct(20),
                        $this->getWidthTwpByPct(20),
                        $this->getWidthTwpByPct(20),
                        $this->getWidthTwpByPct(10),
                        $this->getWidthTwpByPct(10),
                    ] ;
                case self::ROW_COLS_SIX:
                    return [
                        $this->getWidthTwpByPct(12),
                        $this->getWidthTwpByPct(16),
                        $this->getWidthTwpByPct(32),
                        $this->getWidthTwpByPct(12),
                        $this->getWidthTwpByPct(16),
                        $this->getWidthTwpByPct(12),
                    ] ;
                case self::ROW_COLS_SEVEN:
                    return [
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(14),
                        $this->getWidthTwpByPct(16),
                    ] ;
                // COL_STYLE_FULL falls through
                default:
                    return [$this->getWidthTwpByPct(),0,0];
            }
        }
// TWG: set the default Style for each column, based on the table $columnLayout. Typically this is just alignment
    public function getDefaultStylesForColumns(int $rowStyle = self::ROW_COLS_FULL): array
    {
        switch ($rowStyle) {
            case self::ROW_COLS_5050:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_2575:
                return [
                    self::STYLE_P_RIGHT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_25705:
                return [
                    self::STYLE_P_RIGHT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_THIRDS:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                    ];
            case self::ROW_COLS_FOURTHS:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                    ];
            case self::ROW_COLS_7525:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_RIGHT,
                    ];
            case self::ROW_COLS_256015:
                return [
                    self::STYLE_P_RIGHT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_FIFTHS:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_SIX:
                return [
                    self::STYLE_P_RIGHT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            case self::ROW_COLS_SEVEN:
                return [
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                    self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT, self::STYLE_P_LEFT,
                ];
            default:
                return [self::STYLE_P_CENTER,0,0];
        }
    }



    /**
     * @param Section $section
     * @param $string
     * @param Font|null $defaultFontStyle font style (size, bold, italic, etc)
     * @param Paragraph|null $defaultPStyle paragraph style (alignment, mostly)
     */

/* TWG: Similar to addText below; use when placing text NOT in a table
*  $defaultFontStyle and $defaultPStyle not defined?
*     difference bewteen addSectionText and addText  ?
  */
    public function addSectionText(Section $section, $string, $defaultFontStyle = null, Paragraph $defaultPStyle = null)
    {
        $defaultFontStyle = $defaultFontStyle ?? new Font();
        $defaultPStyle = $defaultPStyle ?? $this->paragraphStyles[self::STYLE_P_CENTER];

/* TWG: use addTextRun instead of addText if the paragrpah will have different stlyes or include images, etc.
* addText = simple paragraph with just one style
* addTextRun = complex paragraph
*/
        $textRun = $section->addTextRun($defaultPStyle);  // TWG - change this to null so reportParagraphDefault is used?

        $this->addTextToTextRun($textRun, $section, $string, $defaultPStyle, $defaultFontStyle);
    }

    /**
     * @param Row $row
     * @param $cellIndex
     * @param $string
     * @param Font|null $defaultFontStyle font style (size, bold, italic, etc)
     * @param Paragraph|null $defaultPStyle paragraph style (alignment, mostly)
     */
    public function addTableText(
        Row $row,
        $cellIndex,
        $string,
        Font $defaultFontStyle = null,
        Paragraph $defaultPStyle = null
    ) {
        $defaultFontStyle = $defaultFontStyle ?? new Font();
        $defaultPStyle = $defaultPStyle ?? $this->paragraphStyles[self::STYLE_P_LEFT];

        $cell = $row->getCells()[$cellIndex];

        $textRun = $cell->addTextRun($defaultPStyle);

        $this->addTextToTextRun($textRun, $cell, $string, $defaultPStyle, $defaultFontStyle);
    }

    /**
     * @param TextRun $textRun
     * @param $element
     * @param $string
     * @param Paragraph $originalParagraphStyle
     * @param Font $defaultFontStyle
     */


    public function addTextToTextRun(
        TextRun $textRun,
        $element,
        $string,
        Paragraph $originalParagraphStyle,
        Font $defaultFontStyle
    ) {
        $originalAlignment = $originalParagraphStyle->getAlignment();
        $paragraphStyle = clone $originalParagraphStyle;
        $wordsAndStyles = $this->parseForWordsAndStyles($string);

        // startOfLine is 'state' value to determine if 'word' in loop
        // should have a space prepended.  After a linbreak, we're at the
        // 'startOfLine' again, so no prepended space.
        $startOfLine = true;

        foreach ($wordsAndStyles as $position=>$item) {
            $word = trim($item['word']);
            $style = $item['style'];
            if ($position>0 && !$startOfLine) {
                $word = " ".$word;
            }
            $startOfLine = false;
            $styleToUse = clone $defaultFontStyle;
            if (!$defaultFontStyle->isItalic()) {
                if ($style === 'italic') {
                    $styleToUse->setItalic(true);
                }
            }
            if (!$defaultFontStyle->isBold()) {
                if ($style === 'bold') {
                    $styleToUse->setBold(true);
                }
            }

            if ('__STARTCENTER__' === trim($word)) {
                $word = '__NEWLINE__';
                $paragraphStyle->setAlignment('center');
            }

            if ('__ENDCENTER__' === trim($word)) {
                $word = '__NEWLINE__';
                $paragraphStyle->setAlignment($originalAlignment);
            }

            if ('__NEWLINE__' === trim($word)) {
                // create a new text run, which gives us
                // a new line in the cell
                // warning - do not break lines in the middle of
                // 'changed' styles
                $textRun = $element->addTextRun($paragraphStyle);
                $startOfLine = true;
                continue;
            } else {
                if (trim($word) === '') {
                    continue;
                }
                $textRun->addText($word, $styleToUse, $paragraphStyle);
            }
        }
    }

    /**
     * Return array of words, each with a style (plain or italic)
     * to indicate how to format each word in a textRun
     *
     * @param $string
     * @return array
     */
    public function parseForWordsAndStyles($string): array
    {
        $string = str_replace(' & ', ' &amp; ', $string);
        $string = str_replace('<br/>', ' __NEWLINE__ ', $string);
        $string = str_replace('<center>', ' __STARTCENTER__ ', $string);
        $string = str_replace('</center>', ' __ENDCENTER__ ', $string);
        $words = explode(" ", $string);
        $finalWords = [];
        $style = 'plain';
        foreach ($words as $word) {
            $array = [];
            if (substr($word, 0, 3)==='<b>') {
                $word = str_replace("<b>", "", $word);
                $style = 'bold';
            } else {
                if (substr($word, 0, 3)==='<i>') {
                    $word = str_replace("<i>", "", $word);
                    $style = 'italic';
                } else {
                    if (substr($word, 0, 1) === '<') {
                        $word = str_replace("<", "", $word);
                        $style = 'italic';
                    }
                }
            }
            $array['style'] = $style;
            // ideally the word would *end* with > for closing
            // italic, but some end with , now, so this
            // attempts to support those existing scenarios
            if (strpos($word, '</b>')>-1 || strpos($word, '</i>')>-1) {
                if (strpos($word, '</b>')>-1) {
                    $word = str_replace("</b>", "", $word);
                }
                if (strpos($word, '</i>')>-1) {
                    $word = str_replace("</i>", "", $word);
                }
                $style = 'plain';
            } else {
                if (strpos($word, '>') > -1) {
                    $word = str_replace(">", "", $word);
                    $style = 'plain';
                }
            }
            $array['word'] = $word;

            $finalWords[] = $array;
        }
        return $finalWords;
    }

    public function getWidthTwpByPct(float $width = 100)
    {
        return (Converter::cmToTwip(self::PAGE_WIDTH_CM) * ($width / 100));
    }


    public function defineFontStyles()
    {
        $style = new Font();
        $style->setStyleName("fontBold");
        $style->setSize(10);
        $style->setBold(true);
        $this->cellStyles['LARGE_BOLD'] = $style;

        $style = new Font();
        $style->setStyleName("regular");
        $style->setSize(10);
        $this->cellStyles['REGULAR'] = $style;

        $style = new Font();
        $style->setStyleName("italic");
        $style->setSize(10);
        $style->setItalic(true);
        $this->cellStyles['ITALIC'] = $style;

//        $this->tableFont = $this->phpWord->addFontStyle('tableFont', array('size' => 8));
//        $this->tableBoldFont = $this->phpWord->addFontStyle('tableBoldFont', array('bold' => true, 'size' => 8));
//        $this->tableHeaderFont = $this->phpWord->addFontStyle('tableHeaderFont', array('bold' => true, 'size' => 9));
//        $this->headerFont = $this->phpWord->addFontStyle('headerFont', array('bold' => true, 'size' => 10));
//        $this->tableItalicFont = $this->phpWord->addFontStyle('tableItalicFont', array('size' => 8, 'italic'=>true));
    }

    public function defineCellStyles()
    {
        $style = new Paragraph();
        $style->setStyleName("cellCenter");
        $style->setAlignment(JC::CENTER);  // alignment here *might* be vertical alignment??
        $this->cellStyles['CENTER'] = $style;

        $style = new Paragraph();
        $style->setStyleName("cellLeft");
        $style->setAlignment(JC::START);  // self::STYLE_P_LEFT is deprecated
        $this->cellStyles[self::STYLE_P_LEFT] = $style;

        $style = new Paragraph();
        $style->setStyleName("cellRight");
        $style->setAlignment(JC::END);  // self::STYLE_P_RIGHT is deprecated
        $this->cellStyles[self::STYLE_P_RIGHT] = $style;
    }

    public function defineParagraphStyles()
    {

        $style = new Paragraph();
        $style->setStyleName("paragraphCenter");
        $style->setAlignment(JC::CENTER);  // alignment here *might* be vertical alignment??
        $this->paragraphStyles[self::STYLE_P_CENTER] = $style;

        $style = new Paragraph();
        $style->setStyleName("paragraphLeft");
        $style->setAlignment(JC::START);  // self::STYLE_P_LEFT is deprecated
        $this->paragraphStyles[self::STYLE_P_LEFT] = $style;

        $style = new Paragraph();
        $style->setStyleName("paragraphRight");
        $style->setAlignment(JC::END);  // self::STYLE_P_RIGHT is deprecated
        $this->paragraphStyles[self::STYLE_P_RIGHT] = $style;
    }

    public function addTableStyle($name, $style, $styleFirstRow = null)
    {
        return $this->wordDoc->addTableStyle($name, $style, $styleFirstRow);
    }

    public function addFontStyle($name, $fontStyle, $paragraphStyle = null)
    {
        return $this->wordDoc->addFontStyle($name, $fontStyle, $paragraphStyle = null);
    }

    public function addParagraphStyle($name, $styles)
    {
        return $this->wordDoc->addParagraphStyle($name, $styles);
    }
}
