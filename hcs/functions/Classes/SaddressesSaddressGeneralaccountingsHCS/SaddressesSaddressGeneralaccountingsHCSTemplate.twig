<template id="SaddressesSaddressGeneralaccountingsHCSTemplate">
  <div class="wizard-message">
    <h4 class="wizard-headline">{{ i18n.templateTitle }}</h4>
    <p>{{ i18n.templateDescription1 }}</p>
    <p>{{ i18n.templateDescription2 }}</p>

    <form class="smart-form" id="wizard-template-form">
      <fieldset>
        <div class="row">

          <section class="col col-6">
            <label for="effective_date">{{ __('saddress_generalaccountings.effective_date') }}</label>
            <div class="form-group">
              <div class="input-group">
                <input class="form-control opas-datepicker"
                       type="text"
                       name="effective_date"
                       id="effective-date"
                       data-format={{ Oform.readFormat('momentjsDate') }}
                       attr-data-locale={{ Oform.configureRead('App.defaultLocale') }}>
                <span class="input-group-addon opas-datepicker-addon"><i class="fas fa-calendar"></i></span>
              </div>
            </div>
          </section>
        </div>

        <div class="row">
          <section class="col col-12">
            {{ Form.control('amount', {
              label: __('saddress_generalaccountings.amount'),
              min: 0,
              max: 9999999,
              step: 0.1,
              type: 'number',
              val: 0
            }) | raw }}
          </section>
        </div>

        <div class="row">
          <section class="col col-12">
            {{ Form.control('percent_', {
              label: i18n.templateAddPercent,
              min: -999,
              max: 999,
              step: 1.0,
              type: 'number',
              val: 0
            }) | raw }}
          </section>
        </div>
      </fieldset>
    </form>
  </div>
</template>
