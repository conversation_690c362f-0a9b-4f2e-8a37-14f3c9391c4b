
// augment->

var FUNCTIONS = FUNCTIONS || {};

FUNCTIONS.SaddressesSaddressGeneralaccountingsHCSTemplate = function(wizard) {
  
  if ($.fn.datetimepicker) {

    const $datePicker = $('#effective-date');

    const pickerFormat = $datePicker.data('format') || 'DD.MM.YYYY';
    const pickerLocale = $datePicker.attr('attr-data-locale') || 'en'; // im twig zusät<PERSON>lich definiertes attribut im picker

    $datePicker.datetimepicker({
      format: pickerFormat,
      locale: pickerLocale
    });

    // Check form validation and disable next button if conditions not met
    function checkFormValidation() {
      const effectiveDateValue = $datePicker.val();
      const amountValue = parseFloat($('#amount').val()) || 0;
      const percentValue = parseFloat($('input[name="percent_"]').val()) || 0;

      // Disable next button if:
      // 1. datePicker is empty
      // 2. amount and percent_ equal 0
      const isDatePickerEmpty = !effectiveDateValue || effectiveDateValue.trim() === '';
      const areBothValuesZero = amountValue === 0 && percentValue === 0;

      const shouldDisable = isDatePickerEmpty || areBothValuesZero;

      wizard.selectedStep().nextButton().disabled(shouldDisable);
    }

    // Initial check
    checkFormValidation();

    // Monitor changes to the effective date field
    $datePicker.on('dp.change', checkFormValidation);
    $datePicker.on('input', checkFormValidation);
    $datePicker.on('change', checkFormValidation);

    let isUpdating = false;

    // Monitor changes to amount field
    $('#amount, input[name="amount"]').on('input change keyup', function() {
      if (isUpdating) return;

      const amountValue = parseFloat($(this).val()) || 0;
      if (amountValue !== 0) {
        isUpdating = true;
        $('input[name="percent_"]').val(0);
        isUpdating = false;
      }
      setTimeout(checkFormValidation, 10);
    });

    // Monitor changes to percent_ field
    $('input[name="percent_"]').on('input change keyup', function() {
      if (isUpdating) return;

      const percentValue = parseFloat($(this).val()) || 0;
      if (percentValue !== 0) {
        isUpdating = true;
        $('#amount, input[name="amount"]').val(0);
        isUpdating = false;
      }
      setTimeout(checkFormValidation, 10);
    });

  } else {
    console.error(
      'Missing datetimepicker()-Function for the javascript part if the function SaddressesChangeAddressgroup.');
  }

};
