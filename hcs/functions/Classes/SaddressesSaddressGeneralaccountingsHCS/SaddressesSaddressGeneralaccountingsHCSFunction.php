<?php

namespace Customer\hcs\functions\Classes\SaddressesSaddressGeneralaccountingsHCS;

use App\Utility\Functions;
use Cake\Core\Configure;
use Cake\I18n\FrozenDate;
use Cake\ORM\TableRegistry;

/**
 * JH ********  ONCUST-5680
 *
 * neue Einträge in saddress_generalaccountings erzeugen
 *
 */
class SaddressesSaddressGeneralaccountingsHCSFunction extends Functions
{
    /** @inheritdoc */
    protected $templates = [
        [
            'name' => 'SaddressesSaddressGeneralaccountingsHCSTemplate',
            'file' => 'SaddressesSaddressGeneralaccountingsHCSTemplate.php',
            'jsFile' => 'SaddressesSaddressGeneralaccountingsHCSTemplate.js'
        ]
    ];

    /** @inheritdoc */
    public function execute($formData, $dataItemIds, $chunkData = [])
    {

        $effectiveDate = FrozenDate::createFromFormat(Configure::read('Formats.date'), $formData['effective_date']);
        $amount = (float)$formData['amount'];
        $percent = (float)$formData['percent_'];

        $saddresses = TableRegistry::getTableLocator()->get('Saddresses');
        $generalAccountings = TableRegistry::getTableLocator()->get('SaddressGeneralaccountings');

        foreach ($dataItemIds as $addressId) {

            $address = $saddresses->get($addressId);
            $fullName = $address['fullname'];

            if ($percent <> 0) {
                $lastEntry = $generalAccountings->find()
                                ->where([
                                    'address_id' => $addressId
                                    ])
                                ->orderDesc('effective_date')
                                ->toArray();
                if (! empty($lastEntry)) {
                    $amount2add = $lastEntry[0]['amount'] + round($lastEntry[0]['amount'] * $percent / 100, 2);
                } else {
                    $result = [
                        'name' => $fullName,
                        'successMessage' => '',
                        'error' => true,
                        'errorMessage' => 'no value found for percent calculation'
                    ];
                    $this->resultArray[] = $result;
                    continue;
                }
            } else {
                $amount2add = $amount;
            }


            $newEntry = $generalAccountings->newEntity([
                'address_id' => $addressId,
                'amount' => $amount2add,
                'effective_date' => $effectiveDate
            ]);

            $result = [
                'name' => $fullName,
                'successMessage' => '',
                'error' => false,
                'errorMessage' => ''
            ];

            try {
                if ($generalAccountings->save($newEntry)) {
                    $result['successMessage'] = sprintf(
                        $this->i18n['successMessage'],
                        $amount2add
                    );
                    $this->resultArray[] = $result;
                }
            } catch (\Exception $exception) {
                $result['error'] = true;
                $result['errorMessage'] = $exception->getMessage();
                $this->resultArray[] = $result;
                throw $exception;
            }
        }

        return $this->resultArray;
    }
}
