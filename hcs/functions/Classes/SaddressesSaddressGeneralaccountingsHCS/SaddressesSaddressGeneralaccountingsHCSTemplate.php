<?php

namespace Customer\hcs\functions\Classes\SaddressesSaddressGeneralaccountingsHCS;

use App\Utility\FunctionsTemplate;
use Cake\ORM\TableRegistry;

class SaddressesSaddressGeneralaccountingsHCSTemplate extends FunctionsTemplate
{
    /** @inheritdoc  */
    protected $templateName = 'SaddressesSaddressGeneralaccountingsHCSTemplate';

    public function getTemplateData(array $params = [])
    {
        return [];
    }

    public function submit($formDataString = null)
    {
        // Parse the form data string into an array
        parse_str($formDataString, $formData);

        $this->data['formData'] = $formData;

        return parent::submit($formDataString);
    }

    public function getConfirmationMessage()
    {
        $message = $this->i18n['templateConfirmation'];

        $message = sprintf($message,
            $this->data['formData']['amount'] > 0 ? $this->data['formData']['amount'] : $this->data['formData']['percent_'].'%',
            $this->data['formData']['effective_date']
        );

        return $message;
    }

}
