<?php

namespace Customer\det\accounting_rules;

use App\AccountingRules\Rules\AdutyAccountings;
use Cake\Core\Configure;
use Customer\det\accounting_rules\Traits\AccountingDET_trait;

/*
 * JH ******** ONCUST-5739
 * https://opas.atlassian.net/wiki/spaces/KUN/pages/**********/DET+-+Accounting+Rule+2A+Service+Pay+Adjustment+-+Pay+Amount+if+Staff+Musician
 * https://opas.atlassian.net/wiki/spaces/KUN/pages/**********/DET+-+Accounting+Rule+2B+Service+Pay+Adjustment+-+limit+Doubling+to+300+per+week
 *
 */

class AdutyAccountingsDET extends AdutyAccountings
{

    use AccountingDET_trait;

    protected $type = 'custom';
    protected $sorting = 'before|MarkServiceAsFixed';

    protected $name = 'accounting_rules.det.aduty_accountungs.name';
    protected $description = 'Custom Service Pay Adjustment including "Overtime" and "Doubling"';

    protected $message2a = 'Custom calculation on the basis of the weekly salary items';
    protected $message2b = 'The sum of all doubling services cannot exceed the maximum weekly amount of %s.';

    public function initialize()
    {
        $this->settingsTemplateConfiguration[] = [
            'label' => 'Maximum weekly amount for doubling',
            'name' => 'max_amount',
            'type' => 'number',
            'default' => 300
        ];
        parent::initialize();
    }


    protected function _checkAndCalculateItem(array $duty, $adutyAccounting, $percentCount): float
    {
        if ($this->_detApplyRule_2a($duty, $adutyAccounting)) {
            return $this->_detCheckAndCalculateItem2a($duty, $adutyAccounting);
        } elseif ($this->_detApplyRule_2b($duty, $adutyAccounting)) {
            return $this->_detCheckAndCalculateItem2b($duty, $adutyAccounting);
        } else {
            return parent::_checkAndCalculateItem($duty, $adutyAccounting, $percentCount);
        }
    }

    protected function _detCheckAndCalculateItem2a($duty, $adutyAccounting): float
    {

        $salary = $this->_detGetWeeklySalary($duty);
        if ($salary == 0) {
            $this->currentRuleData['accountings'][] = [
                'message' => 'No maching entries found in '. __('saddress_generalaccountings'),
                'notes' => ''
            ];
            return 0;
        }

        $weelyServicesData = $this->_detGetWeeklyServicesData($duty);
        if (empty($weelyServicesData['duties'])) {
            $this->currentRuleData['hasError'] = true;
            $this->currentRuleData['accountings'][] = [
                    'hasError' => true,
                    'message' => 'No ' . __('sseasons.duties') . ' set for the '
                        . __('adates.season_id') . ' "' . $weelyServicesData['name'] . "'"
                ];
            return 0;
        }
        $dutiesPerWeek = $weelyServicesData['duties'];

        $amountFull = $salary / $dutiesPerWeek;

        $expensetypeName = !empty($adutyAccounting->sexpensetype)
            ? $adutyAccounting->sexpensetype->name : '';

        $saccitem = $this->_getSaccitem($adutyAccounting);
        $saccitemAmount = $this->_getEffectiveSaccitemAmount($saccitem, $duty['adate']['date_']);

        $adutyAccountingMessage = [
            'hasError' => false,
            'amount' => 0,
            'expensetype_name' => $expensetypeName,
            'saccitem_name' => $this->message2a
                //$adutyAccounting->saccitem
                //? $adutyAccounting->saccitem->name
                //: (($adutyAccounting->sexpensetype && $adutyAccounting->sexpensetype->saccitem)
                //    ? $adutyAccounting->sexpensetype->saccitem->name
                //    : 'unknown');
        ];

        $accountingCategory = $duty['accounting_category'];
        $saccitemamountValue = $this->_getSaccitemamountValue($saccitemAmount, $accountingCategory);
        $percent = $saccitemamountValue->value;

        $amountItem = $amountFull * ($percent / 100);

        if ($this->_isPayableBasedOnAccountingperiod($adutyAccounting, $duty)) {

            $adutyAccountingMessage['amount'] = $amountItem;
            $adutyAccountingMessage['message'] = $percent . '% of '
                .  '(' . number_format($salary, 2, '.', ',')
                . ' / ' . $dutiesPerWeek . ')';
            $this->adutyAccountingMessages[] = $adutyAccountingMessage;

            $this->currentRuleData['aexpenses'][] = $this->_createAexpense([
                'id' => $adutyAccounting['id'],
                'expensetype_id' => $adutyAccounting['expensetype_id'],
                'accountno' =>  $adutyAccounting->sexpensetype->accountno,
                'amount' => $amountItem,
                'text' => $adutyAccounting['text']
            ], $duty);

        } else {

            $expense = $this->_filterExpenses($adutyAccounting, $duty);
            $payDate = !empty($expense['adate']) ? $expense['adate']['date_']
                : $expense['adate_date'];
            $this->anErrorOccured = true;
            $adutyAccountingMessage['message'] = __(
                'the amount of: {0} was already paid on {2}',
                $this->_formatNumber($amountItem),
                null,
                !empty($payDate) ? $payDate->format(Configure::read('Formats.date')) : '-'
            );
            $adutyAccountingMessage['hasError'] = true;
            $this->adutyAccountingMessages[] = $adutyAccountingMessage;
        }

        return $amountItem;
    }

    protected function _detCheckAndCalculateItem2b($duty, $adutyAccounting): float
    {

        $expensetypeName = !empty($adutyAccounting->sexpensetype)
            ? $adutyAccounting->sexpensetype->name : '';

        $saccitem = $this->_getSaccitem($adutyAccounting);
        $saccitemAmount = $this->_getEffectiveSaccitemAmount($saccitem, $duty['adate']['date_']);

        $maxAmount = $this->ruleSettings['max_amount'];

        $adutyAccountingMessage = [
            'hasError' => false,
            'amount' => 0,
            'expensetype_name' => $expensetypeName,
            'saccitem_name' => sprintf($this->message2b, $maxAmount)
            //$adutyAccounting->saccitem
            //? $adutyAccounting->saccitem->name
            //: (($adutyAccounting->sexpensetype && $adutyAccounting->sexpensetype->saccitem)
            //    ? $adutyAccounting->sexpensetype->saccitem->name
            //    : 'unknown');
        ];

        $accountingCategory = $duty['accounting_category'];
        $saccitemamountValue = $this->_getSaccitemamountValue($saccitemAmount, $accountingCategory);
        $value = $saccitemamountValue->value;

        $expensetypeId = $adutyAccounting->expensetype_id;
        $artistId = $duty['artist_id'];
        $week = $duty['adate']['week'];

        return 0;

        $amountItem = $amountFull * ($percent / 100);

        if ($this->_isPayableBasedOnAccountingperiod($adutyAccounting, $duty)) {

            $adutyAccountingMessage['amount'] = $amountItem;
            $adutyAccountingMessage['message'] = $percent . '% of '
                .  '(' . number_format($salary, 2, '.', ',')
                . ' / ' . $dutiesPerWeek . ')';
            $this->adutyAccountingMessages[] = $adutyAccountingMessage;

            $this->currentRuleData['aexpenses'][] = $this->_createAexpense([
                'id' => $adutyAccounting['id'],
                'expensetype_id' => $adutyAccounting['expensetype_id'],
                'accountno' =>  $adutyAccounting->sexpensetype->accountno,
                'amount' => $amountItem,
                'text' => $adutyAccounting['text']
            ], $duty);

        } else {

            $expense = $this->_filterExpenses($adutyAccounting, $duty);
            $payDate = !empty($expense['adate']) ? $expense['adate']['date_']
                : $expense['adate_date'];
            $this->anErrorOccured = true;
            $adutyAccountingMessage['message'] = __(
                'the amount of: {0} was already paid on {2}',
                $this->_formatNumber($amountItem),
                null,
                !empty($payDate) ? $payDate->format(Configure::read('Formats.date')) : '-'
            );
            $adutyAccountingMessage['hasError'] = true;
            $this->adutyAccountingMessages[] = $adutyAccountingMessage;
        }

        return $amountItem;
    }
}
