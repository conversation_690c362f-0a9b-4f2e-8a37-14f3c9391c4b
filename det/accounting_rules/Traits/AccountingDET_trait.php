<?php

namespace Customer\det\accounting_rules\Traits;

use Cake\Datasource\ConnectionManager;
use Cake\ORM\TableRegistry;

trait AccountingDET_trait
{

    protected $_detDBConn = null;
    protected $_detDutytypeId = null;
    protected $_detApplyRule = false;

    protected function _detApplyRule_1a($duty): bool
    {
        $applyRule = false;

        if ($this->_detIsSubstitute($duty['addressgroup_id'])
            and strtoupper(substr($duty['sdutytype']['code'], 0, 2)) === 'S '
            and $duty['sdutytype']['expensetype_id']
        ) {
            $applyRule = true;
        }

        return $applyRule;
    }

    protected function _detApplyRule_1b($duty): bool
    {
        $applyRule = false;

        if ($this->_detIsSubstitute($duty['addressgroup_id'])) {
            $applyRule = true;
        }

        return $applyRule;
    }

    protected function _detApplyRule_1c($duty): bool
    {
        $applyRule = false;

        if (! in_array($duty['sdutytype']['percentcount'], [0, 100])
            and ! $this->_detIsSubstitute($duty['addressgroup_id'])
        ) {
            $applyRule = true;
        }

        return $applyRule;
    }

    protected function _detApplyRule_2a($duty, $adutyAccounting): bool
    {
        $applyRule = false;

        if (! $this->_detIsSubstitute($duty['addressgroup_id'])
            and $adutyAccounting['saccitem']['billing_type'] === 'p'
        ) {
            $applyRule = true;
        }

        return $applyRule;
    }

    protected function _detApplyRule_2b($duty, $adutyAccounting): bool
    {
        $applyRule = false;

        if (mb_strtoupper(mb_substr($adutyAccounting['sexpensetype']['code'], 0, 4)) === '§DBL'
            and $adutyAccounting['saccitem']['billing_type'] === 'a'
        ) {
            $applyRule = true;
        }

        return $applyRule;
    }

    protected function _detIsSubstitute($addressgroupId): bool
    {

        if (! $this->_detDBConn) {
            $this->_detDBConn = ConnectionManager::get('default');
        }

        $sql = <<<SQL
            select sysgroup_id
            from saddressgroups
            where id = {$addressgroupId}
            SQL;
        $result = $this->_detDBConn
            ->Query($sql)
            ->fetchAll('assoc');

        return ($result[0]['sysgroup_id'] == 12);
    }

    protected function _detGetWeeklySalary($duty): float
    {
        $addressGeneralaccountingsTable = TableRegistry::getTableLocator()->get('SaddressGeneralaccountings');
        $generalAccountings = $addressGeneralaccountingsTable->find()
            ->select(['amount', 'effective_date', 'effectiveend_date'])
            ->contain(['Sexpensetypes'])
            ->where([
                'SaddressGeneralaccountings.address_id' => $duty['artist_id'],
                'SaddressGeneralaccountings.accountingperiod' => 'W',
                'SaddressGeneralaccountings.effective_date <=' => $duty['adate']['date_'],
                'SaddressGeneralaccountings.effectiveend_date >=' => $duty['adate']['date_'],
                'Sexpensetypes.name NOT LIKE' => 'Stipend%'
            ])
            ->all();
        $salary = 0;
        foreach ($generalAccountings as $generalAccounting) {
            $salary += $generalAccounting->amount;
        }
        return $salary;
    }

    protected function _detGetWeeklyServicesData($duty): array
    {
        $seasonsTable = TableRegistry::getTableLocator()->get('Sseasons');
        $season = $seasonsTable->find()
            ->select(['duties', 'name'])
            ->where([
                'id' => $duty['adate']['season_id']
            ])
            ->first();
        return [
            'duties' => $season['duties'],
            'name' => $season['name']
            ];
    }

}
