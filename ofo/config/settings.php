<?php

return [
    //'debug' => true,

    'Error' => [
        'errorLevel' => 0
    ],

    'App' => [
        'defaultLocale' => 'no_NO'
    ],

    'CustomerSettings' => [
        'name' => 'Oslo Filharmonien',
        'timezone' => 'Europe/Berlin'
    ],

    /**
     * Set only those languages you want to
     * show in the header bar
     * If you want to add lang. look into config/app.php -> Languages
     * and copy/paste required languages
     */
    'DisplayLanguages' => [
        'no_NO' => 'norsk',
        'en_GB' => 'uk english'
    ],

    /**
     * Mandatierung
     */
    'Mandating' => [
        'enabled' => false
    ],

    'Formats' => [
        'date' => 'd.m.Y',
        'time' => 'H:i:s',
        'time_short' => 'H:i',
        'full' => 'd.m.Y H:i:s',
        'week' => 'y-W', //week format Adates
        'momentjsFullDate' => 'DD.MM.YYYY HH:mm',
        'momentjsDate' => 'DD.MM.YYYY',
        'momentjsTime' => 'HH:mm',
        'momentjsWeek' => 'YY-WW',
        'calendar' => [
            'monthColumnHeaderFormat' => 'dd', // Mo
            'weekColumnHeaderFormat' => 'ddd DD.MM.', // Mo. 21.10. (Dayname Month-Day)
            'dayColumnHeaderFormat' => 'dddd', // Monday
            'eventTimeFormat' => [
                'hour' => '2-digit', // 'numeric' (7:12), '2-digit' (07:12)
                'minute' => '2-digit', // 'numeric' (7:5), '2-digit' (7:05)
                'meridiem' => false, // false (19:00), 'narrow' (7p), 'short' (7pm)
                'hour12' => false, // false || true (show 12 hour format. set true if meridiem is used)
            ],
        ]
    ],

    /**
     * OpasOnline Settings
     */
    'OpasOnline' => [
        'enable' => 1,
        'memory_limit' => '2G',
        //Test mit FTP
        'ftp' => [
            'sftp' => 0,
            'host' => 'ofo.opas-online.com',
            'port' => '21',
            'timeout' => '30',
            'username' => 'ftp_ofo_oo',
            'password' => 'w2EkG#xP',
            'directory' => 'pickup'
        ]
    ],

    'Modules' => [
        'Contract' => [
            'enabled' => 1
        ],
        'Service' => [
            'enabled' => 1
        ],
        'Daniels' => [
            'enabled' => 1
        ],
        'Api' => [
            'enabled' => 1
        ]
    ],

    'Daniels' => [
        'userId' => '167',
        'token' => 'e2f96941e3d7f920',
    ],

];
