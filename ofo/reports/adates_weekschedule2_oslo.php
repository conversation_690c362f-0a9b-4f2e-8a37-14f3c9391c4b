<?php
/*
20230808 ONCUST-2199
OSLO Week schedule 2
*/

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use App\Model\Table\AdaysTable;

class adates_weekschedule2_oslo extends ReportWord
{

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;
    private $adate = null;


    public $aweeks = array();
    public $aweek = array();
    public $aproject = array();

    private $count_weeks = 0;

    private $section = null;

    private $k = 0;

    private $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('name'=>'F37 Beckett Demi', 'bold' => true);
    public $styleFont_italic = array('name'=>'F37 Beckett', 'italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_strikethrough = array('name'=>'F37 Beckett', 'strikethrough' => true);

    public $styleFont_8 = array('size' => 8);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_11 = array('size' => 11);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_18 = array('size' => 18);
    public $styleBG_grey = array('bgColor'=>'#C0C0C0');

    public $stylePar_center = array('align'=>'center');
    public $stylePar_left = array('align'=>'left');
    public $stylePar_right = array('align'=>'right');
    public $stylePar_spacing_1_2 = array('spacing' => 1.2);
    public $stylePar_border = array('borderSize' => 4, 'borderColor' => '000000');


    public $styleCell_borderTop_none = array('borderTopSize' => 0, 'borderTopColor' => 'white');
    public $styleCell_borderBottom_none = array('borderBottomSize' => 0, 'borderBottomColor' => 'white');
    public $styleCell_borderLeft_none = array('borderLeftSize' => 0, 'borderLeftColor' => 'white');
    public $styleCell_borderRight_none = array('borderRightSize' => 0, 'borderRightColor' => 'white');

    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black', 'borderTopStyle' => 'single');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black', 'borderottomStyle' => 'single');
    public $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => 'black', 'borderLeftStyle' => 'single');
    public $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black', 'borderRightStyle' => 'single');

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan4 = array('gridSpan' => 4);
    private $cellColSpan7 = array('gridSpan' => 7);

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    private $aconductors = array();
    private $asoloists = array();
    private $apersons = array();
    private $aworks = array();

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);

        $this->date_min = null;
        $this->date_max = null;

        $count = 0;

        foreach($this->adates_selected as $adate) {

            if($adate->l_print_details==0) {continue;}

            $adate->programcode = $this->reporttools->getProgramCode($adate);

            $count++;
            if($count==1) {
                $this->date_min = $adate->date_;
            }

            $this->date_max = $adate->date_;
            $date_id = (int)$adate->id;
            $week = $adate->week;
            $project_id = (int)$adate->project_id;
            $programno = $adate->programno;

            $year = (int)$adate->year;


            if (!array_key_exists($week, $this->aweeks)) {
                $this->aweeks[$week] = array(
                    'adays' => array(),
                    'amonths' => array(),
                    'aprojects' => array()
                );

                // Alle Tage der Woche holen
                $table = new AdaysTable();

                $adays = $table
                    ->find('all')
                    ->contain(['Sholydays'])
                    ->where(["Adays.week"=>$week, "Adays.planninglevel"=>1])
                    ->orderAsc('Adays.date_')
                    ->distinct();

                foreach($adays as $aday) {
                    $d = $aday->date_->format('Ymd');

                    if (!array_key_exists($d, $this->aweeks[$week]['adays'])) {
                        $this->aweeks[$week]['adays'][$d] = array(
                            'date_' => $aday->date_,
                            'dow' => $aday->date_->format('N'),
                            'holyday_id' => (int)$aday->holyday_id,
                            'holyday' => ($aday->sholyday ? $aday->sholyday->name : ''),
                            'pweek' => $aday->pweek,
                            'cdate' => $aday->date_->format('j.n.'), //8 ) Dates without leading zeros and the year (i.e. ma 2.1., 3.1. etc.)
                            'cday' => $aday->date_->format('d'),
                            'weekday' => substr($this->reporttools->getWeekday($aday->date_),0,2),
                            'text' => $aday->text_,
                            'adates_l' => array(),
                            'adates_r' => array()
                        );
                    }
                }
            }

            //nur Aufführungen und l_print_details == 1
            // Projekte ohne Aufführung sollen auch berücksichtigt werden

            //20240423
            // dieser Bericht ist eine Projektinformation für diese eine spezifische Woche.
            // Also zeigen wir Programm und maxbesetzung des Projekts in dieser Woche. Programm wird gefüllt nach Aufführung,
            // dann füllen die Termine chronologisch im Wochenverlauf.
            //
            //if($adate->l_print_details == 1 && $adate->seventtype->l_performance==1 && $project_id > 0) {
            if($adate->l_print_details == 1 && $project_id > 0) {
                $project_key = $project_id.'#'.$programno;
                if (!array_key_exists($project_key, $this->aweeks[$week]['aprojects'])) {
                    $this->aweeks[$week]['aprojects'][$project_key] = array(
                        'project' => ($adate->sproject ? $adate->sproject->name : ''),
                        'programno' => $programno,
                        'project_name2' => ($adate->sproject ? $adate->sproject->name2 : ''),
                        'apds' => array(),
                        'adates' => array(),
                        'anotes' => array()
                    );
                }

                if($adate->seventtype->l_performance==1) {
                    $this->aweeks[$week]['aprojects'][$project_key]['apds'][$adate->id] = $adate;
                }

                $adate->order_ = 'k'.
                    ($adate->seventtype && $adate->seventtype->l_performance<>0 ? '1' : '2').'#'.
                    ($adate->date_ ? $adate->date_->format('Ymd') : '').'#'.
                    ($adate->start_ ? $adate->start_->format('H:i') : '');
                $this->aweeks[$week]['aprojects'][$project_key]['adates'][$adate->id] = $adate;
            }

            if($adate->notes>'' && !in_array($adate->notes, $this->aweeks[$week]['aprojects'][$project_key]['anotes'])) {
                $this->aweeks[$week]['aprojects'][$project_key]['anotes'][] = $adate->notes;
            }

            $d = $adate->date_->format('Ymd');
            if($adate->l_ticket_online == 1) {
                $this->aweeks[$week]['adays'][$d]['adates_r'][] = $adate;
            } else {
                $this->aweeks[$week]['adays'][$d]['adates_l'][] = $adate;
            }

            $month_year = $this->reporttools->getMonthName($adate->date_) . ' '.$adate->year;
            //$this->aweeks[$week]['adays'][$d]['text'] .= "\n".'d2='.$d;

            if(!in_array($month_year, $this->aweeks[$week]['amonths'])) {
                $this->aweeks[$week]['amonths'][] = $month_year;
            }
        }

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(9);
        $this->phpWord->setDefaultFontName('F37 Beckett');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $this->count_weeks = 0;
        foreach($this->aweeks as $week => $this->aweek) {
            $this->count_weeks++;

            $this->section = $this->phpWord->addSection(
                array(
                    'headerHeight' => Converter::cmToTwip(0.5),
                    'footerHeight' => Converter::cmToTwip(0.5),
                    'marginLeft' => Converter::cmToTwip(1.5),
                    'marginRight' => Converter::cmToTwip(1),
                    'marginTop' => Converter::cmToTwip(1.5),
                    'marginBottom' => Converter::cmToTwip(1.5))
            );

            // 20231212
            // Fußzeile soll bitte folgenden Text enthalten, schwarze Schrift, mittig zentriert, siehe Foto:
            // OFOs prøveplan legges dagen før den enkelte prøve ut i DP og i Google chat
            // Alt fravær meldes til orkestervakt. Tlf. 457 30 228

            $footer = $this->section->addFooter();

            /*$footer->addText(htmlspecialchars('Alt fravær meldes til'), null, array_merge($this->stylePar_center));
            $footer->addText(htmlspecialchars('ORKESTERVAKT'), null, array_merge($this->stylePar_center));
            $footer->addText(htmlspecialchars('457 30 228'), $this->styleFont_bold, array_merge($this->stylePar_center));
            $footer->addText(htmlspecialchars('OFOs prøveplan legges dagen før den enkelte prøve ut i skyen og leses inn på telefonsvarer 22 83 05 07.'), null, array_merge($this->stylePar_center));
            */

            $footer->addText(htmlspecialchars('OFOs prøveplan legges dagen før den enkelte prøve ut i DP og i Google chat'), null, array_merge($this->stylePar_center));
            $footer->addText(htmlspecialchars('Alt fravær meldes til orkestervakt. Tlf. 457 30 228'), null, array_merge($this->stylePar_center));

            $this->section->addText(htmlspecialchars('Uke '.substr($week,3,2).', '.implode(', ', $this->aweek['amonths'])), $this->styleFont_bold);
            $this->showSchedule();
            $this->showProjects();
        }
    }

    function showSchedule() {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(1.12);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(4.5);
        $this->acol_widths[] = Converter::cmToTwip(1.37);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(3.75);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginBottom' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout'      => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $table = $this->section->addTable($tableProperties);

        $count_days = 0;

        foreach ($this->aweek['adays'] as $aday) {
            $count_days++;

            $holyday_id = $aday['holyday_id'];
            $holyday = $aday['holyday'];

            //aus Terminen
            $max_row = max(sizeof($aday['adates_l']), sizeof($aday['adates_r']));

            //*** I want light red background color on holidays and weekend days.
            $dow = $aday['date_']->format('N');
            $alight_red = array();
            $alight_red_holyday = array();
            if ($dow == 6 || $dow == 7 || $holyday_id > 0) {
                $alight_red = array('bgColor' => 'FE9494');
            }

            if ($holyday_id > 0) {
                $alight_red_holyday = $alight_red;
            }


            $aborder_l = array_merge(
                $this->styleCell_borderTop,
                $this->styleCell_borderBottom_none,
                $this->styleCell_borderLeft,
                $this->styleCell_borderRight
            );

            $aborder_m = array_merge(
                $this->styleCell_borderTop,
                $this->styleCell_borderBottom_none,
                $this->styleCell_borderLeft,
                $this->styleCell_borderRight
            );

            $aborder_r = array_merge(
                $this->styleCell_borderTop,
                $this->styleCell_borderBottom_none,
                $this->styleCell_borderLeft,
                $this->styleCell_borderRight
            );

            if($max_row<=1) {
                $aborder_l = array_merge($aborder_l, $this->styleCell_borderBottom);
                $aborder_m = array_merge($aborder_m, $this->styleCell_borderBottom);
                $aborder_r = array_merge($aborder_r, $this->styleCell_borderBottom);
            }

            $table->addRow();
            $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell, $alight_red, $this->cellRowSpan, $aborder_l));
            $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell, $aborder_m));
            $cell2 = $table->addCell($this->acol_widths[2], array_merge($styleCell, $aborder_m));
            $cell3 = $table->addCell($this->acol_widths[3], array_merge($styleCell, $alight_red_holyday, $aborder_m));
            $cell4 = $table->addCell($this->acol_widths[4], array_merge($styleCell, $aborder_m));
            $cell5 = $table->addCell($this->acol_widths[5], array_merge($styleCell, $aborder_m));
            $cell6 = $table->addCell($this->acol_widths[6], array_merge($styleCell, $aborder_m));
            $cell7 = $table->addCell($this->acol_widths[7], array_merge($styleCell, $aborder_r));

            $cell0->addText(htmlspecialchars($aday['weekday']));
            $cell0->addText(htmlspecialchars($aday['date_']->format('d.m.')));

            if ($holyday>'') {
                $cell3->addText(htmlspecialchars($holyday));
            }

            if (sizeof($aday['adates_l']) == 0 && sizeof($aday['adates_r']) == 0) {
                $cell1->addText(htmlspecialchars('-'), null, $this->stylePar_center);
            }

            for ($i = 0; $i < $max_row; $i++) {
                if ($i > 0 || $holyday_id > 0) {

                    $aborder_l = array_merge(
                        $this->styleCell_borderTop_none,
                        $this->styleCell_borderBottom_none,
                        $this->styleCell_borderLeft,
                        $this->styleCell_borderRight
                    );

                    $aborder_m = array_merge(
                        $this->styleCell_borderTop_none,
                        $this->styleCell_borderBottom_none,
                        $this->styleCell_borderLeft,
                        $this->styleCell_borderRight
                    );

                    $aborder_r = array_merge(
                        $this->styleCell_borderTop_none,
                        $this->styleCell_borderBottom_none,
                        $this->styleCell_borderLeft,
                        $this->styleCell_borderRight
                    );

                    $count = 0;
                    $l_bottom = false;

                    if($i==($max_row-1)) {
                        $l_bottom = true;

                        $aborder_l = array_merge($aborder_l, $this->styleCell_borderBottom);
                        //$aborder_l = array_merge($aborder_l, $this->styleCell_borderBottom, $this->styleCell_borderLeft_none, array('borderTopSize' => 6, 'borderTopColor' => 'red'));
                        $aborder_m = array_merge($aborder_m, $this->styleCell_borderBottom);
                        $aborder_r = array_merge($aborder_r, $this->styleCell_borderBottom);
                    }

                    $table->addRow();
                    $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue, $aborder_l));
                    $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell, $aborder_m));
                    $cell2 = $table->addCell($this->acol_widths[2], array_merge($styleCell, $aborder_m));
                    $cell3 = $table->addCell($this->acol_widths[3], array_merge($styleCell, $aborder_m));
                    $cell4 = $table->addCell($this->acol_widths[4], array_merge($styleCell, $aborder_m));
                    $cell5 = $table->addCell($this->acol_widths[5], array_merge($styleCell, $aborder_m));
                    $cell6 = $table->addCell($this->acol_widths[6], array_merge($styleCell, $aborder_m));
                    $cell7 = $table->addCell($this->acol_widths[7], array_merge($styleCell, $aborder_r));
                }

                // show 'adates_l'
                if (isset($aday['adates_l'][$i])) {
                    $adate = $aday['adates_l'][$i];


                    $project_code = ($adate->sproject ? $adate->sproject->code : '');
                    $programno = $adate->programno;
                    $eventtype_code = ($adate->seventtype ? $adate->seventtype->code : '');
                    $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
                    $eventtype_name2 = ($adate->seventtype ? ($adate->seventtype->name2>'' ? $adate->seventtype->name2 : $adate->seventtype->name) : '');

                    $series = $this->reporttools->getSeries($adate);

                    // *** I want this field to show two things:
                    // *** 1: Name on venue, if not Oslo konserthus (code §OKHL  and  §OKHS)
                    // *** 2: Text-field
                    // *** could be separatede by comma if the activity contains both informations.


                    $l_rs = ($adate->adates_marketing ? $adate->adates_marketing->l_logic_1 : 0);
                    $l_tv = ($adate->adates_marketing ? $adate->adates_marketing->l_logic_2 : 0);
                    $location = $this->getLocation($adate);

                    $astrikethrough = array();
                    if ($eventtype == 'avl. konsert' || substr($eventtype_code, 0, 2) == 'AV') {
                        $astrikethrough = $this->styleFont_strikethrough;
                    }

                    $styleFont = $astrikethrough;

                    $cell1->addText(htmlspecialchars($this->reporttools->getTime($adate)), array_merge($styleFont), $this->stylePar_center);

                    if ($eventtype > '') {
                        $cell2->addText(htmlspecialchars($eventtype_name2), array_merge($styleFont));
                    }
                    if ($series > '') {
                        $cell2->addText(htmlspecialchars($series), array_merge($styleFont));
                    }


                    // *** We use the logic 1+2 (marketing) as a checkbox for Radio/TV production. Can we get that info in the text field? Logic 1 = "Radiosending", Logic 2 = "TV-sending"

                    // *** - When Activity type is IP, then write name of that activity type (today: "Ingen podietjeneste") in text/comment field.
                    if (strtoupper($eventtype_code) == 'IP') {
                        $cell3->addText(htmlspecialchars($eventtype), array_merge($styleFont));
                    }

                    if ($l_rs == 1) {
                        $cell3->addText(htmlspecialchars('Radiosending'), array_merge($styleFont));
                    }

                    if ($l_tv == 1) {
                        $cell3->addText(htmlspecialchars('TV-sending'), array_merge($styleFont));
                    }

                    $text_location = $location;
                    $text_location .= (($adate->text > '' && $location > '') ? ', ' : '');
                    $text_location .= $adate->text;

                    if ($text_location > '') {
                        $cell3->addText(htmlspecialchars($text_location), array_merge($styleFont));
                    }


                    //*** Project code. Use "-"  if empty.
                    if (empty($project_code)) {
                        $project_code = '-';
                    }
                    $cell4->addText(htmlspecialchars(trim($project_code . ' ' . $programno)), array_merge($styleFont));

                }

                // show 'adates_r'
                if (isset($aday['adates_r'][$i])) {
                    $adate = $aday['adates_r'][$i];


                    $project_code = ($adate->sproject ? $adate->sproject->code : '');
                    $programno = $adate->programno;
                    $eventtype_code = ($adate->seventtype ? $adate->seventtype->code : '');
                    $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');

                    $series = $this->reporttools->getSeries($adate);

                    // *** I want this field to show two things:
                    // *** 1: Name on venue, if not Oslo konserthus (code §OKHL  and  §OKHS)
                    // *** 2: Text-field
                    // *** could be separatede by comma if the activity contains both informations.
                    $location = $this->getLocation($adate);

                    $styleFont = array();

                    $cell5->addText(htmlspecialchars($this->reporttools->getTime($adate)));

                    if ($eventtype > '') {
                        $cell6->addText(htmlspecialchars($eventtype), array_merge($styleFont));
                    }
                    if ($series > '') {
                        $cell6->addText(htmlspecialchars($series), array_merge($styleFont));
                    }

                    $text_location = $location;
                    $text_location .= (($adate->text > '' && $location > '') ? ', ' : '');
                    $text_location .= $adate->text;

                    if ($text_location > '') {
                        $cell7->addText(htmlspecialchars($text_location), array_merge($styleFont));
                    }
                }
            }
        }
    }

    function showProjects() {
        $count = 0;
        $this->section->addTextBreak();

        foreach($this->aweek['aprojects'] as $this->aproject) {
            $count++;

            $this->section->addTextBreak(2);

            $project = trim($this->aproject['project'].' '.$this->aproject['programno']).
                ($this->aproject['project_name2'] > '' ? ' ('.$this->aproject['project_name2'].')' : '');


            $pds = '';
            $fpd_id = 0;
            $fpd = null;
            foreach($this->aproject['apds'] as $adate) {
                if($fpd_id==0) {
                    $fpd_id = $adate->id;
                    $fpd = $adate;
                }
                $start = $this->reporttools->getTime($adate, false);

                $pd = $adate->date_->format('d/m'). ($start>'' ? ' - '.$start : '');

                $pds .= ($pds>'' ? ', ' : '').$pd;
        	}

            $oinstrumentation = new instrumentation_ofo();
            //$oinstrumentation->date_id = $fpd_id;
            $oinstrumentation->date_ids = implode(',', array_keys($this->aproject['adates']));
            $oinstrumentation->getInstrumentation();
            $maxinstr = $oinstrumentation->instrumentation_max;
            //$maxinstr.='#'.$oinstrumentation->date_ids;

            $textrun = $this->section->addTextRun();
            $textrun->addText(htmlspecialchars($project), array_merge($this->styleFont_bold, $this->styleFont_underline, $this->styleFont_11));
            $textrun->addText(htmlspecialchars(' ('.$pds.')'), array_merge($this->styleFont_bold, $this->styleFont_underline));

            if($maxinstr>'') {
                $this->section->addText(htmlspecialchars('Maksbesetning: '.$maxinstr));

            }
            $this->prepareProjectInfo();
            $this->section->addTextBreak();
            $this->showCSP();
            $this->section->addTextBreak();
            $this->showProgram();

            //20240117 ONCUST-2784
            $this->showNotes();
        }
    }

    function prepareProjectInfo()
    {

        uasort($this->aproject['adates'], array($this,"usort_dates"));

        $this->aconductors = array();
        $this->asoloists = array();
        $this->apersons = array();
        $this->aworks = array();

        foreach ($this->aproject['adates'] as $adate) {

            foreach ($adate->adate_works as $adate_work) {
                if (!key_exists($adate_work->work_id, $this->aworks)) {
                    $this->aworks[$adate_work->work_id] = $adate_work;
                }
            }

            if ($adate->conductor_id > 0 && $adate->conductoraddress && !key_exists($adate->conductor_id, $this->aconductors)) {
                $this->aconductors[$adate->conductor_id] = $adate->conductoraddress;
            }

            // Soloists
            $where = 'AdateWorks.date_id=' . $adate->id;

            $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');


            $arows = $AdateworkSoloistsTable
                ->find('all')
                ->select([
                    'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                    'AdateworkSoloists.notes',
                    'Saddresses.id', 'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5', 'Saddresses.notes',
                    'Sinstrinstruments.name'
                ])
                ->contain([
                    'AdateWorks',
                    'Saddresses',
                    'Sinstrinstruments'
                ])
                ->where($where)
                ->distinct();

            foreach ($arows as $arow) {

                $k_soloist = $arow->artist_id . '#' . $arow->instrument_id;
                if ($arow->artist_id && $arow->saddress && !key_exists($k_soloist, $this->asoloists)) {
                    $this->asoloists[$k_soloist] = $arow;
                }
            }

            //Persons

            foreach($adate->adate_persons as $adate_person) {
                $k_person = $adate_person->address_id . '#' . $adate_person->addressgroup_id. '#' . $adate_person->function_id . '#' . $adate_person->instrument_id;

                if (!key_exists($k_person, $this->apersons)) {
                    $this->apersons[$k_person] = $adate_person;
                }

            }
        }
    }

    function usort_dates($a, $b) {
        //20240222 ONCUST-2871
        //Gibt es an einem Tag mehrere Termine und davon sind welche ohne Terminart, werden diese als letzte an diesem Tag angezeigt (also erst alle Termine mit Terminart chronologisch an einem Tag und danach Termine ohne Terminart)

        $frm_order_a = $a->order_;
        $frm_order_b = $b->order_;

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function showCSP() {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(9.37);
        $this->acol_widths[] = Converter::cmToTwip(9.14);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginBottom' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout'      => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $table = $this->section->addTable($tableProperties);

        $table->addRow();
        $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell));

        $aconductors = array();

        foreach($this->aconductors as $conductoraddress) {
            // 20231212 ONCUST-2199
            //dirigent bitte nicht fett
            $conductor = $this->reporttools->getLongName($conductoraddress->name2, '', $conductoraddress->name1);

            //$cell0->addText(htmlspecialchars($conductor), array_merge($styleFont, $this->styleFont_bold));
            $textrun = $cell0->addTextRun();
            $textrun->addText(htmlspecialchars($conductor), array_merge($styleFont, $this->styleFont_bold));
            $textrun->addText(htmlspecialchars( ', dirigent'), array_merge($styleFont));
        }

        foreach($this->asoloists as $asoloist) {
            $instrument = ($asoloist->sinstrinstrument ? $asoloist->sinstrinstrument->name : '');
            $soloist = $this->reporttools->getLongName($asoloist->saddress->name2, '', $asoloist->saddress->name1);
            $notes = ($asoloist->notes>'' ? ' ('.$asoloist->notes.')' : '');
            // 20231212 ONCUST-2199
            //Instrument bitte nicht fett
            $textrun = $cell0->addTextRun();
            $textrun->addText(htmlspecialchars($soloist), array_merge($styleFont, $this->styleFont_bold));
            $textrun->addText(htmlspecialchars( ($instrument > '' ? ', ' : '') . $instrument.$notes), array_merge($styleFont));
        }
        //$cell0->addText(print_r($this->apersons,true));
        foreach ($this->apersons as $arow) {
            $address_id = $arow->address_id;


            $addressfunction = ($arow->saddressfunctionitem ? $arow->saddressfunctionitem->name : '');
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');
            $ifg = $instrument.(($instrument>'' && $addressfunction>'') ? ', ' : '').$addressfunction;
            if(empty($ifg)) {
                $ifg = $addressgroup;
            }

            if($address_id > 0) {
                $person = trim($arow->saddress->name2.' '.$arow->saddress->name1);
                // 20231212 ONCUST-2199
                //Funktion bitte nicht fett
                $textrun =  $cell1->addTextRun();
                $textrun->addText(htmlspecialchars($person), array_merge($styleFont, $this->styleFont_bold));
                $textrun->addText(htmlspecialchars( ($ifg>'' ? ', ' : '').$ifg), array_merge($styleFont));

                //$cell1->addText(htmlspecialchars($person), array_merge($styleFont, $this->styleFont_bold));
            }
        }
    }

    //*** Program ***
    function showProgram() {
        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(13.69);
        $this->acol_widths[] = Converter::cmToTwip(5);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginBottom' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout'      => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $table = $this->section->addTable($tableProperties);




        $duration_total = 0;

        foreach ($this->aworks as $adate_work) {
            $bd = ($adate_work->swork->scomposer->birthyear>'' ? $adate_work->swork->scomposer->birthyear:'');
            $bd .= ($adate_work->swork->scomposer->deathyear>'' ? ' - '.$adate_work->swork->scomposer->deathyear:'');

            $composer = $adate_work->swork->scomposer->lastname. ($adate_work->swork->scomposer->firstname>'' ? ', ' : '').$adate_work->swork->scomposer->firstname;

            $title = ($adate_work->title2>'' ? $adate_work->title2 : $adate_work->swork->title1);

            //Add blank line before each new work
            $table->addRow();
            $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell));

            if($adate_work->swork->l_intermission == 1) {
                // 20231212 ONCUST-2199
                //vor und nach der Pause bitte jeweils eine Leerzeile
                $cell0->addTextBreak();
                $cell0->addText(htmlspecialchars($adate_work->swork->title1));
            } else {
                $cell0->addText();
                $textrun = $cell0->addTextRun();

                $textrun->addText(htmlspecialchars($composer.': '), $this->styleFont_bold);

                // 20231212 ONCUST-2199
                //Titel bitte nicht kursiv
                //$textrun->addText(htmlspecialchars($title), $this->styleFont_italic);
                $textrun->addText(htmlspecialchars($title));
                if($adate_work->arrangement>'') {
                    $textrun->addText(htmlspecialchars(' ('.$adate_work->arrangement.')'), $this->styleFont_italic);
                }
                if($adate_work->l_encore==1) {
                    $textrun->addText(htmlspecialchars(' (ekstranummer)'), $this->styleFont_italic);
                }
            }

            $cduration = $adate_work->duration;

            //00:00:00
            $h = (int)substr($cduration,0,2);
            $m = (int)substr($cduration, 3,2);
            $s = (int)substr($cduration,6,2);

            $duration = $h*60*60 + $m*60 + $s;


            $cduration = ($h>0 ? $h.':' : '');
            $cduration .= ($h>0 ? str_pad($m, 2,"0", STR_PAD_LEFT) : $m).':';
            $cduration .= str_pad($s, 2,"0", STR_PAD_LEFT);

            //$cduration.='#'.$hour.'#'.$minute.'#'.$sec;

            $cell1->addText();
            $cell1->addText(htmlspecialchars($cduration));

            //$duration = (int)substr($cduration, 0, 2) * 60 + (int)substr($cduration, 3, 2);




            $duration_total+=$duration;


            if($adate_work->swork->l_intermission == 0) {

                $oinstrumentation = new instrumentation_ofo();
                $oinstrumentation->datework_id = $adate_work->id;
                $oinstrumentation->getInstrumentation();

                $table->addRow();
                $cell = $table->addCell($w_table, array_merge($styleCell, $this->cellColSpan2));
                $cell->addText(htmlspecialchars($oinstrumentation->instrumentation));
            }
        }

        $h = floor($duration_total/60/60);
        $duration_total = $duration_total - $h*60*60;
        $m = floor($duration_total/60);
        $s = $duration_total%60;

        $cduration_total = ($h>0 ? $h.':' : '');
        $cduration_total .= ($h>0 ? str_pad($m, 2,"0", STR_PAD_LEFT) : $m).':';
        $cduration_total .= str_pad($s, 2,"0", STR_PAD_LEFT);


        //$cduration_total = str_pad($h, 2,"0", STR_PAD_LEFT).':'.str_pad($m, 2,"0", STR_PAD_LEFT).':'.str_pad($s,2,"0", STR_PAD_LEFT);


        $table->addRow();
        $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cell0->addText(htmlspecialchars('Spilletid: '), $this->styleFont_bold, $this->stylePar_right);
        $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell1->addText(htmlspecialchars($cduration_total), $this->styleFont_bold);
    }

    function getLocation($adate)
    {
        $location = '';
        $location_code = '';
        if($adate->locationaddress) {
            $location = $adate->locationaddress->name1;
            $location_code = $adate->locationaddress->code;
            $location_place = $adate->locationaddress->place;
            //*** Remove city name when printing venue .
            $location_place = '';

            $location .= ($location_place>'' ? ', ' : '') . $location_place;
        }



        // *** 1: Name on venue, if not Oslo konserthus (code §OKHL  and  §OKHS)
        // *** lcLocation_Code <> '§OKHL' and
        // *** -We want to have venue "OKH Lille sal" (code $OKHL) to show up


	    if($location_code == '§OKHS') {
            $location = '';
        }

        return $location;
    }

    function showNotes() {

        if(sizeof($this->aproject['anotes'])<=0) {
            return;
        }

        $this->section->addTextBreak();

        $count = 0;
        foreach($this->aproject['anotes'] as $notes) {
            $count++;
            if($count>1) {
                $this->section->addTextBreak();
            }
            $this->reporttools->addMemo($this->section, $notes, $this->styleFont_italic);
        }
    }
}

