<?php

namespace Customer\ofo\reports;
use App\Reports\Tools\InstrumentationCommon;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;



class instrumentation_ofo extends InstrumentationCommon {
    function formatInstrumentation()
    {


        //20231204 ONCUST-2199
        //use . instead of /, for timpani, percussion, harp, piano and others, use numbers instead of text.

        //$cast_separator = __('cast_separator');
        $cast_separator = '.';

        // 20231212 ONCUST-2199
        //instrumentierung bitte mit - trennen
        $cast_prefix_br = __('cast_prefix_br');
        $cast_prefix_br = ' - ';

        $cast_prefix_str = __('cast_prefix_str');
        $cast_prefix_str = ' - ';

        $cast_prefix_ex = __('cast_prefix_ex');
        $cast_prefix_ex = ' - ';
        $this->instrumentation = '';

        $cast_open_bracket = __('cast_open_bracket');
        $cast_open_bracket = '(';

        $cast_close_bracket = __('cast_close_bracket');
        $cast_close_bracket = ')';

        if (is_null($this->data_row)) {
            return;
        }


// TOM: for North America: Timpani text is only output if if contains a letter and then
//  After the Timpani number. Also, '-' may be omitted
        $row = $this->data_row;


        if(isset($row->l_intermission) && $row->l_intermission==1) {
            return '';
        }

        //20231204 ONCUST-2199
        //use . instead of /, for timpani, percussion, harp, piano and others, use numbers instead of text.

        // 20231214 ONCUST-2199
        //Nummer statt Text
        /*$timpani = ((isset($row->timpani_text) && $row->timpani_text) > '' ? trim($row->timpani_text) : $row->timpani);
        $percussion = ((isset($row->percussion_text) && $row->percussion_text) > '' ? trim($row->percussion_text) : $row->percussion);
        $harp = ((isset($row->harp_text) && $row->harp_text) > '' ? trim($row->harp_text) : $row->harp);
        $keyboard = ((isset($row->keyboard_text) && $row->keyboard_text) > '' ? trim($row->keyboard_text) : $row->keyboard);
        $extra = ((isset($row->extra_text) && $row->extra_text) > '' ? trim($row->extra_text) : $row->extra);
        */

        //20240117 Es fehlen die Texte bei timp/perc/harp/pno/div , sollen in Klammern sein Beispiel:
        /*
        $timpani = $row->timpani;
        $percussion = $row->percussion;
        $harp = $row->harp;
        $keyboard = $row->keyboard;
        $extra = $row->extra;
        */

        $timpani = $row->timpani . ((isset($row->timpani_text) && $row->timpani_text > '') ? $cast_open_bracket . trim($row->timpani_text) . $cast_close_bracket : '');
        $percussion = $row->percussion . ((isset($row->percussion_text) && $row->percussion_text > '') ? $cast_open_bracket . trim($row->percussion_text) . $cast_close_bracket : '');
        $harp = $row->harp . ((isset($row->harp_text) && $row->harp_text > '') ? $cast_open_bracket . trim($row->harp_text) . $cast_close_bracket : '');
        $keyboard = $row->keyboard . ((isset($row->keyboard_text) && $row->keyboard_text > '') ? $cast_open_bracket . trim($row->keyboard_text) . $cast_close_bracket : '');
        $extra = $row->extra . ((isset($row->extra_text) && $row->extra_text > '') ? $cast_open_bracket . trim($row->extra_text) . $cast_close_bracket : '');

        $vocals =
            ((isset($row->vocals_text) && $row->vocals_text) > '' ? $row->vocals_text :
                ($row->vocals == 0 ? '' :
                    ($row->vocals > 1 ? $row->vocals . __('cast_dash') : '') . 'voc'
                )
            );

        $this->strings =
            $row->violin1 . $cast_separator .
            $row->violin2 . $cast_separator .
            $row->viola . $cast_separator .
            $row->cello . $cast_separator .
            $row->bass;

        // 20230713 ONDEV-4202
        // Wenn unter adate_works.strings_text ein Text hinterlegt ist soll dieser Text angezeigt werden anstelle der Instrumentierung.
        // Bsp.: https://test.opas-next.de/adate-works/edit/24?tab=2 Anstatt: 4[2fl, 2pic] / 2 / 4[2cl, 2bcl] / 4[2bn, 2cbn]     4 / 4 / 3 / 1     P / 10 Sz      /  /  /  /   soll im Bericht stehen: 	4[2fl, 2pic] / 2 / 4[2cl, 2bcl] / 4[2bn, 2cbn]     4 / 4 / 3 / 1     P / 10 Sz      str
        // Der Text soll auch angezeigt werden, wenn bei den Streichinstrumenten Zahlen hinterlegt sind

        if(isset($row->strings_text) && $row->strings_text>'') {
            $this->strings = $row->strings_text;
        }

        $other = $timpani;
        $other .= $cast_separator . $percussion;
        $other .= $cast_separator . $harp;
        $other .= $cast_separator . $keyboard;
        $other .= $cast_separator . $extra;

        $this->instrumentation =
            $row->flute . ((isset($row->flute_text) && $row->flute_text > '') ? $cast_open_bracket . $row->flute_text . $cast_close_bracket : '') . $cast_separator .
            $row->oboe . ((isset($row->oboe_text) && $row->oboe_text > '') ? $cast_open_bracket . $row->oboe_text . $cast_close_bracket : '') . $cast_separator .
            $row->clarinet . ((isset($row->clarinet_text) && $row->clarinet_text > '') ? $cast_open_bracket . $row->clarinet_text . $cast_close_bracket : '') . $cast_separator .
            $row->bassoon . ((isset($row->bassoon_text) && $row->bassoon_text > '') ? $cast_open_bracket . $row->bassoon_text . $cast_close_bracket : '');

        $this->instrumentation .=
            $cast_prefix_br.
            $row->horn . ((isset($row->horn_text) && $row->horn_text > '') ? $cast_open_bracket . $row->horn_text . $cast_close_bracket : '') . $cast_separator .
            $row->trumpet . ((isset($row->trumpet_text) && $row->trumpet_text > '') ? $cast_open_bracket . $row->trumpet_text . $cast_close_bracket : '') . $cast_separator .
            $row->trombone . ((isset($row->trombone_text) && $row->trombone_text > '') ? $cast_open_bracket . $row->trombone_text . $cast_close_bracket : '') . $cast_separator .
            $row->tuba . ((isset($row->tuba_text) && $row->tuba_text > '') ? $cast_open_bracket . $row->tuba_text . ']' : '');

        if($other>'') {
            $this->instrumentation .= $cast_prefix_ex. $other;
        }

        if($this->strings>'') {
            $this->instrumentation .= $cast_prefix_str . $this->strings;
        }


        $this->instrumentation = str_replace('&nbsp;', ' ', $this->instrumentation);

        /*
         /  /  /       /  /  /       /  /  /  /
        */
        if($this->instrumentation ==
            str_replace('&nbsp;', ' ',
                $cast_separator.$cast_separator.$cast_separator.
                $cast_prefix_br.
                $cast_separator.$cast_separator.$cast_separator.
                $cast_prefix_br.
                $cast_separator.$cast_separator.$cast_separator.$cast_separator
            )
        ) {
            $this->instrumentation = '';
        }

        /*
        $this->instrumentation ='#'.$this->instrumentation.'#'. str_replace('&nbsp;', ' ',
                $cast_separator.$cast_separator.$cast_separator.
                $cast_prefix_br.
                $cast_separator.$cast_separator.$cast_separator.
                $cast_prefix_br.
                $cast_separator.$cast_separator.$cast_separator.$cast_separator
            ).'#';

        $this->instrumentation .= '#'.$cast_prefix_str.'#'.$cast_separator.'#';
*/
        $this->instrumentation_max = $this->instrumentation;
        $this->strings_max = $this->strings;
    }
}
