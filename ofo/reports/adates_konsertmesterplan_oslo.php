<?php
/*
20230817 ONCUST-2204

OFO Konsertmesterplan
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use App\Model\Table\AdateworkMovementsTable;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;
use App\Model\Table\AdateWorksTable;
use App\Model\Table\AdaysTable;

class adates_konsertmesterplan_oslo extends ReportSpreadsheet
{
    private $user = '';

    public $aseasons = array();
    public $aseason = array();
    public $aproject = array();

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;

    private $firstrow_project = 0;
    private $oinstrumentation = null;

    private $date_min = null;
    private $date_max = null;

    private $nRN_Header = 0;

    private $nCN_Week = 1;
    private $nCN_Project = 2;
    private $nCN_Dates = 3;
    private $nCN_ProjectInfo = 4;
    private $nCN_Guest = 5;

    private $nMaxColNum = 5;

    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();
    private $borders_thin_tb = array();

    private $borders_medium_top = array();
    private $borders_medium_bottom = array();
    private $borders_medium_top_bottom = array();




    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    private $borders_none_right = array();
    private $borders_none_left = array();

    function initialize()
    {
        parent::initialize();

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);


        $where = array_merge(['Adates.id IN ' => $this->postData['dataItemIds']]);
        $adates_selected = $this->model
            ->find('all')
            ->select()
            ->contain(['Sseasons'])
            ->where($where)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC'])
            ->all();

        $this->aseasons = array();

        $sheetno_season = 0;
        foreach($adates_selected as $adate) {
            $season_id = (int)$adate->season_id;
            $season = ($adate->sseason ? $adate->sseason->name : 'Season '.$season_id);

            if (!array_key_exists($season_id, $this->aseasons)) {

                $sheetno_season++;
                $this->aseasons[$season_id] = array(
                    'sheetno' => $sheetno_season,
                    'season' => $season,
                    'caption' => 'Saison ' . $season,
                    'sheetname' => $season,
                    'ayears' => array(),
                    'akzms' => array()
                );
            }
        }

        $aseason_ids = array_keys($this->aseasons);

        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        $where = array_merge([
            'Aduties.date_id IN' => $this->postData['dataItemIds'],
            'Adates.season_id IN '=>$aseason_ids,
            ['OR' => ['Saddressgroups.name'=>'Konsertmester', ['Sdutytypes.name' => 'Solovikar', 'Sinstrsyssections.code' => 'VL1']]]
            ]
        );

        //Der Bericht berÃ¼cksichtigt nur Dienste mit l_present == 1;
        $this->aduties_selected = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses'],
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses',
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups'],
                'AdutyWorks' => ['AdateWorks' => ['Sworks' => ['Scomposers']]]
            ])
            ->where($where)
            ->order(['Adates.date_', 'Adates.start_', 'Aduties.order_1'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);

        $count = 0;
        $sheetno_season = 0;
        foreach($this->aduties_selected as $aduty) {
            $count++;

            $adate = $aduty->adate;
            $date_id = (int)$adate->id;
            $season_id = (int)$adate->season_id;
            $project_id = (int)$adate->project_id;
            $year = (int)$adate->year;
            $artist_id = (int)$aduty->artist_id;

            $addressgroup = ($aduty->saddressgroup ? $aduty->saddressgroup->name : '');
            $dutytype = ($aduty->sdutytype ? $aduty->sdutytype->name : '');

            if($project_id<=0) {continue;}

            if($addressgroup == 'Konsertmester') {
                if (!array_key_exists($artist_id, $this->aseasons[$season_id]['akzms'])) {
                    $this->aseasons[$season_id]['akzms'][$artist_id] = array(
                        'cn' => 0,
                        'artist_name' => ($aduty->artistaddress ? $aduty->artistaddress->name2 : '')
                    );
                }
            }


            if (!array_key_exists($year, $this->aseasons[$season_id]['ayears'])) {
                $this->aseasons[$season_id]['ayears'][$year] = array(
                    'aprojects' => array()
                );
            }

            if (!array_key_exists($project_id, $this->aseasons[$season_id]['ayears'][$year]['aprojects'])) {
                $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id] = array(
                    'project' => ($adate->sproject ? $adate->sproject->name : 'Project '.$project_id),
                    'project_name2' => ($adate->sproject ? $adate->sproject->name2 : ''),
                    'aweeks' => array(),
                    'adates' => array(),
                    'apds' => array(),
                    'akzms' => array(),
                    'aguests' => array()
                );
            }

            $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['adates'][$date_id] = $adate;

            $week = substr($adate->week, 3,2);
            if($week>'' && !in_array($week, $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['aweeks'])) {
                $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['aweeks'][] = $week;
            }
            if($adate->seventtype->l_performance == 1) {
                $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['apds'][$date_id] = $adate;
            }

            if (!array_key_exists($artist_id, $this->aseasons[$season_id]['aprojects'][$project_id]['akzms'])) {
                $this->aseasons[$season_id]['aprojects'][$project_id]['akzms'][$artist_id] = array(
                    'adutytypes' => array()
                );
            }

            if ($aduty->dutytype_id>0 && !array_key_exists($aduty->dutytype_id, $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['akzms'][$artist_id]['adutytypes'])) {
                $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['akzms'][$artist_id]['adutytypes'][$aduty->dutytype_id] = $aduty->sdutytype;
            }
            //$this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['akzms'][$artist_id]['adutytypes'][$aduty->dutytype_id] = $aduty->sdutytype;


            if($dutytype=='Solovikar') {
                if (!array_key_exists($artist_id, $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['aguests'])) {
                    $this->aseasons[$season_id]['ayears'][$year]['aprojects'][$project_id]['aguests'][$artist_id] = ($aduty->artistaddress ? $aduty->artistaddress->name1.($aduty->artistaddress->name2>'' ? ', ' : '').$aduty->artistaddress->name2 : '');
                }
            }
        }
        return $this;
    }

    public function write_sheets($view = null, $layout = null)
    {
        $this->ospreadsheet->setActiveSheetIndex(0);


        $count = 0;
        foreach ($this->aseasons as $k => $this->aseason) {
            $sheetname = $this->aseason['sheetname'];

            $sheetname = str_replace('/', '_', $sheetname);
            $sheetname = str_replace('\\', '_', $sheetname);
            $sheetname = str_replace('?', '_', $sheetname);
            $sheetname = str_replace('+', '_', $sheetname);
            $sheetname = str_replace('#', '_', $sheetname);
            $sheetname = str_replace('*', '_', $sheetname);
            $sheetname = str_replace(']', '_', $sheetname);
            $sheetname = str_replace('[', '_', $sheetname);

            $sheetname = substr($sheetname, 0, 31);

            $count++;

            if ($count > 1) {
                $this->ospreadsheet->createSheet();
            }

            $this->ospreadsheet->setActiveSheetIndex($count - 1);
            $this->sheet = $this->ospreadsheet->getActiveSheet();
            $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

            $this->sheet->setTitle($sheetname);

            //$this->prepare_data_month();

            $this->write_sheet();
        }
    }

    function write_sheet() {

        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(10);

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(0.9/2.5);
        $this->sheet->getPageMargins()->setRight(0.5/2.5);
        $this->sheet->getPageMargins()->setLeft(1.2/2.5);
        $this->sheet->getPageMargins()->setBottom(0.9/2.5);
        $this->sheet->getPageMargins()->setHeader(0.3/2.5);
        $this->sheet->getPageMargins()->setFooter(0.8/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        $this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,2);

        $this->sheet->getHeaderFooter()->setOddHeader('&C'.$this->report->title);
        $this->sheet->getHeaderFooter()->setOddFooter('&R&30&D / &P');

        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Week))->setWidth(8.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Project))->setWidth(27.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Dates))->setWidth(15+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_ProjectInfo))->setWidth(15+0.71);

        $cn = $this->nCN_ProjectInfo;
        foreach($this->aseason['akzms'] as $k=>$akzm) {
            $cn++;
            $this->aseason['akzms'][$k]['cn'] = $cn;
            $this->sheet->getColumnDimension($this->getColumnLetter($cn))->setWidth(15+0.71);
        }
        $cn++;
        $this->nCN_Guest = $cn;
        $this->nMaxColNum = $this->nCN_Guest;
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Guest))->setWidth(15+0.71);

        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_Duration))->getAlignment()->setHorizontal('right');

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week).':'.$this->getColumnLetter($this->nMaxColNum))->getNumberFormat()->setFormatCode('@');
        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_Week).':'.$this->getColumnLetter($this->nMaxColNum))->getAlignment()->setWrapText(true);

        $this->row = 1;
        $this->sheet->getRowDimension($this->row)->setRowHeight(25.50);

    	$this->nRN_Header = $this->row;

        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) .$this->row . ":" . $this->getColumnLetter($this->nMaxColNum) .$this->row)->applyFromArray(array_merge($this->borders_thin_top));
        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) .$this->row . ":" . $this->getColumnLetter($this->nMaxColNum) .$this->row)->getAlignment()->setHorizontal('center');
        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) .$this->row . ":" . $this->getColumnLetter($this->nMaxColNum) .$this->row)->getAlignment()->setHorizontal('center');
        $this->sheet->mergeCells($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row));
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setBold(true);
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setSize(16);
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Week).($this->row), htmlspecialchars('Konsertmesterplan '.$this->aseason['season']));

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week).$this->row)->getAlignment()->setVertical(PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        $this->sheet
            ->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))
            ->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('FFCC99');

        $count_years = 0;
        foreach($this->aseason['ayears'] as $ayear) {
            $count_years++;

            $this->row++;
            $this->row++;

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Week).($this->row), htmlspecialchars('Uke'));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars('Prosjekt'));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Dates).($this->row), htmlspecialchars('dato'));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_ProjectInfo).($this->row), htmlspecialchars('Prosjektinfo'));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Guest).($this->row), htmlspecialchars('Gjest'));

            foreach($this->aseason['akzms'] as $akzm) {
                $cn = $akzm['cn'];
                $this->sheet->getColumnDimension($this->getColumnLetter($cn))->setWidth(15+0.71);
                $this->sheet->setCellValue($this->getColumnLetter($cn).($this->row), htmlspecialchars($akzm['artist_name']));
            }

            $this->sheet
                ->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('FFCC99');

            $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setBold(true);
            $this->sheet->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setItalic(true);

            foreach($ayear['aprojects'] as $aproject) {

                $this->row++;

                if($this->row%2>0) {
                    $this->sheet
                        ->getStyle($this->getColumnLetter($this->nCN_Week) . ($this->row) . ":" . $this->getColumnLetter($this->nMaxColNum) . ($this->row))
                        ->getFill()
                        ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setARGB('D9D9D9');
                }

                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Week).($this->row), htmlspecialchars(implode(', ', $aproject['aweeks'])));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars($aproject['project']));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Dates).($this->row), htmlspecialchars($this->getMinMaxDays($aproject['adates'])));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_ProjectInfo).($this->row), htmlspecialchars($aproject['project_name2']));

                foreach($this->aseason['akzms'] as $artist_id => $akzm) {
                    $cn = $akzm['cn'];
                    $dutytype = '';
                    $dutytype_html_color = '';

                    $this->sheet->getStyle($this->getColumnLetter($cn) .$this->row)->getAlignment()->setHorizontal('center');

                    //$dutytype = $artist_id.'#'.sizeof($this->aproject['akzms'][$artist_id]['adutytypes']).'#';

                    //$this->aseasons[$season_id]['aprojects'][$project_id]['akzms'][$artist_id]['adutytypes'][$aduty->dutytype_id] = $aduty->sdutytype;
                    if(isset($aproject['akzms'][$artist_id])) {
                        foreach($aproject['akzms'][$artist_id]['adutytypes'] as $dutytype_id=>$sdutytype) {
                            //$dutytype .= $dutytype_id.'#';
                            $dutytype .= ($dutytype > '' ? '/' : '') . $sdutytype->code;
                            if(empty($dutytype_html_color)) {
                                $dutytype_html_color = substr($sdutytype->html_color,1);
                            }
                        }
                    }
                    $this->sheet->getColumnDimension($this->getColumnLetter($cn))->setWidth(15+0.71);
                    $this->sheet->setCellValue($this->getColumnLetter($cn).($this->row), htmlspecialchars($dutytype));


                    if($dutytype_html_color>'') {
                        $this->sheet
                            ->getStyle($this->getColumnLetter($cn).($this->row))
                            ->getFill()
                            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                            ->getStartColor()
                            ->setARGB($dutytype_html_color);
                    }

                }

                $guests = implode('/', $aproject['aguests']);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Guest).($this->row), htmlspecialchars($guests));
            }
        }

        foreach ($this->sheet->getColumnIterator() as $column) {
            $this->sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }
        /*
        for ($i = $this->nCN_Week; $i <= $this->nMaxColNum; $i++) {
            $this->sheet->getColumnDimension($i)->setAutoSize(true);
        }
        */
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) .(1) . ":" . $this->getColumnLetter($this->nMaxColNum) .$this->row)->applyFromArray(array_merge($this->borders_thin_all));
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_tb = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_medium_top_bottom = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];

        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if($count==1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates)>0) {
            $y_min = $date_min->format('Y');
            $m_min = $date_min->format('m');
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $date_max->format('m');
            $d_max = $date_max->format('d');

            $minmax =
                $d_min.'.'.
                ($m_min<>$m_max ? $m_min.'.' : '').
                '-'.
                $d_max.'.'.$m_max.'.';
        }

        return $minmax;
    }
}
