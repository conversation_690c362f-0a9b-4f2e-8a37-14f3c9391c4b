<?php
/*
 * 20231218 ONCUST-2620
OF Program pr. prosjekt
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use App\Model\Table\AdateworkMovementsTable;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;
use App\Model\Table\AdateWorksTable;
use App\Model\Table\AdaysTable;

class adates_program_per_project_ofo extends ReportSpreadsheet
{
    private $report = null;
    public $aprojects = array();
    public $aproject = array();

    private $sheet = null;

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;

    private $firstrow_project = 0;
    private $oinstrumentation = null;

    private $nRN_Header = 0;

    private $nCN_week = 1;
    private $nCN_Project = 2;
    private $nCN_Persons = 3;
    private $nCN_Composer = 4;
    private $nCN_Title = 5;

    private $nMaxColNum = 5;

    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();
    private $borders_thin_tb = array();

    private $borders_medium_top = array();
    private $borders_medium_bottom = array();
    private $borders_medium_top_bottom = array();




    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    private $borders_none_right = array();
    private $borders_none_left = array();

    function initialize()
    {
        parent::initialize();

        //$this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);


        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);
        $this->prepare_aprojects();

        return $this;
    }

    function prepare_aprojects() {

        $this->aprojects = array();

        foreach ($this->adates_selected as $adate) {
            $season_id = $adate->season_id;
            $project_id = $adate->project_id;
            $planninglevel = (int)$adate->planninglevel;

            $k_project = $season_id . '#' . $project_id . '#' . $planninglevel;

            if (!array_key_exists($k_project, $this->aprojects)) {

                $this->aprojects[$k_project] = array(
                    'season_id' => $season_id,
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'project_id' => $project_id,
                    'planninglevel' => $planninglevel,
                    'aweeks' => array(),
                    'project_code' => ($adate->sproject ? $adate->sproject->code : ''),
                    'project' => ($adate->sproject ? $adate->sproject->name : ''),
                    'project_notes' => ($adate->sproject ? $adate->sproject->notes : ''),
                    'adates' => array(),
                    'fpd' => array(),
                    'apds' => array(),
                    'aconductors' => array(),
                    'asoloists' => array(),
                    'apersons' => array(),
                    'adate_works' => array()
                );
            }
        }
    }

    function prepare_projectdata() {
        $awhere = [
            'Adates.project_id'=>$this->aproject['project_id'],
            'Adates.season_id'=>$this->aproject['season_id'],
            'Adates.planninglevel'=>$this->aproject['planninglevel']
        ];

        $adates_project = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

        $workNumber = 0;
        $count_pd = 0;
        foreach($adates_project as $adate) {

            $week = (int)substr($adate->week,3);
            if (!in_array($week, $this->aproject['aweeks'])) {
                $this->aproject['aweeks'][] = $week;
            }

            if ($adate->seventtype && $adate->seventtype->l_performance == 1) {
                $this->aproject['apds'][$adate->id] = $adate;

                $count_pd++;
                if($count_pd==1) {
                    $this->aproject['fpd'] = $adate;
                }

                foreach ($adate->adate_works as $adate_work) {

                    if($adate_work->swork->l_intermission==1) {
                        continue;
                    }
                    if(!key_exists($adate_work->work_id, $this->aproject['adate_works'])) {
                        $this->aproject['adate_works'][$adate_work->work_id] = $adate_work;
                    }
                }
            }


            // Dirigrnt, Solisten und Personen aus allen Terminen
            $conductor = '';

            if($adate->conductoraddress) {
                $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, $adate->conductoraddress->name5, $adate->conductoraddress->name1);
            }

            if ($conductor>'' && !in_array($conductor, $this->aproject['aconductors'])) {
                $this->aproject['aconductors'][] = $conductor;
            }

            $this->prepare_soloists($adate->id);
            $this->prepare_persons($adate);

            $this->aproject['adates'][$adate->id] = $adate;
        }
    }

    function prepare_soloists($date_id)
    {

        $arows = $this->reporttools->getSoloists($date_id);

        foreach ($arows as $arow) {
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');

            if($arow->artist_id > 0 && !key_exists($arow->artist_id, $this->aproject['asoloists'])) {
                $this->aproject['asoloists'][$arow->artist_id] = array(
                    'name' => $this->reporttools->getLongName($arow->saddress->name2, $arow->saddress->name5, $arow->saddress->name1),
                    'instrument' => $instrument
                );
            }
        }
    }

    function prepare_persons($adate)
    {

        foreach($adate->adate_persons as $arow) {

            $addressfunction = ($arow->saddressfunctionitem ? $arow->saddressfunctionitem->name : '');
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');
            $ifg = $instrument.(($instrument>'' && $addressfunction>'') ? ', ' : '').$addressfunction;
            if(empty($ifg)) {
                $ifg = $addressgroup;
            }

            if($arow->address_id > 0 && !key_exists($arow->address_id, $this->aproject['apersons'])) {
                $this->aproject['apersons'][$arow->address_id] = array(
                    'name' => $this->reporttools->getLongName($arow->saddress->name2, $arow->saddress->name5, $arow->saddress->name1),
                    'ifg' => $ifg
                );
            }
        }
    }

    public function write_sheets($view = null, $layout = null)
    {
        $this->ospreadsheet->setActiveSheetIndex(0);

        $sheetname = $this->report->title;

        $sheetname = str_replace('/', '_', $sheetname);
        $sheetname = str_replace('\\', '_', $sheetname);
        $sheetname = str_replace('?', '_', $sheetname);
        $sheetname = str_replace('+', '_', $sheetname);
        $sheetname = str_replace('#', '_', $sheetname);
        $sheetname = str_replace('*', '_', $sheetname);
        $sheetname = str_replace(']', '_', $sheetname);
        $sheetname = str_replace('[', '_', $sheetname);

        $sheetname = substr($sheetname, 0, 31);

        $this->ospreadsheet->setActiveSheetIndex(0);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

        $this->sheet->setTitle($sheetname);

        $this->write_sheet();
    }

    function write_sheet() {

        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(10);

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(1.5/2.5);
        $this->sheet->getPageMargins()->setRight(1/2.5);
        $this->sheet->getPageMargins()->setLeft(2/2.5);
        $this->sheet->getPageMargins()->setBottom(1/2.5);
        $this->sheet->getPageMargins()->setHeader(0.3/2.5);
        $this->sheet->getPageMargins()->setFooter(0.8/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        $this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,3);

        $this->sheet->getHeaderFooter()->setOddHeader('&C'.$this->report->title);
        $this->sheet->getHeaderFooter()->setOddFooter('&R&30&D / &P');

        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_week))->setWidth(3.7+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Project))->setWidth(20.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Persons))->setWidth(24.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Composer))->setWidth(30+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Title))->setWidth(52.57+0.71);

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week).':'.$this->getColumnLetter($this->nMaxColNum))->getNumberFormat()->setFormatCode('@');
        //$this->sheet->getStyle($this->getColumnLetter($this->nCN_week).':'.$this->getColumnLetter($this->nMaxColNum))->getAlignment()->setWrapText(true);

        $this->row = 1;
        //$this->sheet->getRowDimension($this->row)->setRowHeight(25.50);
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('OF Program pr. prosjekt'));

        $this->row++;
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('Oppdatert: '.date('d.m.Y')));

        $this->row++;

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) .$this->row)->getAlignment()->setHorizontal('center');
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_week).($this->row), htmlspecialchars('Uke'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars('Prosjektnavn'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Persons).($this->row), htmlspecialchars('Artister'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer).($this->row), htmlspecialchars('Komponist'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title).($this->row), htmlspecialchars('Tittel'));

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->applyFromArray(array_merge($this->borders_thin_top));
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setBold(true);

        $this->nRN_Header = $this->row;

        foreach($this->aprojects as $this->aproject) {

            $this->row++;
            $this->prepare_projectdata();

            $this->firstrow_project = $this->row;
            $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->applyFromArray(array_merge($this->borders_thin_top));

            //20240122 ONCUST-2844
            //Change to week number (smaller column)
            //
            $weeks = implode(', ', $this->aproject['aweeks']);
            //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_week).($this->row), htmlspecialchars($this->aproject['project_code']));
            $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) .$this->row)->getAlignment()->setHorizontal('center');
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_week).($this->row), htmlspecialchars($weeks));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars($this->aproject['project']));

            $row = $this->firstrow_project-1;

            foreach($this->aproject['aconductors'] as $conductor) {
                $row = $this->add_row($row);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Persons).($row), htmlspecialchars($conductor));
            }

            foreach($this->aproject['asoloists'] as $asoloist) {
                $row = $this->add_row($row);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Persons).($row), htmlspecialchars($asoloist['name'] . (!empty($asoloist['instrument']) ? ', ' : '') . $asoloist['instrument']));
            }

            $count_persons = 0;
            foreach($this->aproject['apersons'] as $aperson) {
                $count_persons++;
                //20240710 ONCUST-3851
                //[blank row]
                if($count_persons==1) {
                    $row = $this->add_row($row);
                }
                $row = $this->add_row($row);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Persons).($row), htmlspecialchars($aperson['name'] . (!empty($aperson['ifg']) ? ', ' : '') . $aperson['ifg']));
            }

            $row = $this->firstrow_project-1;

            foreach($this->aproject['adate_works'] as $adate_work) {
                $row = $this->add_row($row);

                //<- use title 2!
                $title = ($adate_work->title2>'' ? $adate_work->title2 : $adate_work->swork->title1);

                // 20240122 ONCUST-2844
                // add arrangement field after title in parentheses.
                $title .= (!empty($adate_work->arrangement) ? ' ('.$adate_work->arrangement.')' : '');

                $composer = ($adate_work->swork && $adate_work->swork->scomposer ? trim($adate_work->swork->scomposer->firstname.' '.$adate_work->swork->scomposer->lastname) : '');

                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer).($row), htmlspecialchars($composer));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title).($row), str_replace('&quot;', '"', htmlspecialchars($title)));
            }
        }

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_week) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->applyFromArray(array_merge($this->borders_thin_bottom));

        for ($i=$this->nCN_week; $i<=$this->nMaxColNum; $i++) {
            $this->sheet->getStyle($this->getColumnLetter($i) . ($this->nRN_Header).":".$this->getColumnLetter($i) . ($this->row))->applyFromArray(array_merge($this->borders_thin_left));
            $this->sheet->getStyle($this->getColumnLetter($i) . ($this->nRN_Header).":".$this->getColumnLetter($i) . ($this->row))->applyFromArray(array_merge($this->borders_thin_right));
        }


        /*foreach ($this->sheet->getColumnIterator() as $column) {
            $this->sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
        }
        */
        /*
        for ($i = $this->nCN_Week; $i <= $this->nMaxColNum; $i++) {
            $this->sheet->getColumnDimension($i)->setAutoSize(true);
        }
        */
    }
    function add_row($row) {

        $row++;

        if($row>$this->row) {
            $this->row = $row;
        }

        return $row;
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_tb = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_medium_top_bottom = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];

        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

    }
}
