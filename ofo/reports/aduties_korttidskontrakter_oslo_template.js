/**
 * Nachgeladene JS-Erweiterung fÃ¼r das Template
 *
 * Hier wird in dem Namespace FUNCTIONS unter dem Namen des Templates eine Funktion abgelegt, die sobald aufgerufen
 * wird, wenn das Template im Wizard angezeigt wird.
 */

var NAMESPACE = "aduties_korttidskontrakter_oslo_template";

var REPORTS = REPORTS || {};

REPORTS.aduties_korttidskontrakter_oslo_template = function () {

  $('.cb').change(function() {
    $name =$(this).attr('name');
    $is_checked = ($(this).is(":checked") ? 1:0);
    writeToStorage($name, $is_checked);
  });

  $('#report_type').change(function() {
    $name = $(this).attr('name');
    $val = $(this).val();
    writeToStorage($name, $val);
  });

  $('#syssection_id').change(function() {
    $name = $(this).attr('name');
    $val = $(this).val();
    writeToStorage($name, $val);
  });

  $('#set_all').click(function() {

    $set_all =$('#set_all').is(":checked");

    $('.cb').each(function () {
      $(this).prop("checked", $set_all);

      $name = $(this).attr('name');
      $val = ($set_all ? 1:0);
      writeToStorage($name, $val);
    });
  });

  function writeToStorage(key, value) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = serializedData ? JSON.parse(serializedData) : {};
    data[key] = value;
    localStorage.setItem(NAMESPACE, JSON.stringify(data));
  }

  function readFromStorage(key) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = JSON.parse(serializedData);
    return data ? data[key] : undefined;
  }

  function setValues() {
    const serializedData = localStorage.getItem(NAMESPACE);

    const data = JSON.parse(serializedData);

    if(data) {
      $.each(data, function (key, val) {
        $val = val > 0;

         if ($("#" + key).length) {
          //$("#" + key).val(val);
          $("#" + key).prop("checked", $val);
        }
      });

      $('#report_type').val(data['report_type']);
      $('#syssection_id').val(data['syssection_id']);
    }
  }

  setValues();
};

