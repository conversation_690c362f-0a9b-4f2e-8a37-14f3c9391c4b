<?php
/*
20231219 ONCUST-2619
OF Framføringsoversikt
*/

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use Cake\ORM\Query;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\Style\ListItem;

use App\Model\Table\AdateworkMovementsTable;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use App\Model\Table\AdaysTable;

class adate_works_composersworks_ofo extends ReportWord
{

    public $postData = null;
    public $reporttools = null;


    public $aworks = array();
    public $awork = array();

    public $awhere = array();

    private $count_weeks = 0;

    private $section = null;

    private $k = 0;

    private $acol_widths = array();

    public $styleFont = array();
    // 20240315 ONCUST-2845
    //Font Change: We have problems with reading after printing. All text should be F37 Beckett Demi

    public $styleFont_bold = array('name'=>'F37 Beckett Demi');
    public $styleFont_italic = array('name'=>'F37 Beckett Demi', 'italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_strikethrough = array('name'=>'F37 Beckett Demi', 'strikethrough' => true);

    public $styleFont_8 = array('size' => 8);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_11 = array('size' => 11);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_16 = array('size' => 16);
    public $styleBG_grey = array('bgColor'=>'#C0C0C0');

    public $stylePar_center = array('align'=>'center');
    public $stylePar_left = array('align'=>'left');
    public $stylePar_right = array('align'=>'right');
    public $stylePar_spacing_1_2 = array('spacing' => 1.2);
    public $stylePar_border = array('borderSize' => 4, 'borderColor' => '000000');


    public $styleCell_borderTop_none = array('borderTopSize' => 0, 'borderTopColor' => 'white');
    public $styleCell_borderBottom_none = array('borderBottomSize' => 0, 'borderBottomColor' => 'white');
    public $styleCell_borderLeft_none = array('borderLeftSize' => 0, 'borderLeftColor' => 'white');
    public $styleCell_borderRight_none = array('borderRightSize' => 0, 'borderRightColor' => 'white');

    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black', 'borderTopStyle' => 'single');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black', 'borderottomStyle' => 'single');
    public $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => 'black', 'borderLeftStyle' => 'single');
    public $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black', 'borderRightStyle' => 'single');

    public $table = null;
    public $w_table = 0;

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan3 = array('gridSpan' => 3);
    private $cellColSpan4 = array('gridSpan' => 4);
    private $cellColSpan7 = array('gridSpan' => 7);

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    private $template_start = '';
    private $template_end  = '';

    protected $templates = [
        [
            'name' => 'adate_works_composersworks_ofo_template',
            'file' => 'adate_works_composersworks_ofo_template.php',
            'jsFile' => 'adate_works_composersworks_ofo_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

    }
    public function prepare_where() {

            $this->awhere = ['Adates.date_ >=' => $this->template_start, 'Adates.date_ <=' => $this->template_end];

    }
    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);


        $this->template_start =  Date::parse($this->postData['formData']['template_start']);
        if(empty($this->template_start)) {
            $this->template_start = time();
        }

        $this->template_end = Date::parse($this->postData['formData']['template_end']);
        if(empty($this->template_end)) {
            $this->template_end = time();
        }

        $this->prepare_where();

        $adate_works_selected = TableRegistry::getTableLocator()->get('AdateWorks')
            ->find('all')
            ->select()
            ->contain([
                'Sworkpremieres',
                'Sworks' => ['Scomposers'],
                'AdateworkSoloists' =>
                    function (Query $query) {
                        return $query
                            ->contain(['Sinstrinstruments', 'Saddresses'])
                            ->orderAsc('AdateworkSoloists.artist_order');
                    },
                'Adates' => [
                    'Seventtypes' => ['Seventtypegroups'],
                    'Locationaddresses' => ['Scountries'],
                    'Conductoraddresses'
                    ]
            ])
            ->where($this->awhere)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC', 'AdateWorks.work_order' => 'ASC'])
            ->all();

        $this->aworks = array();

        $this->date_min = null;
        $this->date_max = null;

        $count = 0;

        foreach($adate_works_selected as $adate_work) {

            if($adate_work->swork->l_intermission == 1) {
                continue;
            }

            $count++;
            if($count==1) {
                $this->date_min = $adate_work->adate->date_;
            }

            $this->date_max = $adate_work->adate->date_;
            $work_id = $adate_work->work_id;

            if (!array_key_exists($work_id, $this->aworks)) {
                $composer = $adate_work->swork->scomposer->lastname . ($adate_work->swork->scomposer->firstname > '' ? ', ' : '') . $adate_work->swork->scomposer->firstname;
                $title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);

                //Add arranger field after title.

                $title .= ($adate_work->arrangement>'' ?  ' ('.$adate_work->arrangement.')' : '');
                $this->aworks[$work_id] = array(
                    'title' => $title,
                    'composer' => $composer,
                    'adate_works' => array()
                );
            }

            $this->aworks[$work_id]['adate_works'][] = $adate_work;
        }

        //20240122 ONCUST-2845
        //List should be alphabetical on composers, then work titles.
        uasort($this->aworks, array($this,"usort_aworks"));

        return $this;
    }

    function usort_aworks($a, $b) {
        $frm_order_a = $a['composer'].'#'.$a['title'];
        $frm_order_b = $b['composer'].'#'.$b['title'];

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(10.5);
        $this->phpWord->setDefaultFontName('F37 Beckett Demi');

        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(0.5),
                'footerHeight' => Converter::cmToTwip(0.5),
                'marginLeft' => Converter::cmToTwip(1.5),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(1.5),
                'marginBottom' => Converter::cmToTwip(1.5))
        );
        $header = $this->section->addHeader();
        $header->addImage(CUSTOMER_REP_DIR. 'adate_works_composersworks_ofo.png', array('width'=>Converter::cmToPoint(3.5),'align'=>'left'));

        $footer = $this->section->addFooter();
        $footer->addPreserveText('{PAGE} (of) {NUMPAGES}.');

        $mindate = ($this->date_min ? $this->date_min->format('d.m.Y') : '');
        $maxdate = ($this->date_max ? $this->date_max->format('d.m.Y') : '');

        $this->section->addText(htmlspecialchars('Framføringsoversikt'), array_merge($this->styleFont_bold, $this->styleFont_16));
        $this->section->addText(htmlspecialchars('Periode: '.$mindate.' - '.$maxdate), array_merge($this->styleFont_bold, $this->styleFont_16));
        $this->section->addTextBreak();

        /*
        $multilevelNumberingStyleName = 'multilevel';
        $this->phpWord->addNumberingStyle(
            $multilevelNumberingStyleName,
            [
                'type' => 'multilevel',
                'levels' => [
                    ['format' => 'decimal', 'text' => '%1.', 'left' => 360, 'hanging' => 360, 'tabPos' => 360],
                    ['format' => 'upperLetter', 'text' => '%2.', 'left' => 720, 'hanging' => 360, 'tabPos' => 720],
                ],
            ]
        );
// Lists
        $this->section->addText('Multilevel list.');
        $this->section->addListItem('List Item I', 0, null, $multilevelNumberingStyleName);
        $this->section->addListItem('List Item I.a', 1, null, $multilevelNumberingStyleName);
        $this->section->addListItem('List Item I.b', 1, null, $multilevelNumberingStyleName);
        $this->section->addListItem('List Item II', 0, null, $multilevelNumberingStyleName);
        $this->section->addListItem('List Item II.a', 1, null, $multilevelNumberingStyleName);
        $this->section->addListItem('List Item III', 0, null, $multilevelNumberingStyleName);
        $this->section->addTextBreak(2);

        $this->section->addListItem('List Item 1', 0);
        $this->section->addListItem('List Item 2', 0);
        $this->section->addListItem('List Item 3', 0);

        // Add listitem elements
        $listStyle = array('listType'=>ListItem::TYPE_SQUARE_FILLED);

        $this->section->addListItem('List Item 2', 0, null, $listStyle);
        $this->section->addListItem('List Item 3', 0, null, $listStyle);
        $this->section->addTextBreak(2);
*/
        $listStyle = array(

        );

        $styleTabs = array(
            'indentation' => array(
                'left' => Converter::cmToTwip(0.5),
                'hanging' => Converter::cmToTwip(0.5)
            ),
            'tabs' => array(
                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(3.2)),
                //new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(3.8)),
                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(6.5)),
                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(13.5))
            )
        );

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(3.5);
        $this->acol_widths[] = Converter::cmToTwip(3.5);
        $this->acol_widths[] = Converter::cmToTwip(6);
        $this->acol_widths[] = Converter::cmToTwip(4.5);

        $this->w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->w_table += $col_width;
        }

        $this->tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginBottom' => Converter::cmToTwip(0.01),
            'width' => $this->w_table,
            'unit' => TblWidth::TWIP,
            'layout'      => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $this->table = $this->section->addTable($this->tableProperties);

        $this->count_works = 0;
        foreach($this->aworks as $work_id => $this->awork) {
            $this->count_works++;

            $this->table->addRow();
            $cell = $this->table->addCell($this->w_table, array_merge($styleCell, $this->cellColSpan4));

            $this->table->addRow();
            //20240315
            //2. To identify works easier, please make the composer/title row underlined. example:
            $cell = $this->table->addCell($this->w_table, array_merge($styleCell, $this->cellColSpan4, $this->styleCell_borderBottom));
            $cell->addText(htmlspecialchars($this->awork['composer'].': '.$this->awork['title']), $this->styleFont_bold);
            //$listStyle = array('listType'=>ListItem::TYPE_NUMBER);

            $this->showSchedule();
        }
    }

    function showSchedule() {
        foreach($this->awork['adate_works'] as $adate_work) {
            //$this->section->addListItem(htmlspecialchars($adate->date_->format('d.m.Y')), 0, null, $listStyle);

            $adate = $adate_work->adate;

            $start = $this->reporttools->getTime($adate, false);
            $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
            $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);
            $location = '';

            if($adate->locationaddress) {
                $location = $adate->locationaddress->name1;
                $location_place = $adate->locationaddress->place;

                //$location .= ($location_place>'' ? ', ' : '') . $location_place;
            }

            $styleCell = array();
            $this->table->addRow();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($adate->date_->format('d.m.Y').' '.$start));

            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($eventtype));

            $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell->addText(htmlspecialchars($location));

            $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell->addText(htmlspecialchars($conductor));

            $styleTabs_soloists = array(
                'indentation' => array(
                    'left' => Converter::cmToTwip(0.5),
                    'hanging' => Converter::cmToTwip(0)
                ),
            );

            $count_soloists = 0;
            foreach ($adate_work->adatework_soloists as $adatework_soloist) {
                $count_soloists++;
            }

            if($count_soloists>0) {
                $this->table->addRow();
                $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));

                $w = $this->acol_widths[1] + $this->acol_widths[2] + $this->acol_widths[3];
                $cell = $this->table->addCell($w, array_merge($styleCell, $this->cellColSpan3));

                foreach ($adate_work->adatework_soloists as $adatework_soloist) {

                    $instrument = ($adatework_soloist->sinstrinstrument ? $adatework_soloist->sinstrinstrument->name : '');
                    $soloist = $this->reporttools->getLongName($adatework_soloist->saddress->name2, '', $adatework_soloist->saddress->name1);
                    $notes = ($adatework_soloist->notes > '' ? ' (' . $adatework_soloist->notes . ')' : '');
                    // 20231212 ONCUST-2199
                    //Instrument bitte nicht fett
                    $cell->addText(htmlspecialchars(
                        $soloist . ($instrument > '' ? ', ' : '') . $instrument . $notes
                    ), null);
                }
            }
        }
    }
}

