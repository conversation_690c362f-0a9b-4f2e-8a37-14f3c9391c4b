<script type="text/html" id="aduties_korttidskontrakter_oslo_template">

  <div class="wizard-message">

    <h3 class="wizard-headline">{{ i18n.templateHeadline }}</h3>

    <p>{{ i18n.templateDescription }}</p>

    <form class="smart-form" id="wizard-template-form">

      <fieldset>

        <div class="row">

          <section class="col col-6">
          <div class="form-group">
            <div class="input-group">

              <p style="width:100%; white-space: nowrap;">
              <input name="set_all" id="set_all" type="checkbox" value="1">
              <label for="set_all"> select all </label>
              </p>

              <br>
                {% for ssyssection in data.asyssections %}

                  <p style="width:100%; white-space: nowrap;">
                    <label class="">
                    <input type="checkbox" name="syssection_{{ ssyssection['id'] }}" id="syssection_{{ ssyssection['id'] }}" value="{{ ssyssection['id'] }}" class="cb" checked> {{ ssyssection['name'] }}
                    </label>
                  </p>
                {% endfor %}
            </div>
          </div>
          </section>
        </div>
      </fieldset>
    </form>
  </div>
</script>
