<?php
set_time_limit(0);

ini_set('max_execution_time', 300);
ini_set('request_terminate_timeout', 300);
/*
20240805 ONCUST-3762
OF Lønnsrapport
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Table;
use PhpOffice\PhpSpreadsheet\Worksheet\Table\TableStyle;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Color;

//use \App\Reports\ReportRtf;
use Cake\Core\Configure;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;

use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;
use App\Model\Table\AdateWorksTable;

class adates_lonnsrapport_ofo extends ReportSpreadsheet
{
    public $user = '';

    private $sheet = null;

    public $caption = '';
    public $row = 0 ;

    public $postData = null;
    public $reporttools = null;

    public $adates_selected = null;
    public $adate = null;
    public $aprojects = array();
    public $aproject = array();

    private $sheet_no = 0;
    private $sheet_caption = '';

    public $rn_first = 5;
    public $rn_first_vikarer = 5;
    //public $rn_first_P = 50;//26;

    private $cn_name1 = 1;
    private $cn_name2 = 2;
    private $cn_payment = 3;
    private $cn_accitem_name2 = 4;
    private $cn_accitem_code = 5;
    private $cn_amount = 6;
    private $cn_belop = 7;
    private $cn_timer = 8;
    private $cn_dutytype = 9;
    private $cn_short = 10;
    private $cn_long = 11;
    private $cn_2duties = 12;

    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_medium_top = array();
    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)

       $this->template_filename = CUSTOMER_REP_DIR . 'adates_lonnsrapport_ofo.xlsx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();
    }

    public function collect(array $where = [])
    {
        $this->prepare_borders();
        $this->postData = $this->getRequest()->getData();

        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);


        $this->prepare_aprojects();

        return $this;
    }

    public function prepare_aprojects() {

        $this->aprojects = array();

        foreach ($this->adates_selected as $adate) {

            $season_id = (int)$adate->season_id;
            $project_id = (int)$adate->project_id;
            $eventtypegroup = ($adate->seventtype->seventtypegroup ? $adate->seventtype->seventtypegroup->name : '');
            $sheetno_project = 0;


            if ($project_id==0) {continue;}

            $kproject = $season_id . '_' . $project_id;
            if (!array_key_exists($kproject, $this->aprojects)) {
                $sheetno_project++;

                $this->aprojects[$kproject] = array(
                    'sheetno' => $sheetno_project,
                    'project_id' => $project_id,
                    'sheetname' => $adate->sproject->name,
                    'caption'=> $adate->sproject->name,
                    'project_code' => (!empty($adate->sproject->code) ? $adate->sproject->code : $adate->sproject->name),
                    'project'=> $adate->sproject->name,
                    'adates' => array(),
                    'apds' => array(),
                    'adates_prove' => array(),
                    'aduties_p' => array(),
                    'avikarer' => []

                );
                $this->prepare_project_data($kproject, $season_id, $project_id, $eventtypegroup);
            }
        }
    }

    function prepare_project_data($kproject, $season_id, $project_id, $eventtypegroup) {
        $awhere = [
            'Adates.season_id' => $season_id,
            'Adates.project_id' => $project_id
        ];

        $adates = TableRegistry::getTableLocator()->get('Adates')
            ->find('all')
            ->select()
            ->contain([
                'Sdatestatuses',
                'Sdresses',
                'Sseasons',
                'Sprojects' => ['Sprojecttypes'],
                'Seventtypes' => ['Seventtypegroups'],
                'Locationaddresses' => ['Scountries'],
                'Orchestraaddresses',
                'Conductoraddresses',
                'AdateWorks' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sworks' => ['Scomposers'],
                                'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses'],
                                'Sworkpremieres'
                            ])
                            ->orderAsc('AdateWorks.work_order');
                    },
                'AdatePersons' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Saddresses',
                                'Saddressgroups',
                                'Sinstrinstruments',
                                'Saddressfunctionitems'
                            ])
                            ->orderAsc('AdatePersons.person_order');
                    }
            ])
            ->where($awhere)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC'])
            ->all();


        $count=0;
        foreach ($adates as $adate) {
            $count++;
            $date_id = $adate->id;

            $this->aprojects[$kproject]['adates'][$date_id] = $adate;
            if($adate->seventtype->l_performance == 1) {
                $this->aprojects[$kproject]['apds'][$date_id] = $adate;
            }

            //<number of all types of rehearsals in project (activity group = Prøve)
            if($eventtypegroup == 'Prøve') {
                $this->aprojects[$kproject]['adates_prove'][$date_id] = $adate;
            }
        }

        $aduties = TableRegistry::getTableLocator()->get('Aduties')
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses'],
                'Sdutytypes' => ['Saccitems' => [
                    'SaccitemAmounts' =>
                        function (Query $query) {
                            return $query
                                ->contain(['SaccitemamountValues'])
                                ->orderAsc('SaccitemAmounts.effective_date');
                        }]
                    ],
                'Saddressfunctionitems',
                'Artistaddresses' => ['SaddressPersdata' => ['Spayments']],
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']

            ])
            ->where($awhere)
            ->order(['Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order', 'Sdutytypes.l_present'=>'DESC', 'Aduties.order_1'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);

        foreach($aduties as $aduty) {
            $dutytype_code = ($aduty->sdutytype ? $aduty->sdutytype->code : '');
            $l_present = ($aduty->sdutytype ? $aduty->sdutytype->l_present : '');

            $d = $aduty->adate->date_->format('Ymd');

            $artist_id = $aduty->artist_id;

            if($dutytype_code == 'P2') {
                if(!array_key_exists($artist_id, $this->aprojects[$kproject]['aduties_p'])) {
                    $this->aprojects[$kproject]['aduties_p'][$artist_id] = array(
                        'name1' => $aduty->artistaddress->name1,
                        'name2' => $aduty->artistaddress->name2
                    );
                }
            }

            //List persons in the project with presence types:
            //And count their presences/duties
            //V-SAO	+T-TAO
            //V-TAO	+T-TF
            //V-SF
            //V-TF

            if(in_array($dutytype_code, ['V', 'V-SAO', 'V-TAO', 'V-SF', 'V-TF', '+T-TAO', '+T-TF'])) {
                if(!array_key_exists($artist_id, $this->aprojects[$kproject]['avikarer'])) {
                    $accounting_category = 0;
                    $payment = '';
                    if($aduty->artistaddress->saddress_persdata) {
                        $accounting_category = $aduty->artistaddress->saddress_persdata->accounting_category;
                        $payment = ($aduty->artistaddress->saddress_persdata->spayment ? $aduty->artistaddress->saddress_persdata->spayment->name : '');
                    }

                    $accitem_name2 = '';
                    $accitem_code = '';
                    if($aduty->sdutytype) {
                        if($aduty->sdutytype->saccitem) {
                            $accitem_name2 = (!empty($aduty->sdutytype->saccitem->name2) ? $aduty->sdutytype->saccitem->name2 : $aduty->sdutytype->saccitem->name);
                            $accitem_code = $aduty->sdutytype->saccitem->code;

                            $amount = 0;
                            foreach ($aduty->sdutytype->saccitem->saccitem_amounts as $saccitem_amount) {

                                if($saccitem_amount->effective_date<=$aduty->adate->date_) {
                                    if($accounting_category>=1 && $accounting_category<=9) {
                                        $amount = $saccitem_amount['amount_'.$accounting_category];
                                    }
                                }
                            }

                        }

                    }

                    $this->aprojects[$kproject]['avikarer'][$artist_id] = array(
                        'name1' => $aduty->artistaddress->name1,
                        'name2' => $aduty->artistaddress->name2,
                        'accounting_category' => $accounting_category,
                        'payment' => $payment,
                        'accitem_name2' => $accitem_name2,
                        'accitem_code' => $accitem_code,
                        'amount' => $amount,
                        'adays' => [],
                        'adutytypes' => [],
                        'short' => 0,
                        'long' => 0,
                        '2duties' => 0
                    );
                }

                if(!array_key_exists($d,  $this->aprojects[$kproject]['avikarer'][$artist_id]['adays'])) {
                    $adays[$d] = [
                        'aduties' => [],
                        'duration' => 0
                    ];
                }
                $nduration = (int)substr($adate->duration, 0, 2) * 60 + (int)substr($adate->duration, 3, 2);
                $this->aprojects[$kproject]['avikarer'][$artist_id]['adays'][$d]['aduties'][$aduty->id] = $aduty;
                $this->aprojects[$kproject]['avikarer'][$artist_id]['adays'][$d]['duration'] += $nduration;
                if($aduty->sdutytype) {
                    $this->aprojects[$kproject]['avikarer'][$artist_id]['adutytypes'][] = $aduty->sdutytype->name;
                }
            }
        }

        //prepare short, long, 2duties
        foreach($this->aprojects as $kproject => $aproject) {
            foreach($aproject['avikarer'] as $artist_id => $avikarer) {
                $short = 0;
                $long = 0;
                $duties2 = 0;

                foreach($avikarer['adays'] as $aday) {
                    if(sizeof($aday)>1) {
                        $duties2++;
                    } elseif ($aday['duration']>=3*60) {
                        $long++;
                    } else {
                        $short++;
                    }
                }

                $this->aprojects[$kproject]['avikarer'][$artist_id]['short'] = $short;
                $this->aprojects[$kproject]['avikarer'][$artist_id]['long'] = $long;
                $this->aprojects[$kproject]['avikarer'][$artist_id]['2duties'] = $duties2;
            }
        }
    }

    public function write_sheets($view = null, $layout = null)
    {
        //$locale = 'en';
        //$validLocale = \PhpOffice\PhpSpreadsheet\Settings::setLocale($locale);

        $this->write_sheet_tariffer();
        $this->ospreadsheet->setActiveSheetIndex(0);
        $sheet_origin = $this->ospreadsheet->getActiveSheet();

        $sc = $sheet_origin->copy();

        $this->sheet = $this->ospreadsheet->getActiveSheet();
        //$this->sheet->setCellValue('A1', 'XXX');
        //$this->sheet->setCellValue('A2', sizeof($this->aprojects));

        $count = 0;

        foreach ($this->aprojects as $this->aproject) {
            $sheetname = $this->aproject['sheetname'];

            $sheetname = str_replace('/', '_', $sheetname);
            $sheetname = str_replace('\\', '_', $sheetname);
            $sheetname = str_replace('?', '_', $sheetname);
            $sheetname = str_replace('+', '_', $sheetname);
            $sheetname = str_replace('#', '_', $sheetname);
            $sheetname = str_replace('*', '_', $sheetname);
            $sheetname = str_replace(']', '_', $sheetname);
            $sheetname = str_replace('[', '_', $sheetname);

            $sheetname = substr($sheetname, 0, 31);

            if ($count == 0) {
                $this->ospreadsheet->setActiveSheetIndex(0);
                $this->sheet = $this->ospreadsheet->getActiveSheet();
            } else {
                //$temporarySheet = clone $clonedSheet;
                $temporarySheet = clone $sc;
                $this->ospreadsheet->addSheet($temporarySheet, $count);
                unset($temporarySheet);
            }

            $this->ospreadsheet->setActiveSheetIndex($count);
            $this->sheet = $this->ospreadsheet->getActiveSheet();

            //$this->sheet->setTitle($sheetname);

            $count++;

            $this->write_sheet();
        }

    }

    function write_sheet_tariffer() {
        $this->ospreadsheet->setActiveSheetIndex(1);

        $this->sheet = $this->ospreadsheet->getActiveSheet();


        $table = new Table();
        $table->setName('Tilleggstabell');
        $table->setShowTotalsRow(false);
        $table->setRange('C4:F8'); // Using $j so that we get +1 for the totals row

// Optional: apply some styling to the table
        $tableStyle = new TableStyle();
        $tableStyle->setTheme(TableStyle::TABLE_STYLE_MEDIUM18);
        $tableStyle->setShowRowStripes(true);
        $table->setShowTotalsRow(false);

        $table->setStyle($tableStyle);

        $this->sheet->addTable($table);

    }

    function write_sheet() {

        $this->sheet->setCellValue('B1', $this->aproject['project_code']);

        //<number of all types of rehearsals in project (activity group = Prøve)
        //< number of concerts in project

        $this->sheet->setCellValue('B2', sizeof($this->aproject['adates_prove']));
        $this->sheet->setCellValue('B3', sizeof($this->aproject['apds']));





        $this->show_Vikarer();
        $this->show_Tillegg();
        $this->show_Permisjon_uten_lonn();
    }

    function show_Vikarer() {
        //List persons in the project with presence types:
        //And count their presences/duties
        //V-SAO	+T-TAO
        //V-TAO	+T-TF
        //V-SF
        //V-TF

        $rn = $this->rn_first_vikarer;
        for($j=1;$j<=(3+sizeof($this->aproject['avikarer']));$j++) {
            $this->sheet->insertNewRowBefore($rn);

            //$this->sheet->setCellValue($this->getColumnLetter(1) . ($rn), htmlspecialchars($j));

            for ($i = 1; $i <= 100; $i++) {
                $styleArray = $this->sheet->getStyle($this->getColumnLetter($i) . ($rn+1))->exportArray();

                //$this->sheet->setCellValue($this->getColumnLetter($i) . ($this->row), '');
                $this->sheet->getStyle($this->getColumnLetter($i) . ($rn))->applyFromArray($styleArray);
            }

        }

        $this->row = $this->rn_first_vikarer;

        $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), htmlspecialchars('Vikarer og tilleggsmusikere'));
        $this->sheet->getStyle($this->getColumnLetter(1).$this->row)->getFont()->setSize(16);
        $this->row++;
        $this->row++;
        $this->rn_first = $this->row;

        $this->sheet->setCellValue($this->getColumnLetter($this->cn_name1) . ($this->row), htmlspecialchars('Etternavn'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_name2) . ($this->row), htmlspecialchars('Fornavn'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_payment) . ($this->row), htmlspecialchars('Utbetalingmåte'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_accitem_name2) . ($this->row), htmlspecialchars('Lønnskategori'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_accitem_code) . ($this->row), htmlspecialchars('L.tr.'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_amount) . ($this->row), htmlspecialchars('Tariff'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_belop) . ($this->row), htmlspecialchars('Beløp'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_timer) . ($this->row), htmlspecialchars('Timer'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_dutytype) . ($this->row), htmlspecialchars('Vikarkategori'));

        $this->sheet->getStyle($this->getColumnLetter($this->cn_short) . ($this->row))->getAlignment()->setWrapText(true);
        $this->sheet->getStyle($this->getColumnLetter($this->cn_long) . ($this->row))->getAlignment()->setWrapText(true);
        $this->sheet->getStyle($this->getColumnLetter($this->cn_2duties) . ($this->row))->getAlignment()->setWrapText(true);
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_short) . ($this->row), htmlspecialchars('3-6 timer'."\n".'(1/17)'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_long) . ($this->row), htmlspecialchars('<3 timer'."\n".'(1/22)'));
        $this->sheet->setCellValue($this->getColumnLetter($this->cn_2duties) . ($this->row), htmlspecialchars('2 oppm.'."\n".'(1/13)'));


        foreach($this->aproject['avikarer'] as $aaddress) {

            $this->row++;
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_name1) . ($this->row), htmlspecialchars($aaddress['name1']));
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_name2) . ($this->row), htmlspecialchars($aaddress['name2']));
            //Three alternatives.
            //Payment category is unique to each person, could it maybe entered into to each contact in OPAS?
            //If "Faktura", the total sum ("Beløp") is multiplied with 1,27792, otherwise multiplied with 1.
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_payment) . ($this->row), htmlspecialchars($aaddress['payment']));

            //"Fixed Amount" table for presence type:
            //Field:
            //Name 2
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_accitem_name2) . ($this->row), htmlspecialchars($aaddress['accitem_name2']));

            //"""Fixed Amount"" table for presence type:
            //Field:
            //Code"

            $this->sheet->setCellValue($this->getColumnLetter($this->cn_accitem_code) . ($this->row), htmlspecialchars($aaddress['accitem_code']));

            //"""Fixed Amount"" table for presence type:
            //Field:
            //Account
            //Year salary for calculating.

            $this->sheet->setCellValue($this->getColumnLetter($this->cn_amount) . ($this->row), htmlspecialchars($aaddress['amount']));

            //=WENN(C8="Faktura";1,27792;1)*(1/17*F8*J8+1/22*F8*K8+1/13*F8*L8)/12
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_belop) . ($this->row),
                '=IF('.$this->getColumnLetter($this->cn_payment) . ($this->row).'="Faktura",1.27792,1)*'.
                '('.
                    '1/17*'.$this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_short) . ($this->row).'+'.
                    '1/22*'.$this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_long) . ($this->row).'+'.
                    '1/13*'.$this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_2duties) . ($this->row).
                ')/12'
            );
            //=J8*162,5/17+K8*162,5/22+L8*162,5/13
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_timer) . ($this->row),
                '='.
                $this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_short) . ($this->row).'*162.5/17+'.
                $this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_long) . ($this->row).'*162.5/22+'.
                $this->getColumnLetter($this->cn_amount) . ($this->row).'*'.$this->getColumnLetter($this->cn_2duties) . ($this->row).'*162.5/13'
            );

            $dutytypes = implode(', ', array_unique($aaddress['adutytypes']));
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_dutytype) . ($this->row), htmlspecialchars($dutytypes));
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_short) . ($this->row), htmlspecialchars($aaddress['short']));
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_long) . ($this->row), htmlspecialchars($aaddress['long']));
            $this->sheet->setCellValue($this->getColumnLetter($this->cn_2duties) . ($this->row), htmlspecialchars($aaddress['2duties']));
        }


        $table = new Table();
        $table->setName('Vikarer');
        $table->setShowTotalsRow(false);
        $table->setRange($this->getColumnLetter(1).($this->rn_first).':'.$this->getColumnLetter($this->cn_2duties).($this->row)); // Using $j so that we get +1 for the totals row


// Optional: apply some styling to the table
        $tableStyle = new TableStyle();
        $tableStyle->setTheme(TableStyle::TABLE_STYLE_LIGHT4);
        $tableStyle->setShowRowStripes(true);
        $table->setShowTotalsRow(false);

        $table->setStyle($tableStyle);

        $this->sheet->addTable($table);



    }


    function show_Tillegg() {
        //einfach übernehmen

        //$this->row = $this->row+10;



        for($i=1; $i<=11; $i++) {
            $this->row++;

            if($i>=5 && $i<8) {

                /*
                $this->sheet->setCellValue($this->getColumnLetter(4) . ($this->row),
                    '='.
                        '$B$2*XLOOKUP([@[Type tillegg]],Tilleggstabell[Type tillegg],Tilleggstabell[Prøve])+$B$3*XLOOKUP([@[Type tillegg]],Tilleggstabell[Type tillegg],Tilleggstabell[Konsert])+(XLOOKUP([@[Type tillegg]],Tilleggstabell[Type tillegg],Tilleggstabell[Produksjon]))'
                );
                */
            }

        }

        //$this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), htmlspecialchars($aaddress['name1']));

        //=$B$2*XVERWEIS([@[Type tillegg]];Tilleggstabell[Type tillegg];Tilleggstabell[Prøve])+$B$3*XVERWEIS([@[Type tillegg]];Tilleggstabell[Type tillegg];Tilleggstabell[Konsert])+(XVERWEIS([@[Type tillegg]];Tilleggstabell[Type tillegg];Tilleggstabell[Produksjon]))
    }
    function show_Permisjon_uten_lonn() {
        //Table with list of persons with following presence types
        //P2

        //$this->row = $this->rn_first_P;



        $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), htmlspecialchars('Permisjon uten lønn'));
        $this->sheet->getStyle($this->getColumnLetter(1).$this->row)->getFont()->setSize(16);
        $this->row++;
        $this->row++;
        $this->rn_first = $this->row;

        $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), htmlspecialchars('Etternavn'));
        $this->sheet->setCellValue($this->getColumnLetter(2) . ($this->row), htmlspecialchars('Fornavn'));
        $this->sheet->setCellValue($this->getColumnLetter(3) . ($this->row), htmlspecialchars('Tidsperiode'));

        foreach($this->aproject['aduties_p'] as $aaddress) {
            $this->row++;
            $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), htmlspecialchars($aaddress['name1']));
            $this->sheet->setCellValue($this->getColumnLetter(2) . ($this->row), htmlspecialchars($aaddress['name2']));
        }

        $table = new Table();
        $table->setName('Tabell71');
        $table->setShowTotalsRow(false);
        $table->setRange($this->getColumnLetter(1).($this->rn_first).':'.$this->getColumnLetter(3).($this->row)); // Using $j so that we get +1 for the totals row


// Optional: apply some styling to the table
        $tableStyle = new TableStyle();
        $tableStyle->setTheme(TableStyle::TABLE_STYLE_LIGHT4);
        $tableStyle->setShowRowStripes(true);
        $table->setShowTotalsRow(false);

        $table->setStyle($tableStyle);

        $this->sheet->addTable($table);
    }


    function usort_artists($a, $b) {
        $frm_order_a = $a['lastname'].'#'.$a['firstname'];
        $frm_order_b = $b['lastname'].'#'.$b['firstname'];

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

    }
}

/*

    function write_test() {

        $this->ospreadsheet->setActiveSheetIndex(0);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $spreadsheet = $this->ospreadsheet;

        $spreadsheet->setActiveSheetIndex(0);
        $spreadsheet->getActiveSheet()->setCellValue('A1', 'Year')
            ->setCellValue('B1', 'Quarter')
            ->setCellValue('C1', 'Country')
            ->setCellValue('D1', 'Sales');

        $dataArray = [
            ['2010', 'Q1', 'United States', 790],
            ['2010', 'Q2', 'United States', 730],
            ['2010', 'Q3', 'United States', 860],
            ['2010', 'Q4', 'United States', 850],
            ['2011', 'Q1', 'United States', 800],
            ['2011', 'Q2', 'United States', 700],
            ['2011', 'Q3', 'United States', 900],
            ['2011', 'Q4', 'United States', 950],
            ['2010', 'Q1', 'Belgium', 380],
            ['2010', 'Q2', 'Belgium', 390],
            ['2010', 'Q3', 'Belgium', 420],
            ['2010', 'Q4', 'Belgium', 460],
            ['2011', 'Q1', 'Belgium', 400],
            ['2011', 'Q2', 'Belgium', 350],
            ['2011', 'Q3', 'Belgium', 450],
            ['2011', 'Q4', 'Belgium', 500],
        ];

        $spreadsheet->getActiveSheet()->fromArray($dataArray, null, 'A2');

// Table


        $table = new Table();
        $table->setName('SalesData');
        $table->setShowTotalsRow(true);
        $table->setRange('A1:D18'); // +1 row for totalsRow

        $tableStyle = new TableStyle();
        $tableStyle->setTheme(TableStyle::TABLE_STYLE_MEDIUM11);
        $tableStyle->setShowRowStripes(true);
        //$table->setShowTotalsRow(true);

        $table->setStyle($tableStyle);


// Table column label not implemented yet,
        $table->getColumn('A')->setTotalsRowLabel('Total');

// So set the label directly to the cell
        $spreadsheet->getActiveSheet()->getCell('A18')->setValue('Total');


// Table column function not implemented yet,
        $table->getColumn('D')->setTotalsRowFunction('SUM');


// Add Table to Worksheet

        $spreadsheet->getActiveSheet()->addTable($table);

// So set the formula directly to the cell
        //$spreadsheet->getActiveSheet()->setCellValueExplicit('D18', '=SUBTOTAL(109,SalesData[Sales])', PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_FORMULA);
        //$sheet->setCellValue('A5', $formula2);

        $spreadsheet->getActiveSheet()->getCell('D18')->setValue('=SUM([Sales])');

    }


 * */
