<?php
set_time_limit(0);

//namespace App\Utility\Reports;

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use PhpOffice\PhpWord\SimpleType\Jc;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use App\Reports\Tools\InstrumentationCommon;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;

/**
20230824 ONCUST-2203
OFO Etikett Arkivmateriale
 */
class alibraries_etikett_arkivmateriale_oslo extends ReportWord
{
    public $paperSize = 'A4';
    public $marginLeft = 1.5;
    public $marginRight = 1.5;
    public $marginTop = 1.25;
    public $marginBottom = 1.27;
    public $headerHeight = 1.25;
    public $footerHeight = 1.25;

    public $alibraries = array();
    public $awork = null;
    public $alibrary = null;

    public $awhere = null;

    public $reporttools = null;

    public $styleFont = array();
    public $styleFont_bold = array('name' => 'F37 Beckett Demi');
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_8 = array('size' => 8);
    public $styleFont_11 = array('size' => 11);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_16 = array('size' => 16);
    public $styleFont_18 = array('size' => 18);
    public $styleFont_26 = array('size' => 26);
    public $styleFont_48 = array('size' => 48);

    public $valign_center = ['valign' => Jc::CENTER];
    public $align_center = ['align' => Jc::CENTER];
    public $align_right = ['align' => 'right'];

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    public $rowno = 0;

    //https://github.com/PHPOffice/PHPWord/blob/master/src/PhpWord/SimpleType/Border.php
    //thickThinSmallGap
    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black');
    public $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => 'black');
    public $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');

    public $cellColSpan2 = array('gridSpan' => 2);
    public $cellColSpan3 = array('gridSpan' => 3);
    public $cellColSpan4 = array('gridSpan' => 4);
    public $cellColSpan5 = array('gridSpan' => 5);
    public $cellColSpan7 = array('gridSpan' => 7);
    public $cellColSpan9 = array('gridSpan' => 9);

    public $section = null;

    public function collect(array $where = [])
    {
        $this->paperSize = Configure::read('opasReports.paperFormat', 'A4');

        // Get the POST data
        $postData = $this->getRequest()->getData();

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($postData['id']);

        // Collect the selected dates with all required data

        if (isset($postData['dataItemIds'])) {
            $where = ['id IN' => $postData['dataItemIds']];
        }

        $this->alibraries = $this->model
            ->find('all')
            ->select()
            ->contain([
                'Slibrarytypes',
                'Publisheraddresses',
                'AlibrScores'=> ['Slibrscoretypes', 'Publisheraddresses'],
                'AlibrBoweds',
                'Sworks' => ['Scomposers']
            ])
            ->where(['Alibraries.id IN' => $postData['dataItemIds']])
            ->order(['Scomposers.lastname' => 'ASC', 'Sworks.title1' => 'ASC']);

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(14);
        $this->phpWord->setDefaultFontName('F37 Beckett');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $count = 0;

        foreach($this->alibraries as $this->alibrary) {
            $count++;

            $this->section = $this->phpWord->addSection(
                array(
                    'paperSize' => $this->paperSize,
                    'headerHeight' => Converter::cmToTwip($this->headerHeight),
                    'footerHeight' => Converter::cmToTwip($this->footerHeight),
                    'marginLeft' => Converter::cmToTwip($this->marginLeft),
                    'marginRight' => Converter::cmToTwip($this->marginRight),
                    'marginTop' => Converter::cmToTwip($this->marginTop),
                    'marginBottom' => Converter::cmToTwip($this->marginBottom))
            );

            $sectionStyle = $this->section->getStyle();
            $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

            $this->showLibrary();
        }
    }

    function showLibrary()
    {
        $work_id = $this->alibrary->work_id;
        $title = '';
        $arrangement = '';
        $composer = '';

        $catalog = $this->alibrary->catalog;

        $update_info = ($this->alibrary->updatedate ? $this->alibrary->updatedate->format(Configure::read('Formats.date')) : '').' '.$this->alibrary->updateuser;

        if($this->alibrary->swork) {
            $title = ($this->alibrary->swork->title3 > '' ? $this->alibrary->swork->title3 : $this->alibrary->swork->title1);
            $arrangement = $this->alibrary->swork->arrangement;

            if($this->alibrary->swork->scomposer) {
                $composer = trim($this->alibrary->swork->scomposer->firstname. ' ' . $this->alibrary->swork->scomposer->lastname);
            }
        }

        $title3_arrangement = $title.($arrangement>'' ? ' ('.$arrangement.')' : '');

        $oinstrumentation = new instrumentation_ofo();
        $oinstrumentation->work_id = $work_id;
        $oinstrumentation->getInstrumentation();
        $instrumentation = $oinstrumentation->instrumentation;



        $this->acol_widths = array();

        //$this->acol_widths[] = Converter::cmToTwip(3.54);
        //$this->acol_widths[] = Converter::cmToTwip(6.78);

        $this->acol_widths[] = Converter::cmToTwip(4.99);
        $this->acol_widths[] = Converter::cmToTwip(12.76);
        $this->acol_widths[] = null;


        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        //width. Table width in Fiftieths of a Percent or Twentieths of a Point.
        //'unit' => TblWidth::TWIP
        $tableProperties = array(
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.07),
            'cellMarginBottom' => Converter::cmToTwip(0.07),
            'cellMarginLeft' => Converter::cmToTwip(0.19),
            'cellMarginRight' => Converter::cmToTwip(0.19)
        );

        $styleCell = array();
        $styleFont = array();

        $table = $this->section->addTable($tableProperties);

        //***************
        $table->addRow();

        $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowSpan));
        $cell->addImage(CUSTOMER_REP_DIR. 'alibraries_etikett_arkivmateriale_oslo.png', array('width'=>Converter::cmToPoint(3.5),'align'=>'left'));

        $cell = $table->addCell($this->acol_widths[1]);
        $this->addMemo($cell, htmlspecialchars($catalog), array_merge($styleFont, $this->styleFont_48, $this->styleFont_bold), $this->align_right);

        //***************
        $table->addRow();
        $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowContinue));

        $cell = $table->addCell($this->acol_widths[1]);
        //*** 20170223
        //*** Reversmateriale/Kopisett ist alibrary.l_permanentloan
        if($this->alibrary->l_permanentloan == 1) {
            $cell->addText(htmlspecialchars('Revers/kopisett'), array_merge($styleFont, $this->styleFont_18, $this->styleFont_bold), $this->align_right);
        }

        if($this->alibrary->l_stringmasters == 1) {
            $cell->addText(htmlspecialchars('Strykersett'), array_merge($styleFont, $this->styleFont_18, $this->styleFont_bold), $this->align_right);
        }

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, $this->cellColSpan2);
        $cell->addText(htmlspecialchars($composer), array_merge($styleFont, $this->styleFont_26, $this->styleFont_bold));

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, $this->cellColSpan2);
        $cell->addText(htmlspecialchars($title3_arrangement), array_merge($styleFont, $this->styleFont_26, $this->styleFont_bold));

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, array_merge($this->cellColSpan2, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Besetning:'), array_merge($styleFont, $this->styleFont_16));

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, $this->cellColSpan2);
        $cell->addText(htmlspecialchars($instrumentation), array_merge($styleFont, $this->styleFont_16));

        //***************
        $row = $table->addRow();
        $cell1 = $row->addCell(Converter::cmToTwip(2));
        $cell2 = $row->addCell(null);

        $publisher = ($this->alibrary->publisheraddress ? $this->alibrary->publisheraddress->name1 : '');

        if($publisher>'') {
            $cell1->addText(htmlspecialchars('Forlag'));
            $cell2->addText(htmlspecialchars($publisher));
        }

        // *** 20170213
        // *** Strykere should be the strings field from archive table (text-field)
        if($this->alibrary->strings > '') {
            $cell1->addText(htmlspecialchars('Strykere'));
            $cell2->addText(htmlspecialchars($this->alibrary->strings));
        }

        foreach ($this->alibrary->alibr_boweds as $alibr_bowed) {
            if($alibr_bowed->text>'') {
                $cell1->addText(htmlspecialchars('Strøk'));
                $cell2->addText(htmlspecialchars($alibr_bowed->text));
            }

        }

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, array_merge($this->cellColSpan2, $this->styleCell_borderTop));

        //***************

        $count_scores = 0;
        foreach($this->alibrary->alibr_scores as $alibr_score) {
            $count_scores ++;
        }
        if($count_scores>0) {
            $table->addRow();
            $cell = $table->addCell($w_table, $this->cellColSpan2);
            $cell->addText(htmlspecialchars('Partiturer:'), $this->styleFont_bold);

            $table->addRow();
            $cell = $table->addCell($w_table, $this->cellColSpan2);
            $this->showScores($cell);
        }

        if(!empty($this->alibrary->notes)) {
            $table->addRow();
            $cell = $table->addCell($w_table, $this->cellColSpan2);
            $this->addMemo($cell, htmlspecialchars($this->alibrary->notes), array_merge($styleFont, $this->styleFont_14, $this->styleFont_italic));
        }




        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, $this->cellColSpan2);

        //***************
        $table->addRow();
        $cell = $table->addCell($w_table, $this->cellColSpan2);
        $this->addMemo($cell, htmlspecialchars($update_info), array_merge($styleFont, $this->styleFont_11, $this->styleFont_italic));

    }

    function showScores($cell) {
        $styleTabs = array(
            'indentation' => array(
                'left' => Converter::cmToTwip(10),
                'hanging' => Converter::cmToTwip(10)
            ),
            'tabs' => array(
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(7)),
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(10))
            )
        );

        foreach ($this->alibrary->alibr_scores as $alibr_score) {

            $publisher = ($alibr_score->publisheraddress ? $alibr_score->publisheraddress->name1 : '');
            //*** 20170213
            //*** Instead of "Full (Normal)" (=score type name) Use the A/B/C/PR codes (=Score type code)
            //*** Notes on score should show here too (total 3 column)

            $score = $publisher . "\t" . ($alibr_score->slibrscoretype ? $alibr_score->slibrscoretype->code : '') . "\t" . $alibr_score->notes;

            $this->addMemo($cell, $score, array_merge($this->styleFont_16, $this->styleFont_italic), $styleTabs);
        }
    }

    public function addMemo($section, $text, $styleFont = array(), $stylePar = array()) {
        $textlines = explode("\n", $text);
        foreach($textlines as $line) {
            $section->addText(htmlspecialchars($line),  $styleFont, $stylePar);
        }
    }

    public function getLongName($firstname = '', $tname5 = '', $lastname = '')
    {

        $config = Configure::read('CustomerSettings.NameString');
        $string = $config['string'];
        $separator = $config['separator'];

        $title = '';
        //$title = isset($address->stitle->name) ? "{$address->stitle->name} " : '';
        //$lastname = $address->name1;
        //$firstname = !empty($address->name2) ? $address->name2 : '';
        $separator = !empty($firstname) ? $separator : '';

        if (!empty($title) && empty($firstname)) {
            $string = '{title}{lastname}';
        }

        return str_replace(
            ['{title}', '{firstname}', '{lastname}', '{separator}'],
            [$title, $firstname, $lastname, $separator],
            $string
        );
    }
}
