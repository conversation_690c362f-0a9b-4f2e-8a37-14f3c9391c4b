<?php
/*
20240126 ONCUST-2876

OF Verkstatistikk
*/

//namespace App\Utility\Reports;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Date;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use App\Utility\FactoryHelper;
use \PhpOffice\PhpSpreadsheet\Style\Alignment;

use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Table;
use PhpOffice\PhpSpreadsheet\Worksheet\Table\TableStyle;

use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;

class sworks_verkstatistikk_ofo extends ReportSpreadsheet
{

    public $sheet = null;

    public $postData = null;
    //public $reporttools = null;

    public $nCN_Composer = 1;
    public $nCN_Title = 2;
    public $nCN_Arrangement = 3;
    public $nCN_Duration = 4;
    public $nCN_CompYear_From = 5;
    public $nCN_CompYear_To = 6;

    //20240221
    //Antall framføringer
    public $nCN_pds_no = 7;

    public $nCN_CompSex_F = 8;
    public $nCN_CompSex_M = 9;
    public $nCN_Norw = 10;
    public $nCN_50 = 11;
    public $nCN_50_nonNorw = 12;
    public $nCN_50_Norw = 13;
    public $nCN_WP = 14;
    public $nCN_Genre = 15;
    public $nCN_Perf = 16;
    public $nCN_Sum = 17;

    public $nMaxColNum = 17;

    public $borders_thin_all = array();
    public $borders_thin_tblr = array();
    public $borders_thin_lr = array();
    public $borders_thin_top = array();
    public $borders_thin_bottom = array();
    public $borders_thin_left = array();
    public $borders_thin_right = array();
    public $borders_thin_tb = array();

    public $borders_medium_top = array();
    public $borders_medium_bottom = array();
    public $borders_medium_top_bottom = array();




    public $borders_medium_r = array();
    public $borders_medium_outside = array();
    public $borders_medium_all = array();
    public $borders_dashed_inside = array();

    public $borders_none_right = array();
    public $borders_none_left = array();

    public $template_start = '';
    public $template_end  = '';

    public $templates = [
        [
            'name' => 'sworks_verkstatistikk_ofo_template',
            'file' => 'sworks_verkstatistikk_ofo_template.php',
            'jsFile' => 'sworks_verkstatistikk_ofo_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //$this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $this->template_start =  Date::parse($this->postData['formData']['template_start']);
        if(empty($this->template_start)) {
            $this->template_start = time();
        }

        $this->template_end = Date::parse($this->postData['formData']['template_end']);
        if(empty($this->template_end)) {
            $this->template_end = time();
        }

        $this->prepare_where();

        $adate_works_selected = TableRegistry::getTableLocator()->get('AdateWorks')
            ->find('all')
            ->select()
            ->contain([
                'Sworkpremieres',
                'Sworks' => ['Sworkgenres', 'Scomposers' => ['ScomposerNationalities'=>['Scountries']]],
                'Adates' => [
                    'Seventtypes' => ['Seventtypegroups'],
                    'Locationaddresses' => ['Scountries'],
                    'Conductoraddresses'
                ]
            ])
            ->where($this->awhere)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC', 'AdateWorks.work_order' => 'ASC'])
            ->all();

        $this->aworks = array();

        $this->date_min = null;
        $this->date_max = null;

        $count = 0;

        foreach($adate_works_selected as $adate_work) {

            if($adate_work->swork->l_intermission == 1) {
                continue;
            }

            $count++;
            if($count==1) {
                $this->date_min = $adate_work->adate->date_;
            }

            $this->date_max = $adate_work->adate->date_;
            $work_id = $adate_work->work_id;

            $l_performance = 0;
            if ($adate_work->adate && $adate_work->adate->seventtype) {
                $l_performance = max($l_performance, $adate_work->adate->seventtype->l_performance);
                $l_performance = $adate_work->adate->seventtype->l_performance;
            }

            if (!array_key_exists($work_id, $this->aworks)) {
                $composer = $adate_work->swork->scomposer->lastname . ($adate_work->swork->scomposer->firstname > '' ? ', ' : '') . $adate_work->swork->scomposer->firstname;
                $title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);

                //Add arranger field after title.

                $title .= ($adate_work->arrangement>'' ?  ' ('.$adate_work->arrangement.')' : '');
                $this->aworks[$work_id] = array(
                    'title' => $title,
                    'composer' => $composer,
                    'arrangement' => $adate_work->arrangement,
                    'l_performance' => 0,
                    'swork' => $adate_work->swork,
                    'l_wp' => false,
                    'apds' => array()
                );
            }

            if($adate_work->sworkpremiere && $adate_work->sworkpremiere->code=='WP') {
                $this->aworks[$work_id]['l_wp'] = true;
            }
            if($l_performance<>0) {
                $this->aworks[$work_id]['l_performance'] = max($l_performance, $this->aworks[$work_id]['l_performance']);
                $this->aworks[$work_id]['apds'][$adate_work->date_id] = $adate_work->date_id;
            }
        }

        //20240122 ONCUST-2845
        //List should be alphabetical on composers, then work titles.
        uasort($this->aworks, array($this,"usort_aworks"));

        return $this;
    }

    public function prepare_where() {

        $this->awhere = ['Adates.date_ >=' => $this->template_start, 'Adates.date_ <=' => $this->template_end];

    }

    function usort_aworks($a, $b) {
        $frm_order_a = $a['composer'].'#'.$a['title'];
        $frm_order_b = $b['composer'].'#'.$b['title'];

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }


    public function write_sheets($view = null, $layout = null)
    {
        $this->ospreadsheet->setActiveSheetIndex(0);

        $sheetname = 'Verkoversikt';

        $sheetname = str_replace('/', '_', $sheetname);
        $sheetname = str_replace('\\', '_', $sheetname);
        $sheetname = str_replace('?', '_', $sheetname);
        $sheetname = str_replace('+', '_', $sheetname);
        $sheetname = str_replace('#', '_', $sheetname);
        $sheetname = str_replace('*', '_', $sheetname);
        $sheetname = str_replace(']', '_', $sheetname);
        $sheetname = str_replace('[', '_', $sheetname);

        $sheetname = substr($sheetname, 0, 31);

        $this->ospreadsheet->setActiveSheetIndex(0);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');
        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setWrapText(true);

        $this->sheet->setTitle($sheetname);

        $this->write_sheet();
    }
    function write_sheet1() {
        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('F37 Beckett Demi');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(11);

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(1.9/2.5);
        $this->sheet->getPageMargins()->setRight(1.8/2.5);
        $this->sheet->getPageMargins()->setLeft(1.8/2.5);
        $this->sheet->getPageMargins()->setBottom(1.9/2.5);
        $this->sheet->getPageMargins()->setHeader(0.8/2.5);
        $this->sheet->getPageMargins()->setFooter(0.7/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        $this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,3);

        $last_col = 10;
        for($j = 1; $j<=15; $j++) {
            for($i=1;$i<=$last_col;$i++) {
                $this->sheet->setCellValue($this->getColumnLetter($i).($j), htmlspecialchars($this->getColumnLetter($i).($j)));
            }
        }


        $this->sheet->setCellValue('N1', $j);

        #-----------------------------------------------------------------
    # Create Table
#-----------------------------------------------------------------
        $table = new Table();
        $table->setName('TransactionData');
        $table->setShowTotalsRow(true);
        $table->setRange('A1:'.$this->getColumnLetter($last_col).($j)); // Using $j so that we get +1 for the totals row


        $this->sheet->addTable($table);

#-----------------------------------------------------------------
# Add Totals
#-----------------------------------------------------------------

        $add_totals_row = true; // If set to false then no errors generated when opening xslx

        if ($add_totals_row) {
            $this->sheet->getCell('A'.$j)->setValue('TOTALS');

            for($i=2;$i<=$last_col;$i++) {
                $table->getColumn($this->getColumnLetter($i))->setTotalsRowFunction('sum');
                $this->sheet->setCellValue($this->getColumnLetter($i).($j), '=SUBTOTAL(109,TransactionData['.$this->getColumnLetter($i).(1).'])');
                $this->sheet->setCellValue($this->getColumnLetter($i).($j+3), 'SUBTOTAL(109,TransactionData['.$this->getColumnLetter($i).(1).'])');
            }


        }
    }

    function write_sheet() {
        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('F37 Beckett Demi');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(11);

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(1.9/2.5);
        $this->sheet->getPageMargins()->setRight(1.8/2.5);
        $this->sheet->getPageMargins()->setLeft(1.8/2.5);
        $this->sheet->getPageMargins()->setBottom(1.9/2.5);
        $this->sheet->getPageMargins()->setHeader(0.8/2.5);
        $this->sheet->getPageMargins()->setFooter(0.7/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        $this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,3);

        //$this->sheet->getHeaderFooter()->setOddHeader('&C'.$this->report->title);
        //$this->sheet->getHeaderFooter()->setOddFooter('&R&30&D / &P');

        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Composer))->setWidth(33.43+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Title))->setWidth(38.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Arrangement))->setWidth(24.43+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Duration))->setWidth(11.86+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_CompYear_From))->setWidth(7.29+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_CompYear_To))->setWidth(14.29+0.71);
        //20240221
        //Antall framføringer
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_pds_no))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_CompSex_F))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_CompSex_M))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Norw))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_50))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_50_nonNorw))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_50_Norw))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_WP))->setWidth(4.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Genre))->setWidth(9.14+0.71);
        //if works are performances (do distinguish from recordings and workshops etc)
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Perf))->setWidth(4.71+0.71);
        //Just a summation column for extra info. Summation at the bottom
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Sum))->setWidth(4.71+0.71);

        $this->row++;
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('OF'));
        $this->sheet->getStyle($this->getColumnLetter(1).($this->row))->getFont()->setSize(16);

        $this->row++;
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('Verkoversikt'));
        $this->sheet->getStyle($this->getColumnLetter(1).($this->row))->getFont()->setSize(14);

        $this->row++;
        $from_to = $this->template_start->format('d.m.Y') . ' - '.$this->template_end->format('d.m.Y');
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars($from_to));
        $this->sheet->getStyle($this->getColumnLetter(1).($this->row))->getFont()->setSize(14);

        $this->row++;
        $this->row++;
        $this->row++;

        $row_start = $this->row;
        $this->sheet->getRowDimension($this->row)->setRowHeight(122.25);

        /*$this->sheet
            ->getStyle($this->getColumnLetter($this->nCN_Composer).($this->row).':'.$this->getColumnLetter($this->nMaxColNum).($this->row))
            ->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('70AD47');
*/
        $acolumns = [];
        $acolumns[$this->nCN_Composer] = array('caption'=>'Fullt navn');
        $acolumns[$this->nCN_Title] = array('caption'=>'Programtittel');
        $acolumns[$this->nCN_Arrangement] = array('caption'=>'Arrangement');
        $acolumns[$this->nCN_Duration] = array('caption'=>'Varighet');
        $acolumns[$this->nCN_CompYear_From] = array('caption'=>'Komponert');
        $acolumns[$this->nCN_CompYear_To] = array('caption'=>'Kommentar');
        //20240221
        //Antall framføringer
        $acolumns[$this->nCN_pds_no] = array('caption'=>'Antall framføringer');
        $acolumns[$this->nCN_CompSex_F] = array('caption'=>'Kvinne');
        $acolumns[$this->nCN_CompSex_M] = array('caption'=>'Mann');
        $acolumns[$this->nCN_Norw] = array('caption'=>'Norsk verk');
        //20240221 ONCUST-2876
        //$acolumns[$this->nCN_50] = array('caption'=>"Samtid 1972\neller senere");
        $acolumns[$this->nCN_50] = array('caption'=>"Samtid\n< 50 år");

        //$acolumns[$this->nCN_50] = array('caption'=>"Samtid 1972 eller senere");
        $acolumns[$this->nCN_50_nonNorw] = array('caption'=>'Samtid Utenlandsk');
        $acolumns[$this->nCN_50_Norw] = array('caption'=>'Samtid Norsk');
        $acolumns[$this->nCN_WP] = array('caption'=>'Urframføringer');
        $acolumns[$this->nCN_Genre] = array('caption'=>"Orkester/\nKammermusill");
        //$acolumns[$this->nCN_Genre] = array('caption'=>"Orkester Kammermusill");
        $acolumns[$this->nCN_Perf] = array('caption'=>'Framføringer konsert');
        $acolumns[$this->nCN_Sum] = array('caption'=>'Annet');

        foreach($acolumns as $cn=>$acolumn) {
            $this->sheet->setCellValue($this->getColumnLetter($cn).($this->row), $acolumn['caption']);
        }

        for($i=$this->nCN_Composer; $i<=$this->nCN_CompYear_To; $i++) {
            $this->sheet->getStyle($this->getColumnLetter($i).($this->row))->getFont()->setSize(16);
        }

        for($i=$this->nCN_pds_no; $i<=$this->nMaxColNum; $i++) {
            $this->sheet->getStyle($this->getColumnLetter($i).($this->row))->getFont()->setSize(12);
            $this->sheet->getStyle($this->getColumnLetter($i).($this->row))->getAlignment()
                ->setTextRotation(90)
                ->setHorizontal('bottom')
                ->setVertical('center');
            $this->sheet->getStyle($this->getColumnLetter($i))->getAlignment()->setHorizontal('center');
        }

        foreach($this->aworks as $awork) {

            $this->row++;

            $swork = $awork['swork'];

            $workgenre_code = ($swork->sworkgenre ? $swork->sworkgenre->code : '');

            $sex = '';
            $l_norsk = false;


            //If composed during the last 50 years (today it is 1974 or later)
            $year_today = date('Y');
            $l_50 = ($year_today- (int)$swork->compyear)<50;
            if($swork->scomposer) {
                $scomposer = $swork->scomposer;
                $sex = $scomposer->sex;

                foreach($swork->scomposer->scomposer_nationalities as $scomposer_nationality) {
                    if($scomposer_nationality->scountry && $scomposer_nationality->scountry->code == 'NO') {
                        $l_norsk = true;
                    }
                }
            }


            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer).($this->row), htmlspecialchars($awork['composer']));
            //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer).($this->row), 1);
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title).($this->row), str_replace('&quot;', '"', htmlspecialchars($awork['title'])));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Arrangement).($this->row), htmlspecialchars($awork['arrangement']));

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Duration).($this->row), htmlspecialchars($swork->duration));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_CompYear_From).($this->row), htmlspecialchars($swork->compyear));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_CompYear_To).($this->row), htmlspecialchars($swork->compyear2));
            //20240221
            //Antall framføringer
            $pds_no = sizeof(array_unique($awork['apds']));

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_pds_no).($this->row), $pds_no);
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_CompSex_F).($this->row), htmlspecialchars(($sex == 'F' ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_CompSex_M).($this->row), htmlspecialchars(($sex == 'M' ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Norw).($this->row), htmlspecialchars(($l_norsk ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_50).($this->row), htmlspecialchars(($l_50 ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_50_nonNorw).($this->row), htmlspecialchars(($l_50 && !$l_norsk ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_50_Norw).($this->row), htmlspecialchars(($l_50 && $l_norsk ? 'x' : '')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_WP).($this->row), htmlspecialchars(($awork['l_wp'] ? 'x' :'')));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Genre).($this->row), htmlspecialchars($workgenre_code));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Perf).($this->row), htmlspecialchars(($awork['l_performance']<>0 ? 'x' : '')));
            //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_Sum).($this->row), htmlspecialchars(''));
        }

        // Create the table
        //$this->row++;

        $this->row++;

        $table = new Table();
        $table->setName('Verkstatistikk');
        $table->setShowTotalsRow(true);
        $table->setRange($this->getColumnLetter($this->nCN_Composer).($row_start).':'.$this->getColumnLetter($this->nMaxColNum).($this->row)); // Using $j so that we get +1 for the totals row

        //$this->sheet->setCellValue('T'.$this->row, $this->row);

// Optional: apply some styling to the table
        $tableStyle = new TableStyle();
        $tableStyle->setTheme(TableStyle::TABLE_STYLE_MEDIUM11);
        $tableStyle->setShowRowStripes(true);
        $table->setShowTotalsRow(true);

        $table->setStyle($tableStyle);

        $this->sheet->addTable($table);

/*        $this->sheet->getCell('A'.$j)->setValue('TOTALS');

        $range = $this->getColumnLetter(1) . ($row_start + 1) . ':' . $this->getColumnLetter(1) . ($this->row - 1);
        $j = $this->row;
        $last_col=2;
        for($i=2;$i<=$last_col;$i++) {
            $table->getColumn($this->getColumnLetter($i))->setTotalsRowFunction('sum');
            $this->sheet->setCellValue($this->getColumnLetter($i).($j), '=SUBTOTAL(109,Verkstatistikk[Programtittel])');

        }
$table->getColumn($this->getColumnLetter(1))->setTotalsRowFunction('count');
        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), '=SUBTOTAL(109,[Fullt navn])');

        $table->getColumn($this->getColumnLetter(2))->setTotalsRowFunction('sum');
        $this->sheet->setCellValue($this->getColumnLetter(2).($this->row), '=SUBTOTAL(109,Verkstatistikk[Programtittel])');
//        $table->getColumn($this->getColumnLetter(1))->setTotalsRowFunction('sum');
//        $this->sheet->setCellValue($this->getColumnLetter(1).$this->row, '=SUBTOTAL(103,Verkstatistikk[Fullt navn])');

        //return;

*/

        foreach($acolumns as $cn=>$acolumn) {
            //$cn = $acolumn['cn'];

            if ($cn == $this->nCN_Genre) {
                //=VERKETTEN(ZÄHLENWENN([Orkester/
                //Kammermusikk];"ORK");" / ";ZÄHLENWENN([Orkester/
                //Kammermusikk];"KAM"))

                $table->getColumn($this->getColumnLetter($cn))->setTotalsRowFunction('custom');
                $this->sheet->setCellValue($this->getColumnLetter($cn) . $this->row,
                    '='.
                    'CONCAT('.
                    'COUNTIF(Verkstatistikk['.$acolumn['caption'].'], "ORK")'.
                    ',"/",'.
                    'COUNTIF(Verkstatistikk['.$acolumn['caption'].'], "KAM")'.
                    ')'
                );
            } else {
                if ($cn == $this->nCN_Composer or $cn>= $this->nCN_CompSex_F) {
                    $table->getColumn($this->getColumnLetter($cn))->setTotalsRowFunction('count');
                    $this->sheet->setCellValue($this->getColumnLetter($cn).($this->row), '=SUBTOTAL(103,Verkstatistikk['.$acolumn['caption'].'])');
                }
            }

        }
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_tb = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_medium_top_bottom = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];

        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

    }
}
