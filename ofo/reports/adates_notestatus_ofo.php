<?php
/*
20240503 ONCUST-3312
OF Notestatus grunnlag
*/

//namespace App\Utility\Reports;

use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Date;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use App\Utility\FactoryHelper;
use \PhpOffice\PhpSpreadsheet\Style\Alignment;

use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Table;
use PhpOffice\PhpSpreadsheet\Worksheet\Table\TableStyle;

use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;

class adates_notestatus_ofo extends ReportSpreadsheet
{

    public $sheet = null;

    public $postData = null;
    //public $reporttools = null;

    private $aworks = array();
    private $awork = array();
    private $alibrary = null;

    public $nCN_Project = 1;
    public $nCN_Composer_lastname = 2;
    public $nCN_Composer_firstname = 3;
    public $nCN_Title_Arrangement = 4;
    public $nCN_Publisher = 5;
    public $nCN_Catalog = 6;
    public $nCN_Type = 7;
    public $nCN_strings = 8;
    public $nCN_permanentloan = 9;
    public $nCN_parts = 10;
    public $nCN_notes = 11;
    public $nCN_details = 12;

    public $nMaxColNum = 12;

    public $borders_thin_all = array();
    public $borders_thin_tblr = array();
    public $borders_thin_lr = array();
    public $borders_thin_top = array();
    public $borders_thin_bottom = array();
    public $borders_thin_left = array();
    public $borders_thin_right = array();
    public $borders_thin_tb = array();

    public $borders_medium_top = array();
    public $borders_medium_bottom = array();
    public $borders_medium_top_bottom = array();




    public $borders_medium_r = array();
    public $borders_medium_outside = array();
    public $borders_medium_all = array();
    public $borders_dashed_inside = array();

    public $borders_none_right = array();
    public $borders_none_left = array();

    function initialize()
    {
        parent::initialize();

        //$this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $awhere = ['AdateWorks.date_id IN' => $this->postData['dataItemIds']];

        //20240826 ONCUST-3312
        //Und kann der Bericht nach der ersten Spalte also den Projekten sortiert rauskommen?
        $adate_works_selected = TableRegistry::getTableLocator()->get('AdateWorks')
            ->find('all')
            ->select()
            ->contain([
                'Sworkpremieres',
                'Sworks' => ['Sworkgenres', 'Scomposers' => ['ScomposerNationalities'=>['Scountries']]],
                'Adates' => [
                    'Sprojects',
                    'Seventtypes' => ['Seventtypegroups'],
                    'Locationaddresses' => ['Scountries'],
                    'Conductoraddresses'
                ]
            ])
            ->where($awhere)
            ->order(['Sprojects.name' => 'ASC', 'Scomposers.lastname' => 'ASC', 'Sworks.title1' => 'ASC', 'Adates.date_' => 'ASC', 'Adates.start_' => 'ASC', 'AdateWorks.work_order' => 'ASC'])
            ->all();

        $this->aworks = array();

        $this->date_min = null;
        $this->date_max = null;

        foreach($adate_works_selected as $adate_work) {

            if($adate_work->swork->l_intermission == 1) {
                continue;
            }

            $work_id = $adate_work->work_id;
            $adate = $adate_work->adate;

            $l_performance = 0;
            if ($adate && $adate->seventtype) {
                $l_performance = $adate->seventtype->l_performance;
            }

            if (!array_key_exists($work_id, $this->aworks)) {
                $composer = $adate_work->swork->scomposer->lastname . ($adate_work->swork->scomposer->firstname > '' ? ', ' : '') . $adate_work->swork->scomposer->firstname;
                //20240620 ONCUST-3312
                //The report looks excellent except one thing: We’d like to have Title 2 + arranger  in the title field. Today it shows Title 1 + arranger.
                $title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);
                //$title = $adate_work->swork->title1;

                //Add arranger field after title.

                //$title .= ($adate_work->arrangement>'' ?  ' ('.$adate_work->arrangement.')' : '');
                $this->aworks[$work_id] = array(
                    'title' => $title,
                    'lastname' => ($adate_work->swork->scomposer ? $adate_work->swork->scomposer->lastname : ''),
                    'firstname' => ($adate_work->swork->scomposer ? $adate_work->swork->scomposer->firstname : ''),
                    'arrangement' => $adate_work->arrangement,
                    'swork' => $adate_work->swork,
                    'aprojects' => array()

                );
            }
            if ($adate->project_id>0 && $adate->sproject && !array_key_exists($adate->project_id,  $this->aworks[$work_id]['aprojects'])) {
                $this->aworks[$work_id]['aprojects'][$adate->project_id] = $adate->sproject->name;
            }
        }

        //20240122 ONCUST-2845
        //List should be alphabetical on composers, then work titles.
        //uasort($this->aworks, array($this,"usort_aworks"));

        return $this;
    }

    function usort_aworks($a, $b) {
        $frm_order_a = $a['project'].'#'.$a['lastname'].'#'.$a['title'];
        $frm_order_b = $b['project'].'#'.$b['lastname'].'#'.$b['title'];

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }


    public function write_sheets($view = null, $layout = null)
    {
        $this->ospreadsheet->setActiveSheetIndex(0);

        $sheetname = 'OF Notestatus grunnlag';

        $sheetname = str_replace('/', '_', $sheetname);
        $sheetname = str_replace('\\', '_', $sheetname);
        $sheetname = str_replace('?', '_', $sheetname);
        $sheetname = str_replace('+', '_', $sheetname);
        $sheetname = str_replace('#', '_', $sheetname);
        $sheetname = str_replace('*', '_', $sheetname);
        $sheetname = str_replace(']', '_', $sheetname);
        $sheetname = str_replace('[', '_', $sheetname);

        $sheetname = substr($sheetname, 0, 31);

        $this->ospreadsheet->setActiveSheetIndex(0);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');
        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setWrapText(true);

        $this->sheet->setTitle($sheetname);

        $this->write_sheet();
    }

    function write_sheet() {
        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('Calibri');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(11);

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(1.9/2.5);
        $this->sheet->getPageMargins()->setRight(1.8/2.5);
        $this->sheet->getPageMargins()->setLeft(1.8/2.5);
        $this->sheet->getPageMargins()->setBottom(1.9/2.5);
        $this->sheet->getPageMargins()->setHeader(0.8/2.5);
        $this->sheet->getPageMargins()->setFooter(0.7/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        $this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,1);

        //$this->sheet->getHeaderFooter()->setOddHeader('&C'.$this->report->title);
        //$this->sheet->getHeaderFooter()->setOddFooter('&R&30&D / &P');

        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Project))->setWidth(17.71+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Composer_lastname))->setWidth(13.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Composer_firstname))->setWidth(13.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Title_Arrangement))->setWidth(30.14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Publisher))->setWidth(20.14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Catalog))->setWidth(8.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Type))->setWidth(14.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_strings))->setWidth(5.14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_permanentloan))->setWidth(5.14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_parts))->setWidth(5.14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_notes))->setWidth(25.57+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_details))->setWidth(40.14+0.71);

        $this->row = 1;
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars('Prosjekt'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer_lastname).($this->row), htmlspecialchars('Komponist'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer_firstname).($this->row), htmlspecialchars('Fornavn'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title_Arrangement).($this->row), htmlspecialchars('Tittel (arrangør)'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Publisher).($this->row), htmlspecialchars('Forlag'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Catalog).($this->row), htmlspecialchars('Arkivnr.'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Type).($this->row), htmlspecialchars('Type'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_strings).($this->row), htmlspecialchars('Stryk'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_permanentloan).($this->row), htmlspecialchars('P Lån/Kopi'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_parts).($this->row), htmlspecialchars('I Bruk'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_notes).($this->row), htmlspecialchars('Notater (OPAS)'));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_details).($this->row), htmlspecialchars('Detaljer Instrumentasjon'));

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Project) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getAlignment()->setVertical('bottom');
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Project) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))->getFont()->setBold(true);
        $this->sheet->getRowDimension($this->row)->setRowHeight(45);
        $this->sheet
            ->getStyle($this->getColumnLetter($this->nCN_Project) . ($this->row).":".$this->getColumnLetter($this->nMaxColNum) . ($this->row))
            ->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('F2F2F2');


        foreach($this->aworks as $work_id=>$this->awork) {


            $Alibraries = TableRegistry::getTableLocator()->get('Alibraries');

            $alibraries = $Alibraries
                ->find('all')
                ->contain(
                    [
                        'Publisheraddresses',
                        'Slibrarytypes',
                        'AlibrScores',
                        'Spublications' => ['Publisheraddresses'],
                        'Owneraddresses'
                    ]
                )
                ->where(['Alibraries.work_id = ' => $work_id])
                ->orderAsc('Alibraries.catalog');

            $count_libraries = 0;
            foreach($alibraries as $this->alibrary) {
                $count_libraries++;
                $this->row++;
                $this->showWorkInfo();
                $this->showLibraryInfo();
            }

            //- Input line a line with work info only (Col. A->D) when there are no archive entry
            if($count_libraries==0) {
                $this->row++;
                $this->showWorkInfo();
            }
        }
        $this->sheet->getStyle($this->getColumnLetter(1) .(1) . ":" . $this->getColumnLetter($this->nMaxColNum) .$this->row)->applyFromArray(array_merge($this->borders_thin_all));
    }

    function showWorkInfo() {
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Project).($this->row), htmlspecialchars(implode("\n", $this->awork['aprojects'])));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer_lastname).($this->row), htmlspecialchars($this->awork['lastname']));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer_firstname).($this->row), htmlspecialchars($this->awork['firstname']));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title_Arrangement).($this->row), str_replace('&quot;', '"', str_replace('&amp;', '&', htmlspecialchars($this->awork['title'] . (!empty($this->awork['arrangement']) ? ' ('.$this->awork['arrangement'].')' : '')))));

    }

    function showLibraryInfo() {
        $librarytype = '';
        $publisher = '';

        if ($this->alibrary->slibrarytype) {
            $librarytype = $this->alibrary->slibrarytype->name;
        }

        if ($this->alibrary->publisheraddress) {
            $publisher = $this->alibrary->publisheraddress->name1 . ($this->alibrary->publisheraddress->name2 > '' ? ', ' : '') . $this->alibrary->publisheraddress->name2;
        }

        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Publisher).($this->row), str_replace('&amp;', '&', htmlspecialchars($publisher)));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Catalog).($this->row), htmlspecialchars($this->alibrary->catalog));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Type).($this->row), htmlspecialchars($librarytype));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_strings).($this->row), htmlspecialchars($this->alibrary->l_stringmasters));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_permanentloan).($this->row), htmlspecialchars($this->alibrary->l_permanentloan));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_parts).($this->row), htmlspecialchars($this->alibrary->l_parts));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_notes).($this->row), htmlspecialchars($this->alibrary->notes));
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_details).($this->row), htmlspecialchars($this->awork['swork']->details));


    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_tb = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_medium_top_bottom = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];

        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

    }
}
