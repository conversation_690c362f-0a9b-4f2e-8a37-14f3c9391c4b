<?php

use App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\Style\Tab;

use Customer\ofo\reports\ReportTools_client;

/*
*** - eine Datei, aber pro Musiker ein Vertrag, nacheinanderfolgend
*** - wird aus Diensteinteilung für den ausgewählten Zeitraum gestartet
*** - Abfrage mit Checkboxen Sections
*** - nicht adressgruppen sind relevant, sondern PresenceTypes Vikar und Tilleggsmusiker.
 * */

/**
 * Class aduties_korttidskontrakter_oslo
20230811 ONCUST-2200
 */

class aduties_korttidskontrakter_oslo extends ReportWord
{
    public $postData = null;
    public $caption = '';

    public $section = null;

    public $acontracts = array();
    public $acontract = array();

    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');

    public $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');

    public $styleFont_12 = array('size' => 12);

    private $cellColSpan3 = array('gridSpan' => 3);

    public $accountholder = '';
    public $accountno = '';
    public $bank = '';
    public $iban = '';
    public $swift = '';
    public $taxno = '';
    public $taxoffice = '';

    public $nationality = '';

    public $reporttools = null;

    public $artist_salutation = '';
    public $artist_name = '';

    protected $templates = [
        [
            'name' => 'aduties_korttidskontrakter_oslo_template',
            'file' => 'aduties_korttidskontrakter_oslo_template.php',
            'jsFile' => 'aduties_korttidskontrakter_oslo_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    /*********************************************/
    function initialize()
    {
        ini_set('pcre.backtrack_limit', *********);

        parent::initialize();

        $this->template_filename = CUSTOMER_REP_DIR . 'aduties_korttidskontrakter_oslo.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();
    }

    public function collect(array $where = [])
    {
        $this->reporttools = new ReportTools_client();

        $this->postData = $this->getRequest()->getData();
        //$this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $where = array_merge(['Aduties.id IN' => $this->postData['dataItemIds']]);

        //$this->syssection_id = (isset($this->postData['formData']['syssection_id']) ? $this->postData['formData']['syssection_id'] : 0);
        //print_r($this->postData['formData']); die;
        $where_syssection_ids = array();
        $where_syssection_ids[] = -1;

        foreach($this->postData['formData'] as $k=>$syssection_id) {

            $syssection_id = (int)$syssection_id;
            if(substr($k,0, strlen('syssection_')) == 'syssection_') {

                if($syssection_id>0) {
                    if($syssection_id>17) {
                        $syssection_id = 17;
                    }

                    $where_syssection_ids[] = $syssection_id;
                }
            }
        }
        //print_r($where_syssection_ids); die;


        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        //$where = array_merge(['Aduties.id IN' => $this->postData['dataItemIds']], ['Sdutytypes.l_present'=>1]);





        //Der Bericht berÃ¼cksichtigt nur Dienste mit l_present == 1;
        $this->aduties_selected = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses'],
                'Sdutytypes',
                'Aexpenses' => ['Sexpensetypes'],
                'Saddressfunctionitems',
                'Artistaddresses' => [
                    'Scountries',
                    'SaddressPersdata' => [
                        'Spayments',
                        'SaddresspersdataBanks' => function (Query $query) {
                            return $query
                                ->contain(['Sbanks'=>['Saddresses']])
                                ->where(['SaddresspersdataBanks.l_main' => 1]);
                        }
                    ]
                ],
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups'],
                'AdutyWorks' => ['AdateWorks' => ['Sworks' => ['Scomposers']]]
            ])
            ->where(
                [
                    'Aduties.id IN' => $this->postData['dataItemIds'],
                    'Sinstrsyssections.id IN' => $where_syssection_ids
                ])
            ->order(['Adates.date_', 'Adates.start_', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);


        //print_r($this->aduties_selected); die;
        $this->acontracts = array();

        foreach ($this->aduties_selected as $aduty) {
            $artist_id = $aduty->artist_id;
            $season_id = $aduty->adate->season_id;
            $project_id = $aduty->adate->project_id;
            $instrument = ($aduty->sinstrinstrument ? $aduty->sinstrinstrument->name : '');
            $project = ($aduty->adate->sproject ? $aduty->adate->sproject->code.' '.$aduty->adate->sproject->name2 : '');

            $dutytype_id = $aduty->dutytype_id;
            $dutytype = ($aduty->sdutytype ? $aduty->sdutytype->name : '');

            $syssection_id = ($aduty->sinstrinstrument && $aduty->sinstrinstrument->sinstrsection ?$aduty->sinstrinstrument->sinstrsection->syssection_id : 0);

            $k = $season_id . '#' . $project_id . '#' . $artist_id. '#' . $dutytype_id;

            if (!array_key_exists($k, $this->acontracts)) {

                $this->acontracts[$k] = array(
                    'artist_id' => $aduty->artist_id,
                    'season_id' => $season_id,
                    'season' => ($aduty->adate->sseason ? $aduty->adate->sseason->name : ''),
                    'syssection_id' => $syssection_id,
                    'project_id' => $project_id,
                    'dutytype_id' => $dutytype_id,
                    'dutytype' => $dutytype,
                    'instrument' => $instrument,
                    'artistaddress' => $aduty->artistaddress,
                    'project' => $project,
                    'ainstruments' => array(),
                    'aexpenses_sysbase_1' => array(),
                    'aexpenses_other' => array(),
                    'arehearsals' => array(),
                    'apds' => array(),
                    'fpd' => array(),
                    'adresses' => array(),
                    'mindate' => $aduty->adate->date_->format(Configure::read('Formats.date')),
                    'maxdate' => $aduty->adate->date_->format(Configure::read('Formats.date'))
                );
            }


            $this->acontracts[$k]['maxdate'] = $aduty->adate->date_->format(Configure::read('Formats.date'));

            if (!in_array($instrument, $this->acontracts[$k]['ainstruments'])) {
                $this->acontracts[$k]['ainstruments'][] = $instrument;
            }

            if($aduty->adate->seventtype && $aduty->adate->seventtype->l_performance==1) {
                if(empty($this->acontracts[$k]['fpd'])) {
                    $this->acontracts[$k]['fpd'] = $aduty->adate;
                }

                $this->acontracts[$k]['apds'][$aduty->date_id] = $aduty->adate;

                $dress = ($aduty->adate->sdress ? $aduty->adate->sdress->name : '');
                if($aduty->adate->dress_id>0 and !in_array($dress, $this->acontracts[$k]['adresses'])) {
                    $this->adresses[] = $dress;
                }
            } else {
                $this->acontracts[$k]['arehearsals'][$aduty->date_id] = $aduty->adate;
            }
        }

        return $this;
    }

    // Create the file and return the filename
    /*********************************************/
    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    /*********************************************/
    function fill_search_patterns()
    {
        $today = date(Configure::read('Formats.date'));

        //print_r($this->acontracts);


        $contracts_no = 0;
        foreach($this->acontracts as $this->acontract) {
            $contracts_no++;
        }

        $this->templateProcessor->cloneBlock('contracts', $contracts_no, true, true);

        $i=0;

        foreach($this->acontracts as $k => $this->acontract) {
            $i++;

            //$pagebreak = new PhpOffice\PhpWord\Element\TextRun();
            if($i<$contracts_no) {
                $pagebreak = '<w:br w:type="page"/>';

            } else {
                $pagebreak = '';
            }

            $this->artist_salutation = 'Herrn/Frau';
            $this->artist_name = '';
            $artist_street = '';
            $artist_zip = '';
            $artist_place = '';
            $zipplace = '';
            $country = '';

            if($this->acontract['artistaddress']) {
                $this->artist_name = trim($this->acontract['artistaddress']->name2 . ' ' . $this->acontract['artistaddress']->name1);
                $artist_street = $this->acontract['artistaddress']->street;
                $zipplace = trim($this->acontract['artistaddress']->zipcode.' '.$this->acontract['artistaddress']->place);
                $artist_zip = $this->acontract['artistaddress']->zipcode;
                $artist_place = $this->acontract['artistaddress']->place;

                if ($this->acontract['artistaddress']->scountry && !empty($this->acontract['artistaddress']->scountry->name)) {
                    $country = $this->acontract['artistaddress']->scountry->name;
                }
            }

            if($this->acontract['artistaddress']->saddress_persdata->sex == 'F') {
                $this->artist_salutation = 'Frau';
            }

            if($this->acontract['artistaddress']->saddress_persdata->sex == 'M') {
                $this->artist_salutation = 'Herrn';
            }

            $conductor = '';
            $soloists = '';
            if($this->acontract['fpd']) {
                if($this->acontract['fpd']->conductoraddress) {
                    $conductor = trim($this->acontract['fpd']->conductoraddress->name2 . ' ' . $this->acontract['fpd']->conductoraddress->name1);
                }

                $adatework_soloists = $this->reporttools->getSoloists($this->acontract['fpd']->id);

                foreach ( $adatework_soloists as $asoloist) {
                    $instrument = ($asoloist->sinstrinstrument ? $asoloist->sinstrinstrument->name : '');
                    $soloist = $this->reporttools->getLongName($asoloist->saddress->name2, '', $asoloist->saddress->name1);

                    //$soloist.= ($instrument>'' ? ', ' :'').$instrument;

                    $soloists .= ($soloists>'' ? ', ' : '') . $soloist;
                }
            }


            $instrument = implode(', ', $this->acontract['ainstruments']);
            $dress = implode(', ', $this->acontract['adresses']);
            $project = $this->acontract['sproject'];

            $personnelno = ($this->aaddress->saddress_persdata->personnelno ? $this->aaddress->saddress_persdata->personnelno : '');
            $artist_email = $this->reporttools->getNumbers_by_type($this->acontract['artist_id'], 'l_email', true);

            $accountholder = '';
            $accountno = '';
            $bank = '';
            $swift = '';
            $iban = '';

            if(isset($this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0])) {
                $accountholder = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->accountholder;
                $accountno = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->accountno;
                $iban = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->iban;
                $bank = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->sbank->bankname;
                $swift = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->sbank->swift;
            }

            //print_r($this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]);

            if(isset($this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress)) {
                $bank = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress->name1;
                $swift = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress->code;
            }

            $nationality = '';
            if(isset($this->acontract['artistaddress']->saddress_persdata->saddresspersdata_passports[0]->scountry)) {
                $nationality = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_passports[0]->scountry->nationality;
                if(empty($nationality)) {
                    $nationality = $this->acontract['artistaddress']->saddress_persdata->saddresspersdata_passports[0]->scountry->name;
                }
            }

            $this->templateProcessor->setValue('date#'.$i, $today);


            $this->templateProcessor->setValue('artist_name#'.$i, htmlspecialchars($this->artist_name));
            //$this->templateProcessor->setValue('artist_name#'.$i, htmlspecialchars($this->artist_name.'#'.$this->acontract['syssection_id']));

            $this->templateProcessor->setComplexBlock('artist_name1#'.$i, $this->getArtist_name(true));

            $this->templateProcessor->setValue('artist_street#'.$i, htmlspecialchars($artist_street));
            $this->templateProcessor->setValue('artist_zip#'.$i, htmlspecialchars($artist_zip));
            $this->templateProcessor->setValue('artist_place#'.$i, htmlspecialchars($artist_place));
            $this->templateProcessor->setValue('artist_country#'.$i, htmlspecialchars($country));
            $this->templateProcessor->setValue('artist_personnelno#'.$i, htmlspecialchars($personnelno));
            $this->templateProcessor->setValue('artist_email#'.$i, htmlspecialchars($artist_email));

            $address = $artist_street;
            $address .= ($zipplace>'' ? ', ' : '') . $zipplace;
            $address .= ($country>'' ? ', ' : '') . $country;

            //$this->templateProcessor->setComplexBlock('artist_address#'.$i, $this->getArtist_address());
            $this->templateProcessor->setValue('artist_address#'.$i, htmlspecialchars($address));
            $this->templateProcessor->setComplexBlock('artist_address1#'.$i, $this->getArtist_address(true));

            $this->templateProcessor->setValue('salutation#'.$i, htmlspecialchars($this->artist_salutation));


            //$this->templateProcessor->setValue('artist_name1#'.$i, htmlspecialchars($this->artist_name));

            $this->templateProcessor->setValue('nationality#'.$i, htmlspecialchars($nationality));

            $date10 = date('d.m.Y', strtotime('+1 day'));
            $this->templateProcessor->setValue('date10#'.$i, htmlspecialchars($date10));

            $this->templateProcessor->setComplexBlock('concerts#'.$i, $this->getConcerts());
            $this->templateProcessor->setComplexBlock('rehearsals#'.$i, $this->getRehearsals());
            $this->templateProcessor->setValue('conductor#'.$i, htmlspecialchars($conductor));
            $this->templateProcessor->setValue('soloists#'.$i, htmlspecialchars($soloists));

            $this->templateProcessor->setValue('project#'.$i, htmlspecialchars($project));
            $this->templateProcessor->setValue('instrument#'.$i, htmlspecialchars($instrument));
            $this->templateProcessor->setValue('dress#'.$i, htmlspecialchars($dress));

            $this->templateProcessor->setValue('accountholder#'.$i, htmlspecialchars($accountholder));
            $this->templateProcessor->setValue('bank#'.$i, htmlspecialchars($bank));
            $this->templateProcessor->setValue('swift#'.$i, htmlspecialchars($swift));
            $this->templateProcessor->setValue('iban#'.$i, htmlspecialchars($iban));

            $minmax = $this->acontract['mindate'].'-'.$this->acontract['maxdate'];
            $this->templateProcessor->setValue('minmax#'.$i, htmlspecialchars($minmax));

            $dutytype_instrument = $this->acontract['dutytype'].'-'.$instrument;
            $this->templateProcessor->setValue('dutytype_instrument#'.$i, htmlspecialchars($dutytype_instrument));

            $this->templateProcessor->setValue('pageBreak#'.$i, $pagebreak);
        }

    }

    function getArtist_name($l_empty_strings)
    {
        $artist_name = new TextRun();

        //saddresses.street, saddresses.zipcode_poboxsaddresses.pobox.

        if ($l_empty_strings) {
            $artist_name->addTextBreak(5);
        }

        if ($this->acontract['artistaddress']) {

            $artist_name->addText($this->artist_salutation);
            $artist_name->addTextBreak();
            $artist_name->addText(trim($this->acontract['artistaddress']->name2 . ' ' . $this->acontract['artistaddress']->name1));

            if ($this->acontract['artistaddress']->co_name > '') {
                $artist_name->addTextBreak();
                $artist_name->addText($this->acontract['artistaddress']->co_name);
            }

        }
        return $artist_name;
    }

    function getArtist_address($l_empty_strings) {

        $artist_address = new TextRun();

        if ($l_empty_strings) {
            $artist_address->addTextBreak(5);
        }

        if($this->acontract['artistaddress']) {

            $artist_address->addText($this->artist_salutation);
            $artist_address->addTextBreak();
            $artist_address->addText(trim($this->acontract['artistaddress']->name2. ' '.$this->acontract['artistaddress']->name1));

            if($this->acontract['artistaddress']->co_name>'') {
                $artist_address->addTextBreak();
                $artist_address->addText($this->acontract['artistaddress']->co_name);
            }

            if (!empty($this->acontract['artistaddress']->street)) {
                $artist_address->addTextBreak();
                $artist_address->addText($this->acontract['artistaddress']->street);
            }
            if (!empty($this->acontract['artistaddress']->pobox)) {
                $artist_address->addTextBreak();
                $artist_address->addText($this->acontract['artistaddress']->pobox);
            }
            if (!empty($this->acontract['artistaddress']->zipcode) || !empty($this->acontract['artistaddress']->place)) {
                $artist_address->addTextBreak();
                $artist_address->addText(trim($this->acontract['artistaddress']->zipcode.' '.$this->acontract['artistaddress']->place));
            }
            if ($this->acontract['artistaddress']->scountry && !empty($this->acontract['artistaddress']->scountry->name)) {
                $artist_address->addTextBreak();
                $artist_address->addText($this->acontract['artistaddress']->scountry->name);
            }
        }
        return $artist_address;
    }

    function getConcerts() {
        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(0.75);
        $this->acol_widths[] = Converter::cmToTwip(5.02);
        $this->acol_widths[] = Converter::cmToTwip(2.75);
        $this->acol_widths[] = Converter::cmToTwip(5.75);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.01),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = new Table($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $count = 0;
        $alocation_ids = [];
        $apds = $this->acontract['apds'];

        foreach ($this->acontract['apds'] as $adate) {
            $count++;

            $table->addRow();

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell, $styleCell_border));
            $cell->addText(htmlspecialchars('Konzert-Datum: '.$adate->date_->format('d.m.Y')), array_merge($this->styleFont, $this->styleFont_bold));

            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell, $styleCell_border));
            $cell->addText(htmlspecialchars('Uhrzeit: '.$adate->start_->format('H:i')), array_merge($this->styleFont, $this->styleFont_bold));

            $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell, $styleCell_border));

            $location = $adate->locationaddress->place;
            $location .= (!empty($location) && !empty($adate->locationaddress->name1) ? ', ' : '') . $adate->locationaddress->name1;

            $cell->addText(htmlspecialchars('Ort: '.$location), array_merge($this->styleFont, $this->styleFont_bold));

            if(!in_array($adate->location_id, $alocation_ids)) {

                $alocation_ids[] = $adate->location_id;

                $zipplace = trim($adate->locationaddress->zipcode.' '.$adate->locationaddress->place);

                if($adate->locationaddress->street>'') {
                    $cell->addText(htmlspecialchars($adate->locationaddress->street), array_merge($this->styleFont, $this->styleFont_bold));
                }

                if($zipplace>'') {
                    $cell->addText(htmlspecialchars($zipplace), array_merge($this->styleFont, $this->styleFont_bold));
                }
            }

            $programcode = $this->reporttools->getProgramCode($adate);

            $l_show_program = true;

            foreach ($apds as $date_selected) {

                $programcode_selected = $this->reporttools->getProgramCode($date_selected);
                if(
                    ($date_selected->date_ < $adate->date_ ||
                        $date_selected->date_ == $adate->date_ && $date_selected->start_ < $adate->start_) &&
                    $date_selected->id <>$adate->id &&
                    $programcode_selected == $programcode) {
                    $l_show_program = false;
                }
            }

            // Program
            if($l_show_program) {
                $count = 0;
                foreach ($adate->adate_works as $awork) {
                    $count++;
                    if($count==1) {
                        $table->addRow();
                    }
                    //$datework_id = $awork->id;
                    $title = ($awork->title2 > '' ? $awork->title2 : $awork->swork->title1);
                    //$composer = ($awork->swork->scomposer ? $awork->swork->scomposer->lastname : '');
                    $composer = ($awork->swork->scomposer ? trim($awork->swork->scomposer->lastname) : '');
                    //$nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);
                    //$cduration = $nduration . "'";

                    $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell = $table->addCell($this->acol_widths[1]+$this->acol_widths[2]+$this->acol_widths[3], array_merge($styleCell, $this->cellColSpan3));

                    $textrun = $cell->addTextRun();
                    $textrun->addText(htmlspecialchars($composer.' '), array_merge($this->styleFont, $this->styleFont_bold));
                    $textrun->addText(htmlspecialchars($title), array_merge($this->styleFont));
                }
            }
        }


        return $table;
    }

    function getRehearsals() {
        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(0.75);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(2.25);
        $this->acol_widths[] = Converter::cmToTwip(4.5);
        $this->acol_widths[] = Converter::cmToTwip(4.75);


        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.01),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = new Table($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $table->addRow();
        $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));

        $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Datum'), array_merge($this->styleFont, $this->styleFont_bold));
        $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Uhrzeit'), array_merge($this->styleFont, $this->styleFont_bold));
        $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Termin'), array_merge($this->styleFont, $this->styleFont_bold));
        $cell = $table->addCell($this->acol_widths[4], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Ort'), array_merge($this->styleFont, $this->styleFont_bold));

        $count = 0;
        $alocation_ids = [];
        foreach ($this->acontract['arehearsals'] as $adate) {
            $count++;

            $table->addRow();

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($adate->date_->format('d.m.Y')), array_merge($this->styleFont));

            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell, $styleCell_border));
            $cell->addText(htmlspecialchars($this->reporttools->getTime($adate)), array_merge($this->styleFont));

            $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell, $styleCell_border));

            if($adate->text>'') {
                $cell->addText(htmlspecialchars($adate->text), array_merge($this->styleFont));
            }
            if($adate->notes>'') {
                $this->reporttools->addMemo($cell, $adate->notes, $this->styleFont_italic);
            }


            $cell = $table->addCell($this->acol_widths[4], array_merge($styleCell, $styleCell_border));

            if($adate->locationaddress) {
                $location = $adate->locationaddress->name1;
                $location .= (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place;
                $cell->addText(htmlspecialchars($location), array_merge($this->styleFont));

                if(!in_array($adate->location_id, $alocation_ids)) {

                    $alocation_ids[] = $adate->location_id;

                    $zipplace = trim($adate->locationaddress->zipcode.' '.$adate->locationaddress->place);

                    if($adate->locationaddress->street>'') {
                        $cell->addText(htmlspecialchars($adate->locationaddress->street), array_merge($this->styleFont));
                    }

                    if($zipplace>'') {
                        $cell->addText(htmlspecialchars($zipplace), array_merge($this->styleFont));
                    }
                }
            }
        }

        return $table;
    }

    public function addMemo($section, $text) {
        $text = str_replace("\r\n", "\n", $text);
        $textlines = explode("\n", $text);
        $count = 0;
        foreach($textlines as $line) {
            //print_r($line.'#');
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line));
        }
    }
}
