<?php

use App\Reports\ReportTemplate;
use Cake\ORM\TableRegistry;

class aduties_korttidskontrakter_oslo_template extends ReportTemplate
{
    /**
     * @inheritdoc
     */
    protected $templateName = 'aduties_korttidskontrakter_oslo_template';

    /**
     * @inheritdoc
     */
    protected $fileLocationType = 'direct';

    /**
     * Get all seasons
     *
     * Get all existing active seasons and pass it to the template. Set the $selectedSeason for the preselection of the
     * season in the template. In case of a matching season in case of the current time, we use this seasons, otherwise
     * we try to select the next season in the future if exists.
     *
     * @return array
     */
    public function getTemplateData(array $params = [])
    {
        $report = TableRegistry::getTableLocator()->get('opasreports')->get($params['id']);

        $where = array_merge(['Aduties.id IN' => $params['selectedIds']]);

        $aduties = TableRegistry::getTableLocator()->get('Aduties')
            ->find('all')
            ->select()
            ->contain([
                'Adates',
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']]
            ])
            ->where($where)
            ->order(['Sinstrsyssections.section_order']);



        $asyssections = [];
        foreach($aduties as $aduty) {
            //$syssection_id = ($aduty->sinstrinstrument->sinstrsection->syssection_id;
            /*$asyssections[-1] = [
                'id'=>-1,
                'name' => 'all'
            ];
            */
            if($aduty->sinstrinstrument && $aduty->sinstrinstrument->sinstrsection && $aduty->sinstrinstrument->sinstrsection->sinstrsyssection) {
                $syssection_id = $aduty->sinstrinstrument->sinstrsection->syssection_id;
                $syssection = $aduty->sinstrinstrument->sinstrsection->sinstrsyssection->name;

                if($syssection_id>17) {
                    $syssection_id = 18;
                    $syssection = 'Other';
                }

                if(!key_exists($syssection_id, $asyssections)) {
                    $asyssections[$syssection_id] = [
                        'id' => $syssection_id,
                        'name' => $syssection
                    ];
                }
            }
        }

        return [
            'asyssections' => $asyssections
        ];
    }

    public function getTemplateName()
    {
        return $this->templateName;
    }

    /**
     * @inheritdoc
     */

    public function submitTemplate()
    {
        try {
            $params = $this->request->getData();
            // Laden der Daten zu der Funktion aus der Datenbank
            $report = $this->Opasreports->get($params['id']);
            // Object zu dem Report erstellen
            $this->Wizard->init($report, self::OPAS_REPORT);

            // Prû¥fung der Daten
            $data = $this->Wizard->getWizardTemplateClass($params['template'])->submit($params['formData'])['data'];

            // Prû¥fung ob ein Fehlerfall vorliegt
            if (!array_key_exists('success', $data)) {
                $data['success'] = true;
            }

            // Status setzen
            if (!array_key_exists('statusCode', $data)) {
                $data['statusCode'] = 200;
            }

            if (!array_key_exists('message', $data) && $data['statusCode'] !== 200) {
                $data['message'] = 'Unknown error';
            }

            $this->viewBuilder()->setClassName('OpasJson');

            $this->response = $this->response->withStatus($data['statusCode']);

            $this->set('success', $data['success']);
            $this->set('data', $data);
        } catch (Exception $e) {
            $this->responseWithError($e);
        }
    }

    public function submit($formDataString = null)
    {
        parse_str($formDataString, $formDataArray);

        $this->data['formData'] = $formDataArray;

        return parent::submit($formDataString);
    }

}
