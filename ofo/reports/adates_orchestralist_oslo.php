<?php
set_time_limit(0);
/*
20230809 ONCUST-2202
OSLO Orchestra list
*/

use \App\Reports\ReportSpreadsheet;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpSpreadsheet\Worksheet\HeaderFooterDrawing;
use PhpOffice\PhpSpreadsheet\Worksheet\HeaderFooter;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\Core\Configure; // required for custom settings.php entries

use Cake\ORM\Query;
use Customer\ofo\reports\ReportTools_client;
use Customer\ofo\reports\instrumentation_ofo;

class adates_orchestralist_oslo extends ReportSpreadsheet
{
    public $awhere = null;
    public $postData = null;

    public $asections_selected = array();
    private $aduties_selected = null;
    public $where_duties = array();

    private $adates = array();
    private $adate = array();
    private $date_id = 0;
    private $aduty = null;

    private $reporttools = null;

    public $report_type = 'all';
    private $section = null;

    private $startrow = 0;

    private $nRN_Header = 0;
    private $nCN_Date = 1;
    private $nCN_Conductor = 2;
    private $nCN_Soloists = 3;
    private $nCN_Composer = 4;
    private $nCN_Title = 5;
    private $nCN_Instrumentation = 6;
    private $nCN_Duration = 7;
    private $nCN_Publisher = 8;
    private $nCN_Notes = 9;
    private $nCN_Archiv = 10;
    private $nCN_Leihgebuehr = 11;
    private $nCN_Ansichtspart = 12;

    private $nMaxColNum = 14;

    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();
    private $borders_thin_inside = array();
    private $borders_thin_horizontal = array();


    private $borders_thin_tb = array();

    private $borders_medium_top = array();
    private $borders_medium_bottom = array();
    private $borders_medium_top_bottom = array();
    private $borders_medium_tblr = array();




    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    private $borders_none_right = array();
    private $borders_none_left = array();

    protected $templates = [
        [
            'name' => 'adates_orchestralist_oslo_template',
            'file' => 'adates_orchestralist_oslo_template.php',
            'jsFile' => 'adates_orchestralist_oslo_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        $this->reporttools = new ReportTools_client();

        $this->postData = $this->getRequest()->getData();
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);
        $this->report_type = (isset($this->postData['formData']['report_type']) ? $this->postData['formData']['report_type'] : 'all');

        $this->where_duties = ['Adates.id IN' => $this->postData['dataItemIds']];

        $this->prepare_asections_selected();
        $this->prepare_duties_selected();
        $this->prepare_adates();

        return $this;
    }

    public function prepare_asections_selected() {
        $this->asections_selected = array();
    }

    public function prepare_duties_selected() {


        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        $this->aduties_selected = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses',
                    'AdateWorks' =>
                        function (Query $query) {
                            return $query
                                ->contain([
                                    'Sworkpremieres',
                                    'Sworks' => ['Scomposers'],
                                    'AdateworkMovements',
                                    'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses']
                                ])
                                ->orderAsc('AdateWorks.work_order');
                        }],
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses' => ['SaddressPersdata'],
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups'],
                'AdutyWorks' => ['AdateWorks' => ['Sworks' => ['Scomposers']]]

            ])
            ->where($this->where_duties)
            ->order(['Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order', 'Sdutytypes.l_present'=>'DESC', 'Aduties.order_1'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);

    }

    function prepare_adates() {
        $this->adates = array();

        $sheetno = 0;
        foreach ($this->aduties_selected as $this->aduty) {
            $date_id = $this->aduty->date_id;
            $adate = $this->aduty->adate;
            $l_present = ($this->aduty->sdutytype ? $this->aduty->sdutytype->l_present : '');
            $l_sub = ($this->aduty->saddressgroup->sysgroup_id == 12); //SUB

            $section_id = -1;

            if ($this->aduty->sinstrinstrument->sinstrsection) {
                $section_id = $this->aduty->sinstrinstrument->sinstrsection->id;
            }

            $sheetname = $adate->date_->format('d.m.Y').' '.$adate->start_->format('H.i');

            if (!array_key_exists($date_id, $this->adates)) {
                foreach($this->adates as $v) {
                    if($v['sheetname'] == $sheetname) {
                        $sheetname .= '('.$date_id.')';
                        break;
                    }
                }

                $sheetno++;

                $this->adates[$date_id] = array(
                    'sheetno' => $sheetno,
                    'sheetname' => $sheetname,
                    'adate' => $adate,
                    'asections' => array()
                );

                if(in_array($this->report_type, ['all','sta'])) {
                    $this->prepareSTA();
                }
            }

            if(
                (empty($this->asections_selected) || in_array($section_id, $this->asections_selected)) &&
                (
                    $this->report_type == 'all' ||
                    $this->report_type == 'sta' && (!$l_sub || $l_present==1) ||
                    $this->report_type == 'present' && ($l_present==1)
                )
            ) {
                $this->addSysSection($this->aduty->sinstrinstrument);
                $this->addArtist($section_id, $this->aduty->artistaddress, $this->aduty);
            }
        }
    }

    function prepareSTA() {
        $aaddresses = TableRegistry::getTableLocator()->get('Saddresses')
            ->find('all')
            ->select()
            ->contain([
                'SaddressPersdata',
                'SaddressAddressgroups' =>
                    function (Query $query) {
                        return $query
                            ->contain(['Saddressgroups'=>['Saddresssysgroups']])
                            ->order(['SaddressAddressgroups.l_main'=>'DESC', 'Saddressgroups.name'=>'ASC']);
                    },
                'SaddressInstruments' =>
                    function (Query $query) {
                        return $query
                            ->contain(['Sinstrinstruments' => ['Sinstrsections' => ['Sinstrsyssections']]])
                            ->order(['SaddressInstruments.l_main'=>'DESC', 'Sinstrinstruments.name'=>'ASC']);
                    }
            ])
            ->order(['Saddresses.order_1', 'Saddresses.name1']);


        //*** Ein - und Austrittsdatum und das Datum 1 und Datum 2 der Adressgruppe beachten
        foreach($aaddresses as $aaddress) {

            foreach ($aaddress->saddress_addressgroups as $saddress_addressgroup) {
                if (
                    $saddress_addressgroup->saddressgroup->saddresssysgroup->code == 'STA'
                    &&
                    (empty($saddress_addressgroup->date1) || $saddress_addressgroup->date1 <= $this->aduty->adate->date_)
                    &&
                    (empty($saddress_addressgroup->date2) || $saddress_addressgroup->date2 >= $this->aduty->adate->date_)
                ) {
                    foreach($aaddress->saddress_instruments as $saddress_instrument) {
                        if($saddress_instrument->l_main==1) {
                            if ($saddress_instrument->sinstrinstrument) {
                                $section_id = $saddress_instrument->sinstrinstrument->section_id;
                            }

                            if(empty($this->asections_selected) || in_array($section_id, $this->asections_selected)) {
                                $section_id = $this->addSysSection($saddress_instrument->sinstrinstrument);
                                $this->addArtist($section_id, $aaddress);
                            }
                        }
                    }
                }
            }
        }
    }

    function addSysSection($sinstrinstrument) {
        $section_id = -1;
        $section = 'unknown';
        $section_order = 0;
        $syssection_id = 0;

        if ($sinstrinstrument->sinstrsection) {
            $section_id = $sinstrinstrument->sinstrsection->id;
            $section = $sinstrinstrument->sinstrsection->name;
            $section_order = $sinstrinstrument->sinstrsection->section_order;
            $syssection_id = $sinstrinstrument->sinstrsection->syssection_id;
        }


        if (!array_key_exists($section_id, $this->adates[$this->aduty->date_id]['asections'])) {
            $this->adates[$this->aduty->date_id]['asections'][$section_id] = array(
                'syssection_id' => $syssection_id,
                'section' => $section,
                'section_order' => $section_order,
                'max_instr' => 0,
                'aartists' => array()
            );
        }
        return $section_id;
    }

    function addArtist($section_id, $aaddress, $aduty=null)
    {

        $personnelno = ($aaddress->saddress_persdata->personnelno ? $aaddress->saddress_persdata->personnelno : '');
        if (!key_exists($aaddress->id, $this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'])) {
            $this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aaddress->id] = array(
                'name1' => $aaddress->name1,
                'name2' => $aaddress->name2,
                'personnelno' => $personnelno,
                'l_sub' => false,
                'aduty' => null,
                'aduty_artist2' => null
            );
        }


        // Duty zur Adresse schreiben
        if($aduty) {
            $l_sub = ($this->aduty->saddressgroup->sysgroup_id == 12); //SUB
            $this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aduty->artist_id]['l_sub'] = $l_sub;
            if(isset($this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aduty->artist_id])) {
                $this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aduty->artist_id]['aduty'] = $aduty;
            }

            if(isset($this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aduty->artist2_id])) {
                
                //$artist2 = ($aduty->artistaddress ? $this->getLongName($aduty->artistaddress->name2, '', $aduty->artistaddress->name1) : '');
                
                $this->adates[$this->aduty->date_id]['asections'][$section_id]['aartists'][$aduty->artist2_id]['aduty_artist2'] = $aduty;
            }
        }
    }

    public function write_sheets($view = null, $layout = null)
    {
        $this->ospreadsheet->setActiveSheetIndex(0);


        $count = 0;
        foreach ($this->adates as $this->adate) {
            $sheetname = $this->adate['sheetname'];

            $sheetname = str_replace('/', '_', $sheetname);
            $sheetname = str_replace('\\', '_', $sheetname);
            $sheetname = str_replace('?', '_', $sheetname);
            $sheetname = str_replace('+', '_', $sheetname);
            $sheetname = str_replace('#', '_', $sheetname);
            $sheetname = str_replace('*', '_', $sheetname);
            $sheetname = str_replace(']', '_', $sheetname);
            $sheetname = str_replace('[', '_', $sheetname);

            $sheetname = substr($sheetname, 0, 31);

            $count++;

            if ($count > 1) {
                $this->ospreadsheet->createSheet();
            }

            $this->ospreadsheet->setActiveSheetIndex($count - 1);
            $this->sheet = $this->ospreadsheet->getActiveSheet();
            $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

            $this->sheet->setTitle($sheetname);

            $this->write_sheet();
        }
    }


    function write_sheet() {

        $this->ospreadsheet->getDefaultStyle()->getFont()->setName('F37 Beckett');
        $this->ospreadsheet->getDefaultStyle()->getFont()->setSize(8);

        $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

        $this->sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_PORTRAIT);
        $this->sheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $this->sheet->getPageSetup()->setFitToWidth(1);
        $this->sheet->getPageSetup()->setFitToHeight(0);
        $this->sheet->getPageMargins()->setTop(2.5/2.5);
        $this->sheet->getPageMargins()->setRight(1/2.5);
        $this->sheet->getPageMargins()->setLeft(2/2.5);
        $this->sheet->getPageMargins()->setBottom(1/2.5);
        $this->sheet->getPageMargins()->setHeader(0.8/2.5);
        $this->sheet->getPageMargins()->setFooter(0.8/2.5);
        $this->sheet->getPageSetup()->setHorizontalCentered(true);
        //$this->sheet->getSheetView()->setZoomScale(40);
        //$this->sheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1,2);

        //20231212
        $drawing = new HeaderFooterDrawing();
        $drawing->setName('OF Logo');
        $drawing->setPath(CUSTOMER_REP_DIR.'adates_orchestralist_oslo.png');
        $drawing->setHeight(75);
        $this->sheet->getHeaderFooter()->addImage($drawing, HeaderFooter::IMAGE_HEADER_RIGHT);

// Set header
        $this->sheet->getHeaderFooter()->setOddHeader('&L&D&R&G');

        //$this->sheet->getHeaderFooter()->setOddHeader('&D');


        /*
                for ($i = 'A'; $i !=  $this->sheet->getHighestColumn(); $i++) {
                    $this->sheet->getColumnDimension($i)->setWidth(3+0.71);
                }
        */

        $this->sheet->getColumnDimension($this->getColumnLetter(1))->setWidth(6+0.71+0.34);
        $this->sheet->getColumnDimension($this->getColumnLetter(2))->setWidth(14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(3))->setWidth(11+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(4))->setWidth(3.5+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(5))->setWidth(3.5+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(6))->setWidth(12+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(7))->setWidth(9+0.71);

        $this->sheet->getColumnDimension($this->getColumnLetter(8))->setWidth(6+0.71+0.34);
        $this->sheet->getColumnDimension($this->getColumnLetter(9))->setWidth(14+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(10))->setWidth(11+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(11))->setWidth(3.5+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(12))->setWidth(3.5+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(13))->setWidth(12+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter(14))->setWidth(9+0.71);

        $this->row = 1;

        $this->showDateHeader();
        //$this->row++;

        $this->startrow = $this->row;
        $this->showSections(1, 1, 4);
        $this->showSections(8, 5, 99);

        $this->sheet->getStyle($this->getColumnLetter(1) .($this->startrow) . ":" . $this->getColumnLetter(1) .$this->row)->applyFromArray(array_merge($this->borders_thin_left));
        $this->sheet->getStyle($this->getColumnLetter(8) .($this->startrow) . ":" . $this->getColumnLetter(8) .$this->row)->applyFromArray(array_merge($this->borders_thin_left));
        $this->sheet->getStyle($this->getColumnLetter(14) .($this->startrow) . ":" . $this->getColumnLetter(14) .$this->row)->applyFromArray(array_merge($this->borders_thin_right));

        $this->sheet->getStyle($this->getColumnLetter(1) .($this->startrow) . ":" . $this->getColumnLetter(14) .$this->startrow)->applyFromArray(array_merge($this->borders_thin_top));
        $this->sheet->getStyle($this->getColumnLetter(1) . ($this->row) . ":" . $this->getColumnLetter(14) .$this->row)->applyFromArray(array_merge($this->borders_thin_bottom));
    }

    function showSections($cn, $section_start, $section_end) {
        $row = $this->startrow;
        $section_count = 0;
        foreach($this->adate['asections'] as $section_id=>$asection) {
            if($asection['syssection_id']<$section_start || $asection['syssection_id']>$section_end) {continue;}

            $row++;
            $section_count++;

            $this->sheet->mergeCells($this->getColumnLetter($cn) . ($row) . ":" . $this->getColumnLetter($cn+2) . ($row+1));
            $this->sheet->mergeCells($this->getColumnLetter($cn+3) . ($row) . ":" . $this->getColumnLetter($cn+4) . ($row+1));
            $this->sheet->mergeCells($this->getColumnLetter($cn+5) . ($row) . ":" . $this->getColumnLetter($cn+5) . ($row+1));
            $this->sheet->mergeCells($this->getColumnLetter($cn+6) . ($row) . ":" . $this->getColumnLetter($cn+6) . ($row+1));

            $this->sheet->getStyle($this->getColumnLetter($cn).$row)->getFont()->setBold(true);
            $this->sheet->getStyle($this->getColumnLetter($cn).$row.':'.$this->getColumnLetter($cn+6).($row+1))->getFont()->setSize(9);

            $this->sheet->setCellValue($this->getColumnLetter($cn) . ($row), htmlspecialchars($asection['section']));

            if($section_count==1) {
                $this->sheet->setCellValue($this->getColumnLetter($cn+3) . ($row), htmlspecialchars('Fravær'));
                $this->sheet->setCellValue($this->getColumnLetter($cn+5) . ($row), htmlspecialchars('Vikar'));
                $this->sheet->setCellValue($this->getColumnLetter($cn+6) . ($row), htmlspecialchars('Info'));
            }

            $row++;

            foreach($asection['aartists'] as $artist_id=>$aartist) {
                $row++;
                $this->sheet->setCellValue($this->getColumnLetter($cn) . ($row), htmlspecialchars($aartist['personnelno']));
                $this->sheet->setCellValue($this->getColumnLetter($cn+1) . ($row), htmlspecialchars($aartist['name1']));
                //$this->sheet->setCellValue($this->getColumnLetter($cn+1) . ($row), htmlspecialchars($aartist['name1'].'#'.implode(',', $this->asections_selected)));

                $this->sheet->setCellValue($this->getColumnLetter($cn+2) . ($row), htmlspecialchars($aartist['name2']));


                if($aartist['aduty']) {
                    $dutytype_code = ($aartist['aduty']->sdutytype ? $aartist['aduty']->sdutytype->code : '');
                    $l_present = ($aartist['aduty']->sdutytype ? $aartist['aduty']->sdutytype->l_present : '');

                    //$this->sheet->setCellValue($this->getColumnLetter($cn+1) . ($row), htmlspecialchars($aartist['name1'].'#'.$dutytype_code.'#'.$l_present));

                    //*** 20140129
                    //*** To make it a little easier for the economy department to spot orchestra members
                    //*** that had an absence type that could possibly affect payments,
                    //*** I would like to have a discreet color marking of the name and code.

                    //*** The codes in question that should generate this marking are: S1, S2, S3, S4, S5, FP, P2 and F6.

                    //*** 20191125
                    //*oExcel.ActiveSheet.Cells(lnRowNum, tnColNum+3)).Interior.Color = RGB(230,184,183)
                    //*To mach the existing red fill, please use RGB values R:184 / G: 230 / B: 183.
                    switch(true) {
                        case in_array(strtoupper($dutytype_code), ['S1', 'S2', 'S3', 'S4', 'S5', 'FP', 'P2', 'F6']):
                            $this->sheet->getStyle($this->getColumnLetter($cn+1) . ($row) . ":" . $this->getColumnLetter($cn+3).$row)
                                ->getFill()
                                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                                ->getStartColor()
                                ->setARGB('E6B8B7'); //RGB(230,184,183)
                        break;

                        //*** 20191115
                        ////*** den Anwesenheittyp "Tilleggsmusiker" (+T) in grün zeigen
                        case in_array(strtoupper($dutytype_code), ['+T']):
                            $this->sheet->getStyle($this->getColumnLetter($cn+1) . ($row) . ":" . $this->getColumnLetter($cn+3).$row)
                                ->getFill()
                                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                                ->getStartColor()
                                ->setARGB('B8E6B7'); //RGB(184,230,183)
                        break;
                    }

                    //*** DutyType für Artist
                    $this->sheet->setCellValue($this->getColumnLetter($cn+3) . ($row), htmlspecialchars($dutytype_code));


                    //** Fett, nur wenn Present
                    if($l_present>0) {
                        $this->sheet->getStyle($this->getColumnLetter($cn+1).$row.':'.$this->getColumnLetter($cn+2).$row)->getFont()->setBold(true);
                    }

                    //** Aushilfe
                    if($aartist['l_sub']) {
                        $this->sheet->getStyle($this->getColumnLetter($cn+1).$row.':'.$this->getColumnLetter($cn+2).$row)->getFont()->setItalic(true);
                    }

                    //*** Aushilfe  suchen, die den Staff ersetzt
                    if($aartist['aduty_artist2']) {
                        $artist2 = ($aartist['aduty_artist2']->artistaddress ? $this->getLongName($aartist['aduty_artist2']->artistaddress->name2, '', $aartist['aduty_artist2']->artistaddress->name1) : '');
                        $artist2_code = ($aartist['aduty_artist2']->artistaddress ? $aartist['aduty_artist2']->artistaddress->code : '');
                        $artist2_dutytype_code = ($aartist['aduty_artist2']->sdutytype ? $aartist['aduty_artist2']->sdutytype->code : '');
                        $this->sheet->setCellValue($this->getColumnLetter($cn + 4) . ($row), htmlspecialchars($artist2_dutytype_code));
                        $this->sheet->setCellValue($this->getColumnLetter($cn + 5) . ($row), htmlspecialchars($artist2));

                        //*** 20160316
                        //*** Code aus der ersetzenden Person
                        $this->sheet->setCellValue($this->getColumnLetter($cn + 6) . ($row), htmlspecialchars($artist2_code));

                        //*** Aushilfe Italic
                        if($aartist['aduty_artist2']->saddressgroup->sysgroup_id == 12) { //SUB
                            $this->sheet->getStyle($this->getColumnLetter($cn+5).$row)->getFont()->setItalic(true);
                        }
                        //*** DutyType für Artist2
                        if($aartist['aduty_artist2']->sdutytype->l_present<>0) {
                            $this->sheet->getStyle($this->getColumnLetter($cn+5).$row)->getFont()->setBold(true);
                        }
                    }
                }
            }
        }

        $this->row = max($this->row, $row);
    }

    function showDateHeader() {

        $adate = $this->adate['adate'];
        $datetime = $adate->date_->format(Configure::read('Formats.date')) . ' '.$adate->start_->format(Configure::read('Formats.time_short'));
        //$datetime = print_r($adate, true);


        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('UKE:'));
        $this->sheet->mergeCells($this->getColumnLetter(2).($this->row).":".$this->getColumnLetter(3).($this->row));
        $this->sheet->setCellValue($this->getColumnLetter(2).($this->row), htmlspecialchars($datetime));


        $conductor = '';
        if($adate->conductoraddress) {
            $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);
        }

        $this->sheet->mergeCells($this->getColumnLetter(4).($this->row).":".$this->getColumnLetter(8).($this->row));
        $this->sheet->setCellValue($this->getColumnLetter(4).($this->row), htmlspecialchars('Dirigent: '.$conductor));

        $this->sheet->setCellValue($this->getColumnLetter(9).($this->row), htmlspecialchars('Program:'));

        $count = 0;
        foreach ($adate->adate_works as $works) {
            if($works->swork->l_intermission==1) {
                continue;
            }

            $count++;
            if($count>1) {$this->row++;}

            $composer = $works->swork->scomposer->lastname;
            $title = ($works->swork ? ($works->swork->title2>'' ? $works->swork->title2 : $works->swork->title1) : '');

            $this->sheet->mergeCells($this->getColumnLetter(10).($this->row).":".$this->getColumnLetter(14).($this->row));
            $this->sheet->setCellValue($this->getColumnLetter(10).($this->row), htmlspecialchars($composer.': '.$title));
        }


        $this->sheet->getStyle($this->getColumnLetter(1).'1'.':'.$this->getColumnLetter($this->nMaxColNum).$this->row)->getFont()->setBold(true);
        $this->sheet->getStyle($this->getColumnLetter(1).'1'.':'.$this->getColumnLetter($this->nMaxColNum).$this->row)->getFont()->setSize(10);

        $this->sheet->getStyle($this->getColumnLetter(1).'1'.':'.$this->getColumnLetter($this->nMaxColNum).$this->row)
            ->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('FFFF99');
    }

    public function getLongName($tname2 = '', $tname5 = '', $tname1 = '')
    {
        $name = trim(trim($tname2 . ' ' . $tname5) . ' ' . $tname1);
        return $name;
    }

    //uasort($this->asections, array($this,"usort_asections"));

    function usort_asections($a, $b) {
        $frm_order_a = strtoupper($a['section_order']);
        $frm_order_b = strtoupper($b['section_order']);

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_horizontal = [
            'borders' => [
                'horizontal' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_tb = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_medium_top_bottom = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];

        $this->borders_medium_tblr = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ]
            ],
        ];


        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_NONE,
                ],
            ],
        ];

    }

}
