<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Season Programs with Percussion Requirements</caption>
  <classfile>SeasonPercReq/SeasonPercReq.php</classfile>
  <classname>customer\spco\reports\SeasonPercReq\SeasonPercReq</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Season Programs with Percussion Requirements</title>
      <notes>Run for any set of PERFORMANCES within a SINGLE SEASON. The report shows each Program performed, with detailed Timpani and Percussion requirements.</notes>
      <reportTitle>Season Programs with Percussion Requirements</reportTitle>
      <performances>Performance(s)</performances>
      <project>Project</project>
      <program>Program</program>
      <percussion>Timp / Percussion</percussion>
      <programPrefix>Program:</programPrefix>
    </default>
    <canada>
      <title>Season Programmes with Percussion Requirements</title>
      <notes>Run for any set of PERFORMANCES within a SINGLE SEASON. The report shows each Programme performed, with detailed Timpani and Percussion requirements.</notes>
      <reportTitle>Season Programmes with Percussion Requirements</reportTitle>
      <performances>Performance(s)</performances>
      <project>Project</project>
      <program>Programme</program>
      <percussion>Timp / Percussion</percussion>
      <programPrefix>Programme:</programPrefix>
    </canada>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
