{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'AdateWorks/edit.twig' %}

{% set disabled = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-music',
    'icon': 'fa-cog',
    'headline': {0: __('adate_works'), 1: __('view'), 2: adateWork.swork.title1 },
    'data': adateWork
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: currentController,
    1: {
      1: 'list',
      2: 'edit',
      3: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
    },
    2: adateWork.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-cog', 1: __('view')} %}
{% endblock %}

{% block dialogs %}
  {% cell 'Dialog::confirm' {0:{
    'id': 'confirmDeleteItem',
    'headline': __('delete record'),
    'message': __('delete record {0}?',adateWork.swork.title1),
    'url': '/adate-works/delete/' ~ adateWork.id
  }} %}
{% endblock %}
