<?php
/*
20240715 ONCUST-2537
RChB Zahlungsanweisung
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use App\Reports\ReportSpreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Cake\ORM\TableRegistry;
use Customer\roc\reports\ReportTools_client;
use App\Model\Table\AdaysTable;

class adates_zahlungsanweisung_rchb extends ReportSpreadsheet
{
    private $sheet = null;
    public $aartists = array();
    public $aartist = array();

    private $date_min = null;
    private $date_max = null;
    private $aseasons_selected = array();
    private $aprojects_selected = array();
    private $postData = null;
    private $reporttools = null;

    private $nCN_personnelno = 1;
    private $nCN_name1 = 2;
    private $nCN_name2 = 3;
    private $nCN_sz_selected = 4;
    private $nCN_sz_season = 5;


    private $nCN_SS30 = 5;
    private $nCN_SS31 = 6;
    private $nCN_SM30 = 7;
    private $nCN_SM31 = 8;
    private $nCN_18a = 9;
    private $nCN_18b = 10;
    private $nMaxColNum = 10;

    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();
    private $borders_thin_vertical = array();


    private $borders_medium_top = array();
    private $borders_medium_bottom = array();
    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    private $borders_none_right = array();
    private $borders_none_left = array();

    function initialize()
    {
        parent::initialize();

        $this->template_filename = CUSTOMER_REP_DIR . 'adates_zahlungsanweisung_rchb.xlsx';

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);


        $awhere = ['Adates.id IN' => $this->postData['dataItemIds']];
        $arows = TableRegistry::getTableLocator()->get('Adates')
            ->find('all')
            ->select()
            ->contain([
                'Sseasons',
                'Sprojects'
            ])
            ->where($awhere)
            ->order(['Sseasons.datestart' => 'ASC'])
            ->all();

        $this->date_min = null;
        $this->date_max = null;
        $aseason_ids_selected = [];
        $aseason_ids_selected[] = -1;
        $this->aseasons_selected = [];
        $this->aprojects_selected = [];

        $count = 0;
        foreach($arows as $arow) {
            $count++;
            if($count==1) {
                $this->date_min = $arow->date_;
            }
            $this->date_max = $arow->date_;

            $aseason_ids_selected[] = $arow->season_id;
            $this->aseasons_selected[] = ($arow->sseason ? $arow->sseason->name : '');
            if($arow->sproject) {
                $this->aprojects_selected[] = $arow->sproject->name . (!empty($arow->sproject->accountno) ? ', ' : '').$arow->sproject->accountno;
            }

        }

        $aseason_ids_selected = array_unique($aseason_ids_selected);
        $this->aseasons_selected = array_unique($this->aseasons_selected);
        $this->aprojects_selected = array_unique($this->aprojects_selected);


        //*** ********
        //*** Szenische Dienste oder Kaostüm oder Maske

        //*** ********
        //*** - auf den bericht sollen nur Festangestellte

        $where =
                "Adates.season_id IN (". implode(',',$aseason_ids_selected) . ") AND ".
                "Sdutytypes.l_present=1 AND ".
                "Adates.date_ <='".$this->date_max->format('Y-m-d')."' AND ".
                "Saddresssysgroups.code='STA' AND ".
                "(
                    LOWER(Seventtypes.logic_2) LIKE '%szenisch%' AND Adates.l_logic_2=1
                    OR
                    Sdresses.name LIKE '%kostüm%'
                    OR
                    Sdresses.name LIKE '%maske%'
                )";

        $aduties_selected = TableRegistry::getTableLocator()->get('Aduties')
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses'],
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses' => ['SaddressPersdata'],
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']
            ])
            ->where(
                $where
            )
            ->order(['SaddressPersdata.personnelno'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC', 'Adates.date_' => 'ASC', 'Adates.start_' => 'ASC']);

        $this->aartists = [];

        foreach($aduties_selected as $aduty) {
            $artist_id = $aduty->artist_id;
            $date_id  = $aduty->date_id;
            $project_id = ($aduty->adate ? $aduty->adate->project_id : 0);
            $eventtype_logic_2 = ($aduty->adate->seventtype ? $aduty->adate->seventtype->logic_2 : '');
            $eventtype_l_logic_2 = ($aduty->adate ? $aduty->adate->l_logic_2 : 0);
            $l_logic_3 = ($aduty->adate ? $aduty->adate->l_logic_3 : 0);

            $dress = ($aduty->adate->sdress ? $aduty->adate->sdress->name : '');
            $l_performance = ($aduty->adate->seventtype ? $aduty->adate->seventtype->l_performance : 0);


            $duties = $aduty->duties;



            if(!key_exists($artist_id, $this->aartists)) {
                $this->aartists[$artist_id] = [
                    'personnelno' => ($aduty->artistaddress->saddress_persdata->personnelno ? $aduty->artistaddress->saddress_persdata->personnelno : ''),
                    'name1' => $aduty->artistaddress->name1,
                    'name2' => $aduty->artistaddress->name2,
                    'sz_selected' => 0,
                    'sz_season' => 0,
                    '18a_selected' => 0,
                    '18b_selected' => 0,
                    'aprojects_18a_selected' => [],
                    'aduties_sz_selected' => array(),
                    'aduties_k_selected' => array(),
                    'aduties_m_selected' => array(),
                    'aduties_kum_selected' => array()
                ];
            }

//print_r($eventtype_logic_2.'#'.$eventtype_l_logic_2.'#');
            if(strpos(mb_strtolower($eventtype_logic_2), 'szenisch') !== false && $eventtype_l_logic_2==1) {
                $this->aartists[$artist_id]['sz_season'] += $duties;
                if(in_array($date_id, $this->postData['dataItemIds'])) {
                    $this->aartists[$artist_id]['sz_selected'] += $duties;

                    $this->aartists[$artist_id]['aduties_sz_selected'][] = $aduty;
                }
            }

            if(
                $l_performance == 1 &&
                (strpos(mb_strtolower($dress), 'maske') !== false || strpos(mb_strtolower($dress), 'kostüm') !== false)
            ) {
                $this->aartists[$artist_id]['18b_selected']++;
            }


                //** Proben und keine Wiederafnahme (l_logic_3=1)
                //*** nur einmal pro Projekt

            if(
                $l_performance == 0 && $l_logic_3==0 &&
                    (strpos(mb_strtolower($dress), 'maske') !== false || strpos(mb_strtolower($dress), 'kostüm') !== false)
            ) {
                if (!in_array($project_id, $this->aartists[$artist_id]['aprojects_18a_selected'])) {
                    $this->aartists[$artist_id]['aprojects_18a_selected'][] = $project_id;
                }

                $this->aartists[$artist_id]['18a_selected'] = sizeof($this->aartists[$artist_id]['aprojects_18a_selected']);
            }



             switch(true) {
                case (strpos(mb_strtolower($dress), 'maske') !== false && strpos(mb_strtolower($dress), 'kostüm') !== false):
                    $this->aartists[$artist_id]['aduties_kum_selected'][] = $aduty;
                    break;

                    case strpos(mb_strtolower($dress), 'kostüm') !== false:
                 $this->aartists[$artist_id]['aduties_k_selected'][] = $aduty;
                 break;

                 case strpos(mb_strtolower($dress), 'maske') !== false:
                    $this->aartists[$artist_id]['aduties_m_selected'][] = $aduty;
                 break;
             }
        }

        return $this;
    }


    public function write_sheets($view = null, $layout = null) {
        //$locale = 'en';
        //$validLocale = \PhpOffice\PhpSpreadsheet\Settings::setLocale($locale);

        $this->ospreadsheet->setActiveSheetIndex(0);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $this->write_sheet_zahlungsanweisung();

        $this->ospreadsheet->setActiveSheetIndex(1);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        $this->write_sheet_musiker();
    }

    function write_sheet_zahlungsanweisung() {
        $row = 4;

        $this->sheet->setCellValue('C1', htmlspecialchars($this->getMinMaxDays_long()));

        $this->sheet->setCellValue('A2', htmlspecialchars(implode('; ', $this->aprojects_selected)));

        $this->sheet->setCellValue('E4', htmlspecialchars(implode(', ', $this->aseasons_selected). ' bis '.$this->date_max->format('d.m.Y')));



        foreach($this->aartists as $artist_id=>$aartist) {
            $row++;

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_personnelno).$row, htmlspecialchars($aartist['personnelno']));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_name1).$row, htmlspecialchars($aartist['name1']));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_name2).$row, htmlspecialchars($aartist['name2']));

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_sz_selected).$row, htmlspecialchars($aartist['sz_selected']));

            $duties_ss = $aartist['sz_season'];
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_SS30).$row, '=IF('.$duties_ss.'>30,30,'.$duties_ss.')');
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_SS31).$row, '=IF('.$duties_ss.'>30,'.$duties_ss.'-30,0)');


            $cell_SS31 = $this->getColumnLetter($this->nCN_SS31).($row);
            $cell_sz_selected = $this->getColumnLetter($this->nCN_sz_selected).($row);

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_SM30).$row,
                '=' .
                    $cell_sz_selected . '-' .
                $this->getColumnLetter($this->nCN_SM31).$row
            );

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_SM31).$row,
                '=IF('.$cell_SS31.'>0, '.
                'MIN('.$cell_SS31.','.$cell_sz_selected.'),'.
					'0'.
				')');

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_18a).$row, htmlspecialchars($aartist['18a_selected']));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_18b).$row, htmlspecialchars($aartist['18b_selected']));
        }

        $this->sheet->getStyle('A5'.':'.$this->getColumnLetter($this->nMaxColNum).$row)->applyFromArray($this->borders_thin_all);
    }

    function write_sheet_musiker() {

        $row = 3;

        //$this->sheet->setCellValue('E4', htmlspecialchars(implode(', ', $this->aseasons_selected). ' bis '.$this->date_max->format('d.m.Y')));

        $arr = [];
        $arr[] = ['aduties' => 'aduties_sz_selected', $row=>'row_sz', 'col'=>4];
        $arr[] = ['aduties' => 'aduties_k_selected', $row=>'row_k', 'col'=>8];
        $arr[] = ['aduties' => 'aduties_m_selected', $row=>'row_m', 'col'=>12];
        $arr[] = ['aduties' => 'aduties_kum_selected', $row=>'row_kum', 'col'=>16];

        foreach($this->aartists as $artist_id=>$aartist) {
            $row++;
            $row++;

            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_personnelno).$row, htmlspecialchars($aartist['personnelno']));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_name1).$row, htmlspecialchars($aartist['name1']));
            $this->sheet->setCellValue($this->getColumnLetter($this->nCN_name2).$row, htmlspecialchars($aartist['name2']));

            $row_start = $row;

            foreach($arr as $aitem) {
                $cn = $aitem['col'];
                $rn = $row_start-1;


                foreach($aartist[$aitem['aduties']] as $aduty) {
                    $rn++;

                    $this->sheet->setCellValue($this->getColumnLetter($cn).$rn, htmlspecialchars(($aduty->adate->date_ ? $aduty->adate->date_->format('d.m.Y') : '')));
                    $this->sheet->setCellValue($this->getColumnLetter($cn+1).$rn, htmlspecialchars(($aduty->adate->start_ ? $aduty->adate->start_->format('H:i') : '')));
                    $this->sheet->setCellValue($this->getColumnLetter($cn+2).$rn, htmlspecialchars(($aduty->adate->seventtype ? $aduty->adate->seventtype->name : '')));

                    if($aitem['aduties'] == 'aduties_sz_selected') {
                        $this->sheet->setCellValue($this->getColumnLetter($cn + 3) . $rn, htmlspecialchars($aduty->duties));
                    } else {
                        $this->sheet->setCellValue($this->getColumnLetter($cn + 3) . $rn, htmlspecialchars(($aduty->adate->sdress ? $aduty->adate->sdress->name : '')));
                    }
                }

                $row = max($row, $rn);
            }
        }


        $this->sheet->getStyle($this->getColumnLetter($this->nCN_personnelno).(4).':'.$this->getColumnLetter($this->nCN_personnelno).$row)->applyFromArray($this->borders_thin_left);
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_name1).(4).':'.$this->getColumnLetter($this->nCN_name1).$row)->applyFromArray($this->borders_thin_left);
        $this->sheet->getStyle($this->getColumnLetter($this->nCN_name2).(4).':'.$this->getColumnLetter($this->nCN_name2).$row)->applyFromArray($this->borders_thin_left);

        foreach($arr as $aitem) {
            $cn = $aitem['col'];
            $this->sheet->getStyle($this->getColumnLetter($cn) . (4) . ':' . $this->getColumnLetter($cn) . $row)->applyFromArray($this->borders_thin_left);

            $this->sheet->getColumnDimension($this->getColumnLetter($cn))->setAutoSize(true);
            $this->sheet->getColumnDimension($this->getColumnLetter($cn+1))->setAutoSize(true);
            $this->sheet->getColumnDimension($this->getColumnLetter($cn+2))->setAutoSize(true);
            $this->sheet->getColumnDimension($this->getColumnLetter($cn+3))->setAutoSize(true);


        }

        $cn = $cn + 3;
        $this->sheet->getStyle($this->getColumnLetter($cn).(4).':'.$this->getColumnLetter($cn).$row)->applyFromArray($this->borders_thin_right);

        $this->sheet->getStyle($this->getColumnLetter(1).($row).':'.$this->getColumnLetter($cn).$row)->applyFromArray($this->borders_thin_bottom);
    }



    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_vertical = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];

        $this->borders_none_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_NONE,
                ],
            ],
        ];

        $this->borders_none_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => Border::BORDER_NONE,
                ],
            ],
        ];

    }

    function getMinMaxDays_long()
    {
        $y_min = $this->date_min->format('Y');
        $m_min = $this->reporttools->getMonthname($this->date_min);
        $d_min = $this->date_min->format('d');

        $y_max = $this->date_max->format('Y');
        $m_max = $this->reporttools->getMonthname($this->date_max);
        $d_max = $this->date_max->format('d');

        $minmax =
            $d_min.' '.$m_min. ' '.$y_min.
            ' - '.
            $d_max.' '.$m_max.' '.$y_max;

        return $minmax;
    }
}
