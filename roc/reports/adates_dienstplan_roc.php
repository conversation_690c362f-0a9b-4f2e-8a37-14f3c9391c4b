<?php
set_time_limit(0);

ini_set('max_execution_time', 300);
ini_set('request_terminate_timeout', 300);

/*
20231114
ROC Dienstplan
*/



use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use Cake\ORM\Query;
use Cake\Core\Configure;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\roc\reports\ReportTools_client;
use Customer\roc\reports\instrumentation_roc;
use Cake\I18n\Date;

class adates_dienstplan_roc extends ReportWord
{
    public $reporttype = 'M';
    public $postData = null;
    public $reporttools = null;
    
    public $section = null;

    public $adates_selected = null;
    public $adate = null;

    public $amonths = array();
    public $amonth = array();

    public $aseasons = array();
    public $aseason = array();

    public $aweeks = array();
    public $aweek = array();
    public $week = '';

    public $k = 0;

    public $aday = array();

    public $alegende = array();
    public $adatenotes = array();
    public $table = null;
    public $acol_widths = array();
    public $w_table = 0;

    //public $cn_week = 0;
    //public $cn_weekday = 0;
    public $cn_day = 1;
    public $cn_time = 2;
    public $cn_location = 3;
    //public $cn_eventtype = 0;
    public $cn_dateinfo = 4;
    public $cn_condsolo = 5;
    //*****
    public $cn_kd = 6;
    public $cn_nk = 7;
    public $cn_kr = 8;

    //*****
    public $cn_rsb_duties = 6;

//*****
    public $cn_rchb_da = 6;
    public $cn_rchb_he = 7;
    public $cn_rchb_sz = 8;
    public $cn_rchb_and = 9;

    public $kd_total = 0;
    public $nk_total = 0;
    public $kr_total = 0;

    public $duties_total = 0;

    public $duties_rchb_da = 0;
    public $duties_rchb_he = 0;
    public $duties_rchb_sz = 0;
    public $duties_rchb_and = 0;
    public $cduties_rchb_sz = '';


    public $duties_rchb_da_total = 0;
    public $duties_rchb_he_total = 0;
    public $duties_rchb_sz_total = 0;
    public $duties_rchb_and_total = 0;


    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_8 = array('size' => 8);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_11 = array('size' => 11);

    public $styleBG_grey = array('bgColor'=>'#C0C0C0');

    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');
    public $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => 'black');
    public $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black');

    public $styleCell_borderTop_none = array('borderTopSize' => 6, 'borderTopColor' => 'white');
    public $styleCell_borderBottom_none = array('borderBottomSize' => 6, 'borderBottomColor' => 'white');
    public $styleCell_borderLeft_none = array('borderLeftSize' => 6, 'borderLeftColor' => 'white');
    public $styleCell_borderRight_none = array('borderRightSize' => 6, 'borderRightColor' => 'white');
    public $aborder_tbrl = array();

    public $cellColSpan2 = array('gridSpan' => 2);
    public $cellColSpan4 = array('gridSpan' => 4);

    public $cellRowSpan = array('vMerge' => 'restart');
    public $cellRowContinue = array('vMerge' => 'continue');

    //public $l_addcols = true;
    public $l_totals_dso = false;
    public $l_totals_rsb = false;
    public $l_totals_rchb = false;
    public $l_totals_rkc = false;
    public $l_besetzung = false;
    public $l_filldays = true;


    public $l_show_header_week = true;

    protected $templates = [
        [
            'name' => 'adates_dienstplan_roc_template',
            'file' => 'adates_dienstplan_roc_template.php',
            'jsFile' => 'adates_dienstplan_roc_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        //$this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->aborder_tbrl = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight
        );
    }


    public function collect(array $where = [])
    {
        $this->postData = $this->getRequest()->getData();

        //$this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $this->l_totals_dso = ($this->postData['formData']['totals'] == 'dso');
        $this->l_totals_rsb = ($this->postData['formData']['totals'] == 'rsb');
        $this->l_totals_rchb = ($this->postData['formData']['totals'] == 'rchb');
        $this->l_totals_rkc = ($this->postData['formData']['totals'] == 'rkc');

        $this->l_besetzung = $this->postData['formData']['template_cb1'];
        $this->l_filldays = $this->postData['formData']['template_cb2'];
        //$this->l_addcols = false;

        // nur AusgewûÊhlte Termine
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);
/*
        $this->prepare_amonths();

        $this->prepare_aweeks();
*/
        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(8);
        $this->phpWord->setDefaultFontName('Microsoft Sans Serif');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $this->section = $this->phpWord->addSection(
            array(
                'breakType' => 'continuous',
                'headerHeight' => Converter::cmToTwip(0.5),
                'footerHeight' => Converter::cmToTwip(0.5),
                'marginLeft' => Converter::cmToTwip(2),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(1),
                'marginBottom' => Converter::cmToTwip(2))
        );

        $sectionStyle = $this->section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

        $header = $this->section->addHeader();

        $styleTabs = array(
            'tabs' => array(
                new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(9)),
                new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(17.5))
            )
        );

        $textrun = $header->addTextrun($styleTabs);

        //$textrun->addText(htmlspecialchars(Configure::read('CustomerSettings.name')), array_merge($this->styleFont_bold));
        //$textrun->addText(htmlspecialchars("\t".$this->report->title), array_merge($this->styleFont_bold));
        $textrun->addText(htmlspecialchars("\t"."\t".date(Configure::read('Formats.date'))));
        $textrun->addText('/');
        $textrun->addField("PAGE", array());

        $this->prepare_amonths();

        $this->prepare_aweeks();

        //$this->section->addText('reporttype='.$this->reporttype);
        //$this->section->addText('reporttype='.$this->reporttype);
        $this->count_weeks = 0;
        switch(true) {
            case $this->reporttype == 'M':
                $this->showMonths();
            break;

            case $this->reporttype == 'S':
                $this->showSeasons();
                break;

            default:
                $this->showWeeks();
                break;
        }
    }

    function prepare_amonths() {

        $this->aseasons = array();
        $this->amonths = array();

        foreach ($this->adates_selected as $adate) {

            $season_id = (int)$adate->season_id;
            $month = $adate->month;
            $year = $adate->year;
            $week_date = $adate->week;
            $k_month = $adate->year.'_'.$adate->month;

            $cdate = $adate->date_->format('Ymd');

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            $programcode = $this->reporttools->getProgramCode($adate);

            if (!array_key_exists($season_id, $this->aseasons)) {
                $this->aseasons[$season_id] = array(
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'season_code' => ($adate->sseason ? $adate->sseason->code : ''),
                    'cmindate' => $adate->date_->format('d.m.Y'),
                    'cmaxdate' => '',
                    'amonths' => array(),
                    'aprogramcodes' => array()
                );
            }

            $this->aseasons[$season_id]['cmaxdate'] = $adate->date_->format('d.m.Y');

            // nur month key
            if (!in_array($k_month, $this->aseasons[$season_id]['amonths'])) {
                $this->aseasons[$season_id]['amonths'][] = $k_month;
            }

            if (!array_key_exists($k_month, $this->amonths)) {

                $this->amonths[$k_month] = array(
                    'year' => $year,
                    'month' => $month,
                    'cmindate' => '',
                    'cmaxdate' => '',
                    'cmonth' => $this->reporttools->getMonthName($adate->date_).' '.$year,
                    'aweeks' => array(),
                    'aprogramcodes' => array()
                );

                // Alle Tage des Monats holen
                if($this->l_filldays) {
                    $adays = TableRegistry::getTableLocator()->get('Adays')
                        ->find('all')
                        ->select([
                            'Adays.date_',
                            'Adays.pweek',
                            'Adays.week',
                            'Adays.weekday',
                            'Adays.holyday_id',
                        ])
                        ->where(["Adays.year=$year AND Adays.month=$month AND Adays.planninglevel=1"])
                        ->orderAsc('Adays.date_')
                        ->distinct();

                    $count_days = 0;
                    foreach($adays as $aday) {
                        $count_days++;
                        $this->addDay($k_month, $aday);
                    }
                }
            }

            if(!$this->l_filldays) {
                $this->addDay($k_month, $adate);
            }

            //$this->section->addText($week_day.'#'.$cdate);
            //$this->section->addText($adate->id.'#'.$adate->date_->format('d.m.Y'));

            if($l_performance==1) {
                $this->amonths[$k_month]['aprogramcodes'][$programcode]['apds'][$adate->id] = $adate;

                $this->aseasons[$season_id]['aprogramcodes'][$programcode]['apds'][$adate->id] = $adate;
            }

            $adate->order_ = 'k'.((int)$adate->eventtype_id<=0 ? '2' : '1').'#'.($adate->start_ ? $adate->start_->format('H:i') : '');
            $this->amonths[$k_month]['aweeks'][$week_date]['adays'][$cdate]['adates'][$adate->id] = $adate;


        }
    }

    function addDay($k_month, $aday) {

        $week_day = $aday->week;
        $d = $aday->date_->format('Ymd');

        if(empty($this->amonths[$k_month]['cmindate'])) {
            $this->amonths[$k_month]['cmindate'] = $aday->date_->format('d.m.Y');
        }
        $this->amonths[$k_month]['cmaxdate'] = $aday->date_->format('d.m.Y');

        if(!key_exists($week_day, $this->amonths[$k_month]['aweeks'])) {
            $this->amonths[$k_month]['aweeks'][$week_day] = array(
                'cmindate' => $aday->date_->format('d.m.Y'),
                'cmaxdate' => $aday->date_->format('d.m.Y'),
                'week' => $week_day,
                'pweek' => $aday->pweek,
                'adays' => array()
            );
        }

        $this->amonths[$k_month]['aweeks'][$week_day]['cmaxdate'] = $aday->date_->format('d.m.Y');

        if(!array_key_exists($d, $this->amonths[$k_month]['aweeks'][$week_day]['adays'])) {
            $this->amonths[$k_month]['aweeks'][$week_day]['adays'][$d] = array(
                'date_' => $aday->date_,
                'dow' => $aday->date_->format('N'),
                'holyday_id' => (int)$aday->holyday_id,
                'cdate' => $aday->date_->format('d.m.Y'),
                'cday' => $aday->weekday.', '.$aday->date_->format('d.m.'),
                'weekday' => $aday->weekday,
                'adates' => array()
            );
        }

    }

    function prepare_aweeks() {

        $this->aweeks = array();

        foreach ($this->adates_selected as $adate) {
            $cdate = $adate->date_->format('Ymd');
            $season_id = $adate->season_id;
            $week = $adate->week;

            $programcode = $this->reporttools->getProgramCode($adate);
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

            if (!array_key_exists($week, $this->aweeks)) {

                $this->aweeks[$week] = array(
                    'cmindate' => '',
                    'cmaxdate' => '',
                    'cmonth' => $this->reporttools->getMonthName($adate->date_).' '.$adate->year,
                    'adays' => array(),
                    'aprogramcodes' => array()
                );

                // Alle Tage des Monats holen
                $adays = TableRegistry::getTableLocator()->get('Adays')
                    ->find('all')
                    ->select([
                        'Adays.date_',
                        'Adays.pweek',
                        'Adays.week',
                        'Adays.weekday',
                        'Adays.holyday_id',
                    ])
                    ->where(["Adays.week"=>$week,
                        "OR"=> [
                            "Adays.season_id"=>$season_id,
                            "Adays.season_id IS NULL"
                            ]
                    ])
                    ->orderAsc('Adays.date_')
                    ->distinct();
                //print_r($adays);die;
                $count = 0;
                foreach($adays as $aday) {
                    $count++;

                    $d = $aday->date_->format('Ymd');

                    if($count==1) {
                        $this->aweeks[$week]['cmindate'] = $aday->date_->format('d.m.Y');
                    }
                    $this->aweeks[$week]['cmaxdate'] = $aday->date_->format('d.m.Y');

                    $this->aweeks[$week]['adays'][$d] = array(
                        'date_' => $aday->date_,
                        'dow' => $aday->date_->format('N'),
                        'holyday_id' => (int)$aday->holyday_id,
                        'cdate' => $aday->date_->format('d.m.Y'),
                        'cday' => $aday->weekday.', '.$aday->date_->format('d.m.'),
                        'weekday' => $aday->weekday,
                        'adates' => array()
                    );
                }
            }

            if($l_performance==1) {
                $this->aweeks[$week]['aprogramcodes'][$programcode]['apds'][$adate->id] = $adate;
            }

//print_r($this->aweeks[$week]['adays']); die;
            $this->aweeks[$week]['adays'][$cdate]['adates'][$adate->id] = $adate;
        }
    }

    function showMonths() {
        $count = 0;
        $this->count_weeks = 0;
        foreach($this->amonths as $this->amonth) {
            $count++;

            if ($count > 1) {
                $this->section->addPageBreak();
            }

            $this->showHeader_month();

            $this->prepare_table();

            $this->alegende = array();
            $this->adatenotes = array();


            foreach ($this->amonth['aweeks'] as $this->week => $this->aweek) {

                $this->showWeek();
            }

            $this->showLegende();

            //20240123 ONCUST-2870
            //Infobox ganz raus
            //$this->showDateNotes();

            if($this->l_besetzung==1) {
                //20240123 ONCUST-2870
                //Kein Seitenumbruch zwischen Plan und Besetzung
                //$this->section->addPageBreak();
                $this->section->addTextBreak();
                $this->showBesetzung($this->amonth['aprogramcodes']);
            }
        }
    }

    function showSeasons() {
        $this->l_show_header_week = false;
        $count_season = 0;
        foreach($this->aseasons as $this->aseason) {
            $count_season++;
            if ($count_season > 1) {
                $this->section->addPageBreak();
            }


            $this->showHeader_season();

            $count = 0;
            foreach ($this->aseason['amonths'] as $k_month) {
                if (!array_key_exists($k_month, $this->amonths)) {
                    continue;
                }

                $this->count_weeks = 0;

                $this->amonth = $this->amonths[$k_month];

                $count++;

                if ($count > 1) {
                    $this->section->addTextBreak();
                }

                $this->section->addTextBreak();
                $this->section->addText(htmlspecialchars($this->amonth['cmonth']), array_merge($this->styleFont_bold, $this->styleFont_10));

                $this->prepare_table();

                $this->alegende = array();
                $this->adatenotes = array();

                foreach ($this->amonth['aweeks'] as $this->week => $this->aweek) {
                    $this->showWeek();
                }
            }

            $this->showLegende();

            //20240123 ONCUST-2870
            //Infobox ganz raus
            //$this->showDateNotes();

            if ($this->l_besetzung == 1) {
                //20240123 ONCUST-2870
                //Kein Seitenumbruch zwischen Plan und Besetzung
                //$this->section->addPageBreak();
                $this->section->addTextBreak();
                $this->showBesetzung($this->aseason['aprogramcodes']);
            }
        }
    }

    function showWeeks() {
        $count = 0;
        foreach($this->aweeks as $this->week => $this->aweek) {
            $count++;

            if ($count > 1) {
                $this->section->addPageBreak();
            }

            $this->showHeader_week();

            $this->prepare_table();
            $this->alegende = array();
            $this->adatenotes = array();
            $this->showWeek();

            $this->showLegende();

            //20240123 ONCUST-2870
            //Infobox ganz raus
            //$this->showDateNotes();

            if($this->l_besetzung==1) {
                //20240123 ONCUST-2870
                //Kein Seitenumbruch zwischen Plan und Besetzung
                //$this->section->addPageBreak();
                $this->section->addTextBreak();
                $this->showBesetzung($this->aweek['aprogramcodes']);
            }
        }
    }

    function showHeader_week() {
        $this->section->addText(htmlspecialchars('WOCHENPLAN '.$this->reporttools->sysclient_name), array_merge($this->styleFont_bold, $this->styleFont_10), $this->styleAlign_center);
        $this->section->addText(htmlspecialchars(substr($this->week,3,2). '. Woche: '.$this->aweek['cmindate'].' - '.$this->aweek['cmaxdate']), $this->styleFont_10, $this->styleAlign_center);
    }

    function showHeader_month() {
        $this->section->addText(htmlspecialchars('DIENSTPLAN '.$this->reporttools->sysclient_name), array_merge($this->styleFont_bold, $this->styleFont_10), $this->styleAlign_center, $this->styleFont_10);
        $this->section->addText(htmlspecialchars($this->amonth['cmindate'].' - '.$this->amonth['cmaxdate']), $this->styleFont_10, $this->styleAlign_center);
        $this->section->addText(htmlspecialchars('Änderungen vorbehalten!'), array_merge($this->styleFont_bold, $this->styleFont_underline, $this->styleFont_10), $this->styleAlign_center);

        switch(true) {
            case $this->l_totals_dso:
                $this->section->addTextBreak();
                $this->section->addText(htmlspecialchars('Abkürzungen Dienstzählung:'), array_merge($this->styleFont_bold, $this->styleFont_10));
                $this->section->addText(htmlspecialchars('KD = Künstlerischer Dienst'), array_merge($this->styleFont_10));
                $this->section->addText(htmlspecialchars('NK = Nichtkünstlerischer Dienst'), array_merge($this->styleFont_10));
                $this->section->addText(htmlspecialchars('KR = Kumulierte Reisedienstzeiten'), array_merge($this->styleFont_10));

                break;
        }


        $this->section->addTextBreak();
        $this->section->addText(htmlspecialchars($this->amonth['cmonth']), array_merge($this->styleFont_bold, $this->styleFont_10));
    }

    function showHeader_season() {
        $this->section->addText(htmlspecialchars('DIENSTPLAN '.$this->reporttools->sysclient_name), array_merge($this->styleFont_bold, $this->styleFont_10), $this->styleAlign_center, $this->styleFont_10);
        $this->section->addText(htmlspecialchars($this->aseason['cmindate'].' - '.$this->aseason['cmaxdate']), $this->styleFont_10, $this->styleAlign_center);
        $this->section->addText(htmlspecialchars('Änderungen vorbehalten!'), array_merge($this->styleFont_bold, $this->styleFont_underline, $this->styleFont_10), $this->styleAlign_center);
    }


    function prepare_table() {
        $this->acol_widths = array();

        $w_day = 1.5;
        $w_time = 2;

        $w_location = 2;
        $w_dateinfo = 3+3+0.9*3;//4+3+0.9*3;
        $w_condsolo = 3.7;//2.7;

        $w_dso_kd = 0.9;
        $w_dso_nk = 0.9;
        $w_dso_rd = 0.9;

        $w_rsb_duties = 1.2;

        $w_rchb_da = 0.9;
        $w_rchb_he = 0.9;
        $w_rchb_sz = 0.9;
        $w_rchb_and = 0.9;

        switch(true) {
            case $this->l_totals_dso:
                $w_dateinfo -= $w_dso_kd;
                $w_dateinfo -= $w_dso_nk;
                $w_condsolo -= $w_dso_rd;
                break;

            case $this->l_totals_rsb || $this->l_totals_rkc:
                $w_dateinfo -= $w_rsb_duties;
                break;

            case $this->l_totals_rchb:
                $w_dateinfo -= $w_rchb_da;
                $w_dateinfo -= $w_rchb_he;
                $w_dateinfo -= $w_rchb_sz;
                $w_condsolo -= $w_rchb_and;
                break;

        }

        $this->acol_widths[$this->cn_day] = Converter::cmToTwip($w_day);
        $this->acol_widths[$this->cn_time] = Converter::cmToTwip($w_time);
        $this->acol_widths[$this->cn_location] = Converter::cmToTwip($w_location);
        $this->acol_widths[$this->cn_dateinfo] = Converter::cmToTwip($w_dateinfo);
        $this->acol_widths[$this->cn_condsolo] = Converter::cmToTwip($w_condsolo);


        switch(true) {
            case $this->l_totals_dso:
                $this->acol_widths[$this->cn_kd] = Converter::cmToTwip($w_dso_kd);
                $this->acol_widths[$this->cn_nk] = Converter::cmToTwip($w_dso_nk);
                $this->acol_widths[$this->cn_kr] = Converter::cmToTwip($w_dso_rd);
                break;

            case $this->l_totals_rsb || $this->l_totals_rkc:
                $this->acol_widths[$this->cn_rsb_duties] = Converter::cmToTwip($w_dso_rd);
                break;

            case $this->l_totals_rchb:
                $this->acol_widths[$this->cn_rchb_da] = Converter::cmToTwip($w_rchb_da);
                $this->acol_widths[$this->cn_rchb_he] = Converter::cmToTwip($w_rchb_he);
                $this->acol_widths[$this->cn_rchb_sz] = Converter::cmToTwip($w_rchb_sz);
                $this->acol_widths[$this->cn_rchb_and] = Converter::cmToTwip($w_rchb_and);
                break;
        }

        $this->w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $this->w_table,
            'unit' => TblWidth::TWIP
        );

        $styleCell = array();

        $this->table = $this->section->addTable($tableProperties);
        
    }

    function showHeader_table_week() {

        if(!$this->l_show_header_week && $this->count_weeks > 1) {return;}

        $this->table->addRow();


        $styleCell = $this->aborder_tbrl;

        $w_merge = 0;

        foreach($this->acol_widths as $k=>$col_width) {
            if($k<($this->cn_day)) {
                $w_merge += $col_width;
            }
        }

        //$cell = $this->table->addCell($w_merge, array_merge($styleCell, $cellColSpan));

        //$this->showHeaderTbl();
        $cell = $this->table->addCell($this->acol_widths[$this->cn_day], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Tag'), array_merge($this->styleFont_bold), $this->styleAlign_center);

        $cell = $this->table->addCell($this->acol_widths[$this->cn_time], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Uhrzeit'), array_merge($this->styleFont_bold), $this->styleAlign_center);

        $cell = $this->table->addCell($this->acol_widths[$this->cn_location], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Ort'), array_merge($this->styleFont_bold), $this->styleAlign_center);

        //$cell = $this->table->addCell($this->acol_widths[$this->cn_eventtype], array_merge($styleCell));
        //$cell->addText(htmlspecialchars('Terminart'), array_merge($this->styleFont_bold), $this->styleAlign_center);


        $cell = $this->table->addCell($this->acol_widths[$this->cn_dateinfo], array_merge($styleCell));
        //20240123 ONCUST-2870
        //“Termin/Programm” in der Spaltenüberschrift rausnehmen (bleibt leer)
        //$cell->addText(htmlspecialchars('Terminart/Programm'), array_merge($this->styleFont_bold), $this->styleAlign_center);

        $cell = $this->table->addCell($this->acol_widths[$this->cn_condsolo], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Dirigent:in/Solist:in'), array_merge($this->styleFont_bold), $this->styleAlign_center);

        switch(true) {
            case $this->l_totals_dso:

                $cell = $this->table->addCell($this->acol_widths[$this->cn_kd], array_merge($styleCell));
                $cell->addText(htmlspecialchars('KD'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_nk], array_merge($styleCell));
                $cell->addText(htmlspecialchars('NK'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_kr], array_merge($styleCell));
                $cell->addText(htmlspecialchars('KR'), array_merge($this->styleFont_bold), $this->styleAlign_center);
                break;

            case $this->l_totals_rsb || $this->l_totals_rkc:
                $cell = $this->table->addCell($this->acol_widths[$this->cn_rsb_duties], array_merge($styleCell));
                $cell->addText(htmlspecialchars('Dienste'), array_merge($this->styleFont_bold), $this->styleAlign_center);
                break;

            case $this->l_totals_rchb:
                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_da], array_merge($styleCell));
                $cell->addText(htmlspecialchars('Da.'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_he], array_merge($styleCell));
                $cell->addText(htmlspecialchars('He.'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_sz], array_merge($styleCell));
                $cell->addText(htmlspecialchars('Sz.'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_and], array_merge($styleCell));
                $cell->addText(htmlspecialchars('And.'), array_merge($this->styleFont_bold), $this->styleAlign_center);

                break;
        }
    }

    function showWeek() {

        $this->count_weeks++;

        $styleCell = array();

        $this->kd_total = 0;
        $this->nk_total = 0;
        $this->kr_total = 0;

        $this->duties_total = 0;

        $this->duties_rchb_da_total = 0;
        $this->duties_rchb_he_total = 0;
        $this->duties_rchb_sz_total = 0;
        $this->duties_rchb_and_total = 0;

        if($this->reporttype == 'M') {
            $this->table->addRow();
            $cellColSpan = array('gridSpan' => (sizeof($this->acol_widths)));
            $cell = $this->table->addCell($this->w_table, array_merge($styleCell, $cellColSpan));
            $cell->addTextBreak();
            $cell->addText(htmlspecialchars('KW '.substr($this->aweek['week'],3,2)), $this->styleFont_bold, $this->styleAlign_center);
        }

        $this->showHeader_table_week();

        $count_days = 0;
        foreach($this->aweek['adays'] as $aday) {
            $count_days++;

            $aborder = $this->aborder_tbrl;

            if(sizeof($aday['adates']) > 1) {
                $aborder = array_merge($aborder, $this->styleCell_borderBottom_none);
            }

            $styleCell = $this->aborder_tbrl;


            $this->table->addRow();

            $styleCell_day = array();
            if ($aday['dow'] >= 6 or $aday['holyday_id'] > 0) {
                $styleCell_day = $this->styleFont_bold;
            }

            //, $this->cellRowSpan
            $borderBottom = [];
            if(sizeof($aday['adates'])>1) {
                $borderBottom = $this->styleCell_borderBottom_none;
            }

            $cell = $this->table->addCell($this->acol_widths[$this->cn_day], array_merge($styleCell, $borderBottom));
            $cell->addText(htmlspecialchars($aday['cday']), $styleCell_day);

            //$cell->addText(sizeof($aday['adates']).'#'.print_r(array_merge($styleCell, $borderBottom), true));

            if(sizeof($aday['adates']) == 0) {
                $w_merge = 0;

                $gridcols = 0;
                foreach($this->acol_widths as $k=>$col_width) {
                    //if($k>($this->cn_condsolo)) {
                    if($k>($this->cn_day)) {
                        $gridcols++;
                        $w_merge += $col_width;
                    }
                }

                $cellColSpan = array('gridSpan' => ($gridcols));
                $cell = $this->table->addCell($w_merge, array_merge($styleCell, $cellColSpan));
                //$cell->addText($gridcols);
            }

            // Reisedienste zählen
            /*
                **** 1.Hin-und Rückreisen bei Tourneen werden zusammengerechnet: Mehr als 4h sind 1 Dienst, mehr als 10h  sind 2 Dienste.
                **** 2. Innerhalb einer Tour zählen
                **** 2.a) Reisen ab 4h als 1 Dienst.
                **** 2.b)Reisen unter 45 min werden nicht gezählt.
                **** 2.c) Reisen über 45 min werden zusammenaddiert und ab 4h als 1 Dienst abgerechnet.
                Wobei diese Zählung (ab 4h gleich 1 Dienst) vielleicht gar nicht von OPAS realisiert werden muss, sondern wir das dann manuell überprüfen.
                Wichtig wäre, dass OPAS uns Reisen von ab 45 min bis zu 4h in der Spalte KR als Minutenreisezeit ausgibt
                (Hin- und Rückreisen von Berlin nach Berlin allerdings ausgeschlossen- denn hier greift die Regel 1, dass Hin- und Rückreisen zusammengerechnet werden).
             * */

            $lastReiseDate_id = -1;
            $sumDuration_Reisen = 0;

            foreach($aday['adates'] as $this->adate) {
                $eventtype = ($this->adate->seventtype ? $this->adate->seventtype->name : '');

                $nduration = (int)substr($this->adate->duration, 0, 2) * 60 + (int)substr($this->adate->duration, 3, 2);

                if (!($eventtype == 'Reise') || $nduration<45) {
                    continue;
                }

                $lastReiseDate_id = $this->adate->id;
                $sumDuration_Reisen += $nduration;
            }

            //20240222 ONCUST-2871
            //Gibt es an einem Tag mehrere Termine und davon sind welche ohne Terminart, werden diese als letzte an diesem Tag angezeigt (also erst alle Termine mit Terminart chronologisch an einem Tag und danach Termine ohne Terminart)
            uasort($aday['adates'], array($this,"usort_dates"));

            $count_dates = 0;
            foreach($aday['adates'] as $this->adate) {
                $count_dates++;
                $startend = $this->reporttools->getTime($this->adate);
                $duties = $this->adate->duties;

                $eventtype = ($this->adate->seventtype ? $this->adate->seventtype->name : '');

                // Terminart+text
                $text = $this->adate->text;

                $l_performance = ($this->adate->seventtype ? $this->adate->seventtype->l_performance : 0);
                $nduration = (int)substr($this->adate->duration, 0, 2) * 60 + (int)substr($this->adate->duration, 3, 2);
                $dress = ($this->adate->sdress ? $this->adate->sdress->name : '');

                $notes = $this->adate->notes;
                if(mb_substr(mb_strtoupper($notes),0,4) == 'DIR.') {
                    $notes = '';
                }



                if($notes>'') {
                    $this->adatenotes[] = array(
                        'cdate' => $this->adate->date_->format('d.m.Y'),
                        'notes' => $notes
                    );
                }



                $location = '';
                if ($this->adate->locationaddress) {
                    $location = ($this->adate->locationaddress->name3 > '' ? $this->adate->locationaddress->name3 : $this->adate->locationaddress->name1);
                    //$location = $this->adate->id.'#'.$location.'#';

                    if(!key_exists($this->adate->location_id, $this->alegende)) {
                        $this->alegende[$this->adate->location_id] = [
                            'name3' => $this->adate->locationaddress->name3,
                            'name1' => $this->adate->locationaddress->name1
                        ];
                    }

                }

                /*** In KD werden im Prinzip fast alle Dienste eingetragen, die bisher auch in der Spalte ohne Bezeichnung standen.
                *** Die Ausnahmen bilden die Termine mit den Terminarten "Reise", "Vorsingen", "Chorversammlung". Diese müssen nun in die Spalte NK.
                */

                $nk = 0;
                $kd = 0;
                $kr = 0;

                switch(true) {
                    case $eventtype == 'Reise':
                        $kr = $nduration;

                        //**** 2.a) Reisen ab 4h als 1 Dienst.
                        //**** 2.b)Reisen unter 45 min werden nicht gezählt.

                        if($this->adate->id == $lastReiseDate_id) {
                            switch(true) {
                                case $sumDuration_Reisen/60>4:
                                    $nk = 1;
                                    break;
                                case $sumDuration_Reisen/60>10;
                                    $nk = 2;
                                    break;
                            }
                        }
                        break;
                    case in_array(mb_strtoupper($eventtype), [mb_strtoupper('Vorsingen'), mb_strtoupper('Chorversammlung')]):
                        $nk = $duties;
                        break;

                    default:
                        $kd = $duties;
                        break;
                }

                $this->prepare_rchb_duties();

                $this->kd_total += $kd;
                $this->nk_total += $nk;
                $this->kr_total += $kr;

                $this->duties_total += $duties;

                $aborder = $this->aborder_tbrl;
                $styleCell = $aborder;

                if ($count_dates > 1) {

                    $aborder = array_merge($aborder, $this->styleCell_borderTop_none);

                    $styleCell = $aborder;

                    $this->table->addRow();

                    /*if ($this->cn_week > 0) {
                        $this->table->addCell($this->acol_widths[$this->cn_week], array_merge($styleCell, $this->cellRowContinue));
                    }
                    */
                    //$this->table->addCell($this->acol_widths[$this->cn_weekday], array_merge($styleCell, $this->cellRowContinue));
                    //20250407 ONCUST-3103
                    //Finden an einem Wochentag mehrere Termine statt, verbinden sich die Datums-Wochentagszellen.
                    // Beispiel: Es gibt nur eine verbundene Zelle für den Fr, 04.04. Das soll bitte nicht passieren,
                    // es sollen weiter zwei Zellen sein, die aber nicht durch eine sichtbare Linie getrennt sind.

                    //$this->table->addCell($this->acol_widths[$this->cn_day], array_merge($styleCell, $this->cellRowContinue));

                    $borderBottom = [];
                    if($count_dates<sizeof($aday['adates'])) {
                        $borderBottom = $this->styleCell_borderBottom_none;
                    }

                    $cell = $this->table->addCell($this->acol_widths[$this->cn_day], array_merge($styleCell, $this->styleCell_borderTop_none, $borderBottom));
                    //$cell->addText($count_dates.'#'.sizeof($aday['adates']).'#'.print_r(array_merge($styleCell, $this->styleCell_borderTop_none, $borderBottom), true));
                }

                $cell = $this->table->addCell($this->acol_widths[$this->cn_time], array_merge($styleCell));
                $cell->addText(htmlspecialchars($startend), array_merge([]));

                //$cell->addText(htmlspecialchars($this->adate->id), array_merge([]));
                //$cell->addText(htmlspecialchars($count_dates), array_merge([]));

                $cell = $this->table->addCell($this->acol_widths[$this->cn_location], array_merge($styleCell));
                $cell->addText(htmlspecialchars($location), array_merge([]));


                //$cell = $this->table->addCell($this->acol_widths[$this->cn_eventtype], array_merge($styleCell));
                $abold = ($l_performance==1 ? $this->styleFont_bold : array());

                $cell = $this->table->addCell($this->acol_widths[$this->cn_dateinfo], array_merge($styleCell));
                if(!empty($eventtype)) {
                    $this->showEventtype($cell, $eventtype, $abold);
                    //$cell->addText(htmlspecialchars($eventtype), $abold);
                }

                if(!empty($text) && strcmp($eventtype, $text)!==0) {
                    $cell->addText(htmlspecialchars($text));
                }

                //$cell->addText(htmlspecialchars('order_='.$this->adate->order_));


                //notes
                //20240123 ONCUST-2870
                //Zeige das Notizfeld in der Spalte Terminart unter den Termindetails (wie es schonmal war)
                if(!empty($notes)) {
                    $this->reporttools->addMemo($cell, $notes);
                }


                // AddAct
                $this->showAddAct($cell);

                // dress
                if(!empty($dress)) {
                    $cell->addText(htmlspecialchars($dress));
                }

                $this->showDateInfo($cell);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_condsolo], array_merge($styleCell));
                $this->showCondSolo($cell);

                switch(true) {
                    case $this->l_totals_dso:
                        $cell = $this->table->addCell($this->acol_widths[$this->cn_kd], array_merge($styleCell));
                        if ($kd <> 0) {
                            $cell->addText(htmlspecialchars($kd), array_merge([]), $this->styleAlign_center);
                        }

                        $cell = $this->table->addCell($this->acol_widths[$this->cn_nk], array_merge($styleCell));
                        if ($nk <> 0) {
                            $cell->addText(htmlspecialchars($nk), array_merge([]), $this->styleAlign_center);
                        }

                        $cell = $this->table->addCell($this->acol_widths[$this->cn_kr], array_merge($styleCell));
                        if ($kr <> 0) {
                            $cell->addText(htmlspecialchars($kr), array_merge([]), $this->styleAlign_center);
                        }
                        break;

                    case $this->l_totals_rsb || $this->l_totals_rkc:
                        $cell = $this->table->addCell($this->acol_widths[$this->cn_rsb_duties], array_merge($styleCell));
                        if ($duties <> 0) {
                            $cell->addText(htmlspecialchars($duties), array_merge([]), $this->styleAlign_center);
                        }
                    break;

                    case $this->l_totals_rchb:
                        $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_da], array_merge($styleCell));
                        if (!is_null($this->duties_rchb_da)) {
                            $cell->addText(htmlspecialchars($this->duties_rchb_da), array_merge([]), $this->styleAlign_center);
                        }

                        $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_he], array_merge($styleCell));
                        if (!is_null($this->duties_rchb_he)) {
                            $cell->addText(htmlspecialchars($this->duties_rchb_he), array_merge([]), $this->styleAlign_center);
                        }

                        $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_sz], array_merge($styleCell));
                        if (!is_null($this->duties_rchb_sz)) {
                            $cell->addText(htmlspecialchars($this->duties_rchb_sz), array_merge([]), $this->styleAlign_center);
                        }
                        //$cell->addText(htmlspecialchars((($this->adate->seventtype) ? $this->adate->seventtype->logic_2 : '')), array_merge([]), $this->styleAlign_center);
                        //$cell->addText(htmlspecialchars(cduties_rchb_sz), array_merge([]), $this->styleAlign_center);


                        $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_and], array_merge($styleCell));
                        if (!is_null($this->duties_rchb_and)) {
                            $cell->addText(htmlspecialchars($this->duties_rchb_and), array_merge([]), $this->styleAlign_center);
                        }

                        break;
                }
            }
        }
        $this->showTotals_week();
    }

    public function showEventtype($cell, $eventtype, $abold) {
        $cell->addText(htmlspecialchars($eventtype), $abold);
    }

    function usort_dates($a, $b) {
        //20240222 ONCUST-2871
        //Gibt es an einem Tag mehrere Termine und davon sind welche ohne Terminart, werden diese als letzte an diesem Tag angezeigt (also erst alle Termine mit Terminart chronologisch an einem Tag und danach Termine ohne Terminart)

        //$frm_order_a = ((int)$a->eventtype_id<=0 ? 2:1).'#'.($a->start_ ? $a->start_->format('H:i') : '');
        //$frm_order_b = ((int)$b->eventtype_id<=0 ? 2:1).'#'.($b->start_ ? $b->start_->format('H:i') : '');

        $frm_order_a = $a->order_;
        $frm_order_b = $b->order_;

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function prepare_rchb_duties() {
        $this->duties_rchb_da = null;
        $this->duties_rchb_he = null;
        $this->duties_rchb_sz = null;
        $this->duties_rchb_and = null;

        $date_ = $this->adate->date_;
        $l_performance = (($this->adate->seventtype) ? $this->adate->seventtype->l_performance : 0);
        $eventtypegroup = (($this->adate->seventtype && $this->adate->seventtype->seventtypegroup) ? $this->adate->seventtype->seventtypegroup->name : '');
        $eventtype_logic_2 = (($this->adate->seventtype) ? $this->adate->seventtype->logic_2 : '');
		$l_date_logic_2 = $this->adate->l_logic_2;

        $duties = $this->adate->duties;
        $text = $this->adate->text;

        //*** Prüfen ob an dem Tag bei irgendeinem Termin die He-Da-Duties
        //*** manuell eingegeben sind.(Number_1<>NULL OR Number_2<>NULL)
        //*** Prüfung läuft über ganzen Tag und nicht nur über ausgew. Termine

        //*** 20150113
        //*** (NVL(Date_Number_1,0)>0 OR NVL(Date_Number_2,0)>0)

        $l_numbers = false;
        foreach($this->adates_selected as $adate) {
            if($adate->date_ == $date_) {
                // Dienste Da. oder He. sind gefüllt, 0 ist auch gefüllt
                if(!is_null($adate->number_1) || !is_null($adate->number_2)) {
                    $l_numbers = true;
                }
            }
        }

        //*** Bei mind. einem Termin sind die Duties manuell eingetragen
        if($l_numbers) {
            $this->duties_rchb_da = $this->adate->number_1;
            $this->duties_rchb_he = $this->adate->number_2;
        } else {
            //*** Duties werden nach aDates.Text verteilt
            // entweder HE. im Text oder weder Da. noch HE.
            if(strpos(mb_strtoupper($text), 'DA.') === false || strpos(mb_strtoupper($text), 'HE.') !== false) {
                $this->duties_rchb_he = $duties;
            }

            // entweder DA. im Text oder weder Da. noch HE.
            if(strpos(mb_strtoupper($text), 'DA.') !== false || strpos(mb_strtoupper($text), 'HE.') === false) {
                $this->duties_rchb_da = $duties;
            }
        }
        /*
        *** Dienst Damen (Künstlerische Dienste: Proben, Konzerte etc.)
		*** Dienst Herren (Künstlerische Dienste: Proben, Konzerte etc.)
		*** Szenischer Dienst
        *** Anderer Dienst (Vorsingen, Ansingen, Reisedienste, Versammlungen, Sicherheitsunterweisung, Sonstiges)


        *** 20150112
        *** -	Die szenischen Dienste müssen bitte auch in den Spalten Dienste Herren und Dienste Damen gelistet werden.
		*** Anderenfalls stimmt meine Bilanz unten nicht (Formeln)
        *** -	Ist es denkbar, dass der Bericht hinsichtlich der Dienstzahl ausschließlich auf das Fenster Dienste zurückgreift?
		*** Bitte auch dann, wenn in Zusätzliche Daten mehr Dienste stehen? Beispiel: am 25. Februar muss ich für Damen und Herren 3 Dienste zählen. Bei der Abrechnung der szenischen Dienste werden aber nur 2 Dienste gezählt.
        */
        //$this->cduties_rchb_sz = mb_strtolower($eventtype_logic_2).'#'.strpos(mb_strtolower($eventtype_logic_2), 'szenisch').'#'.$l_date_logic_2;
        switch(true) {
            case $l_performance==1 || $eventtypegroup == 'Probe':
                break;

                //*** Szenischer Dienst
            case strpos(mb_strtolower($eventtype_logic_2), 'szenisch') !== false && $l_date_logic_2 == 1:
                $this->duties_rchb_sz = $duties;

                break;
            //*** Anderer Dienst (Vorsingen, Ansingen, Reisedienste, Versammlungen, Sicherheitsunterweisung, Sonstiges)
            default:
                $this->duties_rchb_and = $duties;
                break;
        }

        if(!is_null($this->duties_rchb_da)) {
            $this->duties_rchb_da_total += $this->duties_rchb_da;
        }
        if(!is_null($this->duties_rchb_he)) {
            $this->duties_rchb_he_total += $this->duties_rchb_he;
        }
        if(!is_null($this->duties_rchb_sz)) {
            $this->duties_rchb_sz_total += $this->duties_rchb_sz;
        }
        if(!is_null($this->duties_rchb_and)) {
            $this->duties_rchb_and_total += $this->duties_rchb_and;
        }
    }

    function showTotals_week() {
        if($this->reporttype == 'S') {
            return;
        }
        $this->table->addRow();


        $styleCell = $this->aborder_tbrl;
        $cellColSpan = array('gridSpan' => ($this->cn_condsolo));
        $w_merge = 0;

        foreach($this->acol_widths as $k=>$col_width) {
            if($k<=($this->cn_condsolo)) {
                $w_merge += $col_width;
            }
        }

        $cell = $this->table->addCell($w_merge, array_merge($styleCell, $cellColSpan));


        switch(true) {
            case $this->l_totals_dso:
                $cell = $this->table->addCell($this->acol_widths[$this->cn_kd], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->kd_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_nk], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->nk_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_kr], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->kr_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                break;

            case $this->l_totals_rsb || $this->l_totals_rkc:
                $cell = $this->table->addCell($this->acol_widths[$this->cn_rsb_duties], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->duties_total), array_merge($this->styleFont_bold), $this->styleAlign_center);
                break;

            case $this->l_totals_rchb:
                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_da], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->duties_rchb_da_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_he], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->duties_rchb_he_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_sz], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->duties_rchb_sz_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                $cell = $this->table->addCell($this->acol_widths[$this->cn_rchb_and], array_merge($styleCell));
                $cell->addText(htmlspecialchars($this->duties_rchb_and_total), array_merge($this->styleFont_bold), $this->styleAlign_center);

                break;
        }
    }

    function showDateInfo($cell) {
        //Program
        $this->showProgram($cell);

        //hotels
        $this->showHotels($cell);

        $this->showSendetermin($cell);
    }

    // 20240528 ONCUST-3103
    //Zeige bei Konzerten in der 4 Spalte unter dem Programm das Feld “Sendetermin” aus Zusätzliche Daten (siehe Termin Zusätzliche Daten), wenn es gefüllt ist. Zeige es kursiv an.
    function showSendetermin($cell) {
        for($i=1; $i<=10; $i++) {
            $add_text = 'text_'.$i;

            if($this->adate->seventtype && $this->adate->seventtype->$add_text == 'Sendetermin' && !empty($this->adate->$add_text)) {
                $cell->addText(htmlspecialchars($this->adate->$add_text), array_merge($this->styleFont_italic));
            }
        }
    }

    function showAddAct($cell) {
        foreach ($this->adate->adate_activities as $aaddact) {
            $addact = $aaddact->seventtype->name.(!empty($aaddact->text) ? ', ' : '').$aaddact->text;
            $cell->addText(htmlspecialchars($addact));
        }
    }

    function showHotels($cell) {
        foreach ($this->adate->adate_persons as $arow) {

            if($arow->saddress) {

                $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');

                if(!(mb_strtoupper($addressgroup) == 'HOTELS')) {continue;}

                $zip_place = trim($arow->saddress->zipcode . ' ' . $arow->saddress->place);

                $cell->addText(htmlspecialchars($arow->saddress->name));

                if(!empty($arow->saddress->street)) {
                    $cell->addText(htmlspecialchars($arow->saddress->street));
                }

                if(!empty($zip_place)) {
                    $cell->addText(htmlspecialchars($zip_place));
                }

                $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
                    ->find('all')
                    ->contain('Snumbertypes', function (Query $query) {
                        return $query->select([
                            'Snumbertypes.l_phone',
                            'Snumbertypes.l_mobile',
                            'Snumbertypes.l_email',
                            'Snumbertypes.name'
                        ]);
                    })
                    ->where('address_id = ' .$arow->address_id)
                    ->order('SaddressNumbers.number_order')
                    ->all();

                foreach($aaddressnumbers as $anumber) {
                    $numbertype_code = ($anumber->snumbertype ? $anumber->snumbertype->code : '');
                    $cell->addText(htmlspecialchars(
                        (!empty($numbertype_code) ? $numbertype_code.': ': '').
                        $anumber->number_
                    ));
                }
            }
        }
    }

    function showProgram($cell) {

        $styleTabs = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(0.75)))
        );

        $l_performance = ($this->adate->seventtype ? $this->adate->seventtype->l_performance : 0);

        foreach ($this->adate->adate_works as $adate_work) {

            $title = ($adate_work->title2>'' ? $adate_work->title2 : $adate_work->swork->title1);

            $composer = ($adate_work->swork->scomposer ? $adate_work->swork->scomposer->lastname : '');

            $l_intermission = $adate_work->swork->l_intermission;




            //- bei Aufführungen: Komponist (nur Nachname) und adate_works.title2, wenn nicht gefüllt dann sworks.title1
            //- bei allen anderen Terminarten: Komponist (Nachname)
            if($l_performance==1) {
                $work = $composer.', '.$title;
            } else {
                $work = $composer;
            }

            if ($l_intermission == 1) {
                $work = $composer;
            }

            $cell->addText(htmlspecialchars($work), [], $styleTabs);
        }

    }

    function showCondSolo($cell) {


        // 20231207 ONCUST-2527
        // Nur Nachnamen und durch / voneinander trennen
        //20240217 ONCUST-2870
        //Bei Dirigent und Solist:in etc. doch Vor- und Nachnamen eintragen


        /* 20240222 ONCUST-2871
        Zeige als 1. Dirigent (Vorname Nachname, Funktion)
        Zeige als 2. Solisten (Vorname Nachname, Instrument)
        Zeige als 3. Sonstige Beteiligte (Vorname Nachname, Funktion) → relevant sind hier alle mit der Adressgruppe “Fremdleistung fett”
        Die Personen werden mit Zeilenumbruch getrennt (immer und ohne Slash)

        20240229
        Ich sehe noch keine Sonstigen Beteiligten mit der Adressgruppe Fremdleistung / Fett.
        */

        //Zeige als 1. Dirigent (Vorname Nachname, Funktion)
        //$cell->addText(htmlspecialchars($this->adate->id));
        //$cell->addText(htmlspecialchars($this->adate->conductor_id));
        $conductor = ($this->adate->conductoraddress ? $this->reporttools->getLongName($this->adate->conductoraddress->name2, '', $this->adate->conductoraddress->name1) : '');
        if(!empty($conductor)) {

            $sex = ($this->adate->conductoraddress->saddress_persdata ? $this->adate->conductoraddress->saddress_persdata->sex : '');
            $cell->addText(htmlspecialchars($conductor.', '.($sex=='F' ? 'Dirigentin' : 'Dirigent')));
        }
        //$condsolo = ($this->adate->conductoraddress ? $this->adate->conductoraddress->name1 : '');

        //Zeige als 2. Solisten (Vorname Nachname, Instrument)

        $asoloists = $this->reporttools->getSoloists($this->adate->id);
        foreach($asoloists as $asoloist) {
            $instrument = ($asoloist->sinstrinstrument ? $asoloist->sinstrinstrument->name : '');

            if(mb_strtolower($instrument == 'chor')) {
                $instrument = '';
            }

            //20240217 ONCUST-2870
            //Bei Dirigent und Solist:in etc. doch Vor- und Nachnamen eintragen
            $soloist = $this->reporttools->getLongName($asoloist->saddress->name2, '', $asoloist->saddress->name1);
            //$soloist = $asoloist->saddress->name1;

            $notes = ($asoloist->notes>'' ? ' ('.$asoloist->notes.')' : '');

            $soloist .= (!empty($instrument) ? ', ' : '').$instrument;

            //$soloist .= '#'.$asoloist->artist_id;
            //$soloist .= '#'.$asoloist->artist_order2;
            $cell->addText(htmlspecialchars($soloist));
            //$condsolo .= (!empty($condsolo) ? ' / ' : '').$soloist;
        }

        //Zeige als 3. Sonstige Beteiligte (Vorname Nachname, Funktion) → relevant sind hier alle mit der Adressgruppe “Fremdleistung fett”



        //20241206 ONCUST-3103
        //Zusammenfassung Wunsch Kunde:
        //Zeige alle Sonstigen Beteiligten unabhängig ihrer Adressgruppe
        //Zeige Vorname Namen, Funktion → wenn nicht gefüllt, zeige Instrument

        foreach ($this->adate->adate_persons as $aperson) {
            //$this->section->addText($aperson->date_id.'#'.$aperson->artist_id.'#'.$aperson->addressgroup_id);

            $addressgroup = ($aperson->saddressgroup ? $aperson->saddressgroup->name : '');
            $instrument = ($aperson->sinstrinstrument ? $aperson->sinstrinstrument->name : '');
            $addressfunctionitem = ($aperson->saddressfunctionitem ? $aperson->saddressfunctionitem->name : '');
            $name = $this->reporttools->getLongName($aperson->saddress->name2, '', $aperson->saddress->name1);
            $person = $name . ', ' . $addressfunctionitem . ($aperson->notes > '' ? ', ' : '') . $aperson->notes;

            $if = (!empty($addressfunctionitem) ? $addressfunctionitem : $instrument);

            //Fremdleistung / fett
            //if(strpos($addressgroup, 'Fremdleistung') !== false) {
                $person = $name . ', ' . $addressfunctionitem . ($aperson->notes > '' ? ', ' : '') . $aperson->notes;
            //}

            $cell->addText(htmlspecialchars($person));
        }

        //if($condsolo>'') {
        //    $cell->addText(htmlspecialchars($condsolo));
        //}
        //$this->showSoloists($condsol);

    }

    function showDateNotes() {


        //Infobox immer anzeigen, auch wenn leer
/*        if (sizeof($this->adatenotes) == 0) {
            return;
        }
*/
        $this->section->addTextBreak();

        $acol_widths = array();

        $acol_widths[] = Converter::cmToTwip(2);
        $acol_widths[] = Converter::cmToTwip(8);

        $w_table = 0;
        foreach ($acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginTop' => Converter::cmToTwip(0.05),
            'cellMarginBottom' => Converter::cmToTwip(0.05),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $table = $this->section->addTable($tableProperties);


        $table->addRow();
        $styleCell = array_merge($this->styleCell_borderTop, $this->styleCell_borderLeft, $this->styleCell_borderRight);
        $cell = $table->addCell($w_table, array_merge($styleCell, $this->cellColSpan2));
        $cell->addText(htmlspecialchars('Infobox'), $this->styleFont_bold);

        $count = 0;
        foreach ($this->adatenotes as $adatenote) {
            $count++;

            $styleCell_btlr = array_merge($this->styleCell_borderTop_none, $this->styleCell_borderLeft, $this->styleCell_borderRight, $this->styleCell_borderBottom_none);

            if($count==sizeof($this->adatenotes)) {
                $styleCell_btlr = array_merge($styleCell_btlr, $this->styleCell_borderBottom);
            }

            $table->addRow();

            $cell = $table->addCell($acol_widths[0], array_merge($styleCell_btlr, $this->styleCell_borderRight_none));
            $cell->addText(htmlspecialchars($adatenote['cdate']));

            $cell = $table->addCell($acol_widths[1], array_merge($styleCell_btlr, $this->styleCell_borderLeft_none));

            $this->reporttools->addMemo($cell, $adatenote['notes']);
        }

        if($count==0) {
            $table->addRow();
            $cell = $table->addCell($acol_widths[0], array_merge($styleCell_btlr));

            $cell = $table->addCell($acol_widths[1], array_merge($styleCell_btlr));

        }
    }

    function showLegende()
    {
        $this->section->addTextBreak();

        if (sizeof($this->alegende) == 0) {
            return;
        }

        $acol_widths = array();

        $acol_widths[] = Converter::cmToTwip(4);
        $acol_widths[] = Converter::cmToTwip(6);

        $w_table = 0;
        foreach ($acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'black',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginTop' => Converter::cmToTwip(0.05),
            'cellMarginBottom' => Converter::cmToTwip(0.05),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );

        $styleCell = array();
        $styleFont = array();


        $table = $this->section->addTable($tableProperties);


        $table->addRow();
        $cell = $table->addCell($acol_widths[0], array_merge($styleCell));
        $cell->addText(htmlspecialchars('Spielort - Legende'), $this->styleFont_bold);
        $cell = $table->addCell($acol_widths[1], array_merge($styleCell));

        foreach ($this->alegende as $alocation) {

            $table->addRow();
            $cell = $table->addCell($acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($alocation['name3']));
            $cell = $table->addCell($acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($alocation['name1']));
        }
    }

    function showBesetzung($aprogramcodes) {
        foreach($aprogramcodes as $aprogramcode) {
            $fpd = null;
            $count = 0;
            foreach ($aprogramcode['apds'] as $adate) {
                $count++;
                if ($count == 1) {
                    $fpd = $adate;
                    $this->section->addText(htmlspecialchars(($adate->sproject ? $adate->sproject->name : '')), array_merge($this->styleFont_bold));
                    $this->section->addTextBreak();
                }

                $location = '';
                if ($adate->locationaddress) {
                    $location = $adate->locationaddress->place . ($adate->locationaddress->place > '' ? ', ' : '') . $adate->locationaddress->name1;
                }

                //$persons = $this->getPersons_PD($adate);

                $pd = '';
                if($adate->date_) {
                    $pd = $adate->date_->format('d.m.Y');
                }
                $pd .=
                    ($location > '' ? ', ' : '') . $location;

                    //.
                    //($persons > '' ? ', ' : '') . $persons;

                $this->section->addText(htmlspecialchars($pd), array_merge($this->styleFont_bold));
            }

            if($count>0) {
                $this->section->addTextBreak();
                $this->showCondSolo_PD($fpd);

                $this->section->addTextBreak();
                $this->showProgram_PD($fpd);
            }
        }
    }

    /*function getPersons_PD($adate)
    {
        $persons = '';
        foreach ($adate->adate_persons as $aperson) {
            //$this->section->addText($aperson->date_id.'#'.$aperson->artist_id.'#'.$aperson->addressgroup_id);

            $addressgroup = ($aperson->saddressgroup ? $aperson->saddressgroup->name : '');
            $name = $this->reporttools->getLongName($aperson->saddress->name2, '', $aperson->saddress->name1);


            $person = $name . ', ' . $addressgroup . ($aperson->notes > '' ? ', ' : '') . $aperson->notes;
            $persons .= ($persons>''? ' / ' : '').$person;
        }

        return $persons;
    }
    */

    function showCondSolo_PD($adate) {

        $this->acondsolo = array();

        if($adate->conductoraddress) {
            $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);

            $textrun = $this->section->addTextRun();
            $textrun->addText(htmlspecialchars($conductor));
            $textrun->addText(htmlspecialchars(', Dirigent'), []);
        }

        $asoloists = $this->reporttools->getSoloists($adate->id);
        foreach($asoloists as $arow) {

            $name = '';
            if($arow->saddress) {
                $name = $this->reporttools->getLongName($arow->saddress->name2, '', $arow->saddress->name1);
            }

            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            if(strpos('chor', mb_strtolower($instrument)) !== false) {
                $instrument = '';
            }

            $textrun = $this->section->addTextRun();
            $textrun->addText(htmlspecialchars($name));
            if($instrument>'') {
                $textrun->addText(htmlspecialchars(', '.$instrument));
            }
        }

        if($adate->orchestraaddress) {
            $orchestra = $this->reporttools->getLongName($adate->orchestraaddress->name2, '', $adate->orchestraaddress->name1);

            $textrun = $this->section->addTextRun();
            $textrun->addText(htmlspecialchars($orchestra));
        }
    }

    function showProgram_PD($adate)
    {

        $oinstrumentation = new instrumentation_roc();

        $this->section->addText(htmlspecialchars('Programm'));

        foreach ($adate->adate_works as $adate_work) {
            $title =($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);

            $composer = trim($adate_work->swork->scomposer->firstname . ' ' . $adate_work->swork->scomposer->lastname);
            $bd = ($adate_work->swork->scomposer->birthyear > '' ? $adate_work->swork->scomposer->birthyear : '');
            $bd .= ($adate_work->swork->scomposer->deathyear > '' ? ' - ' . $adate_work->swork->scomposer->deathyear : '');

            $composer .= ($bd > '' ? ' (' . $bd . ')' : '');

            $nduration = (int)substr($adate_work->duration, 0, 2) * 60 + (int)substr($adate_work->duration, 3, 2);
            $premiere = ($adate_work->sworkpremiere->name > '' ? $adate_work->sworkpremiere->name : '');

            $l_intermission = $adate_work->swork->l_intermission;
            $encore = '';
            if ($adate_work->l_encore == 1) {
                $encore = '   Zugabe';
            }

            $cduration = '';
            if ($nduration <> 0) {
                $cduration = ' [' . $nduration . "']";
            }

            $oinstrumentation->datework_id = $adate_work->id;
            $oinstrumentation->getInstrumentation();


            if($l_intermission==0) {
                $this->section->addTextBreak();
                $this->section->addText(htmlspecialchars($composer));

                if ($adate_work->arrangement > '') {
                    $this->section->addText(htmlspecialchars($adate_work->arrangement), array_merge($this->styleFont_bold));
                }
            }

            $this->section->addText(htmlspecialchars($title . $cduration . $encore));

            if ($premiere > '') {
                $this->section->addText(htmlspecialchars($premiere));
            }
            if(!empty($oinstrumentation->instrumentation)) {
                $this->section->addText(htmlspecialchars($oinstrumentation->instrumentation));
            }

            $this->showMovements($adate_work->id);
        }
    }
    function showMovements($datework_id)
    {
        $this->table = new AdateworkMovementsTable();
        $query = $this->table
            ->find('all')
            ->where(['datework_id = ' => $datework_id]);
        $movements =$query->all();
        /* @var AdateworkMovement $movement */
        foreach ($movements as $movement) {
            $this->section->addText("\t".htmlspecialchars($movement->name), $this->styleFont_10);
        }
    }
}
