<?php


//namespace App\Reports\Tools;
namespace Customer\roc\reports;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;

/**
 * Class ReportTools_client
 * @package App\Reports\Tools
 *
 * bereitet kundenspetifische Daten vor, die in mehreren Berichten benutzt werden können
 */
class ReportTools_client
{
    public $user = '';
    public $user_email = '';
    public $user_phone = '';
    public $user_mobile = '';
    public $user_function = '';

    public $sysclient_name = '';

    public function __construct()
    {
        $this->initUser();
    }

    function initUser()
    {
        //$this->user = $_SESSION['Auth']['User']['sh'];
        $this->user = $_SESSION['Auth']['User']['fullname'];
        $address_id = (int)$_SESSION['Auth']['User']['address_id'];

        $sysclient_id = (int)$_SESSION['Auth']['User']['sysclient_id'];

        $sysclients = TableRegistry::getTableLocator()->get('Sysclients')
            ->find()
            ->where(['Sysclients.id'=>$sysclient_id])
            ->first();

        $this->sysclient_name = ($sysclients->name ? $sysclients->name : $sysclient_id);

        //$this->sysclient_name.='#'.print_r($sysclients,true);
        //$this->sysclient_name.=print_r($_SESSION['Auth']['User'],true);
        //$this->sysclient_name.='#'.print_r($_SESSION['Auth']['User'],true);

        $this->user_email = '';
        $this->user_phone = '';
        $this->user_mobile = '';
        $this->user_function = '';

        /*
        $where = "TRIM(UPPER(IFNULL(Opasusers.sh,''))) + '#' = '" . strtoupper($this->user) . "#' ";
        $opasusers = TableRegistry::getTableLocator()->get('Opasusers')
            ->find()
            ->select(['Opasusers.id', 'Opasusers.sh', 'Opasusers.name', 'Opasusers.fullname', 'Opasusers.address_id'])
            ->where($where)
            ->first();

        $address_id = ($opasusers->address_id ? $opasusers->address_id : 0);
        $this->user = ($opasusers->fullname ? $opasusers->fullname : '');
        */

        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.l_work',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' .$address_id)
        ->toArray();

        $this->user_email = '';
        foreach ($aaddressnumbers as $anumber) {
            //20210804
            //Bitte im Kopf - Email geschäftlich nehmen
            if($anumber->snumbertype->l_phone == 1 && $anumber->snumbertype->l_work==1) {
                $this->user_phone = ($anumber->number_ ? $anumber->number_ : '');
            }
            if($anumber->snumbertype->l_email == 1 && ($anumber->snumbertype->l_work==1 || empty($this->user_email))) {
                $this->user_email = ($anumber->number_ ? $anumber->number_ : '');
            }
            if($anumber->snumbertype->l_mobile == 1 && $anumber->snumbertype->l_work==1) {
                $this->user_mobile = ($anumber->number_ ? $anumber->number_ : '');
            }

            //$this->user_mobile.='X#'.($anumber->number_ ? $anumber->number_ : '').'#'.$anumber->snumbertype->l_work;
        }

        $aaddressfunctions = TableRegistry::getTableLocator()->get('SaddressFunctions')
            ->find('all')
            ->contain('Saddressfunctionitems', function (Query $query) {
                return $query->select([
                    'Saddressfunctionitems.id',
                    'Saddressfunctionitems.name'
                ]);
            })
            ->where('address_id = ' .$address_id)
            ->toArray();

        $this->user_function = 'address_id = ' .$address_id;
        foreach ($aaddressfunctions as $afunction) {
            //20210804
            //und die Funtkion aus dem Adressbuch zeigen.
            if($afunction->l_main == 1 || empty($this->user_function)) {
                $this->user_function = ($afunction->saddressfunctionitem ? $afunction->saddressfunctionitem->name : '');
            }
        }
    }

    public function getSoloists(int $date_id)
    {

        $where = 'AdateWorks.date_id=' . $date_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where([
                'AdateWorks.date_id' => $date_id,
                'AdateworkSoloists.artist_order2 IS NOT NULL'
            ])
            ->orderAsc('artist_order2')
            ->distinct();

        return $arows;
    }

    function prepareData_selected($postData, $model, $awhere=array())
    {
        if(sizeof($awhere)==0) {
            $awhere = ['Adates.id IN' => $postData['dataItemIds']];
        }

        $this->adates_selected = $model
            ->find('all')
            ->select()
            ->contain([
                'Sdatestatuses',
                'Sdresses',
                'Sseasons',
                'Sprojects' => ['Sprojecttypes'],
                'Seventtypes' => ['Seventtypegroups'],
                'Locationaddresses' => ['Scountries'],
                'Orchestraaddresses',
                'Conductoraddresses' => ['SaddressPersdata'],
                'AdateWorks' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sworks' => ['Scomposers'],
                                'AdateworkVocals' => ['Sinstrinstruments'],
                                'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses'],
                                'Sworkpremieres'
                            ])
                            ->orderAsc('AdateWorks.work_order');
                    },
                'AdatePersons' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Saddresses',
                                'Saddressgroups',
                                'Sinstrinstruments',
                                'Saddressfunctionitems'
                            ])
                            ->orderAsc('AdatePersons.person_order');
                    },
                'AdateActivities' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Seventtypes' => ['Seventtypegroups']
                            ])
                            ->orderAsc('AdateActivities.activity_order');
                    },
                'AdateSeries' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sseries'
                            ])
                            ->orderAsc('Sseries.serie_order');
                    }
            ])
            ->where($awhere)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC'])
            ->all();

        return $this->adates_selected;
    }

    function prepareContracts_selected($postData, $model, $awhere=array()) {

        $acontracts_selected = $model
            ->find('all')
            ->select()
            ->contain([
                'Sprojects' => ['Sprojecttypes'],
                'Sseasons',
                'Scontractstatus',
                'Scontractgroups',
                'Spayments',
                'Sjobs',
                'Sinstrinstruments',
                'Artistaddresses' => [
                    'Scountries',
                    'Ssalutations',
                    'Stitles',
                    'SaddressPersresidents' => ['Scountries'],
                    'SaddressPersons'=> ['Stitles', 'Ssalutations','Contactaddresses'=>['Stitles', 'Ssalutations']],
                    'SaddressPersdata' => [
                        'SaddresspersdataBanks' => function (Query $query) {
                            return $query
                                ->contain(['Sbanks'=>['Saddresses']])
                                ->where(['SaddresspersdataBanks.l_main' => 1]);
                        },
                        'SaddresspersdataPassports' => function (Query $query) {
                            return $query
                                ->contain(['Spassporttypes', 'Scountries'])
                                ->where(['SaddresspersdataPassports.l_main' => 1]);
                        }
                    ],
                    'SaddressFunctions' =>
                        function (Query $query) {
                            return $query
                                ->contain(['Saddressfunctionitems'])
                                ->order(['SaddressFunctions.l_main'=>'DESC', 'Saddressfunctionitems.name'=>'ASC']);
                        },
                    'SaddressAgents' => [
                        'SaddressPersons'=> ['Stitles', 'Ssalutations','Contactaddresses'=>['Stitles', 'Ssalutations']]
                    ]
                ],
                'Agentaddresses' => ['Scountries','SaddressPersdata' => ['SaddresspersdataBanks'=>['Sbanks'=>['Saddresses']]]],
                'AcontractDates' => function (Query $query) {
                    return $query
                        ->contain(['Adates' => ['Seventtypes', 'Sdresses',
                            'Locationaddresses',
                            'Conductoraddresses' => ['SaddressPersdata'],
                            'Orchestraaddresses',
                            'AdatePersons' =>
                                function (Query $query) {
                                    return $query
                                        ->contain([
                                            'Saddresses',
                                            'Saddressgroups',
                                            'Sinstrinstruments',
                                            'Saddressfunctionitems'
                                        ])
                                        ->orderAsc('AdatePersons.person_order');
                                },
                            'AdateWorks' =>
                                function (Query $query) {
                                    return $query
                                        ->contain([
                                            'Sworks' => ['Scomposers'],
                                            'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses'],
                                            'Sworkpremieres'
                                        ])
                                        ->orderAsc('AdateWorks.work_order');
                                },
                            'AdateActivities' =>
                                function (Query $query) {
                                    return $query
                                        ->contain([
                                            'Seventtypes' => ['Seventtypegroups']
                                        ])
                                        ->orderAsc('AdateActivities.activity_order');
                                }
                        ]])
                        ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC']);
                },
                'AcontractClauses' => ['Scontractclauses' => ['Scontractclausetypes']],
                'Aexpenses' => ['Sexpensetypes', 'Scurrencies', 'Adates']
            ])
            ->where(['Acontractsoloists.id IN' => $postData['dataItemIds']])
            ->order(['Acontractsoloists.id' => 'ASC']);

        return $acontracts_selected;
    }

    public function getLongName($tname2='', $tname5='', $tname1='')
    {
        // 20230620
        $tname5 = '';
        $name = trim(trim($tname2.' '.$tname5).' '.$tname1);
        return $name;
    }

    public function getShortName($tname2='', $tname5='', $tname1='', $format = '251')
    {
        // 20230620
        $tname5 = '';

        $name = trim($tname2);

        $name2 = substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : '');

        while (strpos(' ', $name) !== false) {
            $name = trim(substr($name, 0, strpos(' ', $name)));

            $name2 = trim($name2. ' '.substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : ''));
        }

        switch (true) {
            case $format == '251':
                $name = trim($name2.' '.trim(trim($tname5).' '.trim($tname1)));
                break;

            default:
                $name = trim(trim($tname5).' '.trim($tname1)).
                    (!empty($name2) ? ', ' : '').$name2;
                break;
        }

        return $name;
    }

    function getWeekday_de($date) {
        // N - Numerische ReprÃƒÂ¤sentation des Wochentages gemÃƒÂ¤ÃƒÅ¸ ISO-8601 (in PHP 5.1.0 hinzugefÃƒÂ¼gt) 1 (fÃƒÂ¼r Montag) bis 7 (fÃƒÂ¼r Sonntag)

        if (is_null($date)) {
            $dow = 0;
        } else {
            $dow = $date->format('N');
        }

        switch ($dow) {
            case 1:
                return 'Montag';
                break;
            case 2:
                return 'Dienstag';
                break;
            case 3:
                return 'Mittwoch';
                break;
            case 4:
                return 'Donnerstag';
                break;
            case 5:
                return 'Freitag';
                break;
            case 6:
                return 'Samstag';
                break;
            case 7:
                return 'Sonntag';
                break;
            default:
                return '';
                break;
        }

    }
    function getWeekday_en($date)
    {
        // N - Numerische ReprÃ¤sentation des Wochentages gemÃ¤ÃŸ ISO-8601 (in PHP 5.1.0 hinzugefÃ¼gt) 1 (fÃ¼r Montag) bis 7 (fÃ¼r Sonntag)

        if (is_null($date)) {
            $dow = 0;
        } else {
            $dow = $date->format('N');
        }

        switch ($dow) {
            case 1:
                return 'Monday';
                break;
            case 2:
                return 'Tuesday';
                break;
            case 3:
                return 'Wednesday';
                break;
            case 4:
                return 'Thursday';
                break;
            case 5:
                return 'Friday';
                break;
            case 6:
                return 'Saturday';
                break;
            case 7:
                return 'Sunday';
                break;
            default:
                return '';
                break;
        }

    }

    function getMonthName_de($date = null, $m = 0)
    {
        // n - Monatszahl, ohne fÃ¼hrende Nullen 1 bis 12
        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }

        switch ($m) {
            case 1:
                return 'Januar';
                break;
            case 2:
                return 'Februar';
                break;
            case 3:
                return 'MÃ¤rz';
                break;
            case 4:
                return 'April';
                break;
            case 5:
                return 'Mai';
                break;
            case 6:
                return 'Juni';
                break;
            case 7:
                return 'Juli';
                break;
            case 8:
                return 'August';
                break;
            case 9:
                return 'September';
                break;
            case 10:
                return 'Oktober';
                break;
            case 11:
                return 'November';
                break;
            case 12:
                return 'Dezember';
                break;
            default:
                return '';
                break;
        }

    }

    function getMonthName_en($date = null, $m = 0)
    {
        // n - Monatszahl, ohne fÃ¼hrende Nullen 1 bis 12
        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }

        switch ($m) {
            case 1:
                return 'January';
                break;
            case 2:
                return 'February';
                break;
            case 3:
                return 'March';
                break;
            case 4:
                return 'April';
                break;
            case 5:
                return 'May';
                break;
            case 6:
                return 'June';
                break;
            case 7:
                return 'July';
                break;
            case 8:
                return 'August';
                break;
            case 9:
                return 'September';
                break;
            case 10:
                return 'October';
                break;
            case 11:
                return 'November';
                break;
            case 12:
                return 'December';
                break;
            default:
                return '';
                break;
        }

    }

    function AmountInWords($amount, $language = 'uk')
    {
        //*** german
        //*** uk
        //*** dutch
        // spanish
        // catalan
        //france

        $this->amount_lng = strtolower($language);
        $this->setArrLang();

        $amount = (int)$amount;

        $nTausend = (int)floor($amount / 1000);

        $cTausend = $this->getThousandIW($nTausend);

        if ($this->amount_lng == 'france' and $nTausend == 1) {
            $cTausend = '';
        }

        $cTText = $this->getArrItem(1000);

        $cTausend = $cTausend . ($nTausend  > 0 ? $cTText : '');


        $nHundert = $amount - $nTausend*1000;
        $cHundert = $this->getThousandIW($nHundert);

        return trim($cTausend . $cHundert);
    }


    function getThousandIW($nHS) {
        $n100 = (int)(FLOOR($nHS / 100) * 100);
        $n1_99 = $nHS % 100;

        $cH1 = $this->getArrItem($n100);

        $c1_99 = $this->getArrItem($n1_99);

        return $cH1 . $c1_99;
    }

    function getArrItem($key) {
        if(isset($this->aamount_numbers[$key])) {
            return $this->aamount_numbers[$key];
        }

        return '';
    }

    function setArrLang() {
        switch(true) {
            case $this->amount_lng == 'german':
                $this->aamount_numbers = array(
                    0 => "",
                    1 => "ein",
                    2 => "zwei",
                    3 => "drei",
                    4 => "vier",
                    5 => "fÃ¼nf",
                    6 => "sechs",
                    7 => "sieben",
                    8 => "acht",
                    9 => "neun",

                    10 => "zehn",

                    11 => "elf",
                    12 => "zwÃ¶lf",
                    13 => "dreizehn",
                    14 => "vierzehn",
                    15 => "fÃ¼nfzehn",
                    16 => "sechszehn",
                    17 => "siebzehn",
                    18 => "achtzehn",
                    19 => "neunzehn",

                    20 => "zwanzig",
                    30 => "dreissig",
                    40 => "vierzig",
                    50 => "fÃ¼nfzig",
                    60 => "sechzig",
                    70 => "siebzig",
                    80 => "achtzig",
                    90 => "neunzig"
                );

                for($i=2; $i<=9;$i++) {
                    for($j=1; $j<=9;$j++) {
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($j) . 'und' . $this->getArrItem($i*10);
                    }
                }

                //*		setArrItem(100 => 'hundert")
                for($i=1; $i<=9;$i++) {
                    $this->aamount_numbers[$i*100] = $this->getArrItem($i) . 'hundert';
                }

                $this->aamount_numbers[1000] = 'tausend';
                break;

            case $this->amount_lng == 'dutch':
                $this->aamount_numbers = array(
                    1 => 'een',
                    2 => 'twee',
                    3 => 'drie',
                    4 => 'vier',
                    5 => 'vijf',
                    6 => 'zes',
                    7 => 'zeven',
                    8 => 'acht',
                    9 => 'negen',

                    10 => 'tien',

                    11 => 'elf',
                    12 => 'twaalf',
                    13 => 'dertien',
                    14 => 'veertien',
                    15 => 'vijftien',
                    16 => 'zestien',
                    17 => 'zeventien',
                    18 => 'achttien',
                    19 => 'negentien',

                    20 => 'twintig',
                    30 => 'dertig',
                    40 => 'veertig',
                    50 => 'vijftig',
                    60 => 'zestig',
                    70 => 'zeventig',
                    80 => 'tachtig',
                    90 => 'negentig'
                );

                for($i=2; $i<=9;$i++) {
                    for($j=1; $j<=9;$j++) {
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($j) . 'een' . $this->getArrItem($i*10);
                    }
                }

                for($i=1; $i<=9;$i++) {
                    $this->aamount_numbers[$i*100] = $this->getArrItem($i) . 'honderd';
                }

                $this->aamount_numbers[1000] = 'duizend';
                break;

            case $this->amount_lng == 'uk':
                $this->aamount_numbers = array(
                    0 => 'null',
                    1 => 'one',
                    2 => 'two',
                    3 => 'three',
                    4 => 'four',
                    5 => 'five',
                    6 => 'six',
                    7 => 'seven',
                    8 => 'eight',
                    9 => 'nine',

                    10 => 'ten',

                    11 => 'eleven',
                    12 => 'twelve',
                    13 => 'thirteen',
                    14 => 'fourteen',
                    15 => 'fifteen',
                    16 => 'sixteen',
                    17 => 'seventeen',
                    18 => 'eighteen',
                    19 => 'nineteen',

                    20 => 'twenty',
                    30 => 'thirty',
                    40 => 'forty',
                    50 => 'fifty',
                    60 => 'sixty',
                    70 => 'seventy',
                    80 => 'eighty',
                    90 => 'ninety'
                );

                for($i=2; $i<=9;$i++) {
                    for($j=1; $j<=9;$j++) {
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($j) . '-' . $this->getArrItem($i*10);
                    }
                }

                for($i=1; $i<=9;$i++) {
                    $this->aamount_numbers[$i*100] = $this->getArrItem($i) . ' hundred ';
                }

                $this->aamount_numbers[1000] = ' thousand ';

                break;

            case $this->amount_lng == 'uk':
                $this->aamount_numbers = array(
                    0 => 'cero',
                    1 => 'uno',
                    2 => 'dos',
                    3 => 'tres',
                    4 => 'cuatro',
                    5 => 'cinco',
                    6 => 'seis',
                    7 => 'siete',
                    8 => 'ocho',
                    9 => 'nueve',

                    10 => 'diez',

                    11 => 'once',
                    12 => 'doce',
                    13 => 'trece',
                    14 => 'catorce',
                    15 => 'quince',
                    16 => 'diecisÃ©is',
                    17 => 'diecisiete',
                    18 => 'dieciocho',
                    19 => 'diecinueve',

                    20 => 'veinte',

                    30 => 'treinta',
                    40 => 'cuarenta',
                    50 => 'cincuenta',
                    60 => 'sesenta',
                    70 => 'setenta',
                    80 => 'ochenta',
                    90 => 'noventa',

                    21 => 'veintiuno',
                    22 => 'veintidÃ³s',
                    23 => 'veintitrÃ©s',
                    24 => 'veinticuatro',
                    25 => 'veinticinco',
                    26 => 'veintisÃ©is',
                    27 => 'veintisiete',
                    28 => 'veintiocho',
                    29 => 'veintinueve',

                    100 => ' cien ',

                    200 => ' doscientos ',
                    300 => ' trescientos ',
                    400 => ' cuatrocientos ',
                    500 => ' quinientos ',
                    600 => ' seiscientos ',
                    700 => ' setecientos ',
                    800 => ' ochocientos ',
                    900 => ' novecientos ',

                    1000 => ' mil'
                );

                for($i=3; $i<=9;$i++) {
                    for($j=1; $j<=9;$j++) {
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($j) . ' y ' . $this->getArrItem($i*10);
                    }
                }
                break;

            case $this->amount_lng == 'catalan':
                $this->aamount_numbers = array(
                    0 => 'zero',
                    1 => 'un',
                    2 => 'dos',
                    3 => 'tres',
                    4 => 'quatre',
                    5 => 'cinc',
                    6 => 'sis',
                    7 => 'set',
                    8 => 'vuit',
                    9 => 'nou',

                    10 => 'deu',

                    11 => 'onze',
                    12 => 'dotze',
                    13 => 'tretze',
                    14 => 'catorze',
                    15 => 'quinze',
                    16 => 'setze',
                    17 => 'disset',
                    18 => 'divuit',
                    19 => 'dinou',

                    20 => 'vint',

                    21 => 'vint-i-un',
                    22 => 'vint-i-dos',
                    23 => 'vint-i-tres',
                    24 => 'vint-i-quatre',
                    25 => 'vint-i-cinc',
                    26 => 'vint-i-sis',
                    27 => 'vint-i-set',
                    28 => 'vint-i-vuit',
                    29 => 'vint-i-nou',

                    30 => 'trenta',
                    40 => 'quaranta',
                    50 => 'cinquanta',
                    60 => 'seixanta',
                    70 => 'setanta',
                    80 => 'vuitanta',
                    90 => 'noranta',

                    100 => ' cent ',
                    200 => ' dos-cents ',
                    300 => ' tres-cents ',
                    400 => ' quatre-cents ',
                    500 => ' cinc-cents ',
                    600 => ' sis-cents ',
                    700 => ' set-cents ',
                    800 => ' vuit-cents ',
                    900 => ' nou-cents ',

                    1000 => ' mil'
                );

                for($i=3; $i<=9;$i++) {
                    for($j=1; $j<=9;$j++) {
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($j) . '-' . $this->getArrItem($i*10);
                    }
                }
                break;

            case $this->amount_lng == 'france':
                $this->aamount_numbers = array(
                    0 => 'zÃ©ro',
                    1 => 'un',
                    2 => 'deux',
                    3 => 'trois',
                    4 => 'quatre',
                    5 => 'cinq',
                    6 => 'six',
                    7 => 'sept',
                    8 => 'huit',
                    9 => 'neuf',

                    10 => 'dix',

                    11 => 'onze',
                    12 => 'douze',
                    13 => 'treize',
                    14 => 'quatorze',
                    15 => 'quinze',
                    16 => 'seize',
                    17 => 'dix-sept',
                    18 => 'dix-huit',
                    19 => 'dix-neuf',

                    20 => 'vingt',
                    30 => 'trente',
                    40 => 'quarante',
                    50 => 'cinquante',
                    60 => 'soixante',
                    70 => 'soixante-dix',
                    80 => 'quatre-vingt',
                    90 => 'quatre-vingt-dix',

                    100 => 'cent ',

                    1000 => ' mille '
                );

                //*** 21, 31, 41, 51, 61
                for($i=2; $i<=6;$i++) {
                    //*** vingt et un, trente et un, ...
                    $this->aamount_numbers[$i*10+1] = $this->getArrItem($i*10) . ' et ' . $this->getArrItem(1);

                    //*** 22-29, 32-39, ...
                    for($j=2; $j<=9;$j++) {
                        //*** vingt-deux, vingt-trois, ...
                        $this->aamount_numbers[$i*10+$j] = $this->getArrItem($i*10) . '-' . $this->getArrItem($j);
                    }
                }

                //*** 71-79
                $this->aamount_numbers[71] = $this->getArrItem(60) . ' et ' . $this->getArrItem(11);

                for($i=2; $i<=9;$i++) {
                    //*** soixante-et-onze, soixante-douze
                    $this->aamount_numbers[70+$i] = $this->getArrItem(60) . '-' . $this->getArrItem($i+10);
                }

                //*** 81-99
                for($i=1; $i<=19;$i++) {
                    //*** quatre-vingt-un quatre-vingt-une
                    $this->aamount_numbers[80+$i] = $this->getArrItem(80) . '-' . $this->getArrItem($i);
                }


                for($i=2; $i<=9;$i++) {
                    //*** quatre-vingt-un quatre-vingt-une
                    $this->aamount_numbers[$i*100] = $this->getArrItem($i) . ' cents ';
                }
                break;
        }
    }

    function getWeekday($date) {
        // N - Numerische ReprÃƒÂ¤sentation des Wochentages gemÃƒÂ¤ÃƒÅ¸ ISO-8601 (in PHP 5.1.0 hinzugefÃƒÂ¼gt) 1 (fÃƒÂ¼r Montag) bis 7 (fÃƒÂ¼r Sonntag)

        if (is_null($date)) {
            $dow = 0;
        } else {
            $dow = $date->format('N');
        }

        switch ($dow) {
            case 1:
                return __('monday');
                break;
            case 2:
                return __('tuesday');
                break;
            case 3:
                return __('wednesday');
                break;
            case 4:
                return __('thursday');
                break;
            case 5:
                return __('friday');
                break;
            case 6:
                return __('saturday');
                break;
            case 7:
                return __('sunday');
                break;
            default:
                return '';
                break;
        }

    }

    function getMonthName($date = null, $m = 0)
    {
        // n - Monatszahl, ohne fÃ¼hrende Nullen 1 bis 12
        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }

        switch ($m) {
            case 1:
                return __('january');
                break;
            case 2:
                return __('february');
                break;
            case 3:
                return __('march');
                break;
            case 4:
                return __('april');
                break;
            case 5:
                return __('may');
                break;
            case 6:
                return __('june');
                break;
            case 7:
                return __('july');
                break;
            case 8:
                return __('august');
                break;
            case 9:
                return __('september');
                break;
            case 10:
                return __('october');
                break;
            case 11:
                return __('november');
                break;
            case 12:
                return __('december');
                break;
            default:
                return '';
                break;
        }

    }

    public function getTime($adate, $l_only_start = false)
    {
        $start = '';
        $end = '';

        if ($adate->start_) {
            $start = $adate->start_->format('H:i');
        }
        if ($adate->end_) {
            $end = $adate->end_->format('H:i');
        }
        if ($start == '00:00') {
            $start = '';
        }
        if ($end == '00:00') {
            $end = '';
        }

        if($l_only_start) {
            $startend = $start;
        } else {
            $startend = $start . (!empty($end) ? ' - ' : '') . $end;
        }


        return $startend;
    }

    function getAmount($accitem_code, $date, $field, $l_name = false)
    {
        $saccitems = TableRegistry::getTableLocator()->get('Saccitems')
            ->find('all')
            ->where(($l_name ? 'name' : 'code') ."='".$accitem_code . "'");


        $accitem_id = -1;
        foreach($saccitems as $saccitem) {
            $accitem_id = $saccitem->id;

        }

        $saccitemamounts = TableRegistry::getTableLocator()->get('SaccitemAmounts')
            ->find('all')
            ->where("accitem_id=".$accitem_id);

        $amount = 0;
        foreach ($saccitemamounts as $saccitemamount) {

            if($saccitemamount->effective_date<=$date) {
                $amount = $saccitemamount[$field];
                //return 'YYYY='.$saccitemamount->effective_date->format('d.m.Y').'#'.$field.'#'.$amount;
            }
        }
        return $amount;
    }

    function getAmount_new($accitem_code, $accitem_id, $date, $category, $l_name = false)
    {
        if($accitem_id==0) {
            $saccitems = TableRegistry::getTableLocator()->get('Saccitems')
                ->find('all')
                ->where(($l_name ? 'name' : 'code') . "='" . $accitem_code . "'");


            $accitem_id = -1;
            foreach ($saccitems as $saccitem) {
                $accitem_id = $saccitem->id;
            }
        }


        $saccitemamounts = TableRegistry::getTableLocator()->get('SaccitemAmounts')
            ->find('all')
            ->contain(['SaccitemamountValues'])
            ->where("accitem_id=".$accitem_id);

        $amount = 0;

        foreach ($saccitemamounts as $saccitemamount) {

            if($saccitemamount->effective_date<=$date) {
                foreach($saccitemamount->saccitemamount_values as $saccitemamount_value) {
                    //$amount .='#cat=#'.$saccitemamount_value->category.'#'.$saccitemamount_value->amount;
                    if(is_null($saccitemamount_value->category) || $saccitemamount_value->category == $category) {
                        $amount = $saccitemamount_value->value;
                    }
                }

            }
        }
        return $amount;
    }

    public function addMemo($section, $text, $styleFont = array(), $stylePar = array()) {
        $textlines = explode("\n", $text);
        foreach($textlines as $line) {
            $section->addText(htmlspecialchars($line),  $styleFont, $stylePar);
        }
    }

    public function addMemo_textrun($section, $text, $styleFont = array(), $stylePar = array())
    {
        $count = 0;
        $textlines = explode("\n", $text);
        foreach ($textlines as $line) {
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line), $styleFont, $stylePar);
        }
    }

    function getNumbers_by_type($address_id=-1, $numbertype='', $separator = ', ') {
        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' .$address_id)
            ->toArray();

        $numbers = '';

        foreach ($aaddressnumbers as $anumber) {

            if(
                $anumber->number_ &&
                (
                    empty($numbertype) ||
                    $anumber->snumbertype->l_phone == 1 && strtolower($numbertype) == 'l_phone' ||
                    $anumber->snumbertype->l_email == 1 && strtolower($numbertype) == 'l_email' ||
                    $anumber->snumbertype->l_mobile == 1 && strtolower($numbertype) == 'l_mobile'
                )
            ) {
                $numbers .= ($numbers>'' ? $separator : '') . $anumber->number_;
            }
        }
        return $numbers;
    }

    function getProgramCode($adate) {

        $project_id = $adate->project_id;
        $conductor_id = $adate->conductor_id;
        $programCode = $project_id.'#'.$conductor_id;
        //$programCode = $conductor_id;

        foreach ($adate->adate_works as $works) {
            $programCode .= '#'. $works->swork->id;
        }
        return $programCode;
    }

    //roc
    public function getFPD_roc($postData, $model, $adate) {

        $season_id = $adate->season_id;
        $project_id = $adate->project_id;
        $planninglevel = $adate->planninglevel;
        $programno = $adate->programno;
        $programtitle = $adate->programtitle;

        // *** 20220913 OCCUST-2088
        // *** die Programminformation aus dem ersten Termin mit dem Terminart Code: Ã‚Â§K     entnehmen und nicht mehr aus dem "AuffÃƒÂ¼hrung" wie aktuell programmiert.
        // *** Wenn sich das Programm unterscheidet dann bitte weiterhin  bei dem jeweiligen Konzerttermin dieses zeigen.

        $where = '2>1';
        if ($season_id > 0) {
            $where = 'Adates.season_id=' . (int)$season_id;
        }

        $where .= ($where > '' ? ' AND ' : '') . 'Adates.project_id=' . (int)$project_id;
        $where .= ($where > '' ? ' AND ' : '') . 'Adates.planninglevel=' . (int)$planninglevel;

        //20191022
        $where .= " AND trim(UPPER(IFNULL(Adates.programno,''))) + '#' = '" . strtoupper($programno) . "#' ";
        $where .= " AND trim(UPPER(IFNULL(Adates.programtitle,''))) + '#' = '" . strtoupper($programtitle) . "#' ";

        $awhere = [$where];

        $adates = $this->prepareData_selected($postData, $model, $awhere);

        $fpd = null;
        $firstdate = null;

        $count = 0;

        foreach($adates as $adate) {
            $count ++;
            if($count==1) {
                $firstdate = $adate;
            }

            switch(true) {
                case $adate->seventtype->l_performance==1:
                    if(is_null($fpd)) {
                        $fpd = $adate;
                    }
                    break;
            }
        }

        if(is_null($fpd)) {
            $fpd = $firstdate;
        }

        return $fpd;
    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if($count==1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates)>0) {
            $y_min = $date_min->format('Y');
            $m_min = $date_min->format('m');
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $date_max->format('m');
            $d_max = $date_max->format('d');

            $minmax =
                $d_min.'.'.
                ($m_min<>$m_max ? $m_min.'.' : '').
                ($y_min<>$y_max ? $y_min : '').
                '-'.
                $d_max.'.'.$m_max.'.'.$y_max;
        }

        return $minmax;
    }
}
