<?php
/*
20240904 ONCUST-4123
20240904 ONCUST-4123
*/

require_once('aduties_besetzungsliste_rchb_common.php');

class aduties_besetzungsliste_rchb extends aduties_besetzungsliste_rchb_common
{
    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];
    }


    public function collect(array $where = [])
    {
        $this->postData = $this->getRequest()->getData();
        $awhere = ['Aduties.id IN' => $this->postData['dataItemIds']];

        return parent::collect($awhere);
    }
}
