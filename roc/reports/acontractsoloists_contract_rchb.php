<?php
/**
 RChB Version
*/

require_once('acontractsoloists_contract_roc_common.php');

use PhpOffice\PhpWord\Element\TextRun;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;

class acontractsoloists_contract_rchb extends acontractsoloists_contract_roc_common
{
    public function collect(array $where = [])
    {
        parent::collect();
        $this->template_filename = CUSTOMER_REP_DIR . 'acontractsoloists_contract_rchb.docx';
        return $this;
    }


    /*********************************************/
    function _createFileName()
    {
        $fileName = parent::_createFileName();

        //20241218 ONCUST-2634
        //auch diese Verträge bitte analog zu den Aushilfenabrechnungen den entsprechenden Dateinamen geben: „Vertrag Kaun RSB Walton“ (Nachname/Projekt)
        $aartists = [];
        $aprojects = [];

        foreach ($this->acontracts as $acontract) {
            $project = '';
            if($acontract->sproject) {
                $project = $acontract->sproject->name;
            }
            if($acontract->artist_id>0) {
                $aartists[$acontract->artist_id] = ($acontract->artistaddress ? $acontract->artistaddress->name1.' '.$acontract->artistaddress->name2 : '').' '.$project;
            }

            if($acontract->sproject) {
                $aprojects[$acontract->project_id] = $acontract->sproject->name;
            }
        }

        switch(true) {
            case sizeof($aartists) == 1:
                $fileName = $aartists[array_key_first($aartists)];
                break;
            case sizeof($aprojects) == 1:
                $fileName = $aprojects[array_key_first($aprojects)];
                break;
            default:
                $fileName = 'mehrere Projekte';
                break;
        }
        $fileName = str_replace('+', '_', $fileName);
        $fileName = str_replace(':', '_', $fileName);
        $fileName = str_replace('/', '_', $fileName);
        $fileName = str_replace('?', '_', $fileName);

        return $fileName;
    }

    function getHonorar() {

        $chonorar = parent::getHonorar();

        if(!$this->l_auslaender) {
            $chonorar .=  ' brutto  (incl. evtl. zu zahlender MwSt).';
        }

        return $chonorar;
    }

    public function getHonorar_Part1() {

        $textrun = new TextRun(array('align' => 'left'));
        //Das ist der Standardparagraph. Es gibt eine weitere Version für festangestellt Musiker*innen, diese wird als Klausel eingefügt und ersetzt den Standard:

        //20241218
        //Im Inländervertrag außerdem bitte noch korrigieren:
        //Der Künstler erhält für seine Mitwirkung sowie die unter § 3 näher bezeichnete Rechteübertragung ein All-in-Honorar in Höhe von
        //5.000,00 € brutto  (incl. evtl. zu zahlender MwSt). à (inkl. …)

        if(!$this->l_auslaender) {
            $textrun->addText(htmlspecialchars($this->DerKuenstlerIn.' erhält für '.$this->seine.' Mitwirkung sowie die unter § 3 näher bezeichnete Rechteübertragung ein All-in-Honorar in Höhe von'));
        } else {
            parent::getHonorar_Part1();
        }

        return $textrun;
    }

    public function getHonorar_Part2() {
        $textrun = new TextRun(array('align' => 'left'));

        if($this->l_auslaender) {
            $textrun->addText(htmlspecialchars('Das Honorar ist nach § 4 Abs. 20 UstG steuerfrei.'));
        } else {
            $textrun->addText(htmlspecialchars('Das Honorar beinhaltet alle weiteren Steuern und Abgaben.'));
        }

        return $textrun;
    }

    function getArtist_address() {

        //20240902 ONCUST-2634
        //Zeilenabstand wie linker Block
        $textrun = new TextRun(array('align' => 'left', 'spacing' => Converter::pointToTwip(14), 'spacingLineRule'=>'exact'));  // space between lines in twips));

        //saddresses.street, saddresses.zipcode_poboxsaddresses.pobox.

        if($this->acontract->artistaddress) {

            $salutation = '';

            switch(true) {
                case $this->acontract->artistaddress->saddress_persdata->sex == 'F':
                    $salutation = ($this->language == 'de' ? 'Frau ' : 'Mrs. ');
                    break;

                case $this->acontract->artistaddress->saddress_persdata->sex == 'M':
                    $salutation = ($this->language == 'de' ? 'Herrn ' : 'Mr. ');
                    break;
            }


            if($salutation>'') {
                $textrun->addText(htmlspecialchars($salutation), array_merge($this->styleFont));
            }

            $artistname = trim($this->acontract->artistaddress->name2 . ' ' . $this->acontract->artistaddress->name1);
            $textrun->addText(htmlspecialchars($artistname), array_merge($this->styleFont));

            $country = ($this->acontract->artistaddress->scountry ? $this->acontract->artistaddress->scountry->name : '');
            //if (strtoupper($country) == 'DEUTSCHLAND') {
            //    $country = '';
            //}

            if ($this->acontract->artistaddress->street > '') {
                $textrun->addTextBreak();
                $textrun->addText(htmlspecialchars($this->acontract->artistaddress->street), array_merge($this->styleFont));
            }

            if ($this->acontract->artistaddress->pobox > '') {
                $textrun->addTextBreak();
                $textrun->addText(htmlspecialchars($this->acontract->artistaddress->pobox), array_merge($this->styleFont));
            }

            if (!empty($this->acontract->artistaddress->zipcode) || !empty($this->acontract->artistaddress->place)) {
                $textrun->addTextBreak();
                $textrun->addText(htmlspecialchars(trim($this->acontract->artistaddress->zipcode . ' ' . $this->acontract->artistaddress->place)), array_merge($this->styleFont));
            }

            if ($country > '') {
                $textrun->addTextBreak();
                $textrun->addText(htmlspecialchars($country), array_merge($this->styleFont));
            }

        }

        return $textrun;
    }


    function getSchedule_short() {
        //20240902 ONCUST-2634
        // 1)	Zeige keinen Probenplan mehr, sondern nach Zeitraum: ersten bis letzten Tag des Projekts
        // 2)	Schreibe in der Zeile darunter: vorläufiger Probenplan im Anhang

        $textrun = new TextRun(array('align' => 'left', 'spacing' => Converter::pointToTwip(14), 'spacingLineRule'=>'exact'));  // space between lines in twips));

        $textrun->addText(htmlspecialchars('Zeitraum: '.$this->reporttools->getMinMaxDays($this->adates_contract)));
        $textrun->addTextBreak();
        $textrun->addText(htmlspecialchars('vorläufiger Probenplan im Anhang'));

        return $textrun;
    }

    function getSchedule() {
        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(2.75);
        $this->acol_widths[] = Converter::cmToTwip(3);
        $this->acol_widths[] = Converter::cmToTwip(4.75);
        $this->acol_widths[] = Converter::cmToTwip(5.75);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $styleIdent = array('left' => Converter::cmToTwip(0), 'right' => Converter::cmToTwip(0));

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.01),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w,
            'unit' => TblWidth::TWIP,
            'spacing' => 0,
            'indentation' => $styleIdent
        );

        //,
        //'indentation' => array('left' => Converter::cmToTwip(1.5), 'right' => Converter::cmToTwip(1.5))

        $table = new Table($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $count = 0;
        foreach($this->acontract->acontract_dates as $acontract_date) {
            //foreach ($this->adates_contract as $adate) {
            $adate = $acontract_date->adate;
            $count++;

            $eventtype = $adate->seventtype ? $adate->seventtype->name : '';

            $table->addRow();

            $abold = array();

            if($adate->seventtype->l_performance == 1) {
                $abold = $this->styleFont_bold;
            }

            $cdate = ($adate->date_ ? $adate->date_->format('d.m.Y') : '');

            $startend = $this->reporttools->getTime($adate);

            $location = $adate->locationaddress->name1;
            //$location .= (!empty($location) && !empty($adate->locationaddress->street) ? ', ' : '') . $adate->locationaddress->street;
            $location .= (!empty($location) && !empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place;

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($cdate), $abold);

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($startend), $abold);

            //20241218 ONCUST-2634
            //08.01.2025	10:00 - 14:00	bitte Terminart [NAME] und Text ergänzen:	HdR Helmut-Koch-Saal, Berlin
            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell->addText(htmlspecialchars(trim($eventtype. ' '.$adate->text)), $abold);

            //Zusätzliche Aktivitäten: [Name] [Text]
            foreach ($adate->adate_activities as $aaddact) {
                $eventtype = ($aaddact->seventtype ? $aaddact->seventtype->name : '');
                $addact = trim($eventtype . ' ' . $aaddact->text);
                $cell->addText(htmlspecialchars($addact));
            }

            $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell->addText(htmlspecialchars($location), $abold);
        }

        //20240924 ONCUST-2635
        //mind.1 Zeile
        if($count==0) {
            $table->addRow();
            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell));
        }

        return $table;
    }

    function getProgram()
    {
        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(4.25);
        $this->acol_widths[] = Converter::cmToTwip(9);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $styleIdent = array('left' => Converter::cmToTwip(0), 'right' => Converter::cmToTwip(0));

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.01),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w,
            'unit' => TblWidth::TWIP,
            'spacing' => 0,
            'indentation' => $styleIdent
        );

        //,
        //'indentation' => array('left' => Converter::cmToTwip(1.5), 'right' => Converter::cmToTwip(1.5))

        $table = new Table($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();


        foreach ($this->aprogram as $adate_work) {
            if ($adate_work->swork->l_intermission == 1) {
                continue;
            }

            // 20231128
            //- Künstler*innenversion: Nur die Werke anzeigen, bei denen der Solist mitspielt
            // - Dirigentenversion: komplettes Programm der Aufführung anzeigen

            if(!$this->l_conductor) {
                $l_played = false;

                foreach ($adate_work->adatework_soloists as $soloist) {
                    if($soloist->artist_id == $this->acontract->artist_id) {
                        $l_played = true;
                    }
                }
                if(!$l_played) {
                    continue;
                }
            }

            $title = trim($adate_work->swork->title1 . ' ' . $adate_work->title2);
            $composer = ($adate_work->swork->scomposer ? trim($adate_work->swork->scomposer->firstname . ' ' . $adate_work->swork->scomposer->lastname) : '');
            //$nduration = (int)substr($adate_work->duration, 0, 2) * 60 + (int)substr($adate_work->duration, 3, 2);
            //$cduration = $nduration . "'";

            $table->addRow();

            //$cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($composer));

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($title));
        }
        return $table;
    }
}
