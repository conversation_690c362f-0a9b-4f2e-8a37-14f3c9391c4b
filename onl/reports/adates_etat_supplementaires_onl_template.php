<?php

use App\Reports\ReportTemplate;
use Cake\ORM\TableRegistry;

class adates_etat_supplementaires_onl_template extends ReportTemplate
{
    /**
     * @inheritdoc
     */
    protected $templateName = 'adates_etat_supplementaires_onl_template';

    /**
     * @inheritdoc
     */
    protected $fileLocationType = 'direct';

    /**
     * Get all seasons
     *
     * Get all existing active seasons and pass it to the template. Set the $selectedSeason for the preselection of the
     * season in the template. In case of a matching season in case of the current time, we use this seasons, otherwise
     * we try to select the next season in the future if exists.
     *
     * @return array
     */
    public function getTemplateData(array $params = [])
    {
        // GET vars in $params enthalten. selectedIds sind die gewählten IDs aus der Liste
        //$postData = $this->getRequest()->getData();
        //print_r($postData);die;

        return array();
    }

    public function getTemplateName()
    {
        return $this->templateName;
    }

    /**
     * @inheritdoc
     */

    public function submitTemplate()
    {
        try {
            $params = $this->request->getData();
            // Laden der Daten zu der Funktion aus der Datenbank
            $report = $this->Opasreports->get($params['id']);
            // Object zu dem Report erstellen
            $this->Wizard->init($report, self::OPAS_REPORT);

            // Prû¥fung der Daten
            $data = $this->Wizard->getWizardTemplateClass($params['template'])->submit($params['formData'])['data'];

            // Prû¥fung ob ein Fehlerfall vorliegt
            if (!array_key_exists('success', $data)) {
                $data['success'] = true;
            }

            // Status setzen
            if (!array_key_exists('statusCode', $data)) {
                $data['statusCode'] = 200;
            }

            if (!array_key_exists('message', $data) && $data['statusCode'] !== 200) {
                $data['message'] = 'Unknown error';
            }

            $this->viewBuilder()->setClassName('OpasJson');

            $this->response = $this->response->withStatus($data['statusCode']);

            $this->set('success', $data['success']);
            $this->set('data', $data);
        } catch (Exception $e) {
            $this->responseWithError($e);
        }
    }

    public function submit($formDataString = null)
    {
        parse_str($formDataString, $formDataArray);

        $this->data['formData'] = $formDataArray;

        return parent::submit($formDataString);
    }

}
