<?php
/*
20211028 OOCUST-966
ONL Ticket resto
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\onl\reports\ReportTools_client;
use Customer\onl\reports\instrumentation_onl;
use App\Model\Table\AdateWorksTable;

class adates_ticket_resto_onl extends ReportSpreadsheet
{
    private $user = '';

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;
    private $adates_location = null;
    private $alocation_order1s = array();

    private $aexpensetypes_all = array();

    private $location_order_1 = '';
    private $alocation_order1 = null;

    private $day = '';
    private $adate = null;
    private $adays = array();
    private $atickets = array();

    private $artist_id = 0;
    private $mindate = '';
    private $maxdate = '';
    private $ncol_total = 0;
    private $ncol_firstday = 3;
    private $row_firstartist = 10;



    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_medium_top = array();
    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();



    protected $templates = [
        [
            'name' => 'adates_ticket_resto_onl_template',
            'file' => 'adates_ticket_resto_onl_template.php',
            'jsFile' => 'adates_ticket_resto_onl_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        $this->template_filename = CUSTOMER_REP_DIR.'adates_ticket_resto_onl.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();
    }

    public function collect(array $where = [])
    {
        $this->postData = $this->getRequest()->getData();

        $this->adates_selected = $this->reporttools->prepareData_from_to($this->postData, $this->model);

        /*
        artist_id
        name1
        name2
        date_
        expensetype_id
        expensetype
        count
        dates_vm = 0 && Termine von 0 bis 14 Uhr
        dates_12 = 0 && Termine von 14 bis 14 Uhr
        dates_nm = 0

         */

        $this->aexpensetypes_all = TableRegistry::getTableLocator()->get('Sexpensetypes')
            ->find('all')
            ->toArray();

        $this->atickets = array();

        //prepareProgramCode($adates);
//print_r($this->adates_selected); die;

        // 20211029
        // die Busabfahrt selbst hat keine Venue eingetragen, daher bitte Order_1 des Nachfolgetermins beachten.
        // Die meisten Busabfahrten finden aber nachmittags statt, d.h. in dem Fall gibt es kein TR, nur dasjenige für den Abendspieldienst)

        $day_old = 'XXX';
        $dep_id = 0;
        $this->adates_location = array();
        $count = 0;
        foreach($this->adates_selected as $this->adate) {
            $count++;

            $date_id = $this->adate->id;
            $day = $this->adate->date_->format('Ymd');
            if($count==1) {
                $this->mindate = $this->adate->date_->format('d.m.Y');
            }
            $this->maxdate = $this->adate->date_->format('d.m.Y');

            if ($day_old <> $day) {
                $day_old = $day;
                $dep_id = 0;
            }

            // location_order_1 vermerken (wegen Reisetermine, die keine location_id haben)
            $location_order_1 = ($this->adate->locationaddress ? $this->adate->locationaddress->order_1 : '');
            $location_place = ($this->adate->locationaddress ? $this->adate->locationaddress->place : '');
            $project = ($this->adate->sproject ? $this->adate->sproject->name : '');


            $this->adates_location[$date_id] = array(
                'location_order_1' => $location_order_1,
                'location_place' => $location_place,
                'project' => $project
            );

            if ($dep_id > 0) {
                $this->adates_location[$dep_id]['location_order_1'] = $location_order_1;
                $this->adates_location[$dep_id]['location_place'] = $location_place;
                $this->adates_location[$dep_id]['project'] = $project;

                //$this->adates_location[$dep_id]['location_place'] .= '#DEP'.$dep_id;
                //$this->adates_location[$dep_id]['project'] .= '#DEP'.$dep_id.'#'.$date_id.'#'.$day.'#';

                $dep_id = 0;
            }

            // Reisetermin ohne Spielort merken
            if ($this->adate->seventtype->code == 'DEP' and !$this->adate->locationaddress) {
                $dep_id = $date_id;
            }
        }

        $count = 0;

        foreach($this->adates_selected as $this->adate) {
            $count++;

            $this->date_id = $this->adate->id;
            $this->day = $this->adate->date_->format('Ymd');
            //08. Okt

            $this->location_order_1 = $this->adates_location[$this->date_id]['location_order_1'];
//if(!($this->location_order_1=='REP')) {continue;}
            //$hour_end = 99;
            $hour_end = ($this->adate->end_ ? (int)$this->adate->end_->format('G')*60+(int)$this->adate->end_->format('i') : '');

            //Rückkehr nach Mitternacht:
            //-wenn der letzte Termin eines Tages nach 00:30 endet, dann gibt eine zusätzliche Aufwandsentschädigung
            //-in dem Fall zusätzlich zu den errechneten TR noch eine Kostenart Code DEC “Découcher” eintragen

            //end_ zwischen 00:30 und 03:00
            if($hour_end>=30 && $hour_end<=3*60) {
                $this->location_order_1 = 'DEC';

                $this->add_alocation_order1s();
            }


            // nur Tage mit order_1 sind interessant
            if ($this->location_order_1 > '') {
                $this->add_alocation_order1s();
            }
        }

        return $this;
    }

    function add_alocation_order1s() {
        $cday = $this->adate->date_->format('j') . '. ' . substr($this->reporttools->getMonthName($this->adate->date_), 0, 3);
        $location_place = $this->adates_location[$this->date_id]['location_place'];
        $project = $this->adates_location[$this->date_id]['project'];

        if($this->day=='20211008') {
//print_r($this->location_order_1.'#'.$project.'#'.$this->date_id.'#');
        }

        if (!array_key_exists($this->location_order_1, $this->alocation_order1s)) {
            $expensetype = $this->location_order_1;

            foreach($this->aexpensetypes_all as $aexpensetype) {
                if($aexpensetype->code == $this->location_order_1) {
                    $expensetype = $aexpensetype->name;
                }
            }

            $this->alocation_order1s[$this->location_order_1] = array(
                'expensetype' => $expensetype,
                'aartists' => array(),
                'adays' => array()
            );
        }

        if (!array_key_exists($this->day, $this->alocation_order1s[$this->location_order_1]['adays'])) {
            $this->alocation_order1s[$this->location_order_1]['adays'][$this->day] = array(
                'cday' => $cday,
                'aprojects' => array(),
                'aplaces' => array()
            );
        }

        if ($project>'' && !in_array($project, $this->alocation_order1s[$this->location_order_1]['adays'][$this->day]['aprojects'])) {
            $this->alocation_order1s[$this->location_order_1]['adays'][$this->day]['aprojects'][] = $project;
        }

        if ($location_place>'' && !in_array($location_place, $this->alocation_order1s[$this->location_order_1]['adays'][$this->day]['aplaces'])) {
            $this->alocation_order1s[$this->location_order_1]['adays'][$this->day]['aplaces'][] = $location_place;
        }

        if (!array_key_exists($this->day, $this->adays)) {

            $this->adays[$this->day] = array(
                'cday' => $cday,
                'aartists' => array(),
                'aexpensetypes' => array(),
            );

            $this->prepare_day_duties();
        }
    }

    function prepare_day_duties() {
        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        $aday_duties = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates',
                'Sdutytypes',
                'Artistaddresses', //artistaddresses
                'Sinstrinstruments' => ['Sinstrsections' => ['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']
            ])
            ->where(['Adates.date_' => $this->adate->date_, 'Sdutytypes.l_present' => 1, 'Saddresssysgroups.code'=>'STA'])
            ->order(['Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC', 'Adates.date_'=>'ASC']);

       /* $aday_duties = $aduties
            ->find('all')
            ->select([
                'Aduties.id',
                'Aduties.date_id',
                'Aduties.artist_id',
                'Aduties.instrument_id'
            ])
            ->contain('Adates', function (Query $query) {
                return $query->select([
                    'Adates.id',
                    'Adates.date_',
                    'Adates.start_',
                    'Adates.end_'
                ]);
            })
            ->contain('Sdutytypes', function (Query $query) {
                return $query->select([
                    'Sdutytypes.id',
                    'Sdutytypes.name',
                    'Sdutytypes.code',
                    'Sdutytypes.l_present'
                ]);
            })
            ->contain('Saddressgroups', function (Query $query) {
                return $query->select([
                    'Saddressgroups.id',
                    'Saddressgroups.name',
                    'Saddressgroups.code',
                    'Saddressgroups.sysgroup_id'
                ])->contain('Saddresssysgroups', function (Query $query) {
                    return $query->select([
                        'Saddresssysgroups.id',
                        'Saddresssysgroups.name',
                        'Saddresssysgroups.code'
                    ]);
                });
            })
            ->contain('Saddresses', function (Query $query) {
                return $query->select([
                    'Saddresses.id',
                    'Saddresses.code',
                    'Saddresses.name1',
                    'Saddresses.name2',
                    'Saddresses.order_1'
                ]);
            })
            ->where(['Adates.date_' => $this->adate->date_, 'Sdutytypes.l_present' => 1, 'Saddresssysgroups.code'=>'STA'])
            ->order(['Saddresses.name1' => 'ASC', 'Saddresses.name2' => 'ASC'])
            ->all();
*/
        foreach($aday_duties as $aduty) {
            $this->artist_id = $aduty->artist_id;
            $artist_name = $aduty->artistaddress->name1.($aduty->artistaddress->name2>'' ? ', ': ''). $aduty->artistaddress->name2;
            $this->location_order_1 = '';
            if(isset($this->adates_location[$aduty->date_id]['location_order_1'])) {
                $this->location_order_1 = $this->adates_location[$aduty->date_id]['location_order_1'];
            }

            // nur Dienste mit Order_1 sind interessant
            if(empty($this->location_order_1)) {continue;}

            $hour = (int)$aduty->adate->start_->format('G');
            $hour_end = (int)$aduty->adate->end_->format('G');


            if (!array_key_exists($this->artist_id, $this->alocation_order1s[$this->location_order_1]['aartists'])) {
                $this->alocation_order1s[$this->location_order_1]['aartists'][$this->artist_id] = array(
                    'name1' => $aduty->artistaddress->name1,
                    'name2' => $aduty->artistaddress->name2
                );
            }

            if (!array_key_exists($this->artist_id, $this->adays[$this->day]['aartists'])) {

                $this->adays[$this->day]['aartists'][$this->artist_id] = array(
                    'artist_id' => $this->artist_id,
                    'artist_name' => $artist_name,
                    'CD' => 0,
                    'dates_vm' => 0,
                    'expensetype_vm' => '',
                    'dates_12' => 0,
                    'expensetype_12' => '',
                    'dates_nm' => 0,
                    'expensetype_nm' => '',
                    'DEC' => 0
                );
            }

            //Rückkehr nach Mitternacht:
            //-wenn der letzte Termin eines Tages nach 00:30 endet, dann gibt eine zusätzliche Aufwandsentschädigung
            //-in dem Fall zusätzlich zu den errechneten TR noch eine Kostenart Code DEC “Découcher” eintragen

            if($hour_end==0) {
                $this->adays[$this->day]['aartists'][$this->artist_id]['DEC']=1;
            }

            switch(true) {
                case $this->location_order_1 == 'CD':
                    $this->adays[$this->day]['aartists'][$this->artist_id]['CD']++;
                    break;

                default:
                    switch (true) {
                        case $hour<12:
                            $this->adays[$this->day]['aartists'][$this->artist_id]['dates_vm']++;

                            if (empty($this->adays[$this->day]['aartists'][$this->artist_id][$this->artist_id]['expensetype_vm'])) {
                                $this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_vm'] = $this->location_order_1;
                            }
                        break;

                        case $hour < 14:
                            $this->adays[$this->day]['aartists'][$this->artist_id]['dates_12']++;

                            if (empty($this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_12'])) {
                                $this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_12'] = $this->location_order_1;
                            }
                        break;

                        case $hour >= 19:
                            $this->adays[$this->day]['aartists'][$this->artist_id]['dates_nm']++;

                            if (empty($this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_nm'])) {
                                $this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_nm'] = $this->location_order_1;
                            }
                        break;
                    }
                break;
            }
        }

        $this->adays[$this->day]['aartists'][$this->artist_id]['expensetype_nm'] = $this->location_order_1;
    }

    public function write_sheets($view = null, $layout = null) {
        $count=0;
        foreach($this->alocation_order1s  as $this->location_order_1=>$this->alocation_order1) {

            $caption = $this->location_order_1;

            $count++;

            if ($count > 1) {
                $this->ospreadsheet->createSheet();
            }

            $this->ospreadsheet->setActiveSheetIndex($count-1);
            $this->sheet = $this->ospreadsheet->getActiveSheet();

            $this->sheet->setTitle($caption);

            $this->write_sheet();
        }
    }

    function write_sheet() {
        $this->sheet->getDefaultColumnDimension()->setWidth(17);
        $this->sheet->getColumnDimension($this->getColumnLetter(1))->setWidth(24.29);
        $this->sheet->getColumnDimension($this->getColumnLetter(2))->setWidth(16.43);

        $this->row = 1;

        //$this->sheet->mergeCells("A1:B1");

        //$this->sheet->mergeCells("C1:".$this->getColumnLetter($this->lastcolnum).'1');
        $this->sheet->setCellValue('A1', $this->alocation_order1['expensetype']);
        $this->sheet->getStyle('A1')->getFont()->setBold(true);

        $this->sheet->setCellValue('B1', 'musiciens permanents');

        $this->sheet->setCellValue('A2', $this->location_order_1);
        $this->sheet->setCellValue('A3', 'Du');
        $this->sheet->setCellValue('B3', $this->mindate);

        $this->sheet->setCellValue('A4', 'Au');
        $this->sheet->setCellValue('B4', $this->maxdate);

        $this->row = 5;

        $this->sheet->setCellValue('B'.($this->row+1), htmlspecialchars('Nom série'));
        $this->sheet->getStyle('B'.($this->row+1))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $this->sheet->setCellValue('B'.($this->row+2), htmlspecialchars('Ville'));
        $this->sheet->getStyle('B'.($this->row+2))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $this->sheet->setCellValue('B'.($this->row+3), htmlspecialchars('date'));
        $this->sheet->getStyle('B'.($this->row+3))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);


        $col = 2;
        foreach($this->alocation_order1['adays'] as $this->day=>$aday) {

            //20211120
            //Prüfen, ob es tatsäclich Tickets gibt
            $l_ok = false;
            foreach($this->alocation_order1['aartists'] as $this->artist_id=>$aartist) {

                $tickets = $this->getTickets();
                if ($tickets>0) {
                    $l_ok = true;
                    break;
                }
            }

            if(!$l_ok) {
                $this->alocation_order1['adays'][$this->day]['col'] = -1;
                continue;
            }

            $col++;

            $this->alocation_order1['adays'][$this->day]['col'] = $col;
            $cday = $aday['cday'];
            $projects = implode(', ', $aday['aprojects']);
            $places = implode(', ', $aday['aplaces']);

            $this->sheet->setCellValue($this->getColumnLetter($col).($this->row+1), htmlspecialchars($projects));
            $this->sheet->getStyle($this->getColumnLetter($col).($this->row+1))->getFont()->setBold(true);
            $this->sheet->getStyle($this->getColumnLetter($col).($this->row+1))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

            $this->sheet->setCellValue($this->getColumnLetter($col).($this->row+2), htmlspecialchars($places));

            $this->sheet->getStyle($this->getColumnLetter($col).($this->row+2))->getFont()->setBold(true);
            $this->sheet->getStyle($this->getColumnLetter($col).($this->row+2))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

            $this->sheet->setCellValue($this->getColumnLetter($col).($this->row+3), htmlspecialchars($cday));
            $this->sheet->getStyle($this->getColumnLetter($col).($this->row+3))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        }

        $col++;
        $this->ncol_total = max($col,4);

        $this->sheet->setCellValue($this->getColumnLetter($col).($this->row+1), htmlspecialchars('TOTAL'));

        $this->sheet->getStyle($this->getColumnLetter($col).($this->row+1))->getFont()->setBold(true);
        $this->sheet->getStyle($this->getColumnLetter($col).($this->row+1))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $this->sheet->setCellValue($this->getColumnLetter($col).($this->row+2), htmlspecialchars('période'));
        $this->sheet->getStyle($this->getColumnLetter($col).($this->row+2))->getFont()->setBold(true);
        $this->sheet->getStyle($this->getColumnLetter($col).($this->row+2))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $this->sheet->getStyle($this->getColumnLetter($this->ncol_firstday).($this->row+1).':'.$this->getColumnLetter($this->ncol_total).($this->row+3))->applyFromArray($this->borders_medium_outside);

        $this->row += 4;

        $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars('Nom'));
        $this->sheet->getStyle($this->getColumnLetter(1).($this->row))->getFont()->setBold(true);
        $this->sheet->setCellValue($this->getColumnLetter(2).($this->row), htmlspecialchars('Prénom'));
        $this->sheet->getStyle($this->getColumnLetter(2).($this->row))->getFont()->setBold(true);

        //20211120
        //print_r($this->alocation_order1['aartists']);
        uasort(
            $this->alocation_order1['aartists'],
            function ($a, $b) {

                switch(true) {
                    case $a['name1'] == $b['name1'] && $a['name2'] == $b['name2']:
                        return 0;

                    case $a['name1'] > $b['name1'] || $a['name1'] == $b['name1'] && $a['name2'] > $b['name2']:
                        return 1;
                    default:
                        return 0;
                }
            }
        );
//print_r($this->alocation_order1['aartists']); die;
        foreach($this->alocation_order1['aartists'] as $this->artist_id=>$aartist) {

            $this->row++;

            $this->sheet->setCellValue($this->getColumnLetter(1).($this->row), htmlspecialchars($aartist['name1']));
            $this->sheet->setCellValue($this->getColumnLetter(2).($this->row), htmlspecialchars($aartist['name2']));

            foreach($this->alocation_order1['adays'] as $this->day=>$aday) {
                $col = $aday['col'];

                // keitne Tickets am Tag
                if($col<0) {
                    continue;
                }

                $tickets = $this->getTickets();

                if($tickets>0) {
                    $this->sheet->setCellValue($this->getColumnLetter($col).($this->row), htmlspecialchars($tickets));
                }
                $this->sheet->getStyle($this->getColumnLetter($col).($this->row))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            }

            $this->sheet->setCellValue($this->getColumnLetter($this->ncol_total).($this->row), '=SUM('.$this->getColumnLetter($this->ncol_firstday).$this->row.':'.$this->getColumnLetter($this->ncol_total-1).$this->row.')');
            $this->sheet->getStyle($this->getColumnLetter($this->ncol_total).($this->row))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $this->sheet->getStyle($this->getColumnLetter($this->ncol_total).($this->row))->getFont()->setBold(true);
        }



        $this->sheet->getStyle($this->getColumnLetter(1).($this->row_firstartist).':'.$this->getColumnLetter($this->ncol_total).($this->row))->applyFromArray($this->borders_medium_outside);
        $this->sheet->getStyle($this->getColumnLetter(1).($this->row_firstartist).':'.$this->getColumnLetter($this->ncol_total).($this->row))->applyFromArray($this->borders_dashed_inside);

        $this->row++;
        $this->row++;

        $this->sheet->getStyle($this->getColumnLetter($this->ncol_firstday).$this->row.':'.$this->getColumnLetter($this->ncol_total).($this->row))->applyFromArray($this->borders_medium_all);

        for($i=$this->ncol_firstday; $i<=$this->ncol_total; $i++) {
            $this->sheet->setCellValue($this->getColumnLetter($i).($this->row), '=SUM('.$this->getColumnLetter($i).$this->row_firstartist.':'.$this->getColumnLetter($i).($this->row-2).')');
            $this->sheet->getStyle($this->getColumnLetter($i).($this->row))->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $this->sheet
                ->getStyle($this->getColumnLetter($i).$this->row)
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('FFFF00');
        }
    }

    function getTickets() {
        $v = 0;
        //print_r($this->location_order_1.'#'.$this->day.'#'.$this->artist_id); die;
        foreach($this->adays[$this->day]['aartists'] as $artist_id=>$aartist_day) {
            if ($artist_id == $this->artist_id) {
                switch (true) {
                    case $this->location_order_1 == 'CD':
                        //-Musiker muss mind.  2 Dienste l_present am Tag haben in einer Venue mit Oder_1 = CD, er bekommt max 1
                        if ($aartist_day['CD'] > 1) {
                            $v = 1;
                        }
                        break;

                    default:
                        $expensetype_vm = '';
                        $expensetype_12 = '';
                        $expensetype_nm = '';

                        $dates_vm = 0;
                        $dates_12 = 0;
                        $dates_nm = 0;

                        $v = 0;
                        if ($aartist_day['expensetype_vm'] == $this->location_order_1) {
                            $expensetype_vm = $aartist_day['expensetype_vm'];
                            $dates_vm = $aartist_day['dates_vm'];
                        }

                        if ($aartist_day['expensetype_12'] == $this->location_order_1) {
                            $expensetype_12 = $aartist_day['expensetype_12'];
                            $dates_12 = $aartist_day['dates_12'];
                        }

                        if ($aartist_day['expensetype_nm'] == $this->location_order_1) {
                            $expensetype_nm = $aartist_day['expensetype_nm'];
                            $dates_nm = $aartist_day['dates_nm'];
                        }

                        // NM Termine immer + 1
                        if (!empty($expensetype_nm)) {
                            $v++;
                        }

                        // 12Uhr - Termine immer + 1
                        if (!empty($expensetype_12)) {
                            $v++;
                        }

                        // VM - Termine nur, wenn NM nicht leer
                        if (!empty($expensetype_vm) && $expensetype_nm) {
                            $v++;
                        }

                        // höchstens 2 Termine
                        $v = min($v, 2);
                        break;
                }

                //$v .= '#'.$this->location_order_1.'#'.$this->adays[$day]['aartists']['CD'];
            }
        }
        return $v;
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

    }

}
