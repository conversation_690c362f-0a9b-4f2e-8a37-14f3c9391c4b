/**
 * Nachgeladene JS-Erweiterung fÃ¼r das Template
 *
 * Hier wird in dem Namespace FUNCTIONS unter dem Namen des Templates eine Funktion abgelegt, die sobald aufgerufen
 * wird, wenn das Template im Wizard angezeigt wird.
 */

var REPORTS = REPORTS || {};

REPORTS.adates_ticket_resto_onl_template = function () {

  if ($.fn.datetimepicker) {

    $('#template_start').datetimepicker({
      format: 'DD.MM.YYYY',
      locale: 'de'
    });

    $('#template_end').datetimepicker({
      format: 'DD.MM.YYYY',
      locale: 'de'
    });

  } else {
    console.error('Missing datetimepicker()-Function for the javascript part if the function AdatesChangeTime.')
  }

};
