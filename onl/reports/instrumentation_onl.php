<?php

namespace Customer\onl\reports;
use App\Reports\Tools\InstrumentationCommon;


/**
 * Class Instrumentation_kap
 * @package App\Reports\Tools
 *
 * formatiert Instrumentierung nach KAP-Vorgaben
 */
class adates_bilan_de_saison_onl extends InstrumentationCommon {

    function formatInstrumentation()
    {
        $this->instrumentation = '';

        if (is_null($this->data_row)) {
            return;
        }

// TOM: for North America: Timpani text is only output if if contains a letter and then
//  After the Timpani number. Also, '-' may be omitted
        $row = $this->data_row;


        $harp = ($row->harp==0 ? '': ($row->harp>1 ? $row->harp : '') .'hp') . ($row->harp_text > '' ? '[' . $row->harp_text . ']' : '');

        if ($this->datework_id > 0) {
            $percussions = $this->getGridInstr($this->datework_id, 'Percussion', 'Percussions', '', '', '', true, ',');

            $keyboards = $this->getGridInstr($this->datework_id, 'keyboard', 'keyboards', '-', '', '', false, ',');

            $vocals = $this->getGridInstr($this->datework_id, 'vocals', 'vocals', '-', 'ch', 'ch', false, '');

            $soloinstr = $this->getGridInstr($this->datework_id, '', 'soloinstr', '-', 'solo', 'soli', false, ',');

            // getGridInstr(tnDateWork_id, tcInstrumentName, tcInstrumentName_Grid, tcPrefix, tcSuffix_Single, tcSuffix_Plural, tlShowNull, tcInstr_GridSeparator)

            $extras = $this->getGridInstr($this->datework_id, '', 'extras', '-', '', '', false, '-');
        } else {
            $timpani =
                ((isset($row->timpani_text) && $row->timpani_text) > '' ? $row->timpani_text :
                    ($row->timpani == 0 ? '' :
                        ($row->timpani > 1 ? $row->timpani . '-' : '') . 'Pk'
                    )
                );
            $percussions =
                ((isset($row->percussion_text) && $row->percussion_text) > '' ? $row->percussion_text :
                    ($row->percussion == 0 ? '' :
                        ($row->percussion > 1 ? $row->percussion . '-' : '') . 'Sz'
                    )
                );



            $keyboard =
                ((isset($row->keyboard_text) && $row->keyboard_text) > '' ? $row->keyboard_text :
                    ($row->keyboard == 0 ? '' :
                        ($row->keyboard > 1 ? $row->keyboard . '-' : '') . 'Tastinstr.'
                    )
                );

            $extras =
                ((isset($row->extra_text) && $row->extra_text) > '' ? $row->extra_text :
                    ($row->extra == 0 ? '' :
                        ($row->extra > 1 ? $row->extra . '-' : '') . 'Extrainstr.'
                    )
                );

            $vocals =
                ((isset($row->vocals_text) && $row->vocals_text) > '' ? $row->vocals_text :
                    ($row->vocals == 0 ? '' :
                        ($row->vocals > 1 ? $row->vocals . '-' : '') . 'vocals'
                    )
                );
        }




        $this->strings =
            $row->violin1 .
            $row->violin2 .
            $row->viola .
            $row->cello .
            $row->bass;

        if ($this->strings == '0000') {
            $this->strings = '';
        }

        $this->instrumentation =
            $row->flute . ((isset($row->flute_text) && $row->flute_text > '') ? '[' . $row->flute_text . ']' : '') .
            $row->oboe . ((isset($row->oboe_text) && $row->oboe_text > '') ? '[' . $row->oboe_text . ']' : '') .
            $row->clarinet . ((isset($row->clarinet_text) && $row->clarinet_text > '') ? '[' . $row->clarinet_text . ']' : '') .
            $row->bassoon . ((isset($row->bassoon_text) && $row->bassoon_text > '') ? '[' . $row->bassoon_text . ']' : '');
        $this->instrumentation .=
            '-' .
            $row->horn . ((isset($row->horn_text) && $row->horn_text > '') ? '[' . $row->horn_text . ']' : '') .
            $row->trumpet . ((isset($row->trumpet_text) && $row->trumpet_text > '') ? '[' . $row->trumpet_text . ']' : '') .
            $row->trombone . ((isset($row->trombone_text) && $row->trombone_text > '') ? '[' . $row->trombone_text . ']' : '') .
            $row->tuba . ((isset($row->tuba_text) && $row->tuba_text > '') ? '[' . $row->tuba_text . ']' : '') .
            '-' .
            $row->timpani . ((isset($row->timpani_text) && $row->timpani_text > '') ? '[' . $row->timpani_text . ']' : '').
            $percussions;

        $this->instrumentation .=
            $harp.
            $keyboard.
            $extras.
            ($this->strings>'' ? '-str' . ($this->strings<>'87543' ? ':'.$this->strings : '') : '');
		    $vocals.
            $soloinstr;

        if ($this->instrumentation == '0000-0000-00') {
            $this->instrumentation = '';
        }

        $this->instrumentation_max = $this->instrumentation;
        $this->strings_max = $this->strings;
    }


    function getGridInstr($datework_id, $instrument, $instrument_grid, $prefix, $suffix_single='', $suffix_plural='', $l_shownull = false, $instr_gridseparator = ',')
    {
        return '';
        /*
            $AgridTable = TableRegistry::getTableLocator()->get('AdateWork'.$instrument);

            $this->data_row = $AdateWorksTable
            ->find('all')
            ->contain([
            'Sworks'
            ])
            ->where('Sworks.l_intermission <> 1 AND AdateWorks.id=' . $this->datework_id)
            ->first();
            break;
        oData.ExecuteCommand(;
            "SELECT " + ;
                "DWI.instrument_order, " + ;
                "DWI.number_, " + ;
                "DWI.text, " + ;
                "sInstrInstruments.Code " + ;
            "FROM adatework_"+tcInstrumentName_Grid+" AS DWI " + ;
                "LEFT JOIN sInstrInstruments ON DWI.Instrument_id = sInstrInstruments.ID " + ;
            "WHERE DWI.Datework_id= "+ STR(tnDateWork_id) + " " + ;
            "ORDER BY DWI.instrument_order", ;
        "crsInstrGrid")

        lcInstr_Grid = ""
        lcText_Old = ''
        SELECT crsInstrGrid
        SCAN
            lcText = NVL(UPPER(ALLTRIM(Text)), '')
            lcInstr_ = IIF(number_>1, ALLT(STR(number_))+' ','') + NVL(ALLT(Code),'')

            lcSeparator = tcInstr_GridSeparator

            IF !EMPTY(lcText) AND lcText==lcText_Old
                lcSeparator = ;
                    IIF(UPPER(ALLTRIM(lcText)) =='A', '/', ;
                    IIF(UPPER(ALLTRIM(lcText)) =='S', '+', ''))

            ENDIF

            lcText_Old = lcText

            lcInstr_Grid = lcInstr_Grid + ;
                IIF(!EMPTY(lcInstr_Grid) AND !EMPTY(lcInstr_), lcSeparator,'') + lcInstr_

            SELECT crsInstrGrid
        ENDSCAN

        lcInstr_Grid = ALLTRIM(lcInstr_Grid + ' ' + ;
            IIF(RECCOUNT("crsInstrGrid")==1, tcSuffix_Single, ;
            IIF(RECCOUNT("crsInstrGrid")>1, tcSuffix_Plural, '')))

        SELECT crsDateWork

        lcInstr = ''

        IF !EMPTY(tcInstrumentName)
            tcInstrumentName_Text = tcInstrumentName + "_Text"


            lnInstr = NVL(&tcInstrumentName.,0)
            lcInstr_Text = NVL(ALLTRIM(&tcInstrumentName_Text.),'')

            IF tlShowNull
                lcInstr = ALLT(STR(lnInstr))
            ELSE
                IF lnInstr>1
                    lcInstr = ALLT(STR(lnInstr))
                ENDIF
            ENDIF
            lcInstr = lcInstr + IIF(!EMPTY(lcInstr_Text), '['+lcInstr_Text+']','')
        ENDIF


        lcInstr = ALLTRIM(lcInstr_Grid + IIF(!EMPTY(lcInstr_Grid) AND !EMPTY(lcInstr), tcInstr_GridSeparator+' ', '') + lcInstr)

        lcInstr = IIF(!EMPTY(lcInstr), tcPrefix, '') + lcInstr

        RETURN lcInstr
        */
    }

}
