<?php


//namespace App\Reports\Tools;
namespace Customer\onl\reports;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use App\Model\Table\AdateworkSoloistsTable;
use Cake\I18n\Date;

/**
 * Class ReportTools_client
 * @package App\Reports\Tools
 *
 * bereitet kundenspetifische Daten vor, die in mehreren Berichten benutzt werden können
 */
class ReportTools_client
{
    public $user = '';
    public $user_email = '';
    public $user_phone = '';
    public $user_mobile = '';

    public function __construct()
    {
        $this->initUser();
    }

    function initUser()
    {
        $this->user = $_SESSION['Auth']['User']['sh'];
        $this->user_email = '';
        $this->user_phone = '';
        $this->user_mobile = '';

        $where = "TRIM(UPPER(IFNULL(Opasusers.sh,''))) + '#' = '" . strtoupper($this->user) . "#' ";

        $opasusers = TableRegistry::getTableLocator()->get('Opasusers')
            ->find()
            ->select(['Opasusers.id', 'Opasusers.sh', 'Opasusers.name', 'Opasusers.fullname', 'Opasusers.address_id'])
            ->where($where)
            ->first();

        $address_id = ($opasusers->address_id ? $opasusers->address_id : 0);
        $this->user = ($opasusers->fullname ? $opasusers->fullname : '');
        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' .$address_id)
        ->toArray();

        foreach ($aaddressnumbers as $anumber) {

            if($anumber->snumbertype->l_phone == 1) {
                $this->user_phone = ($anumber->number_ ? $anumber->number_ : '');
            }
            if($anumber->snumbertype->l_email == 1) {
                $this->user_email = ($anumber->number_ ? $anumber->number_ : '');
            }
            if($anumber->snumbertype->l_mobile == 1) {
                $this->user_mobile = ($anumber->number_ ? $anumber->number_ : '');
            }
        }
    }
    /**
     * @param $adates
     * @return string
     * listet Tage aus der AUswahl im Format
     * 12./13./14. Juni 2016
     */
    function getDays($adates)
    {

        $ayears = array();
        foreach ($adates as $date) {
            $y = $date->date_->format('Y');
            $m = $date->date_->format('m');
            $d = $date->date_->format('d');

            $ayears[$y]['amonths'][$m]['adays'][$d] = $date->date_;
        }

        $cyears = '';
        foreach ($ayears as $year => $ayear) {


            $cmonths = '';
            foreach ($ayear['amonths'] as $month => $amonth) {
                $cdays = '';
                foreach ($amonth['adays'] as $day => $date_) {

                    //$weekday = self::getWeekday($date_);
                    $weekday = '';

                    $cdays .= (!empty($cdays) ? ' | ' : '') . trim($weekday . ' ' . $day) . '.';
                }

                $cmonths .= (!empty($cmonths) ? ', ' : '') . $cdays . ' ' . self::getMonthName($date_);
            }


            $cyears .= (!empty($cyears) ? ', ' : '') . $cmonths . ' ' . $year;
        }

        return $cyears;
    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $ayears = array();
        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if($count==1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates)>0) {
            $y_min = $date_min->format('Y');
            $m_min = $date_min->format('m');
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $date_max->format('m');
            $d_max = $date_max->format('d');

            $minmax =
                $d_min.'.'.
                ($m_min<>$m_max ? $m_min.'.' : '').
                ($y_min<>$y_max ? $y_min : '').
                '-'.
                $d_max.'.'.$m_max.'.'.$y_max;
        }

        return $minmax;
    }

    function getWeekday($date)
    {
        // N - Numerische Repräsentation des Wochentages gemäß ISO-8601 (in PHP 5.1.0 hinzugefügt) 1 (für Montag) bis 7 (für Sonntag)

        if (is_null($date)) {
            $dow = 0;
        } else {
            $dow = $date->format('N');
        }

        switch ($dow) {
            case 1:
                return 'Monday';
                break;
            case 2:
                return 'Tuesday';
                break;
            case 3:
                return 'Wednesday';
                break;
            case 4:
                return 'Thursday';
                break;
            case 5:
                return 'Friday';
                break;
            case 6:
                return 'Saturday';
                break;
            case 7:
                return 'Sunday';
                break;
            default:
                return '';
                break;
        }

    }

   function getMonthName($date = null, $m = 0)
    {
        // n - Monatszahl, ohne führende Nullen 1 bis 12
        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }

        switch ($m) {
            case 1:
                return __('january');
                break;
            case 2:
                return __('february');
                break;
            case 3:
                return __('march');
                break;
            case 4:
                return __('april');
                break;
            case 5:
                return __('may');
                break;
            case 6:
                return __('june');
                break;
            case 7:
                return __('july');
                break;
            case 8:
                return __('august');
                break;
            case 9:
                return __('september');
                break;
            case 10:
                return __('october');
                break;
            case 11:
                return __('november');
                break;
            case 12:
                return __('december');
                break;
            default:
                return '';
                break;
        }

    }

    public function getTime($adate)
    {
        $start = '';
        $end = '';

        if ($adate->start_) {
            $start = $adate->start_->format('H:i');
        }
        if ($adate->end_) {
            $end = $adate->end_->format('H:i');
        }
        if ($start == '00:00') {
            $start = '';
        }
        if ($end == '00:00') {
            $end = '';
        }

        $startend = $start . (!empty($end) ? ' - ' : '') . $end;

        return $startend;
    }

    public function addMemo($section, $text, $styleFont = array(), $stylePar = array()) {
        $textlines = explode("\n", $text);
        foreach($textlines as $line) {
            $section->addText(htmlspecialchars($line),  $styleFont, $stylePar);
        }
    }

    function prepareData_from_to($postData, $model)
    {
        //'OR' => [['view_count' => 2], ['view_count' => 3]],

        $or = array();
        switch(true) {
            case (int)$postData['formData']['template_pl1']==1 && (int)$postData['formData']['template_pl2']==1 :
                $or = ['OR' => [['planninglevel'=>1], ['planninglevel'=>2]]];
            break;
            case isset($postData['formData']['template_pl1']) && (int)$postData['formData']['template_pl1']==1:
                $or = ['planninglevel'=>1];
            break;
            case isset($postData['formData']['template_pl2']) && (int)$postData['formData']['template_pl2']==1:
                $or = ['planninglevel'=>2];
            break;
            case isset($postData['formData']['template_pl3']) && (int)$postData['formData']['template_pl3']==1:
                $or = ['planninglevel'=>3];
                break;
            case isset($postData['formData']['template_pl4']) && (int)$postData['formData']['template_pl4']==1:
                $or = ['planninglevel'=>4];
                break;
        }

        $search = [
            'Adates.date_ >=' => $this->time2Date(Date::parse($postData['formData']['template_start'])),
            'Adates.date_ <=' => $this->time2Date(Date::parse($postData['formData']['template_end'])),
            $or
        ];

        $adates_selected = $model
            ->find('all')
            ->select()
            ->contain([
                'Sdresses',
                'Sprojects',
                'Seventtypes' => ['Seventtypegroups'],
                'Locationaddresses' => ['Scountries'],
                'Orchestraaddresses',
                'Conductoraddresses',
                'AdateWorks'=>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sworks'=>['Scomposers'],
                                'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses']
                            ])
                            ->orderAsc('AdateWorks.work_order');
                    },
                'AdatePersons'=>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Saddresses',
                                'Saddressfunctionitems'
                            ])
                            ->orderAsc('AdatePersons.person_order');
                    },
                'AdateActivities'=>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Seventtypes' => ['Seventtypegroups']
                            ])
                            ->orderAsc('AdateActivities.activity_order');
                    }
            ])
            ->where($search)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC'])
            ->all();

        //print_r($adates_selected);
        return $adates_selected;
    }

    function getProgramCode() {

        $conductor_id = $this->adate->conductor_id;
        $programCode = $conductor_id;

        foreach ($this->adate->adate_works as $works) {
            $programCode .= '#'. $works->swork->id;
        }
        return $programCode;
    }

    function getNumbers_by_type($address_id=-1, $numbertype='', $l_top1=false) {
        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' .$address_id)
            ->toArray();

        $count=0;
        $numbers = '';
        foreach ($aaddressnumbers as $anumber) {

            if($l_top1 and $count>1) {
                continue;
            }

            if(
                $anumber->number_ &&
                (
                    $anumber->snumbertype->l_phone == 1 && strtolower($numbertype) == 'l_phone' ||
                    $anumber->snumbertype->l_email == 1 && strtolower($numbertype) == 'l_email' ||
                    $anumber->snumbertype->l_mobile == 1 && strtolower($numbertype) == 'l_mobile'
                )
            ) {
                $count++;
                $numbers .= ($numbers>'' ? ', ' : '') . $anumber->number_;
            }
        }
        return $numbers;
    }

    public function getLongName($tname2='', $tname5='', $tname1='')
    {
        $name = trim(trim($tname2.' '.$tname5).' '.$tname1);
        return $name;
    }

    public function getSoloists(int $date_id)
    {

        $where = 'AdateWorks.date_id=' . $date_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order2')
            ->distinct();

        return $arows;
    }

    public function getSoloists_dw(int $datework_id)
    {

        $where = 'AdateWorks.id=' . $datework_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order')
            ->distinct();

        return $arows;
    }

    public function getShortName($tname2='', $tname5='', $tname1='', $format = '251')
    {

        $name = trim($tname2);

        $name2 = substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : '');

        while (strpos(' ', $name) !== false) {
            $name = trim(substr($name, 0, strpos(' ', $name)));

            $name2 = trim($name2. ' '.substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : ''));
        }

        switch (true) {
            case $format == '251':
                $name = trim($name2.' '.trim(trim($tname5).' '.trim($tname1)));
                break;

            default:
                $name = trim(trim($tname5).' '.trim($tname1)).
                    (!empty($name2) ? ', ' : '').$name2;
                break;
        }

        return $name;
    }

    public function time2Date(\DateTime $time)
    {
        $time->hour(0);
        $time->minute(0);
        $time->second(0);

        return $time;
    }

    function isCAPT($adate) {
        $l_CAPT = false;

//return(sizeof($adate->adate_activities));
        foreach ($adate->adate_activities as $aaddact) {
            if($aaddact->seventtype->seventtypegroup->code == 'CAPT') {
                $l_CAPT = true;
            }
        }
        return $l_CAPT;
    }
}

