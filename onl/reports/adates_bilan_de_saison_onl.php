<?php
/*
20210914
ONL CA bilan de saison
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportWord;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\onl\reports\ReportTools_client;
use Customer\onl\reports\instrumentation_onl;
use App\Model\Table\AdateWorksTable;

class adates_bilan_de_saison_onl extends ReportWord
{
    private $user = '';

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;


    var $aprojects = array();
    var $aproject = array();
    var $project_id = 0;

    var $aconductors_all = array();
    var $asoloists_all = array();
    var $aorchestras_all = array();
    var $aworks_all = array();

    var $no_pds_nons = 0;
    var $no_pds_ns = 0;
    var $no_pds_capt = 0;
    var $no_solo = 0;
    var $no_cond = 0;
    var $no_pds_fest = 0;

    protected $templates = [
        [
            'name' => 'adates_bilan_de_saison_onl_template',
            'file' => 'adates_bilan_de_saison_onl_template.php',
            'jsFile' => 'adates_bilan_de_saison_onl_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        $this->template_filename = CUSTOMER_REP_DIR.'adates_bilan_de_saison_onl.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

    }

    public function collect(array $where = [])
    {
        $this->postData = $this->getRequest()->getData();

        $this->adates_selected = $this->reporttools->prepareData_from_to($this->postData, $this->model);

        $this->aprojects = array();

        $this->aconductors_all = array();
        $this->asoloists_all = array();
        $this->aorchestras_all = array();

        $this->no_pds_nons = 0;
        $this->no_pds_ns = 0;
        $this->no_pds_capt = 0;
        $this->no_solo = 0;
        $this->no_cond = 0;
        $this->no_pds_fest = 0;

        //prepareProgramCode($adates);
//print_r($this->adates_selected); die;
        $count=0;
        foreach($this->adates_selected as $adate) {
            $count++;

            $date_id = $adate->id;

            $project_id = $adate->project_id;
            $l_performance = $adate->seventtype->l_performance;

            $location = ($adate->locationaddress ? $adate->locationaddress->name1 : '');

           if($l_performance==1 && $project_id > 0) {
                // Projekte
                if (!array_key_exists($project_id, $this->aprojects)) {

                    $this->aprojects[$project_id] = array();

                    $this->aprojects[$project_id]['project'] = ($adate->sproject ? $adate->sproject->name : '');
                    $this->aprojects[$project_id]['project_name2'] = ($adate->sproject ? $adate->sproject->name2 : '');
                    $this->aprojects[$project_id]['aconductors'] = array();
                    $this->aprojects[$project_id]['asoloorchs'] = array();
                    $this->aprojects[$project_id]['aworks'] = array();
                    $this->aprojects[$project_id]['acaptations'] = array();
                    $this->aprojects[$project_id]['adates'] = array();
                }


                $this->aprojects[$project_id]['adates'][$date_id] = $adate;
            }

            //Conductors
            if($adate->conductor_id>0 and !array_key_exists($adate->conductor_id, $this->aconductors_all)) {
                $name = $this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);
                $name_order = $adate->conductoraddress->name1.','.$adate->conductoraddress->name2;

                $this->aconductors_all[$adate->conductor_id] = array(
                    'name'=>$name,
                    'name_order' => $name_order
                );
            }

            //Orchestra
            if($adate->orchestra_id>0 and !array_key_exists($adate->orchestra_id, $this->aorchestras_all)) {
                $name = $this->reporttools->getLongName($adate->orchestraaddress->name2, '', $adate->orchestraaddress->name1);
                $name_order = $adate->orchestraaddress->name1.','.$adate->orchestraaddress->name2;

                $this->aorchestras_all[$adate->orchestra_id] = array(
                    'name'=>$name,
                    'name_order' => $name_order
                );
            }

            //Soloists
            $adatework_soloists = $this->reporttools->getSoloists($date_id);
            foreach ( $adatework_soloists as $adatework_soloist) {
                $k=$adatework_soloist->artist_id.'_'.$adatework_soloist->instrument_id;

                $instrument = ($adatework_soloist->sinstrinstrument ? $adatework_soloist->sinstrinstrument->name : '');
                $name = $this->reporttools->getLongName($adatework_soloist->saddress->name2, '', $adatework_soloist->saddress->name1);
                $name .= ($instrument>'' ? ', ' : '').$instrument;

                $name_order = $adatework_soloist->saddress->name1.','.$adatework_soloist->saddress->name2;
                if(!array_key_exists($k, $this->asoloists_all)) {
                    $this->asoloists_all[$k] = array(
                        'name'=>$name,
                        'name_order' => $name_order,
                        'instrument'=>$instrument
                    );
                }
            }

            //Works
            //Werke, die im Zeitraum das erste Mal programmiert wurden; also erster Eintrag in Dates/Works
            foreach ($adate->adate_works as $awork) {
                $work_id = $awork->work_id;
                $title1 = trim($awork->swork->title1);

                $composer = ($awork->swork->scomposer ? $awork->swork->scomposer->lastname . ' : ' : '');

                if ($awork->swork->l_intermission == 0) {
                    if (!array_key_exists($work_id, $this->aworks_all)) {


                        $this->aworks_all[$work_id] = array(
                            'title' => $title1,
                            'composer' => $composer
                        );
                    }
                }
            }

            //Anzahl aller l_performance Termine # Konzerte in Venues die mit NS anfangen (alle Säle im Haus beginnen mit NS, dann müssten wir alle Säle haben)
            //Alle l_Performance Termine in Sälen NS
            //Anzahl aller l_performance mit zusätzl. Terminart Gruppen Code CAPT
            //Anzahl aller Solisten
            //Anzahl aller Dirigenten (auch Dirigenten als Andere TN)
            //Kommt noch, die Daten des Klavierfestivals werden in OPAS noch nicht eingetragen

            if($l_performance==1 && $project_id>0) {

                if(strtoupper(substr($location,0,2)) == 'NS') {
                    $this->no_pds_ns++;
                } else {
                    $this->no_pds_nons++;
                }
                //print_r($location.'#'.$this->no_pds_nons.'#'.$this->no_pds_ns.'#');

                $l_capt=false;
                foreach ($adate->adate_activities as $arow) {
                    $eventtypegroup_code = ($arow->seventtype->seventtypegroup ? $arow->seventtype->seventtypegroup->code : '');
                    if(($eventtypegroup_code=='CAPT')) {
                        $l_capt=true;
                    }
                }
                if($l_capt) {
                    $this->no_pds_capt++;
                }
            }

            $this->no_solo = sizeof($this->asoloists_all);
            $this->no_cond = sizeof($this->aconductors_all);
            $this->no_pds_fest = 0;
        }
        uasort($this->aconductors_all, array($this,"usort_soloists"));
        uasort($this->aorchestras_all, array($this,"usort_soloists"));
        uasort($this->asoloists_all, array($this,"usort_soloists"));




        //print_r('sizeof='.sizeof($this->aprojects));
        return $this;
    }

    /*********************************************/
    function fill_search_patterns()
    {
        //20210113
        // Ganz oben auf der Liste steht das Ausgabedatum bei “Oppdatert”. Das Datum wird jetzt in einem komischen Format ausgegeben mit Minuszeichen vor dem Jahr. Kannst Du das ändern in:
        // DD.MM.YYYY
        // Das ist die offizielle Norwegische Schreibweise (also mit Punkten dazwischen und Tag/Monat/Jahr-Reihenfolge).
        $today = date("d.m.Y");

        $projects_no = sizeof($this->aprojects);

        $this->templateProcessor->cloneBlock('block_projects', $projects_no, true, true);

        $i=0;
        foreach($this->aprojects as $this->project_id=>$this->aproject) {
            $i++;

            //11.-12. okt + 23.-29. nov
            $minmax = $this->aproject['min'] . ' - '.$this->aproject['max'];

            //$pagebreak = new PhpOffice\PhpWord\Element\TextRun();
            if($i<$projects_no) {
                $pagebreak = '<w:p><w:r><w:br w:type="page"/></w:r></w:p>';

            } else {
                $pagebreak = '';
            }

            //$this->templateProcessor->setValue('today#'.$i, $today);

            //$this->templateProcessor->setValue('minmax#'.$i, $minmax);


            $this->templateProcessor->setComplexBlock('projectinfo#'.$i, $this->get_projectinfo($i));

            $this->templateProcessor->setValue('pageBreak#'.$i, $pagebreak);
//die;
        }

        $this->templateProcessor->setComplexBlock('conductors', $this->get_conductors_all());
        $this->templateProcessor->setComplexBlock('soloorchs', $this->get_soloorchs_all());
        $this->templateProcessor->setComplexBlock('works', $this->get_works_all());

        //Anzahl aller l_performance Termine # Konzerte in Venues die mit NS anfangen (alle Säle im Haus beginnen mit NS, dann müssten wir alle Säle haben)
        //Alle l_Performance Termine in Sälen NS
        //Anzahl aller l_performance mit zusätzl. Terminart Gruppen Code CAPT
        //Anzahl aller Solisten
        //Anzahl aller Dirigenten (auch Dirigenten als Andere TN)
        //Kommt noch, die Daten des Klavierfestivals werden in OPAS noch nicht eingetragen
        $this->templateProcessor->setValue('no_pds_nons', $this->no_pds_nons);
        $this->templateProcessor->setValue('no_pds_ns', $this->no_pds_ns);
        $this->templateProcessor->setValue('no_pds_capt', $this->no_pds_capt);
        $this->templateProcessor->setValue('no_solo', $this->no_solo);
        $this->templateProcessor->setValue('no_cond', $this->no_cond);
        $this->templateProcessor->setValue('no_pds_fest', $this->no_pds_fest);

    }

    function get_projectinfo($project_no) {
        // 20201204
        // Die Tabelle ganz oben mit den Solisten, musikalische Leitung etc. muß dynamisch werden.

        //*/

        $styleFont = array();

        $project = ($this->aproject['project_name2']>'' ? $this->aproject['project_name2'] : $this->aproject['project']);

        $textrun = new PhpOffice\PhpWord\Element\TextRun();


        if($project_no>1){
            $textrun->addText(htmlspecialchars('-----'), array_merge($styleFont));
            $textrun->addTextBreak();
        }
        $textrun->addText(htmlspecialchars($project), array_merge($styleFont));
        //$textrun->addTextBreak();

        $this->get_conductors($textrun);
        $this->get_orchestras($textrun);
        $this->get_soloists($textrun);
        $this->get_program($textrun);
        $this->get_pds($textrun);
        $this->get_captation($textrun);

        return $textrun;
    }

    function get_pds($textrun) {
        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {

            //Performance Wochentag (2 erste Ziffern) JJ/MM/AAAA - (00h00) - Venue / Ort / Land wenn # France
            //Je 24/09/2020 - 20h - Auditorium NS / Lille
            $l_performance = $adate->seventtype->l_performance;

            if ($l_performance == 0) {
                continue;
            }
            //if((int)$adate->location_id==0) {continue;}


            $location = '';


            if ($adate->locationaddress) {

                $country = $adate->locationaddress->scountry->name;
                //print_r($country.'#');
                if (strtolower($country) == 'france') {
                    $country = '';
                }
                $location = $adate->locationaddress->name1;
                //print_r($location.'#');
                $location .= ($adate->locationaddress->place > '' ? ' - ' : '') . $adate->locationaddress->place;
                $location .= ($country > '' ? ' - ' : '') . $country;
            }

            if ($adate->start_) {
                $start = $adate->start_->format('H') . 'h' . ($adate->start_->format('i') <> '00' ? $adate->start_->format('i') : '');
            }
            $date = '';
            if($adate->date_) {
                $date = $adate->weekday . ' ' . $adate->date_->format('d/m/Y') . ' - ' . $start . ($location > '' ? ' - ' : '') . $location;
            }

            $styleFont = array();

            //if($count>1) {
            $textrun->addTextBreak();
            //}

            $textrun->addText(htmlspecialchars($date), $styleFont);

        }
        return $textrun;
    }

    function get_conductors($textrun) {
        $aconductors = array();

        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {

            if($adate->conductor_id>0 and !in_array($adate->conductor_id, $aconductors)) {
                $aconductors[] = $adate->conductor_id;
                $count++;

                $conductor = 'Direction : '.$this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);

                $styleFont = array();

                //if($count>1) {
                    $textrun->addTextBreak();
                //}

                $textrun->addText(htmlspecialchars($conductor), $styleFont);
            }
        }
        return $textrun;
    }

    function get_orchestras($textrun) {
        $aorchestras = array();

        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {

            if($adate->orchestra_id>0 and !in_array($adate->orchestra_id, $aorchestras)) {
                $aorchestras[] = $adate->orchestra_id;
                $count++;

                $orchestra = $adate->conductoraddress->name1;

                $styleFont = array();

                //if($count>1) {
                $textrun->addTextBreak();
                //}

                $textrun->addText(htmlspecialchars($orchestra), $styleFont);
            }
        }
        return $textrun;
    }

    function get_soloists($textrun) {

        //Punkt 5: Zuerst Vorname, Nachname und Instrument von Solisten, die in der Tabelle aDateWork_Soloists eingegeben sind.
        // Falls das Textfeld bei den Solisten beschrieben ist, dann soll das hier auch ausgedruckt werden.
        // Danach kommen Solisten, die bei aDate_Persons eingetragen sind mit einer sAddressgroup.Code=ORC.
        // Falls das Notesfeld beschrieben ist, dann soll das hier auch angezeigt werden.

        $asoloists = array();

        $styleFont = array();

        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {
            $adatework_soloists = $this->reporttools->getSoloists($date_id);
            foreach ( $adatework_soloists as $adatework_soloist) {
                $k=$adatework_soloist->artist_id.'_'.$adatework_soloist->instrument_id;

                $instrument = ($adatework_soloist->sinstrinstrument ? $adatework_soloist->sinstrinstrument->name : '');
                $name = $this->reporttools->getLongName($adatework_soloist->saddress->name2, '', $adatework_soloist->saddress->name1);
                $name_order = $adatework_soloist->saddress->name1.','.$adatework_soloist->saddress->name2;
                if(!in_array($k, $asoloists)) {
                    $asoloists[$k] = array(
                        'name'=>$name,
                        'name_order' => $name_order,
                        'instrument'=>$instrument
                    );
                }
            }
        }

        //uasort($asoloists, array($this,"usort_soloists"));

        $count = 0;
        foreach ( $asoloists as $asoloist) {
            $count++;

            //if($count>1) {
                $textrun->addTextBreak();
            //}
            $soloist = $asoloist['name'];
            if(!empty($instrument)) {
                $soloist = $instrument.' : '.$soloist;
            }

            $textrun->addText(htmlspecialchars($soloist), $styleFont);
        }
    }

    function get_conductors_all() {

        $textrun = new PhpOffice\PhpWord\Element\TextRun();

        //Vorname Name alphabetisch aller Dirigenten des ausgewählten Zeitraums
        $styleFont = array();

        $count = 0;

        foreach ( $this->aconductors_all as $arow) {
            $count++;

            if($count>1) {
             $textrun->addTextBreak();
            }
            $name = $arow['name'];

            $textrun->addText(htmlspecialchars($name), $styleFont);
        }

        return $textrun;
    }

    function get_soloorchs_all() {

        $textrun = new PhpOffice\PhpWord\Element\TextRun();

        //Vorname Name alphabetisch aller Dirigenten des ausgewählten Zeitraums
        $styleFont = array();

        $count = 0;

        foreach ( $this->aorchestras_all as $arow) {
            $count++;

            if($count>1) {
                $textrun->addTextBreak();
            }
            $name = $arow['name'];

            $textrun->addText(htmlspecialchars($name), $styleFont);
        }

        foreach ( $this->asoloists_all as $arow) {
            $count++;

            if($count>1) {
                $textrun->addTextBreak();
            }
            $name = $arow['name'];

            $textrun->addText(htmlspecialchars($name), $styleFont);
        }

        return $textrun;
    }

    function get_works_all() {

        $textrun = new PhpOffice\PhpWord\Element\TextRun();

        //Vorname Name alphabetisch aller Dirigenten des ausgewählten Zeitraums


        $count = 0;

        foreach ( $this->aworks_all as $arow) {
            $count++;

            if($count>1) {
                $textrun->addTextBreak();
            }
            $styleFont = array('bold'=>true);
            $textrun->addText(htmlspecialchars($arow['composer']), $styleFont);

            $styleFont = array('italic'=>true);
            $textrun->addText(htmlspecialchars($arow['title']), $styleFont);
        }

        return $textrun;
    }

    function get_projectnbs() {

        $textrun = new PhpOffice\PhpWord\Element\TextRun();

        //Vorname Name alphabetisch aller Dirigenten des ausgewählten Zeitraums


        $count = 0;

        foreach ( $this->aworks_all as $arow) {
            $count++;

            if($count>1) {
                $textrun->addTextBreak();
            }
            $styleFont = array('bold'=>true);
            $textrun->addText(htmlspecialchars($arow['composer']), $styleFont);

            $styleFont = array('italic'=>true);
            $textrun->addText(htmlspecialchars($arow['title']), $styleFont);
        }

        return $textrun;
    }


    function get_captation($textrun) {

        //Zeile nur, wenn zusätzl. Aktivität der Gruppe Code CAPT eingetragen bei einem Performancetermin

        $acaptations = array();
        $captations = '';
        $styleFont = array();

        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {

            foreach ($adate->adate_activities as $arow) {
                $eventtypegroup_code = ($arow->seventtype->seventtypegroup ? $arow->seventtype->seventtypegroup->code : '');
                if(!($eventtypegroup_code=='CAPT')) {continue;}

                $addact = ($arow->seventtype ? ($arow->seventtype->name2 > '' ? $arow->seventtype->name2 : $arow->seventtype->name) : '');

                if(!in_array($addact, $acaptations)) {
                    $acaptations[] = $addact;
                    $captations .= ($captations>'' ? ', ' : '') . $addact;
                }
            }
        }

        if($captations>'') {
            $styleFont = array('bold'=>true);
            $textrun->addText(htmlspecialchars('Captation : '), $styleFont);
            $styleFont = array();
            $textrun->addText(htmlspecialchars($captations), $styleFont);
        }
    }

    function usort_soloists($a, $b) {
        $frm_order_a = $a['name_order'];
        $frm_order_b = $b['name_order'];

        $l_greater = (
            $frm_order_a>$frm_order_b
        );

        return $l_greater;
    }

    function get_locations() {

        // Unterhalb des Schema’s mit den Musikern (Musikerliste) soll ein neuer Abschnitt kommen mit den Sälen.
        // Hier bitte pro Saal eine Zeile machen mit Name des Saales und der Adresse (Straße, Hausnummer, Plz, Ort, Land falls nicht sCountries.Code=N)

        $textrun = new PhpOffice\PhpWord\Element\TextRun();

//$textrun->addText($sql);

        $styleFont = array();
        $styleFont_bold = array('bold' => true);

        $alocations = array();

        $count = 0;
        foreach($this->aproject['adates'] as $date_id => $adate) {

            if($adate->location_id>0 and !in_array($adate->location_id, $alocations)) {
                $alocations[] = $adate->location_id;
                $count++;
                if ($count > 1) {
                    $textrun->addTextBreak();
                }

                $zip_place = trim($adate->locationaddress->zipcode . ' ' . $adate->locationaddress->place);
                $country = $adate->locationaddress->scountry->country;
                if ($country == 'Norge') {
                    $country = '';
                }
                $location = $adate->locationaddress->name1;
                $location .= (!empty($location) && !empty($adate->locationaddress->street) ? ', ' : '') . $adate->locationaddress->street;
                $location .= (!empty($location) && !empty($zip_place) ? ', ' : '') . $zip_place;
                $location .= (!empty($location) && !empty($country) ? ', ' : '') . $country;

                $textrun->addText(htmlspecialchars($location), array_merge($styleFont));
            }
        }

        return $textrun;
    }


   function get_program($textrun) {

        //Name Komponist : worktitel 1

        $date_ids = implode(', ', array_keys($this->aproject['adates']));

         $adate_works = TableRegistry::getTableLocator()->get('AdateWorks')
            ->find('all')
             ->select([
                 'AdateWorks.id',
                 'AdateWorks.work_order',
                 'AdateWorks.date_id',
                 'AdateWorks.work_id',
                 'AdateWorks.duration',
                 'AdateWorks.title2'
             ])
             ->contain('Sworks', function (Query $query) {
                 return $query->select([
                     'Sworks.id',
                     'Sworks.title1'
                 ])->contain('Scomposers', function (Query $query) {
                     return $query->select([
                         'Scomposers.id',
                         'Scomposers.lastname',
                         'Scomposers.firstname'
                     ]);
                 });
             })
             ->contain('Adates', function (Query $query) {
                 return $query->select([
                     'Adates.id',
                     'Adates.date_',
                     'Adates.start_'
                 ]);
             })
            ->where('AdateWorks.date_id in ('.$date_ids.') and Sworks.l_intermission=0')
            ->order('Adates.date_, Adates.start_, AdateWorks.work_order');

        $aworks = array();

        foreach ($adate_works as $awork) {

            $count = 0;
            foreach($this->aproject['adates'] as $date_id => $adate) {
                if ($awork->work_id > 0 and !in_array($awork->work_id, $aworks)) {
                    $aworks[] = $awork->work_id;

                    $textrun->addTextBreak();

                    $title1 = trim($awork->swork->title1);
                    $composer = ($awork->swork->scomposer ? $awork->swork->scomposer->lastname . ' : ' : '');

                    $styleFont = array('bold' => true);
                    $textrun->addText(htmlspecialchars($composer), $styleFont);
                    $styleFont = array();
                    $textrun->addText(htmlspecialchars($title1), $styleFont);

                }
            }
        }
        return $textrun;
    }

    public function addMemo($section, $text, $styleFont = array()) {
        $textlines = explode("\n", $text);

        foreach($textlines as $line) {
            $section->addText(htmlspecialchars($line),  $styleFont);
            $section->addTextBreak();
        }
    }

}
