<?xml version="1.0" encoding="UTF-8" ?>
<maintables xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:noNamespaceSchemaLocation="../../../plugins/OpasOnline/config/oo_export.xsd">
    <!-- Adates  -->
    <Entry>
        <table>adates</table>
        <!-- <where>(sseasons.code='2019' OR sseasons.code='2020') AND (adates.planninglevel=1 OR adates.planninglevel=3)</where> -->
        <where>adates.year>=2018</where>
        <!-- <where_join>INNER JOIN sseasons ON adates.season_id = sseasons.id</where_join> -->
        <where_join>INNER JOIN sseasons ON adates.season_id = sseasons.id</where_join>
        <sqlonstart>DELETE FROM saddresses WHERE id>0</sqlonstart>
        <sqlonend></sqlonend>
        <fields>
            <field>id</field>
            <field>planninglevel</field>
            <field>sysgroup_id</field>
            <field>code</field>
            <field>date_</field>
            <field>weekday</field>
            <field>start_</field>
            <field>end_</field>
            <field>week</field>
            <field>duration</field>
            <field>month</field>
            <field>year</field>
            <field>pweek</field>
            <field>block1</field>
            <field>block3</field>
            <field>duties</field>
            <field>season_id</field>
            <field>project_id</field>
            <field>eventtype_id</field>
            <field>location_id</field>
            <field>conductor_id</field>
            <field>orchestra_id</field>
            <field>text</field>
            <field>abbreviation</field>
            <field>programno</field>
            <field>programtitle</field>
            <field>notes</field>
            <field>dress_id</field>

            <!-- <field>status_id</field>
                 <field>insertdate</field>
                 <field>insertuser</field>
                 <field>updatedate</field>
                 <field>updateuser</field>
                 <field>sysclient_id</field>     -->
        </fields>
        <relations>
            <relation table="adate_activities" source_id="id" target_id="date_id">
                <fields>
                    <field>id</field>
                    <field>date_id</field>
                    <field>eventtype_id</field>
                    <field>l_defaultaccounting</field>
                    <field>activity_order</field>
                    <field>text</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

            <!-- <relation table="adate_persons" source_id="id" target_id="date_id">
              <fields>
                <field>id</field>
                <field>date_id</field>
                <field>person_order</field>
                <field>address_id</field>
                <field>addressgroup_id</field>
                <field>function_id</field>
                <field>instrument_id</field>
                <field>notes</field>
                <field>insertdate</field>
                <field>insertuser</field>
                <field>updatedate</field>
                <field>updateuser</field>
                <field>sysclient_id</field>
              </fields>
            </relation> -->

            <relation table="adate_works" source_id="id" target_id="date_id">
                <fields>
                    <field>id</field>
                    <field>date_id</field>
                    <field>work_id</field>
                    <field>work_order</field>
                    <field>title2</field>
                    <field>title3</field>
                    <field>duration</field>
                    <field>year</field>
                    <field>arrangement</field>
                    <field>l_encore</field>
                    <field>premiere_id</field>
                    <field>flute_text</field>
                    <field>flute</field>
                    <field>oboe_text</field>
                    <field>oboe</field>
                    <field>clarinet_text</field>
                    <field>clarinet</field>
                    <field>bassoon_text</field>
                    <field>bassoon</field>
                    <field>horn_text</field>
                    <field>horn</field>
                    <field>trumpet_text</field>
                    <field>trumpet</field>
                    <field>trombone_text</field>
                    <field>trombone</field>
                    <field>tuba_text</field>
                    <field>tuba</field>
                    <field>timpani_text</field>
                    <field>timpani</field>
                    <field>harp_text</field>
                    <field>harp</field>
                    <field>violin1</field>
                    <field>violin2</field>
                    <field>viola</field>
                    <field>cello</field>
                    <field>bass</field>
                    <field>strings_text</field>
                    <field>percussion</field>
                    <field>percussion_text</field>
                    <field>keyboard</field>
                    <field>keyboard_text</field>
                    <field>extra</field>
                    <field>extra_text</field>
                    <field>vocals</field>
                    <field>vocals_text</field>
                    <field>details</field>
                    <field>notes</field>
                    <field>insertdate</field>
                    <field>updatedate</field>
                    <field>insertuser</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
                <relations>

                    <relation table="adatework_movements" source_id="id" target_id="datework_id">
                        <fields>
                            <field>id</field>
                            <field>datework_id</field>
                            <field>name</field>
                            <field>duration</field>
                            <field>movement_order</field>
                            <field>sourcework_id</field>
                            <field>insertdate</field>
                            <field>insertuser</field>
                            <field>updatedate</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                        </fields>
                    </relation>

                    <relation table="adatework_soloists" source_id="id" target_id="datework_id">
                        <fields>
                            <field>id</field>
                            <field>datework_id</field>
                            <field>artist_order</field>
                            <field>artist_order2</field>
                            <field>artist_id</field>
                            <field>instrument_id</field>
                            <field>notes</field>
                            <field>insertdate</field>
                            <field>updatedate</field>
                            <field>insertuser</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                        </fields>
                    </relation>

                    <relation table="adatework_soloists_archive" source_id="id" target_id="datework_id">
                        <fields>
                            <field>id</field>
                            <field>datework_id</field>
                            <field>artist_order</field>
                            <field>artist_order2</field>
                            <field>artist_id</field>
                            <field>instrument_id</field>
                            <field>notes</field>
                            <field>insertdate</field>
                            <field>updatedate</field>
                            <field>insertuser</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                        </fields>
                    </relation>

                    <!-- Sworks -->
                    <relation table="sworks" source_id="work_id" target_id="id">
                        <fields>
                            <field>id</field>
                            <field>code</field>
                            <field>composer_id</field>
                            <field>l_regular</field>
                            <field>l_intermission</field>
                            <field>genre_id</field>
                            <field>style_id</field>
                            <field>title1</field>
                            <field>title2</field>
                            <field>title3</field>
                            <field>workoriginal_id</field>
                            <field>catalog</field>
                            <field>key_</field>
                            <field>compyear</field>
                            <field>compyear2</field>
                            <field>compyearstatus</field>
                            <field>duration</field>
                            <field>arrangement</field>
                            <field>commission</field>
                            <field>l_activated</field>
                            <field>sourcetext</field>
                            <field>details</field>
                            <field>details_2</field>
                            <field>notes</field>
                            <field>sourcecode</field>
                            <field>insertdate</field>
                            <field>insertuser</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                            <field>flute_text</field>
                            <field>flute_text_2</field>
                            <field>flute</field>
                            <field>oboe_text</field>
                            <field>oboe_text_2</field>
                            <field>oboe</field>
                            <field>clarinet_text</field>
                            <field>clarinet_text_2</field>
                            <field>clarinet</field>
                            <field>bassoon_text</field>
                            <field>bassoon_text_2</field>
                            <field>bassoon</field>
                            <field>horn_text</field>
                            <field>horn_text_2</field>
                            <field>horn</field>
                            <field>trumpet_text</field>
                            <field>trumpet_text_2</field>
                            <field>trumpet</field>
                            <field>trombone_text</field>
                            <field>trombone_text_2</field>
                            <field>trombone</field>
                            <field>horn_text</field>
                            <field>horn_text_2</field>
                            <field>horn</field>
                            <field>tuba_text</field>
                            <field>tuba_text_2</field>
                            <field>tuba</field>
                            <field>timpani_text</field>
                            <field>timpani_text_2</field>
                            <field>timpani</field>
                            <field>harp_text</field>
                            <field>harp_text_2</field>
                            <field>harp</field>
                            <field>violin1</field>
                            <field>violin2</field>
                            <field>viola</field>
                            <field>cello</field>
                            <field>bass</field>
                            <field>strings_text</field>
                            <field>strings_text_2</field>
                            <field>percussion</field>
                            <field>percussion_text</field>
                            <field>percussion_text_2</field>
                            <field>keyboard</field>
                            <field>keyboard_text</field>
                            <field>keyboard_text_2</field>
                            <field>extra</field>
                            <field>extra_text</field>
                            <field>extra_text_2</field>
                            <field>vocals</field>
                            <field>vocals_text</field>
                            <field>vocals_text_2</field>
                        </fields>
                    </relation>

                    <!-- swork_premieres
                        <relation table="swork_premieres" source_id="id" target_id="id">
                      <fields>
                        <field>id</field>
                        <field>premiereyear</field>
                      </fields>
                    </relation>
                    -->

                    <!-- Swork_vocals -->
                    <relation table="swork_vocals" source_id="work_id" target_id="id">
                        <fields>
                            <field>id</field>
                            <field>work_id</field>
                            <field>instrument_order</field>
                            <field>instrument_id</field>
                            <field>number_</field>
                            <field>text</field>
                            <field>insertdate</field>
                            <field>insertuser</field>
                            <field>updatedate</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                        </fields>
                    </relation>

                    <!-- Swork_soloinstr -->
                    <relation table="swork_soloinstr" source_id="work_id" target_id="id">
                        <fields>
                            <field>id</field>
                            <field>work_id</field>
                            <field>instrument_order</field>
                            <field>instrument_id</field>
                            <field>number_</field>
                            <field>text</field>
                            <field>insertdate</field>
                            <field>insertuser</field>
                            <field>updatedate</field>
                            <field>updateuser</field>
                        </fields>
                    </relation>

                    <!-- Swork_percussions -->
                    <relation table="swork_percussions" source_id="work_id" target_id="id">
                        <fields>
                            <field>id</field>
                            <field>work_id</field>
                            <field>instrument_order</field>
                            <field>instrument_id</field>
                            <field>number_</field>
                            <field>text</field>
                            <field>insertdate</field>
                            <field>insertuser</field>
                            <field>updatedate</field>
                            <field>updateuser</field>
                            <field>sysclient_id</field>
                        </fields>
                    </relation>

                    <!-- SworkMovements -->
                    <relation table="swork_movements" source_id="work_id" target_id="id">
                        <fields>
                            <field>id</field>
                            <field>work_id</field>
                            <field>movement_order</field>
                            <field>name</field>
                            <field>duration</field>
                            <field>sourcework_id</field>
                        </fields>
                    </relation>

                </relations>
            </relation>

            <relation table="sdresses" source_id="dress_id" target_id="id">
                <fields>
                    <field>id</field>
                    <field>code</field>
                    <field>name</field>
                    <field>name2</field>
                    <field>men</field>
                    <field>women</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

            <relation table="sdatestatuses" source_id="status_id" target_id="id">
                <fields>
                    <field>id</field>
                    <field>code</field>
                    <field>name</field>
                    <field>name2</field>
                    <field>colour_id</field>
                    <field>notes</field>
                    <field>tag1</field>
                    <field>tag2</field>
                    <field>tag3</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

        </relations>
        <clear_tbl>1</clear_tbl>
        <export_ids>1</export_ids>
    </Entry>

    <!-- Adays -->
    <Entry>
        <table>adays</table>
        <where>year>=2018 AND planninglevel = 1</where>
        <!-- <where>(planninglevel = 1 or planninglevel = 3) AND year=2020</where> -->
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>planninglevel</field>
            <field>date_</field>
            <field>season_id</field>
            <field>week</field>
            <field>pweek</field>
            <field>weekday</field>
            <field>month</field>
            <field>year</field>
            <field>block1</field>
            <field>block3</field>
            <field>duties</field>
            <field>text_</field>
            <field>notes</field>
            <field>status_id</field>
            <field>holyday_id</field>
            <field>colour_id</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- new Adaysperiods -->
    <Entry>
        <table>adaysperiods</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>planninglevel</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>text</field>
            <field>date1</field>
            <field>date2</field>
            <field>project_id</field>
            <field>project_text</field>
            <field>address_id</field>
            <field>address_text</field>
            <field>conductor_id</field>
            <field>conductor_text</field>
            <field>program_text</field>
            <field>soloist_id</field>
            <field>soloist_text</field>
            <field>instrument_id</field>
            <field>instrument_text</field>
            <field>hours</field>
            <field>duties</field>
            <field>days</field>
            <field>notes</field>
            <field>colour_id</field>
            <field>html_color</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
            <field>ts_update</field>
            <field>tag1</field>
            <field>tag2</field>
            <field>tag3</field>
            <field>l_activated</field>
            <field>danielskey</field>
            <field>universalkey</field>
        </fields>
    </Entry>

    <!-- Aduties-->
    <!-- <Entry>
      <table>aduties</table>
      <where>aduties.id>0</where>
      <where_join>INNER JOIN adates ON aduties.date_id=adates.id</where_join>
      <sqlonstart></sqlonstart>
      <sqlonend></sqlonend>
      <clear_tbl>1</clear_tbl>
      <fields>
        <field>id</field>
        <field>date_id</field>
        <field>duties</field>
        <field>start_</field>
        <field>end_</field>
        <field>duration</field>
        <field>artist_id</field>
        <field>instrument_id</field>
        <field>dutytype_id</field>
        <field>addressgroup_id</field>
        <field>function_id</field>
        <field>artist2_id</field>
        <field>jobreason_id</field>
        <field>accounting_category</field>
        <field>seat</field>
        <field>order_1</field>
        <field>order_2</field>
        <field>l_fixed</field>
        <field>notes</field>
        <field>arrangements</field>
        <field>l_keep</field>
        <field>l_lock</field>
        <field>insertdate</field>
        <field>insertuser</field>
        <field>updatedate</field>
        <field>updateuser</field>
        <field>sysclient_id</field>
      </fields>
      <export_ids>1</export_ids>
    </Entry>-->

    <!-- Atodo-->
    <Entry>
        <table>atodo</table>
        <!-- <where>atodo.date_id>0 or atodo.project_id>0</where> -->
        <where>sseasons.code= '2018-19' and adates.l_ticket_online = 1</where>

        <!-- <where_join>sseasons ON adates.season_id=sseasons.id</where_join> -->
        <where_join>INNER JOIN adates ON adates.id = atodo.date_id
            INNER JOIN sseasons ON adates.season_id=sseasons.id
        </where_join>

        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>date_id</field>
            <field>contractsolo_id</field>
            <field>contractsubs_id</field>
            <field>tour_id</field>
            <field>library_id</field>
            <field>project_id</field>
            <field>work_id</field>
            <field>datemarketing_id</field>
            <field>datework_id</field>
            <field>addressmaster_id</field>
            <field>address_id</field>
            <field>job_order</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>date_planned</field>
            <field>time_</field>
            <field>weekday</field>
            <field>week</field>
            <field>month</field>
            <field>year</field>
            <field>date_done</field>
            <field>person</field>
            <field>priority</field>
            <field>status_id</field>
            <field>notes</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
        <relations>
            <relation table="stodostatus" source_id="status_id" target_id="id">
                <fields>
                    <field>id</field>
                    <field>code</field>
                    <field>name</field>
                    <field>name2</field>
                    <field>colour_id</field>
                    <field>l_todolist</field>
                    <field>tag1</field>
                    <field>tag2</field>
                    <field>tag3</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                    <field>l_activated</field>
                </fields>
            </relation>
        </relations>
    </Entry>

    <!-- Saddresses-->
    <Entry>
        <table>saddresses</table>
        <!-- <where>saddresses.id>0</where> -->
        <where>saddresses.id IN (SELECT adates.location_id FROM adates WHERE adates.year>=2015 AND
            adates.planninglevel=1) OR
            saddresses.id IN (SELECT adates.conductor_id FROM adates WHERE adates.year>=2015 AND adates.planninglevel=1)
            OR
            saddresses.id IN (SELECT adates.orchestra_id FROM adates WHERE adates.year>=2015 AND adates.planninglevel=1)
            OR
            saddresses.id IN (SELECT adatework_soloists.artist_id FROM adates
            INNER JOIN adate_works ON adates.id = adate_works.date_id
            INNER JOIN adatework_soloists ON adate_works.id = adatework_soloists.datework_id
            WHERE adates.year>=2015 AND adates.planninglevel=1) OR
            saddresses.id IN (SELECT saddress_addressgroups.address_id FROM saddress_addressgroups
            INNER JOIN saddressgroups ON saddress_addressgroups.addressgroup_id = saddressgroups.id
            LEFT JOIN saddresssysgroups ON saddressgroups.sysgroup_id = saddresssysgroups.id
            WHERE saddresssysgroups.code = 'STA' OR saddresssysgroups.code = 'SUB')
        </where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <relations>

            <relation table="saddress_addressgroups" source_id="id" target_id="address_id">
                <fields>
                    <field>id</field>
                    <field>address_id</field>
                    <field>addressgroup_id</field>
                    <field>l_main</field>
                    <field>insertdate</field>
                    <field>updatedate</field>
                    <field>insertuser</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

            <relation table="saddress_duties" source_id="id" target_id="address_id">
                <fields>
                    <field>id</field>
                    <field>address_id</field>
                    <field>effective_date</field>
                    <field>days</field>
                    <field>days2</field>
                    <field>duties</field>
                    <field>duties2</field>
                    <field>hours</field>
                    <field>hours2</field>
                    <field>dayspercent</field>
                    <field>dayspercent2</field>
                    <field>dutiespercent</field>
                    <field>dutiespercent2</field>
                    <field>hourspercent</field>
                    <field>hourspercent2</field>
                    <field>daysfree</field>
                    <field>daysfree2</field>
                    <field>dutiesfree</field>
                    <field>dutiesfree2</field>
                    <field>hoursfree</field>
                    <field>hoursfree2</field>
                    <field>daysfreepercent</field>
                    <field>daysfreepercent2</field>
                    <field>dutiesfreepercent</field>
                    <field>dutiesfreepercent2</field>
                    <field>hoursfreepercent</field>
                    <field>hoursfreepercent2</field>
                    <field>text</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

            <relation table="saddress_functions" source_id="id" target_id="address_id">
                <fields>
                    <field>id</field>
                    <field>address_id</field>
                    <field>function_id</field>
                    <field>l_main</field>
                    <field>notes</field>
                    <field>insertdate</field>
                    <field>updatedate</field>
                    <field>insertuser</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>

            <relation table="saddress_numbers" source_id="id" target_id="address_id">
                <fields>
                    <field>id</field>
                    <field>address_id</field>
                    <field>number_order</field>
                    <field>number_</field>
                    <field>text</field>
                    <field>insertdate</field>
                    <field>updatedate</field>
                    <field>insertuser</field>
                    <field>updateuser</field>
                    <field>numbertype_id</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>
        </relations>
        <fields>
            <field>id</field>
            <field>category_id</field>
            <field>addressmaster_id</field>
            <field>code</field>
            <field>mark</field>
            <field>order_1</field>
            <field>order_2</field>
            <field>seat</field>
            <field>salutation_id</field>
            <field>title_id</field>
            <field>country_id</field>
            <field>name1</field>
            <field>name2</field>
            <field>name3</field>
            <field>name4</field>
            <field>name5</field>
            <field>street</field>
            <field>pobox</field>
            <field>state</field>
            <field>zipcode</field>
            <field>place</field>
            <field>address</field>
            <field>l_activated</field>
            <field>notes</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Saddressfunctions-->
    <Entry>
        <table>saddressfunctions</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>accitem_id</field>
            <field>expensetype_id</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- SaddressInstruments-->
    <Entry>
        <table>saddress_instruments</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>address_id</field>
            <field>instrument_id</field>
            <field>l_main</field>
            <field>notes</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Saddressgroups-->
    <Entry>
        <table>saddressgroups</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>sysgroup_id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
            <!--<field>l_contract</field> -->
        </fields>
    </Entry>

    <!-- SaddressNumbers-->
    <Entry>
        <table>saddress_numbers</table>
        <!-- <where>saddress_numbers.id>0</where> -->
        <where>saddress_numbers.address_id IN (
            SELECT saddress_addressgroups.address_id
            FROM saddress_addressgroups
            INNER JOIN saddressgroups ON saddress_addressgroups.addressgroup_id = saddressgroups.id
            INNER JOIN saddresssysgroups ON saddressgroups.sysgroup_id = saddresssysgroups.id
            WHERE
            ( saddresssysgroups.code = 'LOC'))
        </where>
        <where_join>LEFT JOIN snumbertypes ON saddress_numbers.numbertype_id = snumbertypes.id</where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>address_id</field>
            <field>number_order</field>
            <field>number_</field>
            <field>text</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>numbertype_id</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Saddresssysgroups-->
    <Entry>
        <table>saddresssysgroups</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
            <field>address_type</field>
        </fields>
    </Entry>

    <!-- Scomposers-->
    <Entry>
        <table>scomposers</table>
        <where>id</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>lastname</field>
            <field>firstname</field>
            <field>name2</field>
            <field>sex</field>
            <field>birthday</field>
            <field>birthmonth</field>
            <field>birthyear</field>
            <field>birthplace</field>
            <field>birthstate</field>
            <field>deathday</field>
            <field>birthcountry_id</field>
            <field>deathmonth</field>
            <field>deathyear</field>
            <field>deathplace</field>
            <field>deathstate</field>
            <field>deathcountry_id</field>
            <field>address_id</field>
            <field>notes</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Scountries-->
    <Entry>
        <table>scountries</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>nationality</field>
            <field>currency_id</field>
            <field>l_ec</field>
            <field>language_id</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Seventtypes-->
    <Entry>
        <table>seventtypes</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>abbreviation</field>
            <field>start_</field>
            <field>end_</field>
            <field>l_performance</field>
            <field>duties</field>
            <field>writetextfield</field>
            <field>accountno</field>
            <field>expensetype_id</field>
            <field>group_id</field>
            <field>colour_id</field>
            <field>l_dates</field>
            <field>l_days</field>
            <field>l_tours</field>
            <field>l_addactivities</field>
            <field>l_duties</field>
            <field>l_save2relateddates</field>
            <field>notes</field>
            <field>text_1</field>
            <field>l_text_1_s</field>
            <field>text_2</field>
            <field>l_text_2_s</field>
            <field>text_3</field>
            <field>l_text_3_s</field>
            <field>text_4</field>
            <field>l_text_4_s</field>
            <field>text_5</field>
            <field>l_text_5_s</field>
            <field>text_6</field>
            <field>l_text_6_s</field>
            <field>text_7</field>
            <field>l_text_7_s</field>
            <field>text_8</field>
            <field>l_text_8_s</field>
            <field>text_9</field>
            <field>l_text_9_s</field>
            <field>text_10</field>
            <field>l_text_10_s</field>
            <field>number_1</field>
            <field>l_number_1_s</field>
            <field>number_2</field>
            <field>l_number_2_s</field>
            <field>number_3</field>
            <field>l_number_3_s</field>
            <field>number_4</field>
            <field>l_number_4_s</field>
            <field>number_5</field>
            <field>l_number_5_s</field>
            <field>date_1</field>
            <field>l_date_1_s</field>
            <field>date_2</field>
            <field>l_date_2_s</field>
            <field>date_3</field>
            <field>l_date_3_s</field>
            <field>date_4</field>
            <field>l_date_4_s</field>
            <field>date_5</field>
            <field>l_date_5_s</field>
            <field>memo_1</field>
            <field>l_memo_1_s</field>
            <field>memo_2</field>
            <field>l_memo_2_s</field>
            <field>logic_1</field>
            <field>l_logic_1_s</field>
            <field>logic_2</field>
            <field>l_logic_2_s</field>
            <field>logic_3</field>
            <field>l_logic_3_s</field>
            <field>l_address_1_s</field>
            <field>address_1</field>
            <field>l_address_2_s</field>
            <field>address_2</field>
            <field>l_address_3_s</field>
            <field>address_3</field>
            <field>time_1</field>
            <field>l_time_1_s</field>
            <field>time_2</field>
            <field>l_time_2_s</field>
            <field>l_text_m_1_s</field>
            <field>text_m_1</field>
            <field>l_text_m_2_s</field>
            <field>text_m_2</field>
            <field>l_text_m_3_s</field>
            <field>text_m_3</field>
            <field>l_text_m_4_s</field>
            <field>text_m_4</field>
            <field>l_text_m_5_s</field>
            <field>text_m_5</field>
            <field>l_text_m_6_s</field>
            <field>text_m_6</field>
            <field>l_text_m_7_s</field>
            <field>text_m_7</field>
            <field>l_text_m_8_s</field>
            <field>text_m_8</field>
            <field>l_text_m_9_s</field>
            <field>text_m_9</field>
            <field>l_text_m_10_s</field>
            <field>text_m_10</field>
            <field>l_number_m_1_s</field>
            <field>number_m_1</field>
            <field>l_number_m_2_s</field>
            <field>number_m_2</field>
            <field>l_number_m_3_s</field>
            <field>number_m_3</field>
            <field>l_number_m_4_s</field>
            <field>number_m_4</field>
            <field>l_number_m_5_s</field>
            <field>number_m_5</field>
            <field>l_date_m_1_s</field>
            <field>date_m_1</field>
            <field>l_date_m_2_s</field>
            <field>date_m_2</field>
            <field>l_date_m_3_s</field>
            <field>date_m_3</field>
            <field>l_date_m_4_s</field>
            <field>date_m_4</field>
            <field>l_date_m_5_s</field>
            <field>date_m_5</field>
            <field>l_memo_m_1_s</field>
            <field>memo_m_1</field>
            <field>l_memo_m_2_s</field>
            <field>memo_m_2</field>
            <field>l_logic_m_1_s</field>
            <field>logic_m_1</field>
            <field>l_logic_m_2_s</field>
            <field>logic_m_2</field>
            <field>l_logic_m_3_s</field>
            <field>logic_m_3</field>
            <field>l_address_m_1_s</field>
            <field>address_m_1</field>
            <field>l_address_m_2_s</field>
            <field>address_m_2</field>
            <field>l_address_m_3_s</field>
            <field>address_m_3</field>
            <field>l_time_m_1_s</field>
            <field>time_m_1</field>
            <field>l_time_m_2_s</field>
            <field>time_m_2</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
        <relations>
            <relation table="seventtypegroups" source_id="group_id" target_id="id">
                <fields>
                    <field>id</field>
                    <field>code</field>
                    <field>name</field>
                    <field>name2</field>
                    <field>insertdate</field>
                    <field>insertuser</field>
                    <field>updatedate</field>
                    <field>updateuser</field>
                    <field>sysclient_id</field>
                </fields>
            </relation>
        </relations>
    </Entry>

    <!-- Sexpensetypes-->
    <Entry>
        <table>sexpensetypes</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>expense_order</field>
            <field>accitem2_id</field>
            <field>accountno</field>
            <field>group_id</field>
            <field>accountingperiod</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sholydays-->
    <Entry>
        <table>sholydays</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>day</field>
            <field>month</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sinstrinstruments-->
    <Entry>
        <table>sinstrinstruments</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>section_id</field>
            <field>job_id</field>
            <field>instrument_order</field>
            <field>l_doubling</field>
            <field>accitem_id</field>
            <field>expensetype_id</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sinstrsections-->
    <Entry>
        <table>sinstrsections</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>sysgroup_id</field>
            <field>syssection_id</field>
            <field>section_order</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sinstrsyssections-->
    <Entry>
        <table>sinstrsyssections</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>section_order</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Snumbertypes-->
    <Entry>
        <table>snumbertypes</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>l_phone</field>
            <field>l_fax</field>
            <field>l_mobile</field>
            <field>l_email</field>
            <field>l_url</field>
            <field>l_work</field>
            <field>l_home</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sprojects-->
    <Entry>
        <table>sprojects</table>
        <where>sprojects.id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>type_id</field>
            <field>code</field>
            <field>name</field>
        </fields>
    </Entry>

    <!-- Sseasons-->
    <Entry>
        <table>sseasons</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>datestart</field>
            <field>dateend</field>
            <field>datestart2</field>
            <field>dateend2</field>
            <field>period</field>
            <field>days</field>
            <field>duties</field>
            <field>hours</field>
            <field>dutmaxday</field>
            <field>hrsmaxday</field>
            <field>daymaxweek</field>
            <field>dutmaxweek</field>
            <field>hrsmaxweek</field>
            <field>daymaxblock</field>
            <field>dutmaxblock</field>
            <field>hrsmaxblock</field>
            <field>daymaxblock3</field>
            <field>dutmaxblock3</field>
            <field>hrsmaxblock3</field>
            <field>daymaxseason</field>
            <field>dutmaxseason</field>
            <field>hrsmaxseason</field>
            <field>number1</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sworkgenres-->
    <Entry>
        <table>sworkgenres</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Swork_extras -->
    <Entry>
        <table>swork_extras</table>
        <where>work_id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>work_id</field>
            <field>instrument_order</field>
            <field>instrument_id</field>
            <field>number_</field>
            <field>text</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Swork_keyboards -->
    <Entry>
        <table>swork_keyboards</table>
        <where>work_id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>work_id</field>
            <field>instrument_order</field>
            <field>instrument_id</field>
            <field>number_</field>
            <field>text</field>
            <field>insertdate</field>
            <field>insertuser</field>
            <field>updatedate</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sworkpremieres -->
    <Entry>
        <table>sworkpremieres</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
        </fields>
    </Entry>

    <!-- Sworkstyles -->
    <Entry>
        <table>sworkstyles</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>

    <!-- Sdutytypes -->
    <Entry>
        <table>sdutytypes</table>
        <where>id>0</where>
        <where_join></where_join>
        <sqlonstart></sqlonstart>
        <sqlonend></sqlonend>
        <clear_tbl>1</clear_tbl>
        <fields>
            <field>id</field>
            <field>code</field>
            <field>name</field>
            <field>name2</field>
            <field>l_present</field>
            <field>percentpresent</field>
            <field>percentfree</field>
            <field>percentcount</field>
            <field>percentperdiem</field>
            <field>l_standby</field>
            <field>colour_id</field>
            <field>accitem_id</field>
            <field>l_netcompensation</field>
            <field>insertdate</field>
            <field>updatedate</field>
            <field>insertuser</field>
            <field>updateuser</field>
            <field>sysclient_id</field>
        </fields>
    </Entry>
</maintables>
