<?php

return [

    'Error' => [
        'errorLevel' => 0
    ],

    'App' => [
        'defaultLocale' => 'sv_SE'
    ],

    'CustomerSettings' => [
        'name' => 'Östgötamusiken',
        'timezone' => 'Europe/Stockholm'
    ],

    'DisplayLanguages' => [
        'sv_SE' => 'swedish',
        'en_GB' => 'uk english',
        'de_DE' => 'german',
        'fr_FR' => 'french'
    ],

    'Calendar' => [
        'Display' => [
            'startTime' => '08:00:00',
            'endTime' => '24:00:00'
        ],
        'Weeks' => [
            'FirstDayofWeek' => 'Mo',
        ],
        'Season' => [
            'ProductionWeek' => [
                'StartWith' => 1,
                'StartAtFirstDayOfWeek' => 0
            ]
        ]
    ],

    'Formats' => [
        'date' => 'd/m/Y',
        'time' => 'H:i:s',
        'time_short' => 'H:i',
        'full' => 'd/m/Y H:i:s',
        'week' => 'y-W',
        'momentjsFullDate' => 'DD/MM/YYYY HH:mm',
        'momentjsDate' => 'DD/MM/YYYY',
        'momentjsTime' => 'HH:mm',
        'momentjsWeek' => 'YY-WW',
        'calendar' => [
            'monthColumnHeaderFormat' => 'MMM',
            'weekColumnHeaderFormat' => 'dddd DD/MM',
            'dayColumnHeaderFormat' => 'dddd',
            'eventTimeFormat' => [
                'hour' => '2-digit', // 'numeric' (7:12), '2-digit' (07:12)
                'minute' => '2-digit', // 'numeric' (7:5), '2-digit' (7:05)
                'meridiem' => false, // false (19:00), 'narrow' (7p), 'short' (7pm)
                'hour12' => false, // false || true (show 12 hour format. set true if meridiem is used)
            ],
        ]
    ],

    'Modules' => [
        'Contract' => [
            'enabled' => 1
        ],
        'Service' => [
            'enabled' => 1
        ],
        'Daniels' => [
            'enabled' => 1
        ],
    ],

    'Daniels' => [
        'userId' => '8368',
        'token' => '1fad97d435d17fbb',
    ],

    'OpasOnline' => [
        'enable' => 1,
        'memory_limit' => '2G',
        //Test mit FTP
        'ftp' => [
            'sftp' => 0,
            'host' => 'ogm.opas-online.com',
            'port' => '21',
            'timeout' => '30',
            'username' => 'ftp_ogm_oo',
            'password' => 'vaZA8c9#C',
            'directory' => 'pickup'
        ]
    ]

];
