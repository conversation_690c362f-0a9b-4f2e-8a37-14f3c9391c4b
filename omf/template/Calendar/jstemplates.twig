{% extends ('../Calendar/jstemplates.twig') %}

{% block adatesTextMonthTemplate %}
  <script type="text/x-jsrender" id="adates_text_month_template">
    <div class="event-text-month">
      PL<%: event.extendedProps.model.planninglevel() %><br/>
      <%if event.extendedProps.model.eventtype_id() %>
        <%: event.extendedProps.model.seventtype.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.project_id() %>
        <%: event.extendedProps.model.sproject.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.location_id() %>
        <%: event.extendedProps.model.locationaddress.locationName() %><br/>
      <%/if%>
      <%if event.extendedProps.model.text() %>
        <%: event.extendedProps.model.text() %><br/>
      <%/if%>
    </div>
  </script>
{% endblock %}

{% block adatesTextWeekTemplate %}
  <script type="text/x-jsrender" id="adates_text_week_template">
    <div class="event-text-week">
      PL<%: event.extendedProps.model.planninglevel() %><br/>
      <%if event.extendedProps.model.eventtype_id() %>
        <%: event.extendedProps.model.seventtype.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.project_id() %>
        <%: event.extendedProps.model.sproject.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.location_id() %>
        <%: event.extendedProps.model.locationaddress.locationName() %><br/>
      <%/if%>
      <%if event.extendedProps.model.text() %>
        <%: event.extendedProps.model.text() %><br/>
      <%/if%>
    </div>
  </script>
{% endblock %}

{% block adatesTextDayTemplate %}
  <script type="text/x-jsrender" id="adates_text_day_template">
    <div class="event-text-day">
      PL<%: event.extendedProps.model.planninglevel() %><br/>
      <%if event.extendedProps.model.eventtype_id() %>
        <%: event.extendedProps.model.seventtype.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.project_id() %>
        <%: event.extendedProps.model.sproject.name() %><br/>
      <%/if%>
      <%if event.extendedProps.model.location_id() %>
        <%: event.extendedProps.model.locationaddress.locationName() %><br/>
      <%/if%>
      <%if event.extendedProps.model.text() %>
        <%: event.extendedProps.model.text() %><br/>
      <%/if%>
      <%if event.extendedProps.model.composers().length > 0 %>
        <%: event.extendedProps.model.composers().join(', ') %><br/>
      <%/if%>
    </div>
  </script>
{% endblock %}
