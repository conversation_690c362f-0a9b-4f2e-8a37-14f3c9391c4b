##Backup
Wir empfehlen vor jedem Update ein Backup der Dateien und der Datenbank zu machen, um im Notfall wieder den vorherigen
Stand herstellen zu können.

##Installation des Updates
Kopieren sie den Inhalt der ZIP-Datei in ihr Kundenverzeichnis der Anwendung (z.B. 
/var/www/vhost/[Domainnamen der Anwendung]/customer/gurz oder /var/www/html/customer/gurz).

<PERSON>ten sie hier, dass die Datei mit ihren Datenbankeinstellungen (/customer/gurz/config/gurz.php)
nicht Teil der ZIP-Datei ist und in dem Verzeichnis erhalten bleiben muss.

##Importieren von Berichten und Funktionen
Sind neue Berichte oder Funktionen Teil des Updates, müssen sie diese manuell über die Anwendung importieren. Gehen
sie hierfür in den Bereich System/Funktionen oder System/Berichte und klicken sie hier auf den Button "Import". Hier
sehen sie alle neuen Berichte bzw. Funktionen, die sie einzlen nach einer Auswahl importieren können oder über den 
Button "Alle synchronisieren" alle auf einmal importieren können. 

##Löschen des Caches
Um gecachte Daten zu löschen, gehen sie über die Kommandozeile in das Hauptverzeichnis der Anwendung und geben sie die
Befehle

$ bin/cake cache clear_all  
$ bin/cake twig_cache clear

alternativ  

$ php bin/cake.php cache clear_all  
$ php bin/cake.php twig_cache clear_all  

ein.

Das Löschen des Caches kann auch über eine spezielle Support-Seite in der Anwendung direkt ausgeführt werden. Gehen sie
in der Anwendung auf die Seite “http[s]://[Domainnamen der Anwendung]/Support” und klicken sie dort auf den Button
“clear cache” im Reiter "System".


 
