<?php
set_time_limit(0);
/*
******** ONCUST-4131
RAM Daily Sheets
*/

use App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\ram\reports\ReportTools_client;
use Customer\ram\reports\instrumentation_ram;

class adates_dailysheets_ram extends ReportWord
{
    private $section = null;
    private $postData = null;

    private $caption = '';
    private $programcode = '';
    private $adates_selected = null;

    private $aprojects = array();
    private $aproject = array();
    private $k_project = '';

    private $styleBG_grey = array('bgColor' => '808080');

    private $aprogramcode = null;

    private $asyssections = array();

    private $reporttools = null;

    private $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_4 = array('size' => 4);
    public $styleFont_8 = array('size' => 8);
    public $styleFont_9 = array('size' => 9);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_16 = array('size' => 16);
    public $styleFont_20 = array('size' => 20);

    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    private $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => '#black');
    private $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');
    private $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => '#black');
    private $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black');

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan3 = array('gridSpan' => 3);
    private $cellColSpan5 = array('gridSpan' => 5);

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'adates_dailysheets_ram.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];
    }

    public function collect(array $where = [])
    {

        $this->reporttools = new ReportTools_client();

        $this->postData = $this->getRequest()->getData();
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        // nur Ausgewählte Termine
        $awhere = ['Adates.id IN' => $this->postData['dataItemIds']];
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->prepare_aprojects();

        $this->phpWord->setDefaultFontSize(10);
        $this->phpWord->setDefaultFontName('Arial');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(1.5),
                'marginRight' => Converter::cmToTwip(1.5),
                'marginTop' => Converter::cmToTwip(1.5),
                'marginBottom' => Converter::cmToTwip(1.5)
            )
        );

        $count = 0;
        foreach ($this->aprojects as $this->aproject) {
            $count++;
            if ($count > 1) {
                $this->section->addPageBreak();
            }
            $this->showProject();
        }
    }

    function prepare_aprojects() {
        $this->aprojects = array();

        $ayears = [];

        foreach ($this->adates_selected as $adate) {
            $date_id = (int)$adate->id;
            $season_id = (int)$adate->season_id;
            $project_id = (int)$adate->project_id;
            $location_id = (int)$adate->location_id;
            $planninglevel = (int)$adate->planninglevel;
            $eventtype_code = ($adate->seventtype ? $adate->seventtype->code : '');
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

            $this->k_project = $project_id . $adate->date_->format('Ymd');

            if (!array_key_exists($this->k_project, $this->aprojects)) {

                $this->aprojects[$this->k_project] = array(
                    'season_id' => $season_id,
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'cdate' => $this->reporttools->getWeekday($adate->date_) . ' ' .$adate->date_->format('d').' '.$this->reporttools->getMonthName($adate->date_),
                    'project_id' => $project_id,
                    'planninglevel' => $planninglevel,
                    'project' => ($adate->sproject ? $adate->sproject->name : ''),
                    'project_name2' => ($adate->sproject ? $adate->sproject->name2 : ''),
                    'project_code' => ($adate->sproject ? $adate->sproject->code : ''),
                    'aprogramcodes' => array(),
                    'adates' => array(),
                    'adresses' => array(),
                );
            }

            $this->aprojects[$this->k_project]['adates'][$date_id] = $adate;
            if($adate->dress_id>0 && $adate->sdress) {
                $this->aprojects[$this->k_project]['adresses'][$adate->dress_id] = $adate->sdress;
            }

            $this->programcode = $adate->programtitle;

            if(!key_exists($this->programcode, $this->aprojects[$this->k_project]['aprogramcodes'])) {
                $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode] = array(
                    'programcode' => $this->programcode,
                    'afpd' => $adate,
                    'apds' => array(),
                    'adates' => array(),
                    'aworks' => array(),
                    'aconductors' => array(),
                    'asoloists' => array(),
                    'apersons' => array()
                );
            }

            if($l_performance == 1) {
                $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['apds'][$adate->id] = $adate;
                if(sizeof($this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['apds']) == 0) {
                    $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['afpd'] = $adate;
                }
            }




            $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['adates'][$adate->id] = $adate;

            foreach ($adate->adate_works as $awork) {
                $work_id = $awork->work_id;

                if (!array_key_exists($work_id, $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['aworks'])) {
                    $this->aprojects[$this->k_project]['aprogramcodes'][$this->programcode]['aworks'][$work_id] = $awork;
                }
            }
        }
    }


    function showProject()
    {
        $this->section->addText(htmlspecialchars($this->aproject['project']), array_merge($this->styleFont_bold, $this->styleFont_20), array_merge($this->styleAlign_center));

        $this->section->addText(htmlspecialchars($this->aproject['cdate']), array_merge($this->styleFont_bold, $this->styleFont_16), array_merge($this->styleAlign_center, $this->styleCell_borderBottom));

        $today = date(Configure::read('Formats.date'));
        $this->section->addText(htmlspecialchars('Correct as at ' . $today . '. Subject to alterations.'), [], array_merge($this->styleAlign_center));
        
        $this->section->addTextBreak(1);

        $this->showSchedule();

        $this->section->addTextBreak(1, $this->styleFont_4);

        $this->showProgramcodes();
    }

    function showSchedule()
    {
        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(3.05);
        $this->acol_widths[] = Converter::cmToTwip(3.43);
        $this->acol_widths[] = Converter::cmToTwip(4.51);
        $this->acol_widths[] = Converter::cmToTwip(3.81);
        $this->acol_widths[] = Converter::cmToTwip(3.49);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.12),
            'cellMarginRight' => Converter::cmToTwip(0.12),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = $this->section->addTable($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = $this->styleBG_grey;
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $count = 0;
        $d_old = '#';
        $location_id_old = -1;


        foreach ($this->aproject['adates'] as $adate) {

            $count++;

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            $eventtype_name = ($adate->seventtype ? $adate->seventtype->name : '');
            $eventtype_name2 = ($adate->seventtype ? $adate->seventtype->name2 : '');

            $title = ($adate->conductoraddress->stitle ? $adate->conductoraddress->stitle->name : '');
            //$title = '';
            $conductor = ($adate->conductoraddress ? trim(trim($title . ' ' . $adate->conductoraddress->name2) . ' ' . $adate->conductoraddress->name1) : '');

            //$row = $table->addRow(Converter::cmToTwip(0.5));
            $row = $table->addRow();

            $d = $adate->date_->format(Configure::read('Formats.date'));

            //$styleCell = array_merge($this->styleCell_borderTop_none, $this->styleCell_borderLeft, $this->styleCell_borderRight, $this->styleCell_borderBottom_none);

            $styleCell = array();

            $this->styleFont = [];
            if ($l_performance == 1) {
                $this->styleFont = $this->styleFont_bold;
            }

            $this->styleFont = array_merge($this->styleFont, $this->styleFont_12);

            //Time
            $cell = $row->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($this->reporttools->getTime($adate, ($l_performance == 0))), array_merge($this->styleFont));

            //Text_eventtype

            if (!empty($adate->text)) {
                $text = $adate->text;
            } elseif (!empty($eventtype_name2)) {
                $text = $eventtype_name2;
            } else {
                $text = $eventtype_name;
            }
            $cell = $row->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($text), array_merge($this->styleFont));

            //Program
            $cell = $row->addCell($this->acol_widths[2], array_merge($styleCell));

            if (!empty($adate->memo_1) && $l_performance == 0) {
                $this->reporttools->addMemo($cell, $adate->memo_1, $this->styleFont);
            } elseif (!empty($adate->programtitle)) {
                $cell->addText(htmlspecialchars($adate->programtitle), array_merge($this->styleFont));
            } else {
                $cell->addText(htmlspecialchars('Concert Programme'), array_merge($this->styleFont));
            }

            //Conductor, coach
            $cell = $row->addCell($this->acol_widths[3], array_merge($styleCell));

            if (!empty($conductor)) {
                $cell->addText(htmlspecialchars($conductor), array_merge($this->styleFont));
            }

            foreach ($adate->adate_persons as $arow) {

                $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');
                if (strpos(mb_strtoupper($addressgroup), 'COACH') !== false) {
                    $title = ($arow->saddress->stitle ? $arow->saddress->stitle->name : '');
                    $coach = ($arow->saddress ? trim(trim($title . ' ', $arow->saddress->name2) . ' ' . $arow->saddress->saddress->name1) : '');
                    $cell->addText(htmlspecialchars($coach), array_merge($this->styleFont));
                }
            }

            //Location
            $cell = $row->addCell($this->acol_widths[4], array_merge($styleCell));
            $cell->addText(htmlspecialchars(($adate->locationaddress ? $adate->locationaddress->name1 : '')), array_merge($this->styleFont));
        }
    }

    function showProgramcodes()
    {
        foreach ($this->aproject['aprogramcodes'] as $this->programcode => $this->aprogramcode) {
            if (sizeof($this->aprogramcode['apds']) == 0) {
                $this->showProgram($this->aprogramcode['aworks']);
            } else {
                $this->showProgram($this->aprogramcode['afpd']->adate_works);
            }


            $this->showPersonnelTable();
        }
    }

    function showProgram($adate_works)
    {
        $styleFont = ['name' => 'Arial Narrow'];

        $this->section->addTextBreak(1, array_merge($styleFont, $this->styleFont_4));

        $oinstrumentation = new instrumentation_ram();

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(3.68);
        $this->acol_widths[] = Converter::cmToTwip(13.02);
        $this->acol_widths[] = Converter::cmToTwip(1.68);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'black',
            'cellMarginLeft' => Converter::cmToTwip(0.12),
            'cellMarginRight' => Converter::cmToTwip(0.12),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = $this->section->addTable($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = $this->styleBG_grey;
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $row = $table->addRow();

        $cell = $table->addCell($w, array_merge($styleCell, $this->cellColSpan3));
        $cell->addText(htmlspecialchars($this->programcode), array_merge($styleFont, $this->styleFont_italic, ['color' => 'FFFFFF']));


        $styleCell = [];
        foreach ($adate_works as $adate_work) {
            $title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);

            //CASE WHEN sComposers.DeathYear IS NULL THEN rtrim(sComposers.FirstName) + ' ' ELSE '' END + rtrim(sComposers.LastName),
            //CASE WHEN aDate_Works.Arrangement > '' THEN 'Arr. ' + rtrim(aDate_Works.Arrangement) ELSE '' END,
            $composer = '';
            if ($adate_work->swork->scomposer) {
                $composer = (empty($adate_work->swork->scomposer->deathyear) ? $adate_work->swork->scomposer->firstname . ' ' : '') . $adate_work->swork->scomposer->lastname;
            }

            $nduration = (int)substr($adate_work->duration, 0, 2) * 60 + (int)substr($adate_work->duration, 3, 2);
            $cduration = '';
            if ($nduration > 0) {
                $cduration = $nduration . "'";
            }

            if ($adate_work->swork->l_intermission == 1) {
                $cduration = '';
                continue;
            }

            $row = $table->addRow();
            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($composer), array_merge($styleFont));

            $swork_composers = TableRegistry::getTableLocator()->get('SworkComposers')
                ->find('all')
                ->contain([
                ])
                ->where(
                    ['SworkComposers.work_id' => $adate_work->work_id]
                )
                ->order(['SworkComposers.composer_order' => 'ASC']);

            foreach ($swork_composers as $swork_composer) {
                $scomposers = TableRegistry::getTableLocator()->get('Scomposers')
                    ->find('all')
                    ->where(
                        ['Scomposers.id' => $swork_composer->composer_id]
                    );

                foreach($scomposers as $scomposer) {
                    //$this->addComposer($adate_work->swork->scomposer, 'komponist#'.$swork_composer->id.'#'.$scomposer->id);
                    $composer = (empty($scomposer->deathyear) ? $scomposer->firstname . ' ' : '') . $scomposer->lastname;
                    $cell->addText(htmlspecialchars($composer), array_merge($styleFont));
                }
            }

            $oinstrumentation->datework_id = $adate_work->id;
            $oinstrumentation->getInstrumentation();

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            //$cell->addText(str_replace('&amp;', '&', htmlspecialchars($title)), array_merge($styleFont, $this->styleFont_bold));
            $cell->addText(htmlspecialchars($title), array_merge($styleFont, $this->styleFont_bold));

            if(!empty($oinstrumentation->instrumentation)) {
                $cell->addText(htmlspecialchars($oinstrumentation->instrumentation), array_merge($styleFont));
            }


            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell->addText(htmlspecialchars($cduration), array_merge($styleFont, $this->styleAlign_right));
        }
    }

    function showPersonnelTable() {

        $this->prepareDuties();

        $this->section->addTextBreak(1, $this->styleFont_4);

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(3.68);
        $this->acol_widths[] = Converter::cmToTwip(3.68);
        $this->acol_widths[] = Converter::cmToTwip(3.68);
        $this->acol_widths[] = Converter::cmToTwip(3.68);
        $this->acol_widths[] = Converter::cmToTwip(3.68);

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'black',
            'cellMarginLeft' => Converter::cmToTwip(0.12),
            'cellMarginRight' => Converter::cmToTwip(0.12),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = $this->section->addTable($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        $styleCell_border = array();

        $row = $table->addRow();

        $styleFont = ['name'=>'Arial Narrow'];

        $cell = $table->addCell($w, array_merge($styleCell, $this->cellColSpan5, $this->styleBG_grey));
        $cell->addText(htmlspecialchars('Personnel'), array_merge($styleFont, $this->styleFont_italic, ['color'=>'FFFFFF']));

        $count = 0;
        $d_old = '#';
        $location_id_old = -1;
        //$styleCell_border_none = $this->styleCell_borderTop_none;


        $count = 0;
        foreach($this->asyssections as $syssection_id => $asyssection) {
            $count++;
            $cellno = ($count % 5)-1;
            if($cellno<0) {
                $cellno = 4;
            }

            if ($cellno==0) {
                $row = $table->addRow();
                $cell0 = $table->addCell($this->acol_widths[1], array_merge($styleCell));
                $cell1 = $table->addCell($this->acol_widths[2], array_merge($styleCell));
                $cell2 = $table->addCell($this->acol_widths[3], array_merge($styleCell));
                $cell3 = $table->addCell($this->acol_widths[4], array_merge($styleCell));
                $cell4 = $table->addCell($this->acol_widths[5], array_merge($styleCell));
            }


            $cell = 'cell'.$cellno;

            $$cell->addText(htmlspecialchars($asyssection['syssection']), array_merge($styleFont, $this->styleFont_bold));
            uasort($asyssection['aartists'], array($this, "usort_artists"));
            foreach ($asyssection['aartists'] as $aartist) {
                $seat = $aartist['seat'];
                $name = $aartist['name'];

                //$instruments = implode(', ', $aartist['ainstruments']);
                //$name .= '#'.$instruments;

                $name = ($aartist['substitute_order'] == 2 ? '* ' : '') . $name;
                $$cell->addText(htmlspecialchars($name), array_merge($styleFont, $this->styleFont_9));


                if(!empty($aduty->arrangements)) {
                    $$cell->addText('('.htmlspecialchars($aduty->arrangements).')', array_merge($styleFont, $this->styleFont_9));
                }
                //$$cell->addText($aartist['aduty']->id.'#'.$seat.'#'.$syssection_id, array_merge($this->styleFont_9));
                //$$cell->addText(strpos($seat, 'R'), array_merge($styleFont, $this->styleFont_9));

                if((strpos($seat, 'R') !== false || strpos($seat, 'B') !== false) && $syssection_id<=5) {
                    //$$cell->addText($aartist['aduty']->id.'#'.$seat.'#'.$syssection_id, array_merge($styleFont, $this->styleFont_9));

                    $$cell->addTextBreak(1, array_merge($styleFont, $this->styleFont_4));
                }
            }
        }
    }

    function usort_artists($a, $b) {
        $frm_order_a = strtoupper($a->order_);
        $frm_order_b = strtoupper($b->order_);

        $l_greater = strcmp($frm_order_a, $frm_order_b);

        //$this->section->addText($frm_order_a.'#'.$frm_order_b.'#'.$l_greater);
        return $l_greater;
    }

    function prepareDuties() {
        //20240917 ONCUST-4173
        //Wenn Musiker in einem Projekt bei einzelnen Proben eingeteilt sind, erscheinen Sie nich auf dem Bericht. Es sollen bitte alle Musiker aufgeführt werden, auch wenn sie nur in einem Event erwähnt werden.
        $where = ['Aduties.date_id IN' => array_keys($this->aprogramcode['adates'])];
        /*if (sizeof($this->aprogramcode['apds']) == 0) {
            $where = ['Aduties.date_id IN' => array_keys($this->aprogramcode['adates'])];
        } else {
            $where = ['Aduties.date_id' => $this->aprogramcode['afpd']->id];
        }
        */

        $aduties = TableRegistry::getTableLocator()->get('Aduties')
            ->find('all')
            ->select()
            ->contain([
                'Adates',
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses',
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']
            ])
            ->where(array_merge($where, ['Sdutytypes.l_present' => 1]))
            ->order(['Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order', 'Sdutytypes.l_present'=>'DESC', 'Aduties.seat'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);

        $this->asyssections = array();
        foreach($aduties as $aduty) {
            $artist_id = $aduty->artist_id;
            $sysgroup_id = ($aduty->saddressgroup ?$aduty->saddressgroup->sysgroup_id : -1);
            $dutytype = ($aduty->sdutytype ? $aduty->sdutytype->code : '');
            $instrument = ($aduty->sinstrinstrument ? $aduty->sinstrinstrument->name : '');

            $syssection_id = -1;
            $syssection = 'unknown';
            $syssection_order = 0;

            $substitute_order = ($aduty->saddressgroup && $aduty->saddressgroup->sysgroup_id == 12 ? 2 : 1);

            if ($aduty->sinstrinstrument->sinstrsection && $aduty->sinstrinstrument->sinstrsection->sinstrsyssection) {
                $syssection_id = $aduty->sinstrinstrument->sinstrsection->sinstrsyssection->id;
                $syssection = $aduty->sinstrinstrument->sinstrsection->sinstrsyssection->name;
                $syssection_order = $aduty->sinstrinstrument->sinstrsection->sinstrsyssection->section_order;
            }

            if (!array_key_exists($syssection_id, $this->asyssections)) {
                $this->asyssections[$syssection_id] = array(
                    'syssection' => $syssection,
                    'syssection_order' => $syssection_order,
                    'max_instr' => 0,
                    'aartists' => array()
                );
            }

            if (!array_key_exists($artist_id, $this->asyssections[$syssection_id]['aartists'])) {
                $name = ($aduty->artistaddress ? str_pad($aduty->artistaddress->name1,50) . str_pad($aduty->artistaddress->name2,50) : '');

                $order_ = mb_strtoupper(
                    $aduty->seat.'#'.
                    $substitute_order.'#'.
                    str_pad(($aduty->order_1>'' ? $aduty->order_1 : 'ZZZ'), 10).'#'.
                    $name);

                $this->asyssections[$syssection_id]['aartists'][$artist_id] = array(
                    'order_' => $order_,
                    'substitute_order' => $substitute_order,
                    'ainstruments' => [],
                    'seat' => $aduty->seat,
                    'name' => ($aduty->artistaddress ? trim($aduty->artistaddress->name2.' '.$aduty->artistaddress->name1) : ''),
                    'adutytypes' => array(($aduty->sdutytype ? $aduty->sdutytype->code : '')),
                    'instrument' => ($aduty->sinstrument ? $aduty->sinstrument->name : ''),
                    'adates' => array()
                );
            }

            if(!in_array($instrument, $this->asyssections[$syssection_id]['aartists'][$artist_id]['ainstruments'])) {
                $this->asyssections[$syssection_id]['aartists'][$artist_id]['ainstruments'][] = $instrument;
            }
        }
    }

    function getProgramCode($adate) {

        //ISNULL(ADates.Programtitle,'Concert Programme')
        $programCode = (!empty(trim($adate->programtitle)) ? trim($adate->programtitle) : 'Concert Programme');


        return $programCode;
    }
}
