<?php
set_time_limit(0);
/*
******** ONCUST-4130
RAM Master Schedule
*/

use App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\ram\reports\ReportTools_client;
use Customer\ram\reports\instrumentation_ram;

class adates_master_schedule_ram extends ReportWord
{
    private $section = null;
    private $postData = null;


    private $adates_selected = null;

    private $aseasons = array();
    private $aweeks = array();
    private $aweek = array();

    private $mindate = null;
    private $maxdate = null;

    private $reporttools = null;

    private $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_4 = array('size' => 4);
    public $styleFont_8 = array('size' => 8);
    public $styleFont_9 = array('size' => 9);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_16 = array('size' => 16);

    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    private $styleCell_borderTop = array('borderTopSize' => 12, 'borderTopColor' => 'black');
    private $styleCell_borderBottom = array('borderBottomSize' => 12, 'borderBottomColor' => 'black');
    private $styleCell_borderLeft = array('borderLeftSize' => 12, 'borderLeftColor' => 'black');
    private $styleCell_borderRight = array('borderRightSize' => 12, 'borderRightColor' => 'black');

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan3 = array('gridSpan' => 3);
    private $cellColSpan5 = array('gridSpan' => 5);

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    public $styleKeepNext = ['keepNext' => true, 'keepLines'=>true];
    public $styleCantSplit = ['cantSplit' => true];


    function initialize()
    {
        parent::initialize();
    }

    public function collect(array $where = [])
    {

        $this->reporttools = new ReportTools_client();

        $this->postData = $this->getRequest()->getData();
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        // nur Ausgewählte Termine
        $awhere = ['Adates.id IN' => $this->postData['dataItemIds']];
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->prepare_aweeks();

        $this->phpWord->setDefaultFontSIze(10);
        $this->phpWord->setDefaultFontName('Arial');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(1),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(1),
                'marginBottom' => Converter::cmToTwip(1)
            )
        );

        $sectionStyle = $this->section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_LANDSCAPE);

        $header = $this->section->addHeader();

        //Samstag 1 Oktober 2022 - Montag 31 Oktober 2022 as at 01/10/2024
        $today = date(Configure::read('Formats.date'));
        $from_to = '';

        if($this->mindate && $this->maxdate) {
            $from_to =
            $this->reporttools->getWeekday($this->mindate).' '.$this->mindate->format('d').' '.$this->reporttools->getMonthName($this->mindate).' '.$this->mindate->format('Y').
            ' - '.
            $this->reporttools->getWeekday($this->maxdate).' '.$this->maxdate->format('d').' '.$this->reporttools->getMonthName($this->maxdate).' '.$this->maxdate->format('Y');

        }

        $textrun = $header->addTextRun();
        $textrun->addText(htmlspecialchars('RAM Master Schedule'), $this->styleFont_bold);
        $textrun->addText(htmlspecialchars(' | ' . $from_to. ' as '.$today));
        $textrun->addTextBreak();



        $count = 0;
        foreach ($this->aweeks as $this->aweek) {
            $count++;
            if ($count > 1) {
                $this->section->addTextBreak(2);
            }
            $this->showWeek();
        }
    }

    function prepare_aweeks() {
        $this->aweeks = array();
        $this->mindate = null;
        $this->maxdate = null;
        $count_dates = 0;
        foreach ($this->adates_selected as $adate) {
            $count_dates ++ ;
            if($count_dates==1) {
                $this->mindate = $adate->date_;
            }
            $this->maxdate = $adate->date_;

            $date_id = $adate->id;
            $week_k = $adate->pweek.'#'.$adate->season_id;
            $day_k = $adate->date_->format('Ymd');
            $season = ($adate->sseason ? $adate->sseason->name : '');
            if(!in_array($season, $this->aseasons)) {
                $this->aseasons[] = $season;
            }


            if(!array_key_exists($week_k, $this->aweeks)) {
                $this->aweeks[$week_k] = array(
                    'week' => $adate->week,
                    'season' => $season,
                    'adays' => array()
                );

                $adays = TableRegistry::getTableLocator()->get('Adays')
                    ->find('all')
                    ->select([
                        'Adays.date_',
                        'Adays.weekday',
                        'Adays.holyday_id',
                        'Adays.pweek',
                        'Adays.week'
                    ])
                    ->where([
                        'Adays.planninglevel'=>1,
                        'Adays.week' => $adate->week,
                        'Adays.season_id' => $adate->season_id
                    ])
                    ->order(['Adays.date_' => 'ASC'])
                    ->all();

                $count_days = 0;
                foreach ($adays as $aday) {
                    $count_days++;

                    if($count_days==1) {
                        $this->aweeks[$week_k]['first_date'] = $aday->date_->format('d ').$this->reporttools->getMonthName($aday->date_);
                    }

                    $d = $aday->date_->format('Ymd');

                    $this->aweeks[$week_k]['adays'][$d] = [
                        'weekday' => $this->reporttools->getWeekday($aday->date_),
                        'cday' => $aday->date_->format('d.m'),
                        'adates_vm' => [],
                        'adates_mm' => [],
                        'adates_nm' => []
                    ];
                }
            }



            if(array_key_exists($day_k, $this->aweeks[$week_k]['adays'])) {
                $hour = ($adate->start_ ? $adate->start_->format('G') : 0);
                switch(true) {
                    case  $hour>=18:
                        $this->aweeks[$week_k]['adays'][$day_k]['adates_nm'][$date_id] = $adate;
                        break;
                    case  $hour>=12:
                        $this->aweeks[$week_k]['adays'][$day_k]['adates_mm'][$date_id] = $adate;
                        break;
                    default:
                        $this->aweeks[$week_k]['adays'][$day_k]['adates_vm'][$date_id] = $adate;
                        break;
                }
            }
        }
    }

    function showWeek()
    {
        $this->section->addText(htmlspecialchars('Week commencing '.$this->aweek['first_date']), array_merge($this->styleFont_bold), $this->styleKeepNext);
        $this->section->addText(htmlspecialchars(''), array_merge($this->styleFont_bold), $this->styleKeepNext);
        //$this->section->addTextBreak(1, $this->styleKeepNext);
        $this->showSchedule();
    }

    function showSchedule()
    {
        $this->acol_widths = array();

        $days_count = sizeof($this->aweek['adays']);
        for($i=1; $i<=$days_count; $i++) {
            $this->acol_widths[] = Converter::cmToTwip(2);
            $this->acol_widths[] = Converter::cmToTwip(1.96);
        }

        $w = 0;
        foreach ($this->acol_widths as $col_width) {
            $w += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => 0,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.12),
            'cellMarginRight' => Converter::cmToTwip(0.12),
            'cellMarginTop' => Converter::cmToTwip(0.12),
            'cellMarginBottom' => Converter::cmToTwip(0.12),
            'width' => $w,
            'unit' => TblWidth::TWIP
        );

        $table = $this->section->addTable($tableProperties);
        //$table = $section->addTable($tableProperties);

        //$styleCell = $this->styleBG_grey;
        $styleCell = [];
        $styleCell_border_tblr = array_merge($this->styleCell_borderTop, $this->styleCell_borderBottom, $this->styleCell_borderLeft, $this->styleCell_borderRight);


        $table->addRow(null,$this->styleCantSplit);

        $col = -1;
        $count_days = 0;
        foreach ($this->aweek['adays'] as $aday) {
            $count_days++;
            $col++;
            $cell = $table->addCell($this->acol_widths[$col], array_merge(['bgColor' => '999999'], $styleCell_border_tblr));
            $cell->addText(htmlspecialchars($aday['weekday']), ['color'=>'white'], array_merge($this->styleAlign_center, $this->styleKeepNext));

            $col++;
            $cell = $table->addCell($this->acol_widths[$col], array_merge($styleCell, $styleCell_border_tblr));
            $cell->addText(htmlspecialchars($aday['cday']), [], array_merge($this->styleAlign_center, $this->styleKeepNext));
        }

        foreach(['adates_vm', 'adates_mm', 'adates_nm'] as $dates_type) {
            $table->addRow(null,$this->styleCantSplit);
            $col = -1;
            foreach ($this->aweek['adays'] as $aday) {
                $col++;

                switch(true) {
                    case $dates_type == 'adates_vm':
                            $styleCell_border = array_merge($this->styleCell_borderTop, $this->styleCell_borderLeft, $this->styleCell_borderRight);
                        break;
                    case $dates_type == 'adates_mm':
                        $styleCell_border = array_merge($this->styleCell_borderLeft, $this->styleCell_borderRight);
                        break;
                    default:
                        $styleCell_border = array_merge($this->styleCell_borderBottom, $this->styleCell_borderLeft, $this->styleCell_borderRight);
                        break;
                }

                $cell = $table->addCell($this->acol_widths[$col] + $this->acol_widths[$col+1], array_merge($this->cellColSpan2, $styleCell_border));
                $this->showDates($cell, $aday[$dates_type]);

                $col++;
            }
        }
    }

    function showDates($cell, $adates) {
        $count = 0;
        foreach ($adates as $adate) {

            $count++;

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            $eventtype_name = ($adate->seventtype ? $adate->seventtype->name : '');
            $eventtype_name2 = ($adate->seventtype ? $adate->seventtype->name2 : '');

            $project = ($adate->sproject ? $adate->sproject->name : '');
            $project_code = ($adate->sproject ? $adate->sproject->code : '');

            $location = ($adate->locationaddress ? (!empty($adate->locationaddress->code) ? $adate->locationaddress->code : $adate->locationaddress->name1) : '');


            $title = ($adate->conductoraddress->stitle ? $adate->conductoraddress->stitle->name : '');

            $conductor = ($adate->conductoraddress ? trim(trim($title . ' ' . $adate->conductoraddress->name2) . ' ' . $adate->conductoraddress->name1) : '');

            //$styleCell = array_merge($this->styleCell_borderTop_none, $this->styleCell_borderLeft, $this->styleCell_borderRight, $this->styleCell_borderBottom_none);


            $styleFont = ['name'=>'Arial Narrow', 'size'=>9];
            if ($l_performance == 1) {
                $styleFont = array_merge($this->styleFont_bold, $styleFont);
            }


            //Text_eventtype

            if (!empty($adate->text)) {
                $text = $adate->text;
            } elseif (!empty($eventtype_name2)) {
                $text = $eventtype_name2;
            } elseif (!empty($eventtype_code)) {
                $text = $eventtype_code;
            } else {
                $text = $eventtype_name;
            }

            $cell->addText(htmlspecialchars(trim($this->reporttools->getTime($adate).' '.$project_code)), array_merge($styleFont), $this->styleKeepNext);
            $cell->addText(htmlspecialchars('     '.trim($text.' '.$location)), array_merge($styleFont), $this->styleKeepNext);
        }
    }
}
