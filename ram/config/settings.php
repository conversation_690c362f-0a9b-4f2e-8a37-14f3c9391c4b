<?php

return [
    //'debug' => true,

    'Error' => [
        'errorLevel' => 0
    ],

    'App' => [
        'defaultLocale' => 'en_GB'
    ],

    'CustomerSettings' => [
        'name' => 'ROYAL ACADEMY OF MUSIC',
        'timezone' => 'Europe/London'
    ],

    'DisplayLanguages' => [
        'en_GB' => 'uk english',
        'de_DE' => 'german'
    ],

    /**
     * Mandatierung
     */
    'Mandating' => [
        'enabled' => false
    ],

    'Formats' => [
        'date' => 'd/m/Y',
        'time' => 'H:i:s',
        'time_short' => 'H:i',
        'full' => 'd/m/Y H:i:s',
        'week' => 'y-W',
        'momentjsFullDate' => 'DD/MM/YYYY HH:mm',
        'momentjsDate' => 'DD/MM/YYYY',
        'momentjsTime' => 'HH:mm',
        'momentjsWeek' => 'YY-WW',
        'calendar' => [
            'monthColumnHeaderFormat' => 'MMM',
            'weekColumnHeaderFormat' => 'dddd DD/MM',
            'dayColumnHeaderFormat' => 'dddd',
            'eventTimeFormat' => [
                'hour' => '2-digit', // 'numeric' (7:12), '2-digit' (07:12)
                'minute' => '2-digit', // 'numeric' (7:5), '2-digit' (7:05)
                'meridiem' => false, // false (19:00), 'narrow' (7p), 'short' (7pm)
                'hour12' => false, // false || true (show 12 hour format. set true if meridiem is used)
            ],
        ]
    ],


    'Modules' => [
        'Contract' => [
            'enabled' => 1
        ],
        'Service' => [
            'enabled' => 1
        ],
        'SeatManagement' => [
            'enabled' => 1
        ]
    ]

];
