<?php

namespace Customer\memph\reports\DatesSocanSubmission;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DateWorkQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\reports\utility\WorkQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Standard Concert Program organized by Project
 * Special heading and layout to match format for SOCAN
 * (Society of Composers, Authors and Music Publishers of Canada)
 */

class DatesSocanSubmission extends ReportWord
{

// Landscape layout on legal paper. Tables are 12.5 wide
    const HALF_COL_WIDTH = 6.25;
    const LABEL_COL = 1.4;
    const MEDIUM_COL = 1.6;
    const LARGE_COL = 2.55;
    const PGM_COL = 3.125;
    const PGM_COL_2 = 2.8;


    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for the compositions on each concert
     *
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;


    /**
     * @var WorkQueryHelper
     */
    private $workQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->workQueryHelper = new WorkQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        // Set report font, table and paragraph styles
        $this->reportStyles = new DatesSocanSubmissionStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Date records selected by user
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    private function renderReport()
    {
//        Set report formats based on client region and preferences
//        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
//        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $projectFont = $this->reportStyles->projectHeaderFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => "Legal",
                'orientation' => "landscape",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.75),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//            RENDER HEADING TABLE
//        Get Orchestra and Season from first entry in dates selected by user
        $firstDate = reset($this->datesResult);
        $orchestra = trim(
            $firstDate['orchestraaddress']['name2'] . ' ' . $firstDate['orchestraaddress']['name1']
        );
        $season = $firstDate['sseason']['name'];

        $headerTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $headerTable->addRow();
        $headerTable->addCell(Converter::inchToTwip(self::HALF_COL_WIDTH), $this->reportStyles->defaultCell)
            ->addText($orchestra, $this->reportStyles->headerFont, $defaultParagraph);
        $rightCell = $headerTable->addCell(
            Converter::inchToTwip(self::HALF_COL_WIDTH),
            $this->reportStyles->defaultCell
        );
        $rightCell->addText(
            $repFileText['header_1'],
            $this->reportStyles->headerFont,
            $this->reportStyles->defaultParagraphRight
        );
        $rightCell->addText(
            $repFileText['header_2'],
            $this->reportStyles->headerFont,
            $this->reportStyles->defaultParagraphRight
        );
        $pageSection->addText('', $defaultFont, $defaultParagraph);

        $pageSection->addText(
            $repFileText['header_3'] . ' ' . $season,
            $this->reportStyles->headerFont,
            $this->reportStyles->bottomBorderParagraph
        );
        $pageSection->addText('', $defaultFont, $defaultParagraph);



//        LOOP THROUGH EACH PROJECT
        foreach ($this->getProjects() as $project) {
//            Anchor Date - date Object
            $concertDate = $this->getAnchorDate($project);

            $conductor = trim(
                $concertDate['conductoraddress']['name2'] . ' ' . $concertDate['conductoraddress']['name1']
            );
            $soloists = $this->getSoloists($concertDate);
            $projectName = $concertDate['sproject']['name'];
            $concertDates = $this->getPerformanceDates($project);

//  RENDER CONDUCTOR, SOLOIST , PROJECT Table
            $projectTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $projectTable->addRow();
            $projectTable->addCell(Converter::inchToTwip(self::LABEL_COL), $this->reportStyles->defaultCell)
                ->addText('CONDUCTOR:', $projectFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::MEDIUM_COL), $this->reportStyles->defaultCell)
                ->addFormattedText($conductor, $defaultFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::LABEL_COL), $this->reportStyles->defaultCell)
                ->addText('ARTIST:', $projectFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::LARGE_COL), $this->reportStyles->defaultCell)
                ->addFormattedText($soloists, $defaultFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::LABEL_COL), $this->reportStyles->defaultCell)
                ->addText('SERIES/DATE:', $projectFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::MEDIUM_COL), $this->reportStyles->defaultCell)
                ->addFormattedText($projectName, $defaultFont, $defaultParagraph);
            $projectTable->addCell(Converter::inchToTwip(self::LARGE_COL), $this->reportStyles->defaultCell)
                ->addText($concertDates, $defaultFont, $defaultParagraph);

//            +++ RENDER PROGRAM +++
            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $programTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $programTable->addRow();
            $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                ->addText('TITLE OF WORK PERFORMED', $projectFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                ->addText('COMPOSER', $projectFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                ->addText('PUBLISHER', $projectFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                ->addText('ARRANGER', $projectFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::LABEL_COL), $this->reportStyles->defaultCell)
                ->addText('AUTHOR', $projectFont, $defaultParagraph);


//            FETCH PROGRAM again to get linked Library
            $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($concertDate['id'])
                ->withLibraries()
                ->withDateWorkSoloists()
                ->getQuery()
                ->toArray();

            foreach ($dateWorks as $dateWork) {
                if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                    $programTable->addRow();

//                     COLUMN 1 - WORK TITLE and Date-Work SOLOISTS
                    $titleCell = $programTable->addCell(
                        Converter::inchToTwip(self::PGM_COL_2),
                        $this->reportStyles->defaultCell
                    );
                    $workTitleTextRun = $titleCell->addTextRun($defaultParagraph);
                    if (!is_null($dateWork['title2']) && trim($dateWork['title2'])) {
                        $workTitleTextRun->addFormattedText($dateWork['title2'], $defaultFont);
                    } else {
                        $workTitleTextRun->addFormattedText($dateWork['swork']['title1'], $defaultFont);
                    }

                    //                            Fetch soloists (array)
                    $soloists = $this->dateWorkQueryHelper->getSoloistsForDateWorkID($dateWork['id'])->getQuery(
                    )->toArray();
                    $dateWorkSoloists = $this->dateWorkQueryHelper->getDateWorkSoloistsAsString(
                        $soloists,
                        $dateWork['conductor_id'] ?? 0,
                        '; ',
                        0,
                        0,
                        ', '
                    );
                    if (!empty($dateWorkSoloists)) {
                        $workTitleTextRun->addTextBreak(1, $defaultFont);
                        $workTitleTextRun->addFormattedText($dateWorkSoloists, $defaultFont);
                    }


//                    COLUMN TWO - Composer
                    $composerName = trim(
                        $dateWork['swork']['scomposer']['firstname'] . ' ' . $dateWork['swork']['scomposer']['lastname']
                    );
                    $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                        ->addText($composerName, $defaultFont, $defaultParagraph);

//                    COLUMN THREE - Date-Work Library or Library publisher
                    $dateWorkPublisher = $this->getDateWorkPublisher($dateWork);
                    $libraryPublisher = $this->getLibraryPublisher($dateWork);
                    $libraryCell = $programTable->addCell(
                        Converter::inchToTwip(self::PGM_COL_2),
                        $this->reportStyles->defaultCell
                    );
                    $libraryTextRun = $libraryCell->addTextRun($defaultParagraph);
                    if ($dateWorkPublisher !== '') {
                        $libraryTextRun->addFormattedText($dateWorkPublisher, $defaultFont);
                    } elseif ($libraryPublisher !== '') {
                        $libraryTextRun->addFormattedText($libraryPublisher, $defaultFont);
                    } else {
                        $libraryTextRun->addText(' ', $defaultFont);
                    }

//                    COLUMN FOUR - Arranger
                    $programTable->addCell(Converter::inchToTwip(self::PGM_COL_2), $this->reportStyles->defaultCell)
                        ->addFormattedText($dateWork['arrangement'], $defaultFont, $defaultParagraph);

//                    COLUMN FIVE - blank
                    $programTable->addCell(Converter::inchToTwip(self::LABEL_COL), $this->reportStyles->defaultCell)
                        ->addText('', $defaultFont, $defaultParagraph);
                }
            }

//  Border bewteen projects
            $pageSection->addText('', $defaultFont, $this->reportStyles->bottomBorderParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);
        }
    }


    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

    private function getAnchorDate(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
        return '';
    }

    private function getSoloists($concertDate): string
    {
        //                    function returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
        $mainSoloist = $this->datePerformerHelper->getSoloists(
            $concertDate['id'],
            $concertDate['conductor_id'],
            '; ',
            0,
            0,
            ', ',
            true,
            ' - ',
            ' / ',
            0
        );
        return $mainSoloist ?? ' ';
    }

    private function getPerformanceDates($project): string
    {
        $perfDateArray = [];
        foreach ($this->datesResult as $headerDate) {
            if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                && $headerDate['seventtype']['l_performance'] == 1) {
                $perfDateArray[] = $headerDate['date_']->format('F j, Y');
            }
        }
        return implode('; ', $perfDateArray) ?? '';
    }

    private function getDateWorkPublisher($dateWork): string
    {
        $dateWorkPublisherArray = [];
        foreach ($dateWork['adatework_libraries'] as $library) {
            $publisher = $library['alibrary']['publisheraddress'];
            $dateWorkPublisherArray[] = trim($publisher['name2'] . ' ' . $publisher['name1']);
        }

        return implode('; ', $dateWorkPublisherArray) ?? '';
    }

    private function getLibraryPublisher($dateWork)
    {
        $libraryPublisherArray = [];
        $libraries = $this->workQueryHelper->getLibrariesForWork($dateWork['work_id'])->getQuery()->toArray();
        foreach ($libraries as $library) {
            $publisher = $library['publisheraddress'];
            $libraryPublisherArray[] = trim($publisher['name2'] . ' ' . $publisher['name1']);
        }
        return implode('; ', $libraryPublisherArray) ?? '';
    }
}
