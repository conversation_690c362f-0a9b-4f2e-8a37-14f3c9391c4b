<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert / Event Roster by Work - Landscape</caption>
  <classfile>DatesEventRosterByWorkLandscape/DatesEventRosterByWorkLandscape.php</classfile>
  <classname>customer\memph\reports\DatesEventRosterByWorkLandscape\DatesEventRosterByWorkLandscape</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Concert / Event Roster by Work - Landscape</title>
      <notes>Run  the report for any single Activity - the report outputs the roster / assigned musicians for that activity</notes>
      <report_title>Personnel Roster</report_title>
      <printed>Printed on:</printed>
    </default>
    <australia>
      <title>Concert/Event Roster by Work  - Landscape</title>
      <notes>Run  the report for any single Activity - the report outputs the roster / assigned musicians for that activity</notes>
      <report_title>Personnel Roster</report_title>
      <printed>Printed on:</printed>
    </australia>
    <canada>
      <title>Concert/Event Roster by Work - Landscape</title>
      <notes>Run  the report for any single Activity - the report outputs the roster / assigned musicians for that activity</notes>
      <report_title>Personnel Roster</report_title>
      <printed>Printed on:</printed>
    </canada>
    <german>
      <title>Musiker-Liste / Werk - Landscape</title>
      <notes>Führen Sie den Bericht für eine beliebige Aktivität aus - der Bericht gibt die Dienstpläne / zugewiesenen Musiker für diese Aktivität aus</notes>
      <report_title>Personnel Roster</report_title>
      <printed>Gedruckt am:</printed>
    </german>
  </text>
  <icon>rtf</icon>
</rep>
