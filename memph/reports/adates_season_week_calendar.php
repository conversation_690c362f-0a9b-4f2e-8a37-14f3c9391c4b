<?php

// namespace Customer\memph\reports;

use App\Reports\ReportsInterface;
use App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;
use Customer\fasutilities\reports\OnWord\OnWord;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DutyQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use Customer\memph\reports\adates_season_week_calendar_styles;

/*
 * JH 20250521 CALSeasonWeekCalendar is the template
 *
 *
 */

class adates_season_week_calendar extends ReportWord
{

    /**
     * Helper class for each event selected by the user
     * @var DateQueryHelper
     */
    private $daysResult;
    private $dateTable;
    private $dbConn;

    private $customerInstrumentation;

    /**
     * Helper class for the  Service records associated with the selecte date
     * @var DutyQueryHelper
     */

    /**
     * Helper class for nconductor/soloist formatting
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    private $repFileText;

    private $seasonId;
    private $planningLevelsList;
    private $fDoW;  // 1 = monday, 7 = sunday
    private $printPeriods;
    private $datePweekSource;
    private $datesClientFilter;
    private $instrGroupSeparator;
    private $timeFormat;

    protected $templates = [
        [
            'name' => 'adates_season_week_calendar_template',
            'file' => 'adates_season_week_calendar_template.php',
            // 'jsFile' => 'adates_season_week_calendar_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    public function initialize()
    {
        parent::initialize();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->customerInstrumentation = new CustomerInstrumentation();
        $this->customerInstrumentation->useInstrumentationGridsName = true;

        $this->reportStyles = new adates_season_week_calendar_styles();

        $this->dateTable = TableRegistry::getTableLocator()->get('adates');

        $this->instrGroupSeparator = Configure::read('Instrumentation.groupSeparator', ' ');

        $this->dbConn = ConnectionManager::get("default");
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    public function collect(array $where = []): ReportsInterface
    {

        $formData = $this->getRequest()->getData()['formData'];

        $this->seasonId = (int)$formData['season_id'];
        $this->planningLevelsList = implode(',', $formData['planninglevels']);
        $this->fDoW = (int)$formData['fdow'];
        $this->printPeriods = (int)$formData['print_periods'] == 1;

        if ((Configure::read('Calendar.Weeks.FirstDayOfWeek') == 'Su' and $this->fDoW == 7)
            or (Configure::read('Calendar.Weeks.FirstDayOfWeek') == 'Mo' and $this->fDoW == 1)
        ) {
            $fdowConflict = false;
        } else {
            $fdowConflict = true;
        }

        $this->timeFormat = Configure::read('Formats.time_short');

        if ($this->fDoW == 7) {
            //  sunday DAYOFWEEK() returns 1(=sunday)-7
            $dayOfWeekFunct = 'DAYOFWEEK(adays.date_)';
        } else {
            //  monday WEEKDAY() returns 0(=monday)-6
            $dayOfWeekFunct = '(WEEKDAY(adays.date_)+1)';
        }

        $sysClientID = $_SESSION['Auth']['User']['sysclient_id'];
        if (!$sysClientID) {
            $this->datesClientFilter = '';
        } else {
            $this->datesClientFilter = ' and (adates.sysclient_id = ' . $sysClientID
                . ' or adates.sysclient_id = 0)';
        }

        // select start and end of the season
        $collectQuery = <<<SQL
            select min(date_) as datestart, max(date_) as dateend
            from adates
            where season_id = {$this->seasonId}
                and planninglevel in ({$this->planningLevelsList})
                {$this->datesClientFilter}
            SQL;
        $result = $this->dbConn
            ->Query($collectQuery)
            ->fetchAll('assoc');
        $calendarStart = $result[0]['datestart'];
        $calendarEnd = $result[0]['dateend'];
        $dateObject = new DateTime($calendarStart);
        $weekdayNumber = $dateObject->format('N');
        if ($weekdayNumber <> $this->fDoW) {
            // complete the first week
            while ($dateObject->format('N') != $this->fDoW) {
                $dateObject->modify('-1 day'); // subtract one day
            }
            $calendarStart = $dateObject->format('Y-m-d H:i:s');
        }
        $dateObject = new DateTime($calendarEnd);
        $weekdayNumber = $dateObject->format('N');
        if ($this->fDoW == 1) {
            $lastDayOfWeek = 7;
        } else {
            $lastDayOfWeek = 6;
        }
        if ($weekdayNumber <> $lastDayOfWeek) {
            // complete the last week
            while ($dateObject->format('N') != $lastDayOfWeek) {
                $dateObject->modify('+1 day'); // add one day
            }
            $calendarEnd = $dateObject->format('Y-m-d H:i:s');
        }

        if (!$fdowConflict) {
            $pweekSource  = 'adays.pweek';
            $this->datePweekSource = 'adates.pweek';
        } else {
            if ($this->fDoW == 7) {
                // 1st day of week is sunday, 4 days of the year
                $pweekSource  = 'WEEK(adays.date_, 2)';
                $this->datePweekSource = 'WEEK(adates.date_, 2)';
            } else {
                // 1st day of week is monday, 4 days of the year
                $pweekSource  = 'WEEK(adays.date_, 1)';
                $this->datePweekSource = 'WEEK(adates.date_, 1)';
            }
        }

        // select all days of season and levels selection
        $collectQuery = <<<SQL
            select 
                CASE 
                    WHEN {$dayOfWeekFunct} IN (1,2,3)
                            AND MONTH(DATE_ADD(adays.date_, INTERVAL 4-{$dayOfWeekFunct} DAY)) <> adays.month -- day 4 belongs to next month
                        THEN MONTH(DATE_ADD(adays.date_, INTERVAL 1 MONTH)) -- days 1 - 3 belong to month of 4th day
                    WHEN {$dayOfWeekFunct} IN (5,6,7)	-- thursday, friday, saturday
                            AND MONTH(DATE_ADD(adays.date_, INTERVAL ({$dayOfWeekFunct}-4)*-1 DAY)) <> adays.month -- day 4 belongs to last month
                        THEN MONTH(DATE_ADD(adays.date_, INTERVAL -1 MONTH)) -- days 5 - 7 belong to month of 4th day
                    ELSE
                        adays.month	-- month_block equals month
                END AS month_block,
                adays.season_id, adays.date_, adays.year, adays.month, {$pweekSource} as pweek,
                group_concat(DISTINCT adays.text_ ORDER BY adays.planninglevel SEPARATOR ', ') as text_,
                group_concat(DISTINCT sholydays.name ORDER BY adays.planninglevel SEPARATOR ', ') as bank_holiday,
                group_concat(adaysperiods.name ORDER BY adaysperiods.date1 SEPARATOR '|') as period
            from adays
                left join sholydays
                    on adays.holyday_id = sholydays.id
                left join adaysperiods
                    on (adays.date_ >= adaysperiods.date1 and adays.date_ <= adaysperiods.date2
                            and adaysperiods.planninglevel in ({$this->planningLevelsList})
                        )
            where adays.date_ >= '{$calendarStart}' and adays.date_ <= '{$calendarEnd}'
                and adays.planninglevel in ({$this->planningLevelsList})
            group by adays.date_
            order by adays.date_
            SQL;

        $this->daysResult = $this->dbConn
            ->Query($collectQuery)
            ->fetchAll('assoc');

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    public function renderReport()
    {

        //  headers/labels from rep file
        $this->repFileText = $this->getRepFileParams();

        $this->onWord->setDefaultParagraphStyle(
            $this->reportStyles->defaultParagraph
        );
        $this->onWord->setDefaultFontName($this->reportStyles->defaultFont->getName());
        $this->onWord->setDefaultFontSize($this->reportStyles->defaultFont->getSize());

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $this->reportStyles->paperSize,
                'orientation' => $this->reportStyles->pageOrientation,
                'marginLeft' => Converter::cmToTwip($this->reportStyles->pageLeftBorderCm),
                'marginRight' => Converter::cmToTwip($this->reportStyles->pageRightBorderCm),
                'marginTop' => Converter::cmToTwip($this->reportStyles->pageTopBorderCm),
                'marginBottom' => Converter::cmToTwip($this->reportStyles->pageBottomBorderCm),
                'breakType' => 'continuous',
            ]
        );

        //  +++ RENDER HEADER +++
        $footer = $pageSection->addHeader();
        $footer->addText(
            htmlspecialchars(
                $this->repFileText['report_title']
                . ' ' . date('j F Y')
            ),
            $this->reportStyles->headerFont,
            $this->reportStyles->defaultParagraph
        );
        $footer->addTextBreak(1, $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);

        //  +++ RENDER FOOTER +++
        $footer = $pageSection->addFooter();
        $footer->addPreserveText(
            'Page {PAGE}',
            $this->reportStyles->footerFont,
            array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'center'])
        );

        $pWeek = -1;
        $month_block = 0;
        $monthTable = null;

        //  set styles/fonts for events
        $eventParagraph = array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'center']);
        $periodParagraph = array_merge($eventParagraph, ['shading' => ['fill' => 'FFFF99']]);

        // Calgary-specific events do not apply here
        //$chorusEventFont = clone $this->reportStyles->defaultFont;
        //$chorusEventFont
        //    ->setBold()
        //    ->setColor('7030A0');
        //$presentationEventFont = clone $this->reportStyles->defaultFont;
        //$presentationEventFont
        //    ->setBold()
        //    ->setColor('FF0000');
        //$orchPerfEventFont = clone $this->reportStyles->defaultFont;
        //$orchPerfEventFont
        //    ->setBold();
        //$orchNoPerfEventFont = $this->reportStyles->defaultFont;

        $nonPerfomanceEventFont = clone $this->reportStyles->defaultFont;
        $perfomanceEventFont = clone $this->reportStyles->defaultFont;
        $perfomanceEventFont->setBold();

        //  loop through all days of season
        foreach ($this->daysResult as $day) {

            //  new month
            if ($day['month_block'] !== $month_block) {

                if ($month_block > 0) {
                    $pageSection->addPageBreak(1);
                }
                $month_block = $day['month_block'];

                if ($day['month'] == 12 and $day['month_block'] == 1) {
                    $year = $day['year'] + 1;
                } elseif ($day['month'] == 1 and $day['month_block'] == 12) {
                    $year = $day['year'] - 1;
                } else {
                    $year = $day['year'];
                }

                $monthTable = $this->addMonthTable($pageSection);
                //->mainTable
                //->monthHeaderCell

                $monthTable->monthHeaderCell->addText(
                    date('F Y', mktime(0, 0, 0, $day['month_block'], 1, $year)),
                    $this->reportStyles->fontBold10,
                    $this->reportStyles->defaultParagraph
                );

            }

            $currPWeek = $day['pweek'];
            if ($currPWeek === null) {
                $currPWeek = 0;
            }

            //  new week
            if ($currPWeek !== $pWeek) {

                $pWeek = $currPWeek;

                $weekCells = $this->addWeekRow($monthTable->mainTable);
                //->weekHeaderCell
                //->weekDutiesCell
                //->weekProjectsCell
                //->dayNumbersCells
                //->dayEventsCells

                $weekCells->weekHeaderCell->addText(
                    '   Week ' . $pWeek,
                    $this->reportStyles->fontBold,
                    array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'center'])
                );

                //  calculate duties sum
                $sql = <<<SQL
                    SELECT
                        if(adates.orchestra_id > 0,
                            if(not ifnull(saddresses.code,'')='',
                                saddresses.code,
                                saddresses.name1
                            ),
                            ''
                            ) AS orchestra,
                        SUM(adates.duties) AS duties_sum,
                        adates.orchestra_id
                    FROM adates
                        LEFT JOIN saddresses ON adates.orchestra_id=saddresses.id
                    WHERE adates.season_id = $this->seasonId AND {$this->datePweekSource} = {$pWeek}
                        AND adates.planninglevel in ({$this->planningLevelsList})
                        AND adates.duties > 0
                        {$this->datesClientFilter}
                    GROUP BY orchestra, adates.orchestra_id
                    SQL;
                $dutiesSums = $this->dbConn
                    ->Query($sql)
                    ->fetchAll('assoc');
                $dutiesString = 'Services: ';
                if (count($dutiesSums) > 0) {
                    if (count($dutiesSums) == 1) {
                        $dutiesString = $dutiesString . number_format($dutiesSums[0]['duties_sum'], 1);
                    } else {
                        $count = 0;
                        foreach ($dutiesSums as $dutySum) {
                            $count++;
                            $dutiesString = $dutiesString . $dutySum['orchestra'] . ': ' . number_format($dutySum['duties_sum'], 1);
                            if ($count < count($dutiesSums)) {
                                $dutiesString = $dutiesString . ' / ';
                            }
                        }
                    }
                } else {
                    $dutiesString = $dutiesString . '0.0';
                }
                $weekCells->weekDutiesCell->addText(
                    htmlspecialchars($dutiesString),
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph
                );

                //  collect projects data
                $sql = <<<SQL
                    SELECT
                        DISTINCT adates.project_id, sprojects.name as project, min(adates.date_) as date_sort
                    FROM adates
                        INNER JOIN sprojects ON adates.project_id=sprojects.id
                    WHERE adates.season_id = $this->seasonId AND {$this->datePweekSource} = $pWeek
                        AND adates.planninglevel in ({$this->planningLevelsList})
                        {$this->datesClientFilter}
                    GROUP BY adates.project_id, sprojects.name
                    ORDER BY date_sort
                    SQL;
                $projects = $this->dbConn
                    ->Query($sql)
                    ->fetchAll('assoc');
                foreach ($projects as $project) {

                    $projectID = $project['project_id'];

                    $weekCells->weekProjectsCell->addText(
                        htmlspecialchars($project['project']),
                        $this->reportStyles->fontBoldUnderline,
                        $this->reportStyles->defaultParagraph
                    );

                    $sql = <<<SQL
                    SELECT
                        adates.id, adates.conductor_id
                    FROM adates
                        INNER JOIN seventtypes on adates.eventtype_id=seventtypes.id
                    WHERE adates.season_id = $this->seasonId AND {$this->datePweekSource} = $pWeek
                        AND adates.planninglevel in ({$this->planningLevelsList})
                        AND adates.project_id = $projectID
                        AND seventtypes.l_performance = 1
                        {$this->datesClientFilter}
                    ORDER BY adates.date_
                    LIMIT 1
                    SQL;
                    $peformance = $this->dbConn
                        ->Query($sql)
                        ->fetchAll('assoc');
                    if ($peformance) {

                        $performers = $this->datePerformerHelper->getConductor($peformance[0]['id'], $peformance[0]['conductor_id']);

                        $soloists = $this->datePerformerHelper->getSoloists($peformance[0]['id'], $peformance[0]['conductor_id'],
                            '; ', 0, 0, ', '
                        );

                        if ($soloists) {
                            if ($performers) {
                                $performers = $performers . ' / ';
                            }
                            $performers = $performers . $soloists;
                        }

                        $weekCells->weekProjectsCell->addText(
                            htmlspecialchars($performers),
                            $this->reportStyles->defaultFont,
                            array_merge($this->reportStyles->defaultParagraph,
                                ['indentation' => [
                                    'left' => Converter::cmToTwip(0.8), // bullshit in phpWord...
                                    'right' => 0,
                                    'firstLine' => 0,
                                    'hanging' => Converter::cmToTwip(0.8)
                                ]
                                ]
                            )

                        );

                        //  program
                        $dateID = $peformance[0]['id'];
                        $sql = <<<SQL
                            SELECT
                                adate_works.title2, adate_works.duration,
                                scomposers.lastname, scomposers.name2,
                                sworks.title1, sworks.l_intermission
                            FROM adate_works
                                INNER JOIN adates on adate_works.date_id=adates.id
                                INNER JOIN sworks on adate_works.work_id=sworks.id
                                INNER JOIN scomposers on sworks.composer_id=scomposers.id
                            WHERE adates.id = $dateID
                                {$this->datesClientFilter}
                            ORDER BY adate_works.work_order
                            SQL;
                        $program = $this->dbConn
                            ->Query($sql)
                            ->fetchAll('assoc');
                        if ($program) {
                            foreach ($program as $work) {
                                if ($work['l_intermission'] == 0) {

                                    $workText = mb_strtoupper((!$work['name2'] ? $work['lastname'] : $work['name2']))
                                        . ': ';
                                    $workText = $workText . (!$work['title2'] ? $work['title1'] : $work['title2']);

                                    $work['duration'] = $work['duration'] ?? '00:00:00';
                                    $minutes = intval(substr($work['duration'], 0, 2)) * 60
                                        + intval(substr($work['duration'], 3, 2));
                                    $seconds = intval(substr($work['duration'], 6, 2));
                                    if ($minutes > 0 or $seconds > 0) {
                                        if ($seconds > 29) {
                                            $minutes++;
                                        }
                                        $duration = $minutes . "'";
                                        $workText = $workText . ' [' . $duration . ']';
                                    }
                                } else {
                                    $workText = $this->repFileText['intermission_text'];
                                }

                                $weekCells->weekProjectsCell
                                    ->addFormattedText($workText,
                                        $this->reportStyles->defaultFont,
                                        array_merge($this->reportStyles->defaultParagraph,
                                            ['indentation' => [
                                                'left' => Converter::cmToTwip(0.8), // bullshit in phpWord...
                                                'right' => 0,
                                                'firstLine' => 0,
                                                'hanging' => Converter::cmToTwip(0.8)
                                            ]
                                            ]
                                        )
                                    );
                            }
                        }

                        //  instrumentation
                        $instrumentation = $this->getInstrumentation($dateID);
                        if ($instrumentation) {
                            $weekCells->weekProjectsCell
                                ->addText(htmlspecialchars($instrumentation),
                                    $this->reportStyles->fontItalic,
                                    array_merge($this->reportStyles->defaultParagraph,
                                        ['indentation' => [
                                            'left' => Converter::cmToTwip(0.8), // bullshit in phpWord...
                                            'right' => 0,
                                            'firstLine' => 0,
                                            'hanging' => Converter::cmToTwip(0.8)
                                        ]
                                        ]
                                    )
                                );
                        }

                    }   //  if ($peformance)

                    $weekCells->weekProjectsCell->addTextBreak(1, $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
                }   // projects of the week

                //  week events
                $sql = <<<SQL
                    SELECT
                        adates.date_, adates.start_, adates.end_,
                        adates.eventtype_id, adates.project_id, adates.orchestra_id, adates.location_id,
                        sprojects.name as project, sprojects.code as project_code,
                        seventtypes.abbreviation as activity, seventtypes.code as activity_code, seventtypes.l_performance,
                            seventtypes.name as acticity_name,
                        orchestras.name1 as orchestra,
                        locations.code as location_code, trim(concat(locations.name2, ' ', locations.name1)) as location
                    FROM adates
                        LEFT JOIN sprojects ON adates.project_id=sprojects.id
                        LEFT JOIN seventtypes ON adates.eventtype_id=seventtypes.id
                        LEFT JOIN saddresses orchestras ON adates.orchestra_id=orchestras.id
                        LEFT JOIN saddresses locations ON adates.location_id=locations.id
                    WHERE adates.season_id = $this->seasonId AND {$this->datePweekSource}= $pWeek
                        AND adates.planninglevel in ({$this->planningLevelsList})
                        {$this->datesClientFilter}
                    ORDER BY adates.date_, adates.start_
                    SQL;
                $weekEvants = $this->dbConn
                    ->Query($sql)
                    ->fetchAll('assoc');

            }   // week change


            if ($this->fDoW == 7) {
                // first day is sunday
                $cellIndex = date('w', strtotime($day['date_'])) + 1;   // date('w' returns 0-6
            } else {
                // first day is monday
                $cellIndex = date('N', strtotime($day['date_']));   // date('N' returns 1-7
            }

            $weekCells->dayNumbersCells[$cellIndex]->addText(
                ($day['month'] == $day['month_block'] ? date('j', strtotime($day['date_'])) : date('M j', strtotime($day['date_']))),
                $this->reportStyles->fontBold,
                array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'right'])
            );

            //  collect events
            $eventCell = $weekCells->dayEventsCells[$cellIndex];
            $hasDayText = true;

            if ($day['bank_holiday'] or $day['text_']) {
                if ($day['bank_holiday']) {
                    $eventCell->addText(
                        htmlspecialchars($day['bank_holiday']),
                        $this->reportStyles->fontItalic,
                        $eventParagraph
                    );
                }
                if ($day['text_']) {
                    $eventCell->addText(
                        htmlspecialchars($day['text_']),
                        $this->reportStyles->fontItalic,
                        $eventParagraph
                    );
                }
            } else {
                $hasDayText = false;
            }

            if ($this->printPeriods
                and !empty($day['period'])
            ) {
                $periods = explode('|', $day['period']);
                foreach ($periods as $period) {
                    $eventCell->addText(
                        htmlspecialchars($period),
                        $this->reportStyles->defaultFont,
                        $periodParagraph
                    );
                }
            }

            if (!$hasDayText) {
                $eventCell->addTextBreak(1, $this->reportStyles->fontItalic, $eventParagraph);
            }

            //  day events
            $eventCount = 0;
            foreach ($weekEvants as $event){
                if ($event['date_'] == $day['date_']) {

                    // Calgary-specific events do not apply here
                    //if (str_contains(strtolower($event['orchestra']),'philharmonic chorus')
                    //        or str_contains(strtolower($event['orchestra']),'philharmonic choir')
                    //) {
                    //    //  chorus event
                    //    $eventFont = $chorusEventFont;
                    //} elseif (strtolower($event['project']) == 'administration'
                    //        or (str_contains(strtolower($event['orchestra']),'calgary')
                    //            and ! (str_contains(strtolower($event['orchestra']),'chorus')
                    //                    or str_contains(strtolower($event['orchestra']),'choir')
                    //                    )
                    //            )
                    //) {
                    //    //  Orchestra event
                    //    if ($event['l_performance']) {
                    //        $eventFont = $orchPerfEventFont;
                    //    } else {
                    //        $eventFont = $orchNoPerfEventFont;
                    //    }
                    //} else {
                    //    //  Presentation event
                    //    $eventFont = $presentationEventFont;
                    //}

                    if ($event['l_performance']) {
                        $eventFont = $perfomanceEventFont;
                    } else {
                        $eventFont = $nonPerfomanceEventFont;
                    }

                    if ($eventCount > 0) {
                        $eventCell->addTextBreak(1, $eventFont, $eventParagraph);
                    }
                    $eventCount++;

                    //  start - end
                    $start = strtotime($event['start_']);
                    $end = strtotime($event['end_']);
                    if ($start) {

                        //    if (date('i', $start) > 0) {
                        //        $eventText = date('g:i', $start);
                        //    } else {
                        //        $eventText = date('g', $start);
                        //    }
                        //    if ($end) {
                        //        $eventText = $eventText . ' - ';
                        //        if (date('i', $end) > 0) {
                        //            $eventText = $eventText . substr(date('g:ia', $end),0,-1);
                        //        } else {
                        //            $eventText = $eventText . substr(date('ga', $end),0,-1);
                        //        }
                        //    } else {
                        //        $eventText = $eventText . substr(date('a', $start),0,-1);
                        //    }

                        $eventText = date($this->timeFormat, $start);

                        if ($end) {
                            $eventText = $eventText . ' - ';
                            $eventText = $eventText . date($this->timeFormat, $end);
                        }

                    } else {
                        $eventText = '';
                    }

                    //  Activity
                    if ($event['eventtype_id']) {
                        $eventText .= ' ';
                        $eventText = $eventText
                            . ($event['activity']
                                ? $event['activity']
                                : ($event['activity_code']
                                    ? $event['activity_code']
                                    : $event['acticity_name']
                                )
                            );
                    }

                    //  Project
                    if ($event['project_id']) {
                        $eventText .= ' ';
                        $eventText = $eventText
                            . ($event['project_code']
                                ? $event['project_code']
                                : $event['project']
                                );
                    }

                    //  Venue
                    if ($event['location_id']) {
                        $eventText .= '/';
                        $eventText = $eventText
                            . ($event['location_code']
                                ? $event['location_code']
                                : $event['location']
                            );
                    }

                    $eventCell->addText(
                        htmlspecialchars($eventText),
                        $eventFont,
                        $eventParagraph
                    );

                }   //  ($event['date_'] == $day['date_'])
            } //  day events

        }   //  loop through all days of season

        //  legend
        $pageSection->addPageBreak(1);

        $legendTable = $this->addLegendTable($pageSection);

        //  venues
        $currCell = $legendTable->venuesCell;
        $currCell->addTextBreak(1, $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
        $currCell->addText(
            'Venues',
            $this->reportStyles->fontBoldUnderline9,
            $this->reportStyles->defaultParagraph
        );
        $sql = <<<SQL
            SELECT
                DISTINCT 
                locations.code as location_code, trim(concat(locations.name2, ' ', locations.name1)) as location
            FROM adates
                INNER JOIN saddresses locations ON adates.location_id=locations.id
            WHERE adates.season_id = $this->seasonId
                AND adates.planninglevel in ({$this->planningLevelsList})
                {$this->datesClientFilter}
            ORDER BY 1,2
            SQL;
        $legendItems = $this->dbConn
            ->Query($sql)
            ->fetchAll('assoc');
        foreach ($legendItems as $item) {
            $textLong = $currCell->addTextRun($this->reportStyles->defaultParagraph);
            $textLong->addText(
                htmlspecialchars(
                    ($item['location_code']
                        ? $item['location_code']
                        : $item['location']
                    )
                ),
                $this->reportStyles->fontBold9,
                $this->reportStyles->defaultParagraph
            );
            $textLong->addText(
                htmlspecialchars(
                    ' = ' . $item['location']
                ),
                $this->reportStyles->font9,
                $this->reportStyles->defaultParagraph
            );
        }

        // activities
        $currCell = $legendTable->activitiesCell;
        $currCell->addTextBreak(1, $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
        $currCell->addText(
            'Events',
            $this->reportStyles->fontBoldUnderline9,
            $this->reportStyles->defaultParagraph
        );
        $sql = <<<SQL
            SELECT
                DISTINCT 
                seventtypes.abbreviation as activity, seventtypes.code as activity_code, seventtypes.name as acticity_name
            FROM adates
                INNER JOIN seventtypes ON adates.eventtype_id=seventtypes.id
            WHERE adates.season_id = $this->seasonId
                AND adates.planninglevel in ({$this->planningLevelsList})
                {$this->datesClientFilter}
            SQL;
        $legendItems = $this->dbConn
            ->Query($sql)
            ->fetchAll('assoc');
        foreach ($legendItems as $item) {
            $textLong = $currCell->addTextRun($this->reportStyles->defaultParagraph);
            $textLong->addText(
                htmlspecialchars(
                    ($item['activity']
                        ? $item['activity']
                        : ($item['activity_code']
                            ? $item['activity_code']
                            : $item['acticity_name']
                        )
                    )
                ),
                $this->reportStyles->fontBold9,
                $this->reportStyles->defaultParagraph
            );
            $textLong->addText(
                htmlspecialchars(
                    ' = ' . $item['acticity_name']
                ),
                $this->reportStyles->font9,
                $this->reportStyles->defaultParagraph
            );
        }

    }

    public function getInstrumentation($dateID = 0)
    {
        $perfWorks = $this->dateTable
            ->find('all')
            ->contain(
                [
                    'AdateWorks' => [
                        'AdateworkKeyboards' => ['Sinstrinstruments'],
                        'AdateworkExtras' => ['Sinstrinstruments'],
                        'AdateworkVocals' => ['Sinstrinstruments']
                    ]
                ]
            )
            ->where(['adates.id' => $dateID])
            ->toArray();
        $this->customerInstrumentation->addInstrumentationString($perfWorks[0]);
        $instrGrids = $perfWorks[0]['max_instrumentation_grid_name'];

        $instrGroups = [];

        if ($perfWorks[0]['max_WindsBrass'] !== '') {
            $instrGroups[count($instrGroups)] = $perfWorks[0]['max_WindsBrass'];
        }
        if ($perfWorks[0]['max_TimpPerc'] !== '') {
            $instrGroups[count($instrGroups)] = $perfWorks[0]['max_TimpPerc'];
        }
        if ($perfWorks[0]['max_Harp'] !== '') {
            $instrGroups[count($instrGroups)] = $perfWorks[0]['max_Harp'];
        }
        if ($instrGrids['keyboards'] !== '') {
            $instrGroups[count($instrGroups)] = $instrGrids['keyboards'];
        } else {
            if ($perfWorks[0]['max_Keyboard'] !== '') {
                $instrGroups[count($instrGroups)] = $perfWorks[0]['max_Keyboard'];
            }
        }
        if ($instrGrids['extras'] !== '') {
            $instrGroups[count($instrGroups)] = $instrGrids['extras'];
        } else {
            if ($perfWorks[0]['max_Extra'] !== '') {
                $instrGroups[count($instrGroups)] = $perfWorks[0]['max_Extra'];
            }
        }
        if ($instrGrids['vocals'] !== '') {
            $instrGroups[count($instrGroups)] = $instrGrids['vocals'];
        }
        if ($perfWorks[0]['max_Strings'] !== '') {
            if ($perfWorks[0]['max_StringsLabel'] !== '') {
                $instrGroups[count($instrGroups)] = $perfWorks[0]['max_StringsLabel'].': '.$perfWorks[0]['max_Strings'];
            } else {
                $instrGroups[count($instrGroups)] = $perfWorks[0]['max_Strings'];
            }
        }

        if (count($instrGroups) > 0) {
            $instrumentation = implode($this->instrGroupSeparator, $instrGroups);
        } else {
            $instrumentation = '';
        }

        return $instrumentation;
    }

    public function addMonthTable($pageSection): object
    {
        $returnTable = (object)[];

        $returnTable->mainTable = null;
        $returnTable->monthHeaderCell = null;

        $mainTable = $pageSection->addTable($this->reportStyles->mainTableStyle);

        $returnTable->mainTable = $mainTable;

        //  header with month and weekdays
        $mainTable->addRow(10, ['tblHeader' => true]);

        //  month name
        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->weekColumnWidthCm
                + $this->reportStyles->projectColumnWidthCm),
            array_merge($this->reportStyles->defaultCell, ['bgColor' => adates_season_week_calendar_styles::WEEKDAY_ROW_COLOR])
        );
        $cell->getStyle()->setGridSpan(2);

        $returnTable->monthHeaderCell = $cell;

        //  weekdays
        for ($dayIndex = 1; $dayIndex <= 7; $dayIndex++) {

            $cell = $mainTable->addCell(
                Converter::cmToTwip($this->reportStyles->dayColumnWidthCm),
                array_merge($this->reportStyles->defaultCell, ['bgColor' => adates_season_week_calendar_styles::WEEKDAY_ROW_COLOR])
            );

            if ($this->fDoW == 7) {
                // first day is sunday
                $wDay   = $dayIndex;
            } else {
                // first day is monday
                if ($dayIndex < 7) {
                    $wDay   = $dayIndex + 1;
                } else {
                    $wDay   = 1;
                }
            }
            $cell->addText(
                date('l',mktime(0,0,0,10,$wDay,2023)),  // 2023-10-1 is a monday
                $this->reportStyles->fontBold,
                array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'center'])
            );

        }

        return $returnTable;

    }

    public function addWeekRow($mainTable): object
    {

        $returnCellCollection = (object)[];

        $returnCellCollection->weekDutiesCell = null;
        $returnCellCollection->dayNumbersCells = [];
        $returnCellCollection->weekHeaderCell = null;
        $returnCellCollection->weekProjectsCell = null;
        $returnCellCollection->dayEventsCells = [];

        $mainTable->addRow();

        //  pweek (dummy, no output)
        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->weekColumnWidthCm),
            array_merge($this->reportStyles->defaultCellNoBottom,
                ['bgColor' => adates_season_week_calendar_styles::WEEK_HEADER_COLOR]
            )
        );

        //  duties sum
        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->projectColumnWidthCm),
            $this->reportStyles->defaultCellNoBottom,
        );
        $returnCellCollection->weekDutiesCell = $cell;

        //  day numbers
        for ($wDay = 1; $wDay <= 7; $wDay++) {
            $cell = $mainTable->addCell(
                Converter::cmToTwip($this->reportStyles->dayColumnWidthCm),
                array_merge($this->reportStyles->defaultCellNoBottom,
                    ['bgColor' => adates_season_week_calendar_styles::DAY_NUMBER_COLOR]
                )
            );
            $returnCellCollection->dayNumbersCells[$wDay] = $cell;
        }

        $mainTable->addRow( Converter::cmToTwip(2.00));

        //  pweek
        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->weekColumnWidthCm),
            array_merge($this->reportStyles->defaultCellNoTop,
                ['bgColor' => adates_season_week_calendar_styles::WEEK_HEADER_COLOR,
                    'textDirection' => 'btLr'
                ]
            )
        );
        $returnCellCollection->weekHeaderCell = $cell;

        //  projects
        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->projectColumnWidthCm),
            $this->reportStyles->defaultCellNoTop,
        );
        $returnCellCollection->weekProjectsCell = $cell;

        //  day evemts
        for ($wDay = 1; $wDay <= 7; $wDay++) {
            $cell = $mainTable->addCell(
                Converter::cmToTwip($this->reportStyles->dayColumnWidthCm),
                $this->reportStyles->defaultCellNoTop,
            );
            $returnCellCollection->dayEventsCells[$wDay] = $cell;
        }

        return $returnCellCollection;
    }

    public function addLegendTable($pageSection): object
    {
        $returnTable = (object)[];

        $returnTable->venuesCell = null;
        $returnTable->activitiesCell = null;
        $returnTable->projectsCell = null;

        $mainTable = $pageSection->addTable($this->reportStyles->mainTableStyle);

        $mainTable->addRow();

        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->mainTableWidthCm),
            $this->reportStyles->defaultCell
        );
        $cell->getStyle()->setGridSpan(3);
        $cell->addText(
            'LEGEND',
            $this->reportStyles->fontBold11,
            array_merge($this->reportStyles->defaultParagraph, ['alignment' => 'center'])
        );

        $mainTable->addRow();

        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->mainTableWidthCm / 3),
            $this->reportStyles->legendOuterCell
        );
        $returnTable->venuesCell = $cell;

        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->mainTableWidthCm / 3),
            $this->reportStyles->legendMiddleCell
        );
        $returnTable->activitiesCell = $cell;

        $cell = $mainTable->addCell(
            Converter::cmToTwip($this->reportStyles->mainTableWidthCm / 3),
            $this->reportStyles->legendOuterCell
        );
        $returnTable->projectsCell = $cell;

        return $returnTable;

    }

}
