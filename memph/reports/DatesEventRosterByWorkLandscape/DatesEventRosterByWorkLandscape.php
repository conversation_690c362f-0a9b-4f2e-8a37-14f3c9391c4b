<?php

namespace Customer\memph\reports\DatesEventRosterByWorkLandscape;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;
use Cake\Core\Configure;

use Customer\fasutilities\reports\utility\DutyQueryHelper;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\reports\utility\NameFormatHelper;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * This report has many inefficiencies but was designed for ease in creating customizations.
 * Clients have many different variations of event rosters and this structure will hopefully make it
 *   easier to re-arrange the elements of this report
 * Designed to make it easier to format names, add/remove dedicated program section, schedule and absent musicians
 */

class DatesEventRosterByWorkLandscape extends ReportWord
{
//    table total 9.75 in
    const HEADER_COLUMN_WIDTH = 4.875;
    const STRING_COL_WIDTH = 1.95;
    const WIND_COL_WIDTH = 0.89;

    /**
     * Helper class for each event selected by the user
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the  Service records associated with the selected date
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;

    /**
     * Helper class for nconductor/soloist formatting
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for all name formatting
     * @var NameFormatHelper
     */
    private $nameFormatHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function initialize()
    {
        parent::initialize();
        $this->dutyQueryHelper = new DutyQueryHelper();
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->nameFormatHelper = new NameFormatHelper();
        $this->reportStyles = new DatesEventRosterByWorkLandscapeStyles();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses and status
     */
    public function collect(array $where = []): ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }


    public function renderReport()
    {
//      +++ Set REPORT FORMATS based on client region and preferences +++
        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $customerName = Configure::read('CustomerSettings.name');
        $footerDate = Configure::read('Formats.date');

//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();
//        most often-used font and paragraph styles, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $headerFont = $this->reportStyles->headerFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $centerParagarph = $this->reportStyles->defaultParagraphCenter;
        $shadedCell = $this->reportStyles->shadedCell;
        $shadedBorderCell = $this->reportStyles->borderRightShadedCell;
        $borderCell = $this->reportStyles->borderRightCell;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'orientation' => "landscape",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

//        RENDER FOOTER
        $footer = $pageSection->addFooter();
        $footer->addPreserveText(
            'Page {PAGE} of {NUMPAGES}.'
            . "\t" . date($footerDate)
            . "\t" . $customerName,
            $defaultFont,
            $this->reportStyles->footerParagraph,
        );

//        Typical use case for this report is to run for one project at a time, using the Anchor Date for roster output.
//        For Dates selected by User, loop through Season+Project+ProgramNumber, putting a page break between each
        $p = 0;
        foreach ($this->getProjects() as $project) {
            if ($p > 0) {
                $pageSection->addPageBreak();
            }

//            Anchor Date - date Object
            $event = $this->getAnchorDate($project);

//             Elements of the REPORT HEADER - project, conductor, etc. Identified individually to make future customizations easier
            $projectName = $event['sproject']['name'];
            $eventTitle = htmlspecialchars($event['programtitle']);
            $dateRange = $this->getDateRange($project);

            if ($event['orchestra_id'] > 0) {
                $orchestraName = $this->nameFormatHelper->getEntityName(
                    $event['orchestraaddress']['name1'],
                    $event['orchestraaddress']['name2'],
                    1
                );
            } else {
                $orchestraName = '';
            }
            if ($event['location_id'] > 0) {
                $venueName = $this->nameFormatHelper->getEntityName(
                    $event['locationaddress']['name1'],
                    $event['locationaddress']['name2'],
                    1
                );
            } else {
                $venueName = '';
            }

            if ($event['conductor_id'] > 0) {
                $conductorName = $this->datePerformerHelper->getConductor(
                    $event['id'],
                    $event['conductor_id'],
                    0,
                    0,
                    ', ',
                    ';'
                );
            } else {
                $conductorName = '';
            }

            $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
                $event['id'],
                '</w:t><w:br/><w:t>',
                0,
                0,
                ', ',
                '; '
            );
            $soloists = $this->datePerformerHelper->getSoloists($event['id'], $event['conductor_id'], '; ', 0, 0, ', ');


//            +++ RENDER HEADER +++
            $pageSection->addFormattedText(
                $orchestraName . ' ' . $repFileText['report_title'],
                $headerFont,
                $this->reportStyles->headerParagraph
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            +++ RENDER HEADER TABLE +++
            $headerTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $headerTable->addRow();
            $leftHeaderCell = $headerTable->addCell(
                Converter::inchToTwip(self::HEADER_COLUMN_WIDTH),
                $this->reportStyles->defaultCellStyle
            );
            $rightHeaderCell = $headerTable->addCell(
                Converter::inchToTwip(self::HEADER_COLUMN_WIDTH),
                $this->reportStyles->defaultCellStyle
            );

//            Some elements are rendered as an imploded array so empty elements are properly suppressed
            $leftHeaderCell->addFormattedText($projectName, $headerFont, $defaultParagraph);
            $leftHeaderCell->addText($dateRange, $headerFont, $defaultParagraph);
            $leftHeaderCell->addText(
                $repFileText['printed'] . ' ' . date($topDateFormat),
                $this->reportStyles->defaultFontItalic,
                $defaultParagraph
            );

            $rightHeaderContent = implode(
                '</w:t><w:br/><w:t>',
                array_filter(
                    array($eventTitle, $conductorName, $dateWorkConductors, $soloists)
                )
            );
            $rightHeaderCell->addText($rightHeaderContent, $headerFont, $defaultParagraph);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            +++ RENDER PROGRAM via dedicated class +++
//             Standard Version of report omits this as the Program is rendered in the body of the report
//            This is kept here because some client(s) will want it to be included
//            $programTable = new DatesEventRosterByWorkLandscapeProgram($event['id'], $pageSection);
//            $programTable->renderConcertProgram();
//            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//          FETCH ALL DUTIES associated with the event
            $duties = $this->dutyQueryHelper->getDutiesForDateId($event['id'])
                ->withDutyWorks()
                ->getQuery()
                ->toArray();


//            ++++ RENDER STRINGS SECTIONS +++
            $this->renderStrings($duties, $pageSection, $defaultFont, $defaultFontBold, $defaultParagraph);

//            RENDER FIXED TABLE of SECTION NAMES
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            $this->renderSectionHeaderTable(
                $pageSection,
                $defaultFontBold,
                $centerParagarph,
                $shadedCell,
                $shadedBorderCell
            );
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//            ++++ FETCH WINDS / BRASS PERCUSSION ++++
//            Loop through all Duty Records for the Event
            $musicianArray = [];
            foreach ($duties as $duty) {
//                Loop through all Duty_Works linked to the active Duty record
                if ($duty['sdutytype']['l_present'] == true
                    && $duty['sinstrinstrument']['sinstrsection']['syssection_id'] > 5) {
                    foreach ($duty['aduty_works'] as $dutyWork) {
                        if ($dutyWork['l_play'] == true || !empty(trim($dutyWork['seat']))) {
//                        Create Array of all Duty, DutyWork and DateWork data if the musician plays the work
                            // SORT ORDER - section, duty-work seat, duty seat, duty order, alpha
                            $sortOrder = str_pad(
                                $duty['sinstrinstrument']['sinstrsection']['section_order'],
                                3,
                                '0',
                                STR_PAD_LEFT
                            );
                            $sortOrder .= trim($dutyWork['seat']) ? str_pad(
                                $dutyWork['seat'],
                                2,
                                0,
                                STR_PAD_LEFT
                            ) : 'ZZ';
                            $sortOrder .= trim($duty['seat']) ? str_pad($duty['seat'], 2, 0, STR_PAD_LEFT) : 'ZZ';
                            $sortOrder .= trim($duty['order_1']) ? str_pad(
                                $duty['order_1'],
                                2,
                                0,
                                STR_PAD_LEFT
                            ) : 'ZZ';
                            $sortOrder .= trim($duty['artistaddress']['name1'] . $duty['artistaddress']['name2']);

                            $musicianEntry = array(
                                'sortOrder' => $sortOrder,
                                'sysSectionId' => $duty['sinstrinstrument']['sinstrsection']['syssection_id'],
                                'sectionId' => $duty['sinstrinstrument']['sinstrsection']['id'],
                                'dateWorkId' => $dutyWork['datework_id'],
                                'section' => $duty['sinstrinstrument']['sinstrsection']['name'],
                                'instrument' => $duty['sinstrinstrument']['name'],
                                'instrumentCode' => $duty['sinstrinstrument']['code'],
                                'dutySeat' => $duty['seat'],
                                'dutyWorkSeat' => $dutyWork['seat'],
                                'musicianName' => $this->nameFormatHelper->getEntityName(
                                    $duty['artistaddress']['name1'],
                                    $duty['artistaddress']['name2'],
                                    3
                                ),
                            );
                            $musicianArray[] = $musicianEntry;
                        }
                    }
                }
            }

            //        Sort the Musician Array by Section and musician order within the Section
            $rosterArray = $musicianArray;
            foreach ($rosterArray as $key => $value) {
                $rosterSort[$key] = $value['sortOrder'];
            }
            array_multisort($rosterSort, SORT_ASC, $rosterArray);


//            ++++ RENDER WINDS / BRASS / PERCUSSION /  OTHER SECTIONS BY WORK ++++

//            Sort Date-Work entries by Work Order. Then Loop through each Date Work on the event
            $dateWorkArray = $event['adate_works'];
            usort(
                $dateWorkArray,
                function ($a, $b) {
                    return $a['work_order'] <=> $b['work_order'];
                }
            );

//            Loop through each date-work on the event
//            Build an array of each musician assigned to that work, with all necessary output data
            foreach ($dateWorkArray as $dateWork) {
                if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
//                RENDER DATE-WORK Title and INSTRUMENTATION ROW
                    $dateWorkOutput = mb_strtoupper($dateWork['swork']['scomposer']['lastname'] . ': ');
                    $dateWorkOutput .= trim($dateWork['title2']) ?? trim($dateWork['swork']['title1']);
                    $pageSection->addFormattedText(
                        $dateWorkOutput,
                        $this->reportStyles->headerFont,
                        $defaultParagraph
                    );

                    $this->renderDateWorkInstrumentation(
                        $dateWork,
                        $pageSection,
                        $defaultFont,
                        $centerParagarph,
                        $shadedCell,
                        $shadedBorderCell
                    );


//                   RENDER MUSICIANS ON THE SECTION
//          +++  WIND SECTIONS +++
                    $windHeaderArray = array(
                        '6' => __('sworks.flute'),
                        '7' => __('sworks.oboe'),
                        '8' => __('sworks.clarinet'),
                        '9' => __('sworks.bassoon'),
                        '10' => __('sworks.horn'),
                        '11' => __('sworks.trumpet'),
                        '12' => __('sworks.trombone'),
                        '13' => __('sworks.tuba'),
                        '14' => __('sworks.timpani'),
                        '15' => __('sworks.percussion'),
                        '16' => __('sworks.harp')
                    );
                    $windTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
                    $windTable->addRow();
                    $borderCellArray = [9, 13, 15, 16];

                    for ($sysSection = 6; $sysSection < 17; $sysSection++) {
//                        set right-vertical border on cells between sections
                        if (in_array($sysSection, $borderCellArray)) {
                            $borderStyle = $borderCell;
                        } else {
                            $borderStyle = $this->reportStyles->defaultCellStyle;
                        }

                        $windCell = $windTable->addCell(
                            Converter::inchToTwip(self::WIND_COL_WIDTH),
                            $borderStyle
                        );

                        foreach ($rosterArray as $musician) {
                            if ($musician['sysSectionId'] === $sysSection && $musician['dateWorkId'] === $dateWork['id']) {
                                $windCell->addText($musician['musicianName'], $defaultFont, $defaultParagraph);
                            }
                        }
                    }
//                    add cell for Keyboard/Extra combined
                    $kyeboardCell = $windTable->addCell(
                        Converter::inchToTwip(self::WIND_COL_WIDTH),
                        $this->reportStyles->defaultCellStyle
                    );
                    foreach ($rosterArray as $musician) {
                        if (($musician['sysSectionId'] == 17 || $musician['sysSectionId'] == 18) && $musician['dateWorkId'] === $dateWork['id']) {
                            $kyeboardCell->addText(
                                $musician['musicianName'] . ' - ' . ($musician['instrumentCode'] ?? $musician['instrument']),
                                $defaultFont,
                                $defaultParagraph
                            );
                        }
                    }

                    $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
                }
            }

//            ++++ RENDER SCHEDULE FOR PROJECT ++++
//   remove program number criteria to identify the project  +++
            $pageSection->addFormattedText('Schedule', $headerFont, $this->reportStyles->headerParagraph);
            $schedule = new DatesEventRosterByWorkLandscapeSchedule(
                $this->datesResult,
                $event['season_id'] . ':' . $event['project_id'],
                $pageSection
            );
            $schedule->renderSchedule();
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//            ++++ RENDER ABSENCES FOR ENTIRE PROJECT ++++
//   remove program number criteria to identify the project  +++
            $pageSection->addFormattedText('Musicians Excused', $headerFont, $this->reportStyles->headerParagraph);
            $schedule = new DatesEventRosterByWorkLandscapeAbsences(
                $this->datesResult,
                $event['season_id'] . ':' . $event['project_id'],
                $pageSection
            );
            $schedule->renderAbsences();
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

            $p++;
        }
    }






    //    Typically the use case is to run this report for one project at a time. However, the user could run it for
//      more than one. this function breaks the selected dates down into each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

    private function getAnchorDate(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
        return '';
    }

    private function getDateRange(string $project): string
    {
        $eventArray = [];
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] == $project) {
                $eventArray[] = $dateResult['date_'];
            }
        }
        $firstDate = reset($eventArray);
        $lastDate = end($eventArray);
        if (!empty($eventArray)) {
            return $this->dateQueryHelper->getFormattedDateRange($firstDate, $lastDate, 2);
        } else {
            return '';
        }
    }

    protected function renderStrings($duties, $pageSection, $defaultFont, $defaultFontBold, $defaultParagraph)
    {
        $musicianArray = [];
        foreach ($duties as $duty) {
            if ($duty['sdutytype']['l_present'] == 1) {
                $sortOrder = str_pad(
                    $duty['sinstrinstrument']['sinstrsection']['section_order'],
                    3,
                    '0',
                    STR_PAD_LEFT
                );
                $sortOrder .= trim($duty['seat']) ? str_pad($duty['seat'], 2, 0, STR_PAD_LEFT) : 'ZZ';
                $sortOrder .= trim($duty['order_1']) ? str_pad($duty['order_1'], 2, 0, STR_PAD_LEFT) : 'ZZ';
                $sortOrder .= $duty['artistaddress']['name1'] . $duty['artistaddress']['name2'];
                if (substr($duty['seat'], 1, 1) == 'A') {
                    $chair = substr($duty['seat'], 0, 1) . "\t";
                } else {
                    $chair = "\t";
                }

                $musicianEntry = array(
                    'sortOrder' => $sortOrder,
                    'sysSectionId' => $duty['sinstrinstrument']['sinstrsection']['syssection_id'],
                    'sectionId' => $duty['sinstrinstrument']['sinstrsection']['id'],
                    'section' => $duty['sinstrinstrument']['sinstrsection']['name'],
                    'instrument' => $duty['sinstrinstrument']['name'],
                    'seat' => $duty['seat'],
                    'chair' => $chair,
                    'musicianName' => $this->nameFormatHelper->getEntityName(
                        $duty['artistaddress']['name1'],
                        $duty['artistaddress']['name2'],
                        3
                    ),
                );

                $musicianArray[] = $musicianEntry;
            }
        }
        //        Sort the array by Section and musician order within the Section
        $rosterArray = $musicianArray;
        foreach ($rosterArray as $key => $value) {
            $rosterSort[$key] = $value['sortOrder'];
        }
        array_multisort($rosterSort, SORT_ASC, $rosterArray);

//         +++ STRING SECTIONS +++
        $stringHeaderArray = array(
            '1' => __('sworks.violin1'),
            '2' => __('sworks.violin2'),
            '3' => __('sworks.viola'),
            '4' => __('sworks.cello'),
            '5' => __('sworks.bass')
        );
        $stringTable = $pageSection->addTable($this->reportStyles->rosterTableStyle);
        $stringTable->addRow();

        for ($sysSection = 1; $sysSection <= 5; $sysSection++) {
            $stringCell = $stringTable->addCell(
                Converter::inchToTwip(self::STRING_COL_WIDTH),
                $this->reportStyles->defaultCellStyle
            );
            $stringCell->addText($stringHeaderArray[$sysSection], $defaultFontBold, $defaultParagraph);

            foreach ($rosterArray as $musician) {
                if ($musician['sysSectionId'] === $sysSection) {
                    $stringCell->addText(
                        $musician['chair'] . $musician['musicianName'],
                        $defaultFont,
                        $this->reportStyles->rosterParagraph
                    );
                }
            }
        }
        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
    }

    private function renderSectionHeaderTable(
        $pageSection,
        $defaultFontBold,
        $centerParagarph,
        $shadedCell,
        $shadedBorderCell
    ) {
        $sectionsHeaderTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $sectionsHeaderTable->addRow();
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('flute'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('oboe'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('clarinet'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)->addText(
            __('bassoon'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('horn'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('trumpet'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('trombone'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)->addText(
            __('tuba'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            __('timpani'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)->addText(
            __('percussion'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)->addText(
            __('harp'),
            $defaultFontBold,
            $centerParagarph
        );
        $sectionsHeaderTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)->addText(
            'Kybd/Extra',
            $defaultFontBold,
            $centerParagarph
        );
    }

    private function renderDateWorkInstrumentation(
        $dateWork,
        $pageSection,
        $defaultFont,
        $centerParagarph,
        $shadedCell,
        $shadedBorderCell
    ) {
        $instrumentationTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
        $instrumentationTable->addRow();
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['flute_text'])) ? $dateWork['flute_text'] : $dateWork['flute'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['oboe_text'])) ? $dateWork['oboe_text'] : $dateWork['oboe'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['clarinet_text'])) ? $dateWork['clarinet_text'] : $dateWork['clarinet'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)
            ->addText(
                !empty(trim($dateWork['bassoon_text'])) ? $dateWork['bassoon_text'] : $dateWork['bassoon'],
                $defaultFont,
                $centerParagarph
            );

        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['horn_text'])) ? $dateWork['horn_text'] : $dateWork['horn'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['trumpet_text'])) ? $dateWork['trumpet_text'] : $dateWork['trumpet'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['trombone_text'])) ? $dateWork['trombone_text'] : $dateWork['trombone'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)
            ->addText(
                !empty(trim($dateWork['tuba_text'])) ? $dateWork['tuba_text'] : $dateWork['tuba'],
                $defaultFont,
                $centerParagarph
            );

        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText(
                !empty(trim($dateWork['timpani_text'])) ? $dateWork['timpani_text'] : $dateWork['timpani'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)
            ->addText(
                !empty(trim($dateWork['percussion_text'])) ? $dateWork['percussion_text'] : $dateWork['percussion'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedBorderCell)
            ->addText(
                !empty(trim($dateWork['harp_text'])) ? $dateWork['harp_text'] : $dateWork['harp'],
                $defaultFont,
                $centerParagarph
            );
        $instrumentationTable->addCell(Converter::inchToTwip(self::WIND_COL_WIDTH), $shadedCell)
            ->addText($dateWork['keyboard'] . '/' . $dateWork['extra'], $defaultFont, $centerParagarph);
    }


}
