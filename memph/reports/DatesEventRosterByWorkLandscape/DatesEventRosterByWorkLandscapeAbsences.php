<?php

namespace Customer\memph\reports\DatesEventRosterByWorkLandscape;

use Customer\fasutilities\reports\utility\DutyQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

class DatesEventRosterByWorkLandscapeAbsences
{
    const COL_WIDTH = 3.05;  // approx 1/3 of total 9.25 width

    /** fetch duties for ALL Dates in the Project / Season
     * @var DutyQueryHelper
     */
    private $dutyQueryHelper;
    private $duties;

    /** report styles
     *@var DatesEventRosterByWorkLandscapeStyles
     */
    private $reportStyles;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;

    public function __construct($dates, $project, Section $pageSection)
    {
        $this->dutyQueryHelper = new DutyQueryHelper();
//        $this->duties = $duties;
        $this->dates = $dates;
        $this->project = $project;
        $this->pageSection = $pageSection;

        $this->reportStyles = new DatesEventRosterByWorkLandscapeStyles();
    }



    public function renderAbsences()
    {
        $rosterFont = $this->reportStyles->defaultFont;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

//  While the concert roster (present musicians) outputs for the anchor date, the ABSENCE
//        section shows musician absences for the entire project.
        $absenceArray = [];
        foreach ($this->dates as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] === $this->project) {
                $duties = $this->dutyQueryHelper->getDutiesForDateId($dateResult['id'])->getQuery()->toArray();


//        build array of absent musicians for this event
                foreach ($duties as $duty) {
                    if ($duty['sdutytype']['l_present'] == 0) {
                        $sortOrder = $duty['sinstrinstrument']['sinstrsection']['syssection_id']
                            . $duty['artistaddress']['name1'] . $duty['sartistaddress']['name2']
                            . $duty['sdutytype']['code'] . $duty['adate']['date_'];

                        $absenceArray[] = array(
                            'sortOrder' => $sortOrder,
                            'sysSectionId' => $duty['sinstrinstrument']['sinstrsection']['syssection_id'],
                            'sectionId' => $duty['sinstrinstrument']['sinstrsection']['id'],
                            'section' => $duty['sinstrinstrument']['sinstrsection']['name'],
                            'musicianOutput' => $duty['artistaddress']['name1'] . ' - ' . $duty['sdutytype']['code'],
                            'absenceCode' => $duty['sdutytype']['code'],
                            'absence' => $duty['sdutytype']['name'],
                            'absenceDate' => $duty['adate']['date_']
                        );
                    }
                }
            }
        }


// SORT GRID by Musician / type / date
        usort(
            $absenceArray,
            function ($a, $b) {
                return $a['sortOrder'] <=> $b['sortOrder'];
            }
        );

//        RENDER ABSENCE TABLE
        $this->pageSection->addText('', $rosterFont, $defaultParagraph);

//        THREE - Column table
        $absentTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

        $absentTable->addRow();
        $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell)
            ->addText('STRINGS', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell)
            ->addText('WINDS / BRASS', $this->reportStyles->defaultFontBold, $defaultParagraph);
        $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell)
            ->addText('TIMP / PERC / HARP / ETC.', $this->reportStyles->defaultFontBold, $defaultParagraph);

        $absentTable->addRow();
        $leftCell = $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell);
        $middleCell = $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell);
        $rightCell = $absentTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $this->reportStyles->defaultCell);

//        Column 1 - strings
        for ($sysSection = 1; $sysSection <= 5; $sysSection++) {
            $date = '';
            $output = '';
            foreach ($absenceArray as $musician) {
                if ($musician['sysSectionId'] === $sysSection) {
//      New Row; different musician and absence
                    if ($musician['musicianOutput'] !== $output && $musician['absenceDate']->format('Ymd') !== $date) {
                        $musicianTextRun = $leftCell->addTextRun($defaultParagraph);
                        $musicianTextRun->addText(
                            $musician['musicianOutput'] . ': ' . $musician['absenceDate']->format('n/j'),
                            $rosterFont
                        );
                    } elseif ($musician['musicianOutput'] == $output && $musician['absenceDate']->format(
                            'Ymd'
                        ) !== $date) {
//        same Musician and Absence; BUT different date - append date to same row
                        $musicianTextRun->addText('; ' . $musician['absenceDate']->format('n/j'), $rosterFont);
                    }

                    $output = $musician['musicianOutput'];
                    $date = $musician['absenceDate']->format('Ymd');
                }
            }
        }

        //        Column 2 - winds/brass/percussion
        for ($sysSection = 6; $sysSection <= 13; $sysSection++) {
            $date = '';
            $output = '';
            foreach ($absenceArray as $musician) {
                if ($musician['sysSectionId'] === $sysSection) {
//      New Row; different musician and absence
                    if ($musician['musicianOutput'] !== $output && $musician['absenceDate']->format('Ymd') !== $date) {
                        $musicianTextRun = $middleCell->addTextRun($defaultParagraph);
                        $musicianTextRun->addText(
                            $musician['musicianOutput'] . ': ' . $musician['absenceDate']->format('n/j'),
                            $rosterFont
                        );
                    } elseif ($musician['musicianOutput'] == $output && $musician['absenceDate']->format(
                            'Ymd'
                        ) !== $date) {
//        same Musician and Absence; BUT different date - append date to same row
                        $musicianTextRun->addText('; ' . $musician['absenceDate']->format('n/j'), $rosterFont);
                    }

                    $output = $musician['musicianOutput'];
                    $date = $musician['absenceDate']->format('Ymd');
                }
            }
        }

        //        Column 3 - winds/brass/percussion
        for ($sysSection = 14; $sysSection <= 18; $sysSection++) {
            $date = '';
            $output = '';
            foreach ($absenceArray as $musician) {
                if ($musician['sysSectionId'] === $sysSection) {
//      New Row; different musician and absence
                    if ($musician['musicianOutput'] !== $output && $musician['absenceDate']->format('Ymd') !== $date) {
                        $musicianTextRun = $rightCell->addTextRun($defaultParagraph);
                        $musicianTextRun->addText(
                            $musician['musicianOutput'] . ': ' . $musician['absenceDate']->format('n/j'),
                            $rosterFont
                        );
                    } elseif ($musician['musicianOutput'] == $output && $musician['absenceDate']->format(
                            'Ymd'
                        ) !== $date) {
//        same Musician and Absence; BUT different date - append date to same row
                        $musicianTextRun->addText('; ' . $musician['absenceDate']->format('n/j'), $rosterFont);
                    }

                    $output = $musician['musicianOutput'];
                    $date = $musician['absenceDate']->format('Ymd');
                }
            }
        }
    }







}
