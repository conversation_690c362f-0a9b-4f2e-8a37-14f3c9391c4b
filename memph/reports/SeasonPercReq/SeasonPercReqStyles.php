<?php

namespace Customer\memph\reports\SeasonPercReq;

use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Tab;

class SeasonPercReqStyles
{
    const OPAS_RED = '842020';
    const DARK_GREY = '696B64';

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->smallFont = clone($this->defaultFont);
        $this->smallFont->setSize(5);


        $this->seasonFont = clone($this->defaultFontBold);
        $this->seasonFont->setSize(12);

        $this->titleFont = clone($this->defaultFont);
//        $this->titleFont->setBold(true);
        $this->titleFont->setSize(16);

    }

    /**
     * Define the styles for this section: Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            [
                'alignment' => 'right'
            ]
        );


        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->headerParagraph = array_merge(
            $this->defaultParagraph,
            ['borderBottomSize' => 6, 'borderBottomColor' => self::OPAS_RED]
        );


        // paragraph style for a footer if needed
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        $this->footerParagraphStyle = $phpWord->addParagraphStyle(
            'footerParagraphStyle',
            array(
                'alignment' => 'left',  // left; Right; Center; other options available
                'spaceBefore' => 100,
                'spaceAfter' => 0,
                'spacing' => 0,  // space between lines in twips
                'borderTopSize' => 6,
                'borderColor' => "ded6d5",  // light grey top border
                'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
                    new \PhpOffice\PhpWord\Style\Tab('center', 5040),
                    new \PhpOffice\PhpWord\Style\Tab('right', 10400),
                ),
            )
        );


//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 10440, // 7.25 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];

//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

        $this->headerCell = array_merge(
            $this->defaultCell,
            [
                'borderSize' => 12,
                'borderColor' => self::DARK_GREY,
                'borderRightStyle' => 'single'
            ]


        );

        $this->borderCell = array_merge(
            $this->defaultCell,
            [
                'borderSize' => 6,
                'borderColor' => self::DARK_GREY,
                'borderRightStyle' => 'single'
            ]


        );


        $this->shadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'ECEFE0'
        );
    }

}
