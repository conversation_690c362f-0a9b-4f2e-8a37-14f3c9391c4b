<script type="text/html" id="adates_season_week_calendar_template">

  <div class="wizard-message">

    <h3 class="wizard-headline">{{ i18n.templateHeadline }}</h3>

    <p>{{ i18n.templateDescription }}</p>

    <form class="smart-form" id="wizard-template-form">

      <fieldset>

        <div class="row">

          <section class="col-12">

            <label class="select">
              {{ Oform.control('season_id', {
                class: 'select2',
                id: 'season_id',
                label: __('adates.season_id'),
                value: data.curr_season_id,
                options: data.sseasons
              }) | raw }}
            </label>

          </section>

        </div>

        <div class="row">

          <section class="col-12">

            <label class="select">
              {{ Oform.control('planninglevels', {
                'class': 'select2',
                'data-listlength': 4,
                'label': __('planning levels'),
                'multiple': 'multiple',
                'value': 1,
                'options': {
                  1: 1,
                  2: 2,
                  3: 3,
                  4: 4
                }
              }) | raw }}
            </label>

          </section>

        </div>

      </fieldset>

      <fieldset>

        <div class="row">
          <section class="col-12">

            <label for for="fdow" class="select" style="float:left;">
              {{ i18n.first_day_of_week }}
              <select name="fdow" id="fdow">
                <option value="7" {{ data.fdow == 7 ? 'selected' : '' }}>{{ __('sunday') }}</option>
                <option value="1" {{ data.fdow == 1 ? 'selected' : '' }}>{{ __('monday') }}</option>
              </select>
            </label>

          </section>
        </div>

        <div class="row">
          <br>
        </div>

        <div class="row">
          <section class="col-12">

            <div class="form-group">
              <label class="">
                <input type="checkbox" name="print_periods" id="print_periods" value="1" class="cb" checked> {{ i18n.print_periods }}
              </label>
            </div>

          </section>
        </div>

      </fieldset>
    </form>
  </div>
</script>
