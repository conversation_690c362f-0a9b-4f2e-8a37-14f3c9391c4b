<?xml version="1.0" encoding="UTF-8" ?>
<maintables xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:noNamespaceSchemaLocation="../../../plugins/OpasOnline/config/oo_export.xsd">

  <!-- saddresses -->
  <!--
  20241206
  Hi <PERSON>, könntest Du bitte dringend die upload controls von ON nach OOL von OSR so anpassen, dass man alle Spielzeiten ab 23/24 sieht
im Moment sieht man die Daten ab 25/26 nicht und die werden Freitag Abend ringend gebraucht....
falls es irgend geht
'20/21', '21/22', '22/23', '23/24', '24/25'
'23/24', '24/25', '25/26'
  -->
  <Entry>
    <table>saddresses</table>
    <where>
      <![CDATA[
        saddresses.id IN (
            SELECT adates.location_id
            FROM adates
            LEFT JOIN sseasons ON adates.season_id = sseasons.id
            WHERE adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
        )
        OR
          saddresses.id IN (
            SELECT adates.conductor_id
            FROM adates
            LEFT JOIN sseasons ON adates.season_id = sseasons.id
            WHERE adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
        )
        OR
          saddresses.id IN (
            SELECT adates.orchestra_id
            FROM adates
            LEFT JOIN sseasons ON adates.season_id = sseasons.id
            WHERE adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
        )
        OR
          saddresses.id IN (
            SELECT adatework_soloists.artist_id FROM adates
            LEFT JOIN adate_works ON adates.id = adate_works.date_id
            LEFT JOIN adatework_soloists ON adate_works.id = adatework_soloists.datework_id
            LEFT JOIN sseasons ON adates.season_id = sseasons.id
            WHERE adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
        )
        OR
          saddresses.id IN (
            SELECT adate_persons.address_id FROM adates
            LEFT JOIN adate_persons ON adate_persons.date_id = adates.id
            LEFT JOIN sseasons ON adates.season_id = sseasons.id
            WHERE adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
        )
        OR
          saddresses.id IN (
            SELECT saddress_addressgroups.address_id FROM saddress_addressgroups
            LEFT JOIN saddressgroups ON saddress_addressgroups.addressgroup_id = saddressgroups.id
            LEFT JOIN saddresssysgroups ON saddressgroups.sysgroup_id = saddresssysgroups.id
            WHERE saddresssysgroups.code IN ('STA', 'SUB')
        )
      ]]>
    </where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend>
      <![CDATA[
        UPDATE saddresses
        SET street = '', pobox = '', place = '', state = '', zipcode = '', country_id = 0
        WHERE saddresses.id NOT IN
        (
        SELECT id FROM
          (
            SELECT DISTINCT saddresses.id FROM saddresses
            INNER JOIN saddress_addressgroups ON saddresses.id = saddress_addressgroups.address_id
            INNER JOIN saddressgroups ON saddress_addressgroups.addressgroup_id = saddressgroups.id
            INNER JOIN saddresssysgroups ON saddressgroups.sysgroup_id = saddresssysgroups.id
            WHERE saddresssysgroups.code IN ('LOC', 'STA')
          ) AS tmptable
        )
      ]]>
    </sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>category_id</field>
      <field>addressmaster_id</field>
      <field>code</field>
      <field>mark</field>
      <field>order_1</field>
      <field>order_2</field>
      <field>seat</field>
      <field>salutation_id</field>
      <field>title_id</field>
      <field>private_salutation</field>
      <field>name1</field>
      <field>name2</field>
      <field>name3</field>
      <field>name4</field>
      <field>name5</field>
      <field>co_name</field>
      <field>name_rtf</field>
      <field>street</field>
      <field>house_number</field>
      <field>pobox</field>
      <field>state</field>
      <field>zipcode</field>
      <field>zipcode_pobox</field>
      <field>place</field>
      <field>country_id</field>
      <field>address</field>
      <field>l_activated</field>
      <field>notes</field>
      <field>opasonline_user</field>
      <field>opasonline_password</field>
      <field>opasonline_group</field>
      <field>text_1</field>
      <field>text_2</field>
      <field>text_3</field>
      <field>text_4</field>
      <field>text_5</field>
      <field>text_6</field>
      <field>text_7</field>
      <field>text_8</field>
      <field>text_9</field>
      <field>text_10</field>
      <field>number_1</field>
      <field>number_2</field>
      <field>number_3</field>
      <field>number_4</field>
      <field>number_5</field>
      <field>date_1</field>
      <field>date_2</field>
      <field>date_3</field>
      <field>date_4</field>
      <field>date_5</field>
      <field>memo_1</field>
      <field>memo_2</field>
      <field>l_logic_1</field>
      <field>l_logic_2</field>
      <field>l_logic_3</field>
      <field>address_1_id</field>
      <field>address_2_id</field>
      <field>address_3_id</field>
      <field>time_1</field>
      <field>time_2</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
    <relations>
      <relation table="saddress_addressgroups" source_id="id" target_id="address_id">
        <fields>
          <field>id</field>
          <field>address_id</field>
          <field>addressgroup_id</field>
          <field>l_main</field>
          <field>date1</field>
          <field>date2</field>
          <field>text</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>insertuser</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="saddress_instruments" source_id="id" target_id="address_id">
        <fields>
          <field>id</field>
          <field>address_id</field>
          <field>instrument_id</field>
          <field>l_main</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>updateuser</field>
          <field>insertuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="aaddress_absences" source_id="id" target_id="address_id">
        <fields>
          <field>id</field>
          <field>address_id</field>
          <field>dutytype_id</field>
          <field>date_first</field>
          <field>date_last</field>
          <field>start_</field>
          <field>end_</field>
          <field>season_id</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>insertuser</field>
          <field>updatedate</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="saddress_duties" source_id="id" target_id="address_id">
        <fields>
          <field>id</field>
          <field>address_id</field>
          <field>effective_date</field>
          <field>text</field>
          <field>days</field>
          <field>duties</field>
          <field>hours</field>
          <field>dayspercent</field>
          <field>dutiespercent</field>
          <field>hourspercent</field>
          <field>daysfree</field>
          <field>dutiesfree</field>
          <field>hoursfree</field>
          <field>daysfreepercent</field>
          <field>dutiesfreepercent</field>
          <field>hoursfreepercent</field>
          <field>days2</field>
          <field>duties2</field>
          <field>hours2</field>
          <field>dayspercent2</field>
          <field>dutiespercent2</field>
          <field>hourspercent2</field>
          <field>daysfree2</field>
          <field>dutiesfree2</field>
          <field>hoursfree2</field>
          <field>daysfreepercent2</field>
          <field>dutiesfreepercent2</field>
          <field>hoursfreepercent2</field>
          <field>insertdate</field>
          <field>insertuser</field>
          <field>updatedate</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="saddress_persdata" source_id="id" target_id="address_id">
        <fields>
          <field>id</field>
          <field>address_id</field>
          <field>sex</field>
          <field>datein</field>
          <field>dateout</field>
        </fields>
      </relation>
    </relations>
  </Entry>

  <!-- saddress_numbers -->
  <Entry>
    <table>saddress_numbers</table>
    <where>
      <![CDATA[
        saddress_numbers.address_id IN (
        SELECT saddress_addressgroups.address_id
          FROM saddress_addressgroups
          LEFT JOIN saddressgroups ON saddress_addressgroups.addressgroup_id = saddressgroups.id
          LEFT JOIN saddresssysgroups ON saddressgroups.sysgroup_id = saddresssysgroups.id
          WHERE saddresssysgroups.code IN ('STA', 'SUB')
        )
      ]]>
    </where>
    <where_join>
      <![CDATA[
        LEFT JOIN snumbertypes ON saddress_numbers.numbertype_id = snumbertypes.id
      ]]>
    </where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>address_id</field>
      <field>number_order</field>
      <field>number_</field>
      <field>text</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>numbertype_id</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <Entry>
    <table>adates</table>
    <where>
      <![CDATA[
        adates.planninglevel IN (1, 2, 3, 4) AND sseasons.code IN ('23/24', '24/25', '25/26')
      ]]>
    </where>
    <where_join>
      <![CDATA[
        LEFT JOIN sseasons ON adates.season_id = sseasons.id
      ]]>
    </where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <export_ids>1</export_ids>
    <fields>
      <field>id</field>
      <field>planninglevel</field>
      <field>sysgroup_id</field>
      <field>status_id</field>
      <field>season_id</field>
      <field>code</field>
      <field>date_</field>
      <field>weekday</field>
      <field>start_</field>
      <field>end_</field>
      <field>week</field>
      <field>duration</field>
      <field>month</field>
      <field>year</field>
      <field>pweek</field>
      <field>duties</field>
      <field>duties2</field>
      <field>start2_</field>
      <field>end2_</field>
      <field>start3_</field>
      <field>end3_</field>
      <field>duration2</field>
      <field>duration3</field>
      <field>project_id</field>
      <field>eventtype_id</field>
      <field>l_defaultaccounting</field>
      <field>location_id</field>
      <field>conductor_id</field>
      <field>orchestra_id</field>
      <field>text</field>
      <field>abbreviation</field>
      <field>programno</field>
      <field>programtitle</field>
      <field>block1</field>
      <field>block3</field>
      <field>address_id</field>
      <field>l_duties</field>
      <field>notes</field>
      <field>l_print_details</field>
      <field>l_save2relateddates</field>
      <field>tour_id</field>
      <field>dress_id</field>
      <field>expensetype_id</field>
      <field>accitem_id</field>
      <field>l_lock</field>
      <field>ticket_key</field>
      <field>ticket_url</field>
      <field>ticket_system</field>
      <field>l_ticket_online</field>
      <field>text_1</field>
      <field>text_2</field>
      <field>text_3</field>
      <field>text_4</field>
      <field>text_5</field>
      <field>text_6</field>
      <field>text_7</field>
      <field>text_8</field>
      <field>text_9</field>
      <field>text_10</field>
      <field>number_1</field>
      <field>number_2</field>
      <field>number_3</field>
      <field>number_4</field>
      <field>number_5</field>
      <field>date_1</field>
      <field>date_2</field>
      <field>date_3</field>
      <field>date_4</field>
      <field>date_5</field>
      <field>memo_1</field>
      <field>memo_2</field>
      <field>l_logic_1</field>
      <field>l_logic_2</field>
      <field>l_logic_3</field>
      <field>address_1_id</field>
      <field>address_2_id</field>
      <field>address_3_id</field>
      <field>time_1</field>
      <field>time_2</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
    <relations>
      <relation table="adate_activities" source_id="id" target_id="date_id">
        <fields>
          <field>id</field>
          <field>date_id</field>
          <field>eventtype_id</field>
          <field>l_defaultaccounting</field>
          <field>activity_order</field>
          <field>text</field>
          <field>start_</field>
          <field>end_</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>insertuser</field>
          <field>updatedate</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="adate_persons" source_id="id" target_id="date_id">
        <fields>
          <field>id</field>
          <field>date_id</field>
          <field>status_id</field>
          <field>person_order</field>
          <field>address_id</field>
          <field>addressgroup_id</field>
          <field>function_id</field>
          <field>instrument_id</field>
          <field>l_logic</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>insertuser</field>
          <field>updatedate</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="adate_works" source_id="id" target_id="date_id">
        <fields>
          <field>id</field>
          <field>date_id</field>
          <field>status_id</field>
          <field>conductor_id</field>
          <field>work_id</field>
          <field>work_order</field>
          <field>title2</field>
          <field>title3</field>
          <field>duration</field>
          <field>year</field>
          <field>arrangement</field>
          <field>l_encore</field>
          <field>premiere_id</field>
          <field>l_logic1</field>
          <field>l_fixedinstrumentation</field>
          <field>flute_text</field>
          <field>flute</field>
          <field>oboe_text</field>
          <field>oboe</field>
          <field>clarinet_text</field>
          <field>clarinet</field>
          <field>bassoon_text</field>
          <field>bassoon</field>
          <field>horn_text</field>
          <field>horn</field>
          <field>trumpet_text</field>
          <field>trumpet</field>
          <field>trombone_text</field>
          <field>trombone</field>
          <field>tuba_text</field>
          <field>tuba</field>
          <field>timpani_text</field>
          <field>timpani</field>
          <field>harp_text</field>
          <field>harp</field>
          <field>violin1</field>
          <field>violin2</field>
          <field>viola</field>
          <field>cello</field>
          <field>bass</field>
          <field>strings_text</field>
          <field>percussion_text</field>
          <field>percussion</field>
          <field>keyboard_text</field>
          <field>keyboard</field>
          <field>extra_text</field>
          <field>extra</field>
          <field>vocals</field>
          <field>vocals_text</field>
          <field>details</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>insertuser</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
        <relations>
          <relation table="adatework_soloists" source_id="id" target_id="datework_id">
            <fields>
              <field>id</field>
              <field>datework_id</field>
              <field>status_id</field>
              <field>artist_order</field>
              <field>artist_order2</field>
              <field>artist_id</field>
              <field>instrument_id</field>
              <field>l_logic1</field>
              <field>notes</field>
              <field>insertdate</field>
              <field>updatedate</field>
              <field>insertuser</field>
              <field>updateuser</field>
              <field>sysclient_id</field>
              <field>ts_update</field>
              <field>tag1</field>
              <field>tag2</field>
              <field>tag3</field>
              <field>l_activated</field>
              <field>danielskey</field>
              <field>universalkey</field>
            </fields>
          </relation>
        </relations>
      </relation>
    </relations>
  </Entry>

  <!-- adaysperiods -->
  <Entry>
    <table>adaysperiods</table>
    <where>
      <![CDATA[
        YEAR(adaysperiods.date1) IN (SELECT DISTINCT YEAR(adays.date_) FROM `adays` LEFT JOIN sseasons ON adays.season_id = sseasons.id WHERE sseasons.code IN('23/24', '24/25', '25/26')) AND adaysperiods.planninglevel IN (1, 2, 3, 4)
      ]]>
    </where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>planninglevel</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>text</field>
      <field>date1</field>
      <field>date2</field>
      <field>project_id</field>
      <field>project_text</field>
      <field>address_id</field>
      <field>address_text</field>
      <field>conductor_id</field>
      <field>conductor_text</field>
      <field>program_text</field>
      <field>soloist_id</field>
      <field>soloist_text</field>
      <field>instrument_id</field>
      <field>instrument_text</field>
      <field>hours</field>
      <field>duties</field>
      <field>days</field>
      <field>notes</field>
      <field>colour_id</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- adays -->
  <Entry>
    <table>adays</table>
    <where>
      <![CDATA[
        adays.year IN (SELECT DISTINCT YEAR(adays.date_) FROM `adays` LEFT JOIN sseasons ON adays.season_id = sseasons.id WHERE sseasons.code IN('23/24', '24/25', '25/26')) AND adays.planninglevel IN (1)
      ]]>
    </where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>planninglevel</field>
      <field>season_id</field>
      <field>date_</field>
      <field>week</field>
      <field>pweek</field>
      <field>weekday</field>
      <field>month</field>
      <field>year</field>
      <field>block1</field>
      <field>block3</field>
      <field>duties</field>
      <field>status_id</field>
      <field>holyday_id</field>
      <field>text_</field>
      <field>notes</field>
      <field>colour_id</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- scomposers -->
  <Entry>
    <table>scomposers</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>lastname</field>
      <field>firstname</field>
      <field>name2</field>
      <field>name3</field>
      <field>initials</field>
      <field>name_rtf</field>
      <field>address_id</field>
      <field>sex</field>
      <field>birthday</field>
      <field>birthmonth</field>
      <field>birthyear</field>
      <field>birthplace</field>
      <field>birthstate</field>
      <field>deathday</field>
      <field>birthcountry_id</field>
      <field>deathmonth</field>
      <field>deathyear</field>
      <field>deathplace</field>
      <field>deathstate</field>
      <field>deathcountry_id</field>
      <field>notes</field>
      <field>synonyms</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sprojects -->
  <Entry>
    <table>sprojects</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>type_id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>accountno</field>
      <field>colour_id</field>
      <field>l_duties</field>
      <field>l_coproduction</field>
      <field>l_save2relateddates</field>
      <field>l_lock</field>
      <field>project_order</field>
      <field>date_</field>
      <field>notes</field>
      <field>l_activated</field>
      <field>season_id</field>
      <field>costcenter_id</field>
      <field>text_1</field>
      <field>text_2</field>
      <field>text_3</field>
      <field>text_4</field>
      <field>text_5</field>
      <field>text_6</field>
      <field>text_7</field>
      <field>text_8</field>
      <field>text_9</field>
      <field>text_10</field>
      <field>memo_1</field>
      <field>memo_2</field>
      <field>address_1_id</field>
      <field>address_2_id</field>
      <field>address_3_id</field>
      <field>number_1</field>
      <field>number_2</field>
      <field>number_3</field>
      <field>number_4</field>
      <field>number_5</field>
      <field>date_1</field>
      <field>date_2</field>
      <field>date_3</field>
      <field>date_4</field>
      <field>date_5</field>
      <field>time_1</field>
      <field>time_2</field>
      <field>l_logic_1</field>
      <field>l_logic_2</field>
      <field>l_logic_3</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sseasons -->
  <Entry>
    <table>sseasons</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>datestart</field>
      <field>dateend</field>
      <field>datestart2</field>
      <field>dateend2</field>
      <field>period</field>
      <field>days</field>
      <field>duties</field>
      <field>hours</field>
      <field>dutmaxday</field>
      <field>hrsmaxday</field>
      <field>daymaxweek</field>
      <field>dutmaxweek</field>
      <field>hrsmaxweek</field>
      <field>daymaxblock</field>
      <field>dutmaxblock</field>
      <field>hrsmaxblock</field>
      <field>daymaxblock3</field>
      <field>dutmaxblock3</field>
      <field>hrsmaxblock3</field>
      <field>daymaxseason</field>
      <field>dutmaxseason</field>
      <field>hrsmaxseason</field>
      <field>number1</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sworks -->
  <Entry>
    <table>sworks</table>
    <where><![CDATA[
      sworks.id IN (
        SELECT DISTINCT adate_works.work_id FROM adate_works
        INNER JOIN sworks ON sworks.id = adate_works.work_id
        INNER JOIN adates ON adate_works.date_id = adates.id
        INNER JOIN sseasons ON adates.season_id = sseasons.id
        WHERE sseasons.code IN ('23/24', '24/25', '25/26') AND adates.planninglevel IN (1, 2, 3, 4)
      )
      ]]>
    </where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>composer_id</field>
      <field>l_regular</field>
      <field>l_intermission</field>
      <field>genre_id</field>
      <field>style_id</field>
      <field>workoriginal_id</field>
      <field>title1</field>
      <field>title2</field>
      <field>title3</field>
      <field>title_rtf</field>
      <field>catalog</field>
      <field>key_</field>
      <field>mark</field>
      <field>compyear</field>
      <field>compyear2</field>
      <field>compyearstatus</field>
      <field>duration</field>
      <field>duration2</field>
      <field>arrangement</field>
      <field>sourcetext</field>
      <field>commission</field>
      <field>l_activated</field>
      <field>l_lock</field>
      <field>synonyms</field>
      <field>flute</field>
      <field>flute_text</field>
      <field>flute_2</field>
      <field>flute_text_2</field>
      <field>oboe</field>
      <field>oboe_text</field>
      <field>oboe_2</field>
      <field>oboe_text_2</field>
      <field>clarinet</field>
      <field>clarinet_text</field>
      <field>clarinet_2</field>
      <field>clarinet_text_2</field>
      <field>bassoon</field>
      <field>bassoon_text</field>
      <field>bassoon_2</field>
      <field>bassoon_text_2</field>
      <field>horn</field>
      <field>horn_text</field>
      <field>horn_2</field>
      <field>horn_text_2</field>
      <field>trumpet</field>
      <field>trumpet_text</field>
      <field>trumpet_2</field>
      <field>trumpet_text_2</field>
      <field>trombone</field>
      <field>trombone_text</field>
      <field>trombone_2</field>
      <field>trombone_text_2</field>
      <field>tuba</field>
      <field>tuba_text</field>
      <field>tuba_2</field>
      <field>tuba_text_2</field>
      <field>timpani</field>
      <field>timpani_text</field>
      <field>timpani_2</field>
      <field>timpani_text_2</field>
      <field>harp</field>
      <field>harp_text</field>
      <field>harp_2</field>
      <field>harp_text_2</field>
      <field>violin1</field>
      <field>violin1_2</field>
      <field>violin2</field>
      <field>violin2_2</field>
      <field>viola</field>
      <field>viola_2</field>
      <field>cello</field>
      <field>cello_2</field>
      <field>bass</field>
      <field>bass_2</field>
      <field>strings_text</field>
      <field>strings_text_2</field>
      <field>percussion</field>
      <field>percussion_text</field>
      <field>percussion_2</field>
      <field>percussion_text_2</field>
      <field>keyboard</field>
      <field>keyboard_text</field>
      <field>keyboard_2</field>
      <field>keyboard_text_2</field>
      <field>extra</field>
      <field>extra_text</field>
      <field>extra_2</field>
      <field>extra_text_2</field>
      <field>vocals</field>
      <field>vocals_text</field>
      <field>vocals_2</field>
      <field>vocals_text_2</field>
      <field>details</field>
      <field>details_2</field>
      <field>notes</field>
      <field>instrumentationno</field>
      <field>sourcecode</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
    <relations>
      <relation table="swork_premieres" source_id="id" target_id="work_id">
        <fields>
          <field>id</field>
          <field>work_id</field>
          <field>workpremiere_id</field>
          <field>orchestra_id</field>
          <field>conductor_id</field>
          <field>location_id</field>
          <field>premiereday</field>
          <field>premieremonth</field>
          <field>premiereyear</field>
          <field>notes</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>insertuser</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
      <relation table="swork_movements" source_id="id" target_id="work_id">
        <fields>
          <field>id</field>
          <field>work_id</field>
          <field>movement_order</field>
          <field>name</field>
          <field>name2</field>
          <field>duration</field>
          <field>sourcework_id</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>insertuser</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
    </relations>
  </Entry>

  <!-- seventtypes -->
  <Entry>
    <table>seventtypes</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>abbreviation</field>
      <field>start_</field>
      <field>end_</field>
      <field>duties</field>
      <field>duration</field>
      <field>l_performance</field>
      <field>l_duties</field>
      <field>writetextfield</field>
      <field>accountno</field>
      <field>accitem_id</field>
      <field>expensetype_id</field>
      <field>group_id</field>
      <field>dress_id</field>
      <field>colour_id</field>
      <field>l_dates</field>
      <field>l_days</field>
      <field>l_tours</field>
      <field>l_addactivities</field>
      <field>l_save2relateddates</field>
      <field>l_activated</field>
      <field>eventtype_order</field>
      <field>notes</field>
      <field>text_1</field>
      <field>l_text_1_s</field>
      <field>text_2</field>
      <field>l_text_2_s</field>
      <field>text_3</field>
      <field>l_text_3_s</field>
      <field>text_4</field>
      <field>l_text_4_s</field>
      <field>text_5</field>
      <field>l_text_5_s</field>
      <field>text_6</field>
      <field>l_text_6_s</field>
      <field>text_7</field>
      <field>l_text_7_s</field>
      <field>text_8</field>
      <field>l_text_8_s</field>
      <field>text_9</field>
      <field>l_text_9_s</field>
      <field>text_10</field>
      <field>l_text_10_s</field>
      <field>number_1</field>
      <field>l_number_1_s</field>
      <field>number_2</field>
      <field>l_number_2_s</field>
      <field>number_3</field>
      <field>l_number_3_s</field>
      <field>number_4</field>
      <field>l_number_4_s</field>
      <field>number_5</field>
      <field>l_number_5_s</field>
      <field>date_1</field>
      <field>l_date_1_s</field>
      <field>date_2</field>
      <field>l_date_2_s</field>
      <field>date_3</field>
      <field>l_date_3_s</field>
      <field>date_4</field>
      <field>l_date_4_s</field>
      <field>date_5</field>
      <field>l_date_5_s</field>
      <field>memo_1</field>
      <field>l_memo_1_s</field>
      <field>memo_2</field>
      <field>l_memo_2_s</field>
      <field>logic_1</field>
      <field>l_logic_1_s</field>
      <field>logic_2</field>
      <field>l_logic_2_s</field>
      <field>logic_3</field>
      <field>l_logic_3_s</field>
      <field>address_1</field>
      <field>l_address_1_s</field>
      <field>address_2</field>
      <field>l_address_2_s</field>
      <field>address_3</field>
      <field>l_address_3_s</field>
      <field>time_1</field>
      <field>l_time_1_s</field>
      <field>time_2</field>
      <field>l_time_2_s</field>
      <field>l_text_m_1_s</field>
      <field>text_m_1</field>
      <field>l_text_m_2_s</field>
      <field>text_m_2</field>
      <field>l_text_m_3_s</field>
      <field>text_m_3</field>
      <field>l_text_m_4_s</field>
      <field>text_m_4</field>
      <field>l_text_m_5_s</field>
      <field>text_m_5</field>
      <field>l_text_m_6_s</field>
      <field>text_m_6</field>
      <field>l_text_m_7_s</field>
      <field>text_m_7</field>
      <field>l_text_m_8_s</field>
      <field>text_m_8</field>
      <field>l_text_m_9_s</field>
      <field>text_m_9</field>
      <field>l_text_m_10_s</field>
      <field>text_m_10</field>
      <field>l_number_m_1_s</field>
      <field>number_m_1</field>
      <field>l_number_m_2_s</field>
      <field>number_m_2</field>
      <field>l_number_m_3_s</field>
      <field>number_m_3</field>
      <field>l_number_m_4_s</field>
      <field>number_m_4</field>
      <field>l_number_m_5_s</field>
      <field>number_m_5</field>
      <field>l_date_m_1_s</field>
      <field>date_m_1</field>
      <field>l_date_m_2_s</field>
      <field>date_m_2</field>
      <field>l_date_m_3_s</field>
      <field>date_m_3</field>
      <field>l_date_m_4_s</field>
      <field>date_m_4</field>
      <field>l_date_m_5_s</field>
      <field>date_m_5</field>
      <field>l_memo_m_1_s</field>
      <field>memo_m_1</field>
      <field>l_memo_m_2_s</field>
      <field>memo_m_2</field>
      <field>l_logic_m_1_s</field>
      <field>logic_m_1</field>
      <field>l_logic_m_2_s</field>
      <field>logic_m_2</field>
      <field>l_logic_m_3_s</field>
      <field>logic_m_3</field>
      <field>l_address_m_1_s</field>
      <field>address_m_1</field>
      <field>l_address_m_2_s</field>
      <field>address_m_2</field>
      <field>l_address_m_3_s</field>
      <field>address_m_3</field>
      <field>l_time_m_1_s</field>
      <field>time_m_1</field>
      <field>l_time_m_2_s</field>
      <field>time_m_2</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>danielskey</field>
      <field>universalkey</field>
      <field>adddataconfiguration</field>
      <field>adddataconfiguration_m</field>
    </fields>
    <relations>
      <relation table="seventtypegroups" source_id="group_id" target_id="id">
        <fields>
          <field>id</field>
          <field>code</field>
          <field>name</field>
          <field>name2</field>
          <field>colour_id</field>
          <field>insertdate</field>
          <field>updatedate</field>
          <field>insertuser</field>
          <field>updateuser</field>
          <field>sysclient_id</field>
          <field>ts_update</field>
          <field>tag1</field>
          <field>tag2</field>
          <field>tag3</field>
          <field>l_activated</field>
          <field>danielskey</field>
          <field>universalkey</field>
        </fields>
      </relation>
    </relations>
  </Entry>

  <!-- saddressgroups -->
  <Entry>
    <table>saddressgroups</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>sysgroup_id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>l_contract</field>
      <field>colour_id</field>
      <field>text_1</field>
      <field>l_text_1_s</field>
      <field>text_2</field>
      <field>l_text_2_s</field>
      <field>text_3</field>
      <field>l_text_3_s</field>
      <field>text_4</field>
      <field>l_text_4_s</field>
      <field>text_5</field>
      <field>l_text_5_s</field>
      <field>text_6</field>
      <field>l_text_6_s</field>
      <field>text_7</field>
      <field>l_text_7_s</field>
      <field>text_8</field>
      <field>l_text_8_s</field>
      <field>text_9</field>
      <field>l_text_9_s</field>
      <field>text_10</field>
      <field>l_text_10_s</field>
      <field>number_1</field>
      <field>l_number_1_s</field>
      <field>number_2</field>
      <field>l_number_2_s</field>
      <field>number_3</field>
      <field>l_number_3_s</field>
      <field>number_4</field>
      <field>l_number_4_s</field>
      <field>number_5</field>
      <field>l_number_5_s</field>
      <field>date_1</field>
      <field>l_date_1_s</field>
      <field>date_2</field>
      <field>l_date_2_s</field>
      <field>date_3</field>
      <field>l_date_3_s</field>
      <field>date_4</field>
      <field>l_date_4_s</field>
      <field>date_5</field>
      <field>l_date_5_s</field>
      <field>memo_1</field>
      <field>l_memo_1_s</field>
      <field>memo_2</field>
      <field>l_memo_2_s</field>
      <field>logic_1</field>
      <field>l_logic_1_s</field>
      <field>logic_2</field>
      <field>l_logic_2_s</field>
      <field>logic_3</field>
      <field>l_logic_3_s</field>
      <field>address_1</field>
      <field>l_address_1_s</field>
      <field>address_2</field>
      <field>l_address_2_s</field>
      <field>address_3</field>
      <field>l_address_3_s</field>
      <field>time_1</field>
      <field>l_time_1_s</field>
      <field>time_2</field>
      <field>l_time_2_s</field>
      <field>adddataconfiguration</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- saddresssysgroups -->
  <Entry>
    <table>saddresssysgroups</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>address_type</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- saddressfunctions -->
  <Entry>
    <table>saddressfunctions</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>accitem_id</field>
      <field>expensetype_id</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sinstrinstruments -->
  <Entry>
    <table>sinstrinstruments</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>section_id</field>
      <field>job_id</field>
      <field>instrument_order</field>
      <field>l_doubling</field>
      <field>accitem_id</field>
      <field>expensetype_id</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sinstrumentgroups -->
  <Entry>
    <table>sinstrumentgroups</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>text_1</field>
      <field>l_text_1_s</field>
      <field>text_2</field>
      <field>l_text_2_s</field>
      <field>text_3</field>
      <field>l_text_3_s</field>
      <field>text_4</field>
      <field>l_text_4_s</field>
      <field>text_5</field>
      <field>l_text_5_s</field>
      <field>text_6</field>
      <field>l_text_6_s</field>
      <field>text_7</field>
      <field>l_text_7_s</field>
      <field>text_8</field>
      <field>l_text_8_s</field>
      <field>text_9</field>
      <field>l_text_9_s</field>
      <field>text_10</field>
      <field>l_text_10_s</field>
      <field>memo_1</field>
      <field>l_memo_1_s</field>
      <field>memo_2</field>
      <field>l_memo_2_s</field>
      <field>address_1</field>
      <field>l_address_1_s</field>
      <field>address_2</field>
      <field>l_address_2_s</field>
      <field>address_3</field>
      <field>l_address_3_s</field>
      <field>number_1</field>
      <field>l_number_1_s</field>
      <field>number_2</field>
      <field>l_number_2_s</field>
      <field>number_3</field>
      <field>l_number_3_s</field>
      <field>number_4</field>
      <field>l_number_4_s</field>
      <field>number_5</field>
      <field>l_number_5_s</field>
      <field>date_1</field>
      <field>l_date_1_s</field>
      <field>date_2</field>
      <field>l_date_2_s</field>
      <field>date_3</field>
      <field>l_date_3_s</field>
      <field>date_4</field>
      <field>l_date_4_s</field>
      <field>date_5</field>
      <field>l_date_5_s</field>
      <field>time_1</field>
      <field>l_time_1_s</field>
      <field>time_2</field>
      <field>l_time_2_s</field>
      <field>logic_1</field>
      <field>l_logic_1_s</field>
      <field>logic_2</field>
      <field>l_logic_2_s</field>
      <field>logic_3</field>
      <field>l_logic_3_s</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
      <field>adddataconfiguration</field>
    </fields>
  </Entry>

  <!-- scountries -->
  <Entry>
    <table>scountries</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>nationality</field>
      <field>currency_id</field>
      <field>l_ec</field>
      <field>language_id</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sholydays -->
  <Entry>
    <table>sholydays</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>day</field>
      <field>month</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sworkstyles -->
  <Entry>
    <table>sworkstyles</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sworkpremieres -->
  <Entry>
    <table>sworkpremieres</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sworkgenres -->
  <Entry>
    <table>sworkgenres</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- snumbertypes -->
  <Entry>
    <table>snumbertypes</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>l_phone</field>
      <field>l_fax</field>
      <field>l_mobile</field>
      <field>l_email</field>
      <field>l_url</field>
      <field>l_work</field>
      <field>l_home</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sinstrsyssections -->
  <Entry>
    <table>sinstrsyssections</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>section_order</field>
      <field>colour_id</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sinstrsections -->
  <Entry>
    <table>sinstrsections</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>sysgroup_id</field>
      <field>section_order</field>
      <field>syssection_id</field>
      <field>colour_id</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sdutytypes -->
  <Entry>
    <table>sdutytypes</table>
    <where>id>0</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>l_present</field>
      <field>percentpresent</field>
      <field>percentfree</field>
      <field>percentcount</field>
      <field>percentperdiem</field>
      <field>l_standby</field>
      <field>l_netcompensation</field>
      <field>colour_id</field>
      <field>accitem_id</field>
      <field>expensetype_id</field>
      <field>notes</field>
      <field>dutytype_order</field>
      <field>l_flag_1</field>
      <field>l_flag_2</field>
      <field>l_flag_3</field>
      <field>l_flag_4</field>
      <field>l_flag_5</field>
      <field>l_flag_6</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sdresses -->
  <Entry>
    <table>sdresses</table>
    <where>id</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>men</field>
      <field>women</field>
      <field>insertdate</field>
      <field>updatedate</field>
      <field>insertuser</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- sdatestatuses -->
  <Entry>
    <table>sdatestatuses</table>
    <where>id</where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>colour_id</field>
      <field>notes</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

  <!-- stodostatus -->
  <Entry>
    <table>stodostatus</table>
    <where></where>
    <where_join></where_join>
    <sqlonstart></sqlonstart>
    <sqlonend></sqlonend>
    <clear_tbl>1</clear_tbl>
    <fields>
      <field>id</field>
      <field>code</field>
      <field>name</field>
      <field>name2</field>
      <field>colour_id</field>
      <field>l_todolist</field>
      <field>insertdate</field>
      <field>insertuser</field>
      <field>updatedate</field>
      <field>updateuser</field>
      <field>sysclient_id</field>
      <field>ts_update</field>
      <field>tag1</field>
      <field>tag2</field>
      <field>tag3</field>
      <field>l_activated</field>
      <field>danielskey</field>
      <field>universalkey</field>
    </fields>
  </Entry>

</maintables>
