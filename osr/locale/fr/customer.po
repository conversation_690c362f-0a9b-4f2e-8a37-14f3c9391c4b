# Currencies
msgid "currency_symbol"
msgstr "CHF"

# Expense Combinations
msgid "acalculations"
msgstr "Calculs"

# Substitute Contract
msgid "acontract_accountings.contractsubs_id"
msgstr "Contrat de supplémentaire"

# Substitute Contracts
msgid "acontractsubstitutes"
msgstr "Contrats de supplémentaires"

# Expense Type
msgid "adate_accountingamounts.expensetype_id"
msgstr "Type de Frais"

# Expense Type
msgid "adate_accountings.expensetype_id"
msgstr "Type de Frais"

# Status
msgid "adate_persons.status_id"
msgstr "État"

# Status
msgid "adate_resources.status_id"
msgstr "État"

# Instrument
msgid "adate_soloists.instrument_id"
msgstr "Voix"

# Extra Text
msgid "adate_works.extra_text"
msgstr "Instr. spécialisé texte"

	# Encore
msgid "adate_works.l_encore"
msgstr "Encore"

# Notes
msgid "adate_works.notes"
msgstr "Notes non publ."

# Title
msgid "adate_works.title1"
msgstr "Titre OSR"

# Print Title
msgid "adate_works.title2"
msgstr "Titre Daniels"

msgid "adates.l_defaultaccounting"
msgstr "Décompte stand."

# Print Details
msgid "adates.l_print_details"
msgstr "Sous toutes réserves. Période non éditée."

# Ticket / Web Export
msgid "adates.l_ticket_online"
msgstr "Période éditée."

# Notes
msgid "adates.notes"
msgstr "Info/sce. (publ.)"

# Text
msgid "adates.text"
msgstr "Complément activité"

# Ticket / Web Export
msgid "adates_archive.l_ticket_online"
msgstr "Période éditée."

# Schedule
msgid "adates_schedule"
msgstr "Itinéraires"

# Dates/add'l. Sections
msgid "adatework_addsections"
msgstr "Dates/ajouter pupitres"

# Date Program
msgid "adatework_addsections.datework_id"
msgstr "Date Programme"

# Dates Archive/add'l. Sections
msgid "adatework_addsections_archive"
msgstr "Dates archive/ajouter pupitres"

# Title 2
msgid "adatework_libraries.title2"
msgstr "Titre Daniels"

 # Hours
 msgid "adaysperiods.hours"
 msgstr "Horaires"

 # Address Group
 msgid "aduties.addressgroup_id"
 msgstr "Groupe d'Adresse"

 # Stand/Chair 2
 msgid "aduties.seat_2"
 msgstr "Stand/Chaise 2"

 # Service Archive
 msgid "aduties_archive"
 msgstr "Garder données"

 # Stand/Chair 2
 msgid "aduty_works.seat_2"
 msgstr "Stand/Chaise 2"

 # Current Location
 msgid "alibr_scores.current_location"
 msgstr "Emplacement actuelle"

 # Storage Location
 msgid "alibr_scores.storage_location"
 msgstr "Emplacement de stockage"

 # Shelf No.
 msgid "alibraries.catalog"
 msgstr "N° d'Archive"

 # Archives No.
 msgid "alibraries.catalog_no"
 msgstr "Nº d'Archive"

 # Code
 msgid "alibraries.code"
 msgstr "Code"

 # Current Location
 msgid "alibraries.current_location"
 msgstr "Emplacement actuelle"

 # Parts in Library
 msgid "alibraries.l_parts"
 msgstr "Partition en bibl."

 # Storage Location
 msgid "alibraries.storage_location"
 msgstr "Emplacement de stockage"

 # Requester
 msgid "aperusals.requester_id"
 msgstr "Demandé par"

 # Copyright Fee
 msgid "arentalcontracts.copyright_fee"
 msgstr "Frais d'auteur"

 # Marked
 msgid "atodo.l_mark"
 msgstr "Marké"

 # Passport
 msgid "atour_persons.passport_id"
 msgstr "Pièce d'identité"

 # Tour/Person
 msgid "atourperson_dates.tourperson_id"
 msgstr "Tournée/Participant"

 # Code
 msgid "opasusers.sh"
 msgstr "Code agence"

 # Account Cat.
 msgid "sacccategorynames"
 msgstr "Cat. du décompte"

 # Addresses/Agents
 msgid "saddress_agents"
 msgstr "Agents-Reps."

 # Set Points, Absences
 msgid "saddress_duties"
 msgstr "Musiciens/ Services"

 # Addresses/Instruments
 msgid "saddress_instruments"
 msgstr "Musiciens/ Instruments"

 # Addresses/Numbers
 msgid "saddress_numbers"
 msgstr "Numéros de contact"

 # Birth Country
 msgid "saddress_persdata.birthcountry_id"
 msgstr "Pays naiss. pass."

 # Birth City
 msgid "saddress_persdata.birthplace"
 msgstr "Vrai Lieu naiss."

 # Death City
 msgid "saddress_persdata.deathplace"
 msgstr "Nationalité 2"

 # Death State
 msgid "saddress_persdata.deathstate"
 msgstr "Nom mère"

 # Father's Name
 msgid "saddress_persdata.father"
 msgstr "Nom père"

 # Social Sec. No.
 msgid "saddress_persdata.insuranceno"
 msgstr "Nº AVS"

 # Passp. Copy
 msgid "saddress_persdata.l_passportcopy"
 msgstr "Copie"

 # Mother's Name
 msgid "saddress_persdata.mother"
 msgstr "Nom du Conjoint(e)"

 # Nationality
 msgid "saddress_persdata.nationality_id"
 msgstr "Natio\<nalité"

 # Passport No.
 msgid "saddress_persdata.passportno"
 msgstr "Nº de passeport/id"

 # Personnel No.
 msgid "saddress_persdata.personnelno"
 msgstr "Nº d'employé"

 # Union Affl.
 msgid "saddress_persdata.taxno"
 msgstr "N° Spedidam"

 # Sal.
 msgid "saddress_persons.salutation_id"
 msgstr "Salutation"

 # POB
 msgid "saddress_persresidents.pobox"
 msgstr "CP"

 # c/o Name
 msgid "saddresses.co_name"
 msgstr "Prénom passeport"

 # House Number
 msgid "saddresses.house_number"
 msgstr "Numéro maison"

 # Name 3
 msgid "saddresses.name3"
 msgstr "Nom passeport"

 # Name 4
 msgid "saddresses.name4"
 msgstr "Fonction admin."

 # Name 5
 msgid "saddresses.name5"
 msgstr "Nationalité 1"

 # OPAS-Online Group
 msgid "saddresses.opasonline_group"
 msgstr "OPAS-Online groupe"

 # OPAS-Online Password
 msgid "saddresses.opasonline_password"
 msgstr "OPAS-Online mot de passe"

 # OPAS-Online User
 msgid "saddresses.opasonline_user"
 msgstr "OPAS-Online utilisateur"

 # Alias
 msgid "saddresses.owners.alias"
 msgstr "Utilisateur"

 # priv. salutation
 msgid "saddresses.private_salutation"
 msgstr "Lieu naiss. pass"

 # ZIP POB
 msgid "saddresses.zipcode_pobox"
 msgstr "Nationalité 3"

 # SWIFT
 msgid "saddresspersdata_banks.swift"
 msgstr "SWIFT"

 # Passports
 msgid "saddresspersdata_passports"
 msgstr "Adresses/Passeports / CDS"

 # Passport Copy
 msgid "saddresspersdata_passports.l_passportcopy"
 msgstr "Copie"

 # Passport No
 msgid "saddresspersdata_passports.passportno"
 msgstr "Nº de passeport/id"

 # Passport Type
 msgid "saddresspersdata_passports.passporttype_id"
 msgstr "Type d'identité"

 # Alias
 msgid "saddresspersdata_passports.persons_passports.alias"
 msgstr "Personnes/Passeports / CDS"

 # SWIFT
 msgid "sbanks.swift"
 msgstr "Code Guichet/ SWIFT"

 # Birth City
 msgid "scomposers.birthplace"
 msgstr "Lieu de naiss. selon pass."

 # Initials
 msgid "scomposers.initials"
 msgstr "Initiales"

 # Alias
 msgid "scountries.persons_countries.alias"
 msgstr "Personnes/Pays"

 # Currencies
 msgid "scurrencies"
 msgstr "Devise"

# Scheduling Status
msgid "sdaystatus"
msgstr "Etat Jour"

# Service Adjustment
msgid "sdutyaccountings"
msgstr "Services/Régularisations de paie"

# Services
msgid "services"
msgstr "Titre"

# Address 1 Mark.
msgid "seventtypes.address_m_1"
msgstr "Adresse 1 Mktg."

# Address 2 Mark.
msgid "seventtypes.address_m_2"
msgstr "Adresse 2 Mktg."

# Address 3 Mark.
msgid "seventtypes.address_m_3"
msgstr "Adresse 3 Mktg."

# Type
msgid "sexpense_list.expensetype_id"
msgstr "Type de frais"

# Expense Type Groups
msgid "sexpensetypegroups"
msgstr "Type de frais/ Groupes"

# Account No. 2
msgid "sexpensetypes.accountno_2"
msgstr "Compte N°2"

# Acc.No. 2
msgid "sexpensetypes.accountno2"
msgstr "Compte N°2"

# Section
msgid "sinstrinstruments.section_id"
msgstr "Séction"

# Name (uncoded)
msgid "sinstrsectionconfiguration.name_uncoded"
msgstr "Nom (codé)"

# Section Configuration/Sections
msgid "sinstrsectionconfiguration_sections"
msgstr "Configuration pupitres/Pupitres"

# Systemsection
msgid "sinstrsectionconfiguration_sections.syssection_id"
msgstr "Groupe de pupitre"

# SystemSection
msgid "sinstrsyssections"
msgstr "Séction (Système)"

# Name (uncoded)
msgid "sinstrsyssections.name_uncoded"
msgstr "Nom (codé)"

# Text
msgid "sinstrument_insurancevalues.text"
msgstr "Mutation"

# Text
msgid "sinstrument_insurcats.text"
msgstr "Taux"

# Acc.No.
msgid "sinstrument_repairs.accountno"
msgstr "Dossier ass."

# Date 2
msgid "sinstrument_repairs.date2"
msgstr "Date clôture"

# Instrument Repair Types
msgid "sinstrumentrepairtypes"
msgstr "Types de réparation d'instrument"

# Book Value
msgid "sinstruments.bookvalue"
msgstr "Valeur nominale"

# Code
msgid "sinstruments.code"
msgstr "Position instr."

# Instrument Order
msgid "sinstruments.instrument_order"
msgstr "Commande d'instrument"

# Name 2
msgid "sinstruments.name2"
msgstr "Nom ATA :"

# Number 1
msgid "sinstruments.number1"
msgstr "Propriétaire"

# Number 2
msgid "sinstruments.number2"
msgstr "Cat. Instr. (nbr 2) :"

# Number 3
msgid "sinstruments.number3"
msgstr "Nom Instr. (nbr 3) :"

# Owner
msgid "sinstruments.owner_id"
msgstr "Utilisateur"

# Rental No.
msgid "sinstruments.rentno"
msgstr "Nº de série"

# Case Order
msgid "sinstrumenttransportcases.case_order"
msgstr "Commande des caisses de transports"

# Height
msgid "sinstrumenttransportcases.height"
msgstr "Hauteur Z"

# Length
msgid "sinstrumenttransportcases.length"
msgstr "Profondeur Y"

# Width
msgid "sinstrumenttransportcases.width"
msgstr "Largeur X"

# Section
msgid "soloists.section"
msgstr "Séction"

# Save to rel. Dates
msgid "sprojects.l_save2relateddates"
msgstr "Sauvegarder changement vers dates reliées"

# Alias
msgid "sprojects.sprojects_expenses.alias"
msgstr "Projets (Frais)"

# Publications/Cond. Scores
msgid "spubl_conscores"
msgstr "Partition chef"

# Publications/PV Scores
msgid "spubl_pvscores"
msgstr "Part. Piano/Chant"

# Publications/Reprints
msgid "spubl_reprints"
msgstr "Rééditions"

# Rehearsal Letters
msgid "spublications.l_rehearsal_letters"
msgstr "Chiffrage"

# Rehearsal Numbers
msgid "spublications.l_rehearsal_numbers"
msgstr "Chiffres de répétition"

# Title
msgid "spublications.title1"
msgstr "Titre OSR"

# Publication Types
msgid "spublicationtypes"
msgstr "Type de publication"

# Rental Types
msgid "srentaltypes"
msgstr "Types de location"

# Returned
msgid "sresource_loans.date_return"
msgstr "Retour"

# Return planned
msgid "sresource_loans.date_return_planned"
msgstr "Retour prévue"

# Number 1
msgid "sresources.number1"
msgstr "Model"

# Number 2
msgid "sresources.number2"
msgstr "Catégorie 2"

# Number 3
msgid "sresources.number3"
msgstr "Catégorie 3"

# Rental No.
msgid "sresources.rentno"
msgstr "N° de série"

# Resource Order
msgid "sresources.resource_order"
msgstr "Commande de ressources"

# Resource Order
msgid "sresourcetypes.resource_order"
msgstr "Commande de ressources"

# Services
msgid "sseasons.max_values"
msgstr "Attribution de Services"

# Tour Status
msgid "stourmanagementstatus"
msgstr "Tournée status"

# Print Title
msgid "swork_additionaltitles.title2"
msgstr "Titre imprimé"

# Alt. Title
msgid "swork_additionaltitles.title3"
msgstr "Alt. Title"

# Works/add'l. Sections
msgid "swork_addsections"
msgstr "Œuvre/ajouter pupitres"

# Works/Extras
msgid "swork_extras"
msgstr "Instr. spécialisés"

# Works/Keyboards
msgid "swork_keyboards"
msgstr "Claviers"

# Works/Movements
msgid "swork_movements"
msgstr "Mouvements"

# Works/Perf.Rights
msgid "swork_rights"
msgstr "Droits d'exécution"

# Works/Soloinstruments
msgid "swork_soloinstr"
msgstr "Instr. / Solistes"

# Premiere Types
msgid "sworkpremieres"
msgstr "Premières"

# Comp. Yr. Status
msgid "sworks.compyearstatus"
msgstr "Etat de l'année de comp."

# Suite Key
msgid "sworks.suitekey"
msgstr "Clef"

# Master Title
msgid "sworks.title1"
msgstr "Titre OSR"

# Print Title
msgid "sworks.title2"
msgstr "Titre Daniels"

# Themes - Tags
msgid "sworkthemes"
msgstr "Thèmes"

# Setting
msgid "sys_datasettings.setting"
msgstr "Paramètres"

# Type
msgid "transfers.impform1.clmtype"
msgstr "Type de frais"

msgid "participant"
msgstr "Personne"

msgid "qm"
msgstr "m3"

# Musician Pay Rates
msgid "sartist_accountings"
msgstr "Décompte Artistes"

msgid "type"
msgstr "Type de frais"

msgid "opasmenuitems.title"
msgstr "Titre OSR"

msgid "saeopasbookingstatuses.code"
msgstr "Code agence"

msgid "saerooms.code"
msgstr "Code agence"

msgid "saetemplates.code"
msgstr "Code agence"

# Services
msgid "due"
msgstr "Attribution de Services"

msgid "opasreports.notes"
msgstr "Remarques"

# OpasrightsTree Table
msgid "node"
msgstr "Remarques"

# Code
msgid "opasfunctions.method"
msgstr "Code agence"

# Text
msgid "opasnotes"
msgstr "Remarques"

# Addresses/Checklist
msgid "locationaddresses"
msgstr "Salle/Lieu"

msgid "locationaddresses.name1"
msgstr "Salle/Lieu"

# Notes
msgid "ahtmldocuments.notes"
msgstr "Remarques"

msgid "quickadd.locationaddresses.name1"
msgstr "Salle/Lieu"

#. Kurzbezeichnung Extra im Besetzungsstring
msgid "cast_extra"
msgstr "Instr. spécialisés"

# Code
msgid "sysclients.code"
msgstr "Code agence"

# Services
msgid "modules.daniels"
msgstr "Attribution de Services"

# Dates Planning - Lvl. 4
msgid "adates_4"
msgstr "Planning technique"

# Dates Planning - Lvl. 4
msgid "adates_planning"
msgstr "Planning technique"

#. Beseztungsstring öffnende Klammer
msgid "cast_open_bracket"
msgstr "["

#. Beseztungsstring schließende Klammer
msgid "cast_close_bracket"
msgstr "]"

#. Beseztungsstring Separator
msgid "cast_separator"
msgstr "."

#. Besetzungsstring Trennzeichen
msgid "cast_dash"
msgstr ""

msgid "cast_prefix_ww"
msgstr ""

msgid "cast_prefix_br"
msgstr " / "

msgid "cast_prefix_ex"
msgstr " / "

msgid "cast_prefix_str"
msgstr " // "