
--  ONDEV-5085

UPDATE adays
	INNER JOIN sseasons ON adays.season_id = sseasons.id
SET block1 = CASE WHEN (FLOOR(DATEDIFF(DATE_ADD(date_, INTERVAL 28 DAY), sseasons.datestart) / 28) - 1) > 0
				 THEN
				     FLOOR(DATEDIFF(DATE_ADD(date_, INTERVAL 28 DAY), sseasons.datestart) / 28) - 1
				 ELSE
				     13
				 END
WHERE adays.season_id = 28;

UPDATE adates
	INNER JOIN sseasons ON adates.season_id = sseasons.id
SET block1 = CASE WHEN (FLOOR(DATEDIFF(DATE_ADD(date_, INTERVAL 28 DAY), sseasons.datestart) / 28) - 1) > 0
				 THEN
				     FLOOR(DATEDIFF(DATE_ADD(date_, INTERVAL 28 DAY), sseasons.datestart) / 28) - 1
				 ELSE
				     13
				 END
WHERE adates.season_id = 28;
