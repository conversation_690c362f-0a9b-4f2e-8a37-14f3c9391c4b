/**
 * Nachgeladene JS-Erweiterung fÃ¼r das Template
 *
 * Hier wird in dem Namespace FUNCTIONS unter dem Namen des Templates eine Funktion abgelegt, die sobald aufgerufen
 * wird, wenn das Template im Wizard angezeigt wird.
 */

var REPORTS = REPORTS || {};

REPORTS.sinstruments_assurance_police_osr_template = function () {

  if ($.fn.datetimepicker) {

    $('#template_start').datetimepicker({
      format: 'DD.MM.YYYY',
      locale: 'de'
    });

    $('#template_end').datetimepicker({
      format: 'DD.MM.YYYY',
      locale: 'de'
    });

  } else {
    console.error('Missing datetimepicker()-Function for the javascript part if the function AdatesChangeTime.')
  }

  const NAMESPACE = "sinstruments_assurance_police_osr";

  $('#template_start').datetimepicker().on('dp.change', function (event) {
    $name = $(this).attr('name');
    $val = $(this).val();
    writeToStorage($name, $val);
  });

  $('#template_end').datetimepicker().on('dp.change', function (event) {
    $name = $(this).attr('name');
    $val = $(this).val();
    writeToStorage($name, $val);
  });

  function writeToStorage(key, value) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = serializedData ? JSON.parse(serializedData) : {};
    data[key] = value;
    localStorage.setItem(NAMESPACE, JSON.stringify(data));
  }

  function readFromStorage(key) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = JSON.parse(serializedData);
    return data ? data[key] : undefined;
  }

  function setValues() {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = JSON.parse(serializedData);
    if(data) {
      $.each(data, function (key, val) {
        //$val = val > 0;
        //console.log(key+'='+$val);

        if ($("#" + key).length) {
          $("#" + key).val(val);
          //$("#" + key).prop("checked", $val);
        }
      });



    }

  }

  setValues();
};

