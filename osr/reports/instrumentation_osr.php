<?php
/*
20230511 ONCUST-1770

 / timp / 5perc / hrp / 1 clavier [piano]

soll werden

1.5.1.1

und in der Rubrik “clavier” kommt nur noch Number und Code des Tasteninstrument aus dem Grid

wenn mehrere Pianos dann getrennt mit / und jeweils Leerzeichen davor und danach

zB

2 piano / 1 cel
*/



namespace Customer\osr\reports;
use App\Reports\Tools\InstrumentationCommon;
use Cake\ORM\TableRegistry;


/**
 * Class Instrumentation_kap
 * @package App\Reports\Tools
 *
 * formatiert Instrumentierung nach KAP-Vorgaben
 */
class instrumentation_osr extends InstrumentationCommon {

    public $datework_ids = '';
    public $season_id = 0;
    public $project_id = 0;
    public $l_maxinstr = false;

    public $strings = '';
    public $harmonie = '';
    public $extras = '_extras_';
    public $keyboards = '_keyboards_';
    public $percussions = '_percussions_';
    public $soloinstr = '_soloinstr_';
    public $vocals = '_vocals_';
    public $details = '_details_';
    public $notes = '_notes_';
    public $texte = '_texte_';

    public $effectiv = '';

    public $nstrings = 0;
    public $nharmonie = 0;
    public $nextras = 0;
    public $nkeyboards = 0;
    public $npercussions = 0;
    public $nsoloinstr = 0;
    public $nvocals = 0;

    public $instrumentation = '';

    public $holz = '';
    public $blech = '';
    public $others = '';

    public $cCR = '\par ';

    public $l_strings = true;
    public $l_details = true;
    public $l_text = true;

    //*** 17.	es soll nun hier gar keine Kommas geben, alle sollen durch Slash ersetzt werden
    //*** vor den Streichern ein Doppelslash

    //*cSeparator_Grid = ', '
    public $cSeparator_Grid = ' / ';
    public $cSeparator_Instrument = '.';
    //*** 20161107
    //*** osr möchte unbedingt Leerzeichen vor und nach den /, es ist ok, wenn dies dann auch in den anderen Berichten so ist.  (2.2.2.2 / 4.2.3.1 / etc)
    //*cSeparator_Section = '/'
    public $cSeparator_Section = ' / ';
    public $cSeparator_Strings = ' // ';

    private $nGrid_number = 0;

    // 20240102
    public $l_show_notes = true;

    //20240103
    public $separator_section = ' / ';

    function getInstrumentation() {
        parent::getInstrumentation();

        $this->datework_ids = '';
        $this->season_id = 0;
        $this->project_id = 0;
        $this->datework_id = 0;
        $this->work_id = 0;
        $this->date_id = 0;

        return $this->instrumentation;
    }

   function prepareRow()
   {
       $this->data_row = null;
       switch (true) {
           case $this->datework_ids > '':

               $AdateWorksTable = TableRegistry::getTableLocator()->get('AdateWorks');

               // TODO: Sergej: Variable ist hier unbekannt. Bitte korrigieren!

               $query = $AdateWorksTable
                   ->find('all')
                   ->where('AdateWorks.id IN (' . $this->datework_ids.')');

               $this->data_row = $query
                   ->find('all')
                   ->select([
                       'flute' => $query->func()->max('AdateWorks.flute'),
                       'oboe' => $query->func()->max('AdateWorks.oboe'),
                       'clarinet' => $query->func()->max('AdateWorks.clarinet'),
                       'bassoon' => $query->func()->max('AdateWorks.bassoon'),
                       'horn' => $query->func()->max('AdateWorks.horn'),
                       'trumpet' => $query->func()->max('AdateWorks.trumpet'),
                       'trombone' => $query->func()->max('AdateWorks.trombone'),
                       'tuba' => $query->func()->max('AdateWorks.tuba'),
                       'timpani' => $query->func()->max('AdateWorks.timpani'),
                       'percussion' => $query->func()->max('AdateWorks.percussion'),
                       'harp' => $query->func()->max('AdateWorks.harp'),
                       'keyboard' => $query->func()->max('AdateWorks.keyboard'),
                       'extra' => $query->func()->max('AdateWorks.extra'),
                       'vocals' => $query->func()->max('AdateWorks.vocals'),
                       'violin1' => $query->func()->max('AdateWorks.violin1'),
                       'violin2' => $query->func()->max('AdateWorks.violin2'),
                       'viola' => $query->func()->max('AdateWorks.viola'),
                       'cello' => $query->func()->max('AdateWorks.cello'),
                       'bass' => $query->func()->max('AdateWorks.bass')
                   ])
                   ->first();
               break;

           case $this->project_id > 0:

               $AdateWorksTable = TableRegistry::getTableLocator()->get('AdateWorks');

               $query = $AdateWorksTable
                   ->find('all')
                   ->contain([
                       'Adates'
                   ])
                   ->where('Adates.season_id=' . $this->season_id.' and Adates.project_id='.$this->project_id);

                 $this->data_row = $query
                     ->find('all')
                     ->select([
                         'flute' => $query->func()->max('AdateWorks.flute'),
                         'oboe' => $query->func()->max('AdateWorks.oboe'),
                         'clarinet' => $query->func()->max('AdateWorks.clarinet'),
                         'bassoon' => $query->func()->max('AdateWorks.bassoon'),
                         'horn' => $query->func()->max('AdateWorks.horn'),
                         'trumpet' => $query->func()->max('AdateWorks.trumpet'),
                         'trombone' => $query->func()->max('AdateWorks.trombone'),
                         'tuba' => $query->func()->max('AdateWorks.tuba'),
                         'timpani' => $query->func()->max('AdateWorks.timpani'),
                         'percussion' => $query->func()->max('AdateWorks.percussion'),
                         'harp' => $query->func()->max('AdateWorks.harp'),
                         'keyboard' => $query->func()->max('AdateWorks.keyboard'),
                         'extra' => $query->func()->max('AdateWorks.extra'),
                         'vocals' => $query->func()->max('AdateWorks.vocals'),
                         'violin1' => $query->func()->max('AdateWorks.violin1'),
                         'violin2' => $query->func()->max('AdateWorks.violin2'),
                         'viola' => $query->func()->max('AdateWorks.viola'),
                         'cello' => $query->func()->max('AdateWorks.cello'),
                         'bass' => $query->func()->max('AdateWorks.bass')
                     ])
                     ->first();
                 break;
           default:
               parent::prepareRow();
       }
   }

    function formatInstrumentation()
    {

        //*** 20160125
        //*** in der Zeile Harmonie weitere Slash's nach timb einfügen (statt Punkte), s. Bsp.
        //*** d.h. ab timp kommen immer Slash's: timb/perc/hrp/asx/accordéon/pf
        //*** (bitte die Slashs auch beim total unten im fiche technique)


        $this->instrumentation = '';
        $this->texte = '';

        if (is_null($this->data_row)) {
            return;
        }

        //parent::formatInstrumentation();

        //*** Grids
		//$this->l_maxinstr = false;
        $this->extras =$this->getGridInstr('Extras');
        $extras_number = $this->nGrid_number;

		$cSeparator_Grid_old = $this->cSeparator_Grid;

		$this->cSeparator_Grid = ', ';
		$this->keyboards = $this->getGridInstr('Keyboards');

		$this->cSeparator_Grid = $cSeparator_Grid_old;

        //*** 201810103
        //*** Anzeige Keyboards soll grundsätzlich geändert werden
        //*** Number_instrument_piano plus [instrument.code und (instrument.text)] aus dem Grid
        //*** Bsp:

        //*** 2 claviers [pno+cel, orgue]
        //*** 2 claviers [piano, 2 cel., sampler]
        //*** 1 clavier [cel.]
        //*** 2 claviers [2 synth]

        //*** Number_instrument immer anzeigen
        //*** Number aus dem Grid nur wenn >1

        //*** Number_instrument = 1 =clavier
        //*** Number_instrument > 1 =claviers

        // 20230511 ONCUST-1770
        // und in der Rubrik “clavier” kommt nur noch Number und Code des Tasteninstrument aus dem Grid
        //wenn mehrere Pianos dann getrennt mit / und jeweils Leerzeichen davor und danach

		/*$this->keyboards =
            ($this->data_row->keyboard>0 ?
                $this->data_row->keyboard . ' ' . ($this->data_row->keyboard>1 ? 'claviers' : 'clavier').
                (!$this->l_maxinstr && $this->keyboards>'' ? ' [' . $this->keyboards . ']' : '')
            :
                //Claviers sollen als Extralinie nur gelistet werden, wenn >0
                //'0'
                ''
			);
        */

        //*MESSAGEBOX($this->keyboards)

		$this->percussions = $this->getGridInstr('Percussions');
		$this->soloinstr = $this->getGridInstr('Soloinstr');
		$this->vocals = $this->getGridInstr('Vocals', true);
        $vocals_number = $this->nGrid_number;

        $this->nstrings =
            $this->data_row->violin1 +
            $this->data_row->violin2 +
            $this->data_row->viola +
            $this->data_row->cello +
            $this->data_row->bass;

		$this->nharmonie =
            $this->data_row->flute +
			$this->data_row->oboe +
			$this->data_row->clarinet +
			$this->data_row->bassoon +
			$this->data_row->horn +
			$this->data_row->trumpet +
			$this->data_row->trombone +
			$this->data_row->tuba +
			$this->data_row->timpani +
			$this->data_row->percussion +
			$this->data_row->harp +
			$this->data_row->keyboard +
			$this->data_row->extra +
			$this->data_row->vocals;

        //*** Strings
		$this->strings =
            (int)$this->data_row->violin1 . '.' .
            (int)$this->data_row->violin2 . '.' .
            (int)$this->data_row->viola . '.' .
            (int)$this->data_row->cello . '.' .
            (int)$this->data_row->bass;

        if($this->strings == '0.0.0.0.0') {
            //*this.cStrings = ''
            $this->strings = $this->data_row->strings_text;
            if(empty($this->strings)) {
                $this->strings = '?';
            }
        }

        //*** Blaeser
        $this->holz = '';
        $this->blech = '';
        $this->others = '';

        $this->holz = $this->addInstrument($this->holz, 'flute', true);
        $this->holz = $this->addInstrument($this->holz, 'oboe', true);
        $this->holz = $this->addInstrument($this->holz, 'clarinet', true);
        $this->holz = $this->addInstrument($this->holz, 'bassoon', true);

        $this->blech = $this->addInstrument($this->blech, 'horn', true);
        $this->blech = $this->addInstrument($this->blech, 'trumpet', true);
        $this->blech = $this->addInstrument($this->blech, 'trombone', true);
        $this->blech = $this->addInstrument($this->blech, 'tuba', true);


		//*** 20160125
		//*** in der Zeile Harmonie weitere Slash's nach timb einfügen (statt Punkte), s. Bsp.
		//*** d.h. ab timp kommen immer Slash's: timb/perc/hrp/asx/accordéon/pf
		//*** (bitte die Slashs auch beim total unten im fiche technique)
		$cSeparator_Instrument_old = $this->cSeparator_Instrument;

		// 20230511
        // Others auch mit '.' trennen
        //$this->cSeparator_Instrument = ' / ';

        //*** 20150709
        //*** bitte . zwischen perc und hp mit / ersetzen



		/*

		//5. wenn Harfe, Perc, Timb, Piano. =0 dann bitte die 0 auch unbedingt anzeigen (bei allen Sections wäre dies zu kontrollieren)
        $this->others = $this->addInstrument($this->others, 'timpani', true, 'timp');

		//*** Percussion
		$cPercussion = '0';
		if($this->data_row->percussion>0) {
            $cPercussion = ($this->data_row->percussion > 1 ? $this->data_row->percussion : '') . 'perc';
            if ($this->data_row->percussion_text > '') {
                $cPercussion .= '*';
                $this->texte .= ($this->texte > '' ? ', ' : '') . $this->data_row->percussion_text;
            }
        }

		//*** Harp
		$cHarp = '0';
        if($this->data_row->harp>0) {

            $cHarp = ($this->data_row->harp > 1 ? $this->data_row->harp : '') . 'hrp';
            if ($this->data_row->harp_text > '') {
                $cHarp .= '*';
                $this->texte .= ($this->texte > '' ? ', ' : '') . $this->data_row->harp_text;
            }
        }
		$this->others .= (($this->others && $cPercussion>'') ? $this->cSeparator_Instrument : '') . $cPercussion;
        $this->others .= (($this->others && $cHarp>'') ? $this->cSeparator_Instrument : '') . $cHarp;
		*/

        $this->others = (int)$this->data_row->timpani;
        $this->others .= $this->cSeparator_Instrument . (int)$this->data_row->percussion;
        $this->others .= $this->cSeparator_Instrument . (int)$this->data_row->harp;
        $this->others .= $this->cSeparator_Instrument . (int)$this->data_row->keyboard;

        //in der “effectif” Zeile soll aus sax.al / sax.tén. / sax.bar werden: 3 extra


        if($extras_number>0) {
            $this->others .= ' / ' . $extras_number.' extra';
        }

        if($this->vocals>'') {
            $this->others .= ' / ' . $this->vocals;
        }


        //*** 20150623
        //*** Pianos und Extras: welches Keyboard und welches Extra gemeint ist, soll unbedingt angezeigt werden. Bitte Codes der Instrumente verwenden (die müssten sie dann noch anpassen)
        //$kbd = (empty($this->keyboards) ? '0' : $this->keyboards);
        //$this->others .= (($this->others && $kbd>'') ? $this->cSeparator_Instrument : '').$kbd;
        //$this->others .= (($this->others && $this->extras>'') ? $this->cSeparator_Instrument : '') . $this->extras;

        //*** 20151229
        //*** Kannst Du voc. weglassen, wenn etwas im Grid steht ? Und die Anzahl aus dem Chorfeld oben nehmen plus Instrument (Text) anzeigen (nicht Instrument.Code)?
        //*** Ich frage mich nur, wie es dann wird, wenn es 2 Chöre gibt, dann hätten wir die max Zahl oben und 2x hintereinander Instrument (Text).Instrument (Text)
/*
		if(!$this->l_maxinstr && $this->vocals>'') {
            $this->others .= (($this->others && $this->vocals > '') ? $this->cSeparator_Instrument : '') . $this->vocals;
        } else {
            $this->others = $this->addInstrument($this->others, 'vocals', false, 'voc.');
		}
*/
        //*		$this->others = $this->addInstrument($this->others, 'keyboard', false, 'keyb')
        //*		$this->others = $this->addInstrument($this->others, 'extra', false)
		$this->cSeparator_Instrument = $cSeparator_Instrument_old;
		
		
		$this->details = $this->data_row->details;


        $this->notes = $this->data_row->notes;


    //**************************************************



        /*$this->instrumentation = '';
        $this->instrumentation .= (!empty($this->instrumentation) AND !empty($this->holz) ? $this->cSeparator_Section : '') . $this->holz;
	   	$this->instrumentation .= (!empty($this->instrumentation) AND !empty($this->blech) ? $this->cSeparator_Section : '') . $this->blech;
	   	$this->instrumentation .= (!empty($this->instrumentation) AND !empty($this->others) ? $this->cSeparator_Section : '') . $this->others;
*/
        if ($this->instrumentation == '0.0.0.0 / 0.0.0.0') {
            $this->instrumentation = '';
        }

        if ($this->l_strings) {
            $this->instrumentation .= (($this->instrumentation > '' && $this->strings > '') ? $this->cSeparator_Strings : '') . $this->strings;
        }

        if($this->l_details) {
            $this->instrumentation .= (($this->instrumentation > '' && $this->strings > '') ? "\n" : '') . $this->details;
		}

        $this->harmonie = '';
        $this->harmonie .= (($this->harmonie > '' && $this->holz > '') ? $this->cSeparator_Section : '') . $this->holz;
        $this->harmonie .= (($this->harmonie > '' && $this->blech > '') ? $this->cSeparator_Section : '') . $this->blech;
        //*** 20160928
        // *** Außerdem erscheinen "org, hpsd" auch in der Zeile "harmonie", bitte da wegnehmen
        $this->harmonie .= (($this->harmonie > '' && $this->others > '') ? $this->cSeparator_Section : '') . $this->others;

        $this->effectiv = $this->harmonie . (($this->harmonie > '' && $this->strings > '') ? ' // ' : '') . $this->strings;

        $this->instrumentation = $this->effectiv;

        $this->instrumentation_max = $this->instrumentation;
        $this->strings_max = $this->strings;
    }


    function addInstrument($cSection, $cInstrument, $lShowNull=true, $cCode='') {
        $instrument = '';

        $cInstrument_Text = '';
        $cInstrument_Text_field = $cInstrument . '_text';
        if(isset($this->data_row->$cInstrument_Text_field)) {
            $cInstrument_Text = $this->data_row->$cInstrument_Text_field;
        }

        if($this->l_maxinstr) {
            $cInstrument_Text = '';
        }

		$nInstrument = $this->data_row->$cInstrument;

		if ($cCode>'') {
            if($nInstrument>0) {
                $instrument = ($nInstrument > 1 ? $nInstrument : '') . $cCode;
            }
        } else {
            $instrument = (int)$nInstrument;
		}

		if($cInstrument_Text>'') {
            $instrument .= '*';
            $this->texte .= ($this->texte > '' ? ', ' : '').$cInstrument_Text;
		}

		if($nInstrument == 0 and !$lShowNull) {
//            $instrument = '';
        }
        //$instrument .='#';
		$cSection .= (($cSection>'' && ($instrument>'' || $lShowNull)) ? $this->cSeparator_Instrument : '') . $instrument;

		return $cSection;
	}

    function getGridInstr($instrument_grid, $lShowName = false) {
        // 20230511
        // Gesamtanzahl Instrumente im Grid
        $this->nGrid_number = 0;

        $tbl = 'Adatework'.$instrument_grid;
        //$where = '1<2';
        $where = 'Adates.id<0';


		switch(true) {
            case $this->datework_id >0:
                $where = "AdateWorks.id=". $this->datework_id;

                break;
            case $this->date_id:
                $where = "AdateWorks.date_id=" .$this->date_id;
                break;
            case $this->datework_ids > '':
                $where = 'AdateWorks.id IN (' . $this->datework_ids.')';
                break;

            case $this->project_id > 0:
                $where = 'Adates.season_id=' . $this->season_id.' and Adates.project_id='.$this->project_id;
                break;
		}

        //*** 20170313
        //*** Fälschlicherweise wurden hier statt 2 gitarren, 1 x eine Gitarre in Grid Extras eingetragen:

        //*** Natürlich wäre es besser, wenn bei Number 2 eingetragen würde
        //*** Aber wenn die Daten schon "falsch" eingetragen werden, dann soll OPAS das in den Berichten auch zeigen, damit man es sieht und nicht die beiden Gitarren fusionnieren und nur einmal einzeigen
        //*** wenn 2x 1 guitare im grid, dann im Moment :
        //*** effectif : 3 * .3 * .3 * .3 * / 5.4 * .3.1 / timp / 5perc / hrp / cymbalum, pno / accordéon, gtr, mand / 14.12.10.8.6
        //*** extra : accordéon, gtr, mand

        //*** gewünscht :
        //*** effectif : 3 * .3 * .3 * .3 * / 5.4 * .3.1 / timp / 5perc / hrp * / cymbalum, pno / accordéon, gtr, gtr, mand / 14.12.10.8.6
        //*** extra : accordéon, gtr, gtr, mand
        //* "MAX(aDWI.number_) AS number_, " +

        // 20230317
        // bitte bei den Percussions die sort_order beachten, wenn es keine Sortierung gibt, dann so ausgeben wie es eingetragen wurde (also nach Reihenfolge der Eingabe)
        $ADWITable = TableRegistry::getTableLocator()->get($tbl);
        $data_dwi = $ADWITable
           ->find('all')
           ->contain(['AdateWorks'=>['Adates'], 'Sinstrinstruments'])
           ->where($where)
            ->order(['Adates.date_', 'Adates.start_', 'AdateWorks.work_order', $tbl.'.instrument_order', 'AdateWorks.id', 'Sinstrinstruments.id']);
        //->order([ $tbl.'.instrument_order', $tbl.'.id']);
        //->order(['Adates.date_', 'Adates.start_', 'AdateWorks.work_order', $tbl.'.instrument_order']);

        //print_r('XXXX');
        //print_r($data_dwi); die;

        //*if (tcInstrumentName_Grid == 'extras')
        //*BROWSE LAST normal
        //*endif
		$cInstr_Grid = '';
        $ainstruments = array();

        if($instrument_grid=='Percussions'){
            //return $data_dwi->sql();
                //print_r($data_dwi); die;
        }
        foreach($data_dwi as $adwi) {
            $instrument = ($adwi->sinstrinstrument->code > '' ? $adwi->sinstrinstrument->code : $adwi->sinstrinstrument->name);
            if ($lShowName) {
                $instrument = $adwi->sinstrinstrument->name;
            }
            $k = $adwi->instrument_id . '#' . $adwi->text;
            if (!array_key_exists($k, $ainstruments)) {
                $ainstruments[$k] = array(
                    'max_number' => 0,
                    'instrument_id' => $adwi->instrument_id,
                    'instrument' => $instrument,
                    'text' => $adwi->text
                );
            }

            $ainstruments[$k]['max_number'] = max($ainstruments[$k]['max_number'], $adwi->number_);
        }

        foreach($ainstruments as $ainstrument) {
            $this->nGrid_number += $ainstrument['max_number'];

            $instrument = ($ainstrument['max_number'] > 1 ? $ainstrument['max_number'] . ' ' : '') .$ainstrument['instrument'].
				(!$this->l_maxinstr && $ainstrument['text'] > '' ? ' (' .$ainstrument['text']. ')' : '');

            $cInstr_Grid .= (($cInstr_Grid>'' && $instrument>'') ? $this->cSeparator_Grid : '') . $instrument;
        }


		return $cInstr_Grid;
	}

    function formatInstrumentation_20230511() {
        //*** 20160125
        //*** in der Zeile Harmonie weitere Slash's nach timb einfÃ¼gen (statt Punkte), s. Bsp.
        //*** d.h. ab timp kommen immer Slash's: timb/perc/hrp/asx/accordÃ©on/pf
        //*** (bitte die Slashs auch beim total unten im fiche technique)


        $this->instrumentation = '';
        $this->texte = '';

        if (is_null($this->data_row)) {
            return;
        }

        //parent::formatInstrumentation();

        //*** Grids
        //$this->l_maxinstr = false;
        $this->extras =$this->getGridInstr('Extras');

        $cSeparator_Grid_old = $this->cSeparator_Grid;

        $this->cSeparator_Grid = ', ';
        $this->keyboards = $this->getGridInstr('Keyboards');

        $this->cSeparator_Grid = $cSeparator_Grid_old;

        //*** 201810103
        //*** Anzeige Keyboards soll grundsÃ¤tzlich geÃ¤ndert werden
        //*** Number_instrument_piano plus [instrument.code und (instrument.text)] aus dem Grid
        //*** Bsp:

        //*** 2 claviers [pno+cel, orgue]
        //*** 2 claviers [piano, 2 cel., sampler]
        //*** 1 clavier [cel.]
        //*** 2 claviers [2 synth]

        //*** Number_instrument immer anzeigen
        //*** Number aus dem Grid nur wenn >1

        //*** Number_instrument = 1 =clavier
        //*** Number_instrument > 1 =claviers

        $this->keyboards =
            ($this->data_row->keyboard>0 ?
                $this->data_row->keyboard . ' ' . ($this->data_row->keyboard>1 ? 'claviers' : 'clavier').
                (!$this->l_maxinstr && $this->keyboards>'' ? ' [' . $this->keyboards . ']' : '')
                :
                //Claviers sollen als Extralinie nur gelistet werden, wenn >0
                //'0'
                ''
            );

        //*MESSAGEBOX($this->keyboards)

        $this->percussions = $this->getGridInstr('Percussions');
        $this->soloinstr = $this->getGridInstr('Soloinstr');
        $this->vocals = $this->getGridInstr('Vocals', true);

        $this->nstrings =
            $this->data_row->violin1 +
            $this->data_row->violin2 +
            $this->data_row->viola +
            $this->data_row->cello +
            $this->data_row->bass;

        $this->nharmonie =
            $this->data_row->flute +
            $this->data_row->oboe +
            $this->data_row->clarinet +
            $this->data_row->bassoon +
            $this->data_row->horn +
            $this->data_row->trumpet +
            $this->data_row->trombone +
            $this->data_row->tuba +
            $this->data_row->timpani +
            $this->data_row->percussion +
            $this->data_row->harp +
            $this->data_row->keyboard +
            $this->data_row->extra +
            $this->data_row->vocals;

        //*** Strings
        $this->strings =
            (int)$this->data_row->violin1 . '.' .
            (int)$this->data_row->violin2 . '.' .
            (int)$this->data_row->viola . '.' .
            (int)$this->data_row->cello . '.' .
            (int)$this->data_row->bass;

        if($this->strings == '0.0.0.0.0') {
            //*this.cStrings = ''
            $this->strings = $this->data_row->strings_text;
            if(empty($this->strings)) {
                $this->strings = '?';
            }
        }

        //*** Blaeser
        $this->holz = '';
        $this->blech = '';
        $this->others = '';

        $this->holz = $this->addInstrument($this->holz, 'flute', true);
        $this->holz = $this->addInstrument($this->holz, 'oboe', true);
        $this->holz = $this->addInstrument($this->holz, 'clarinet', true);
        $this->holz = $this->addInstrument($this->holz, 'bassoon', true);

        $this->blech = $this->addInstrument($this->blech, 'horn', true);
        $this->blech = $this->addInstrument($this->blech, 'trumpet', true);
        $this->blech = $this->addInstrument($this->blech, 'trombone', true);
        $this->blech = $this->addInstrument($this->blech, 'tuba', true);

        //5. wenn Harfe, Perc, Timb, Piano. =0 dann bitte die 0 auch unbedingt anzeigen (bei allen Sections wÃ¤re dies zu kontrollieren)
        $this->others = $this->addInstrument($this->others, 'timpani', true, 'timp');

        //*** 20160125
        //*** in der Zeile Harmonie weitere Slash's nach timb einfÃ¼gen (statt Punkte), s. Bsp.
        //*** d.h. ab timp kommen immer Slash's: timb/perc/hrp/asx/accordÃ©on/pf
        //*** (bitte die Slashs auch beim total unten im fiche technique)
        $cSeparator_Instrument_old = $this->cSeparator_Instrument;

        $this->cSeparator_Instrument = ' / ';

        //*** 20150709
        //*** bitte . zwischen perc und hp mit / ersetzen

        //*** Percussion

        $cPercussion = '0';
        if($this->data_row->percussion>0) {
            $cPercussion = ($this->data_row->percussion > 1 ? $this->data_row->percussion : '') . 'perc';
            if ($this->data_row->percussion_text > '') {
                $cPercussion .= '*';
                $this->texte .= ($this->texte > '' ? ', ' : '') . $this->data_row->percussion_text;
            }
        }

        //*** Harp
        $cHarp = '0';
        if($this->data_row->harp>0) {

            $cHarp = ($this->data_row->harp > 1 ? $this->data_row->harp : '') . 'hrp';
            if ($this->data_row->harp_text > '') {
                $cHarp .= '*';
                $this->texte .= ($this->texte > '' ? ', ' : '') . $this->data_row->harp_text;
            }
        }

        $this->others .= (($this->others && $cPercussion>'') ? $this->cSeparator_Instrument : '') . $cPercussion;
        $this->others .= (($this->others && $cHarp>'') ? $this->cSeparator_Instrument : '') . $cHarp;

        //*** 20150623
        //*** Pianos und Extras: welches Keyboard und welches Extra gemeint ist, soll unbedingt angezeigt werden. Bitte Codes der Instrumente verwenden (die mÃ¼ssten sie dann noch anpassen)
        $kbd = (empty($this->keyboards) ? '0' : $this->keyboards);
        $this->others .= (($this->others && $kbd>'') ? $this->cSeparator_Instrument : '').$kbd;
        $this->others .= (($this->others && $this->extras>'') ? $this->cSeparator_Instrument : '') . $this->extras;

        //*** 20151229
        //*** Kannst Du voc. weglassen, wenn etwas im Grid steht ? Und die Anzahl aus dem Chorfeld oben nehmen plus Instrument (Text) anzeigen (nicht Instrument.Code)?
        //*** Ich frage mich nur, wie es dann wird, wenn es 2 ChÃ¶re gibt, dann hÃ¤tten wir die max Zahl oben und 2x hintereinander Instrument (Text).Instrument (Text)

        if(!$this->l_maxinstr && $this->vocals>'') {
            $this->others .= (($this->others && $this->vocals > '') ? $this->cSeparator_Instrument : '') . $this->vocals;
        } else {
            $this->others = $this->addInstrument($this->others, 'vocals', false, 'voc.');
        }

        //*		$this->others = $this->addInstrument($this->others, 'keyboard', false, 'keyb')
        //*		$this->others = $this->addInstrument($this->others, 'extra', false)
        $this->cSeparator_Instrument = $cSeparator_Instrument_old;


        $this->details = $this->data_row->details;
        $this->notes = $this->data_row->notes;

        //**************************************************


        if ($this->instrumentation == '0.0.0.0/0.0.0.0') {
            $this->instrumentation = '';
        }

        if ($this->l_strings) {
            $this->instrumentation .= (($this->instrumentation > '' && $this->strings > '') ? $this->cSeparator_Strings : '') . $this->strings;
        }

        if($this->l_details) {
            $this->instrumentation .= (($this->instrumentation > '' && $this->strings > '') ? "\n" : '') . $this->details;
        }

        $this->harmonie = '';
        $this->harmonie .= (($this->harmonie > '' && $this->holz > '') ? $this->cSeparator_Section : '') . $this->holz;
        $this->harmonie .= (($this->harmonie > '' && $this->blech > '') ? $this->cSeparator_Section : '') . $this->blech;
        //*** 20160928
        // *** AuÃŸerdem erscheinen "org, hpsd" auch in der Zeile "harmonie", bitte da wegnehmen
        $this->harmonie .= (($this->harmonie > '' && $this->others > '') ? $this->cSeparator_Section : '') . $this->others;

        $this->effectiv = $this->harmonie . (($this->harmonie > '' && $this->strings > '') ? ' // ' : '') . $this->strings;

        $this->instrumentation_max = $this->instrumentation;
        $this->strings_max = $this->strings;
    }

}
