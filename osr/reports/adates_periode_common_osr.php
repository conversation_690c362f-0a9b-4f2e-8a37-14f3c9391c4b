<?php

/*
20220720

*** 20161212
*** fiche_bibliotheque und fiche_technique immer pro Konzert oder einzelne Termine, wenn es keine Aufführungen gibt
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportWord;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Language;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\osr\reports\ReportTools_client;
use Customer\osr\reports\instrumentation_osr;

class cls_periode_common extends ReportWord
{
    public $user = '';
    public $caption = '';
    public $cReportType = '';
    public $l_show_fromto = true;
    public $l_show_parts = false;

    public $l_show_effectiv = true;
    //public $l_show_effectiv = true;
    public $l_show_effectiv_osr = true;
    public $l_show_addInstrFields = false;
    //public $l_show_addInstrFields = true;
    //*** 20170911
    public $l_show_notes = false;
    //public $l_show_notes = true;

    public $l_show_PP_CC = true;


    //*** 20170216
    public $l_show_vocals = true;
    public $l_show_keyboards = true;
    public $l_show_extras = true;
    public $l_show_strings = true;

    public $cBlocks = '';
    public $cSeasons = '';
    public $ablocks = array();
    public $aseason_ids = array();


    public $postData = null;
    public $aerrors = array();
    public $reporttools = null;

    public $adates_selected = null;
    public $adate = null;

    public $k = '';
    public $date_id = 0;
    public $work_id = 0;
    public $part = 0;
    public $aparts = array();

    public $aPL2_cf = array();
    public $aPL3_cf = array();


    public $work_count_project = 0;

    // Parameter für //*** Fiche technique, wenn >0
    public $nPD_id = 0;

    public $nPlanningLevel = 0;
    public $cProject = '';
    public $cProject_Code = '';
    public $cProjectType = '';

    public $l_Empty_CC_PP = true;

    public $aprojects = null;
    public $aproject = null;

    public $aworks_all = array();

    public $acol_widths = array();
    public $table_width = 0;


    public $cellColSpan2 = array('gridSpan' => 2);
    public $cellColSpan3 = array('gridSpan' => 3);
    public $cellColSpan4 = array('gridSpan' => 4);
    public $cellColSpan5 = array('gridSpan' => 5);
    public $cellColSpan7 = array('gridSpan' => 7);
    public $cellRowSpan = array('vMerge' => 'restart');
    public $cellRowContinue = array('vMerge' => 'continue');

    public $styleCell_borderTop_red = array('borderTopSize' => 6, 'borderTopColor' => '#FF0000');
    public $styleCell_borderBottom_grey = array('borderBottomSize' => 6, 'borderBottomColor' => '#BFBFBF');


    public $borderColor_Header = array();
    public $borderColor_PDs = array();

    public $BGColor_Header = array();
    public $BGColor_PDs = array();
    public $BGColor_other = array();

    public $styleCell_borderTop_dotted = array('borderTopSize' => 6, 'borderTopColor' => 'black', 'borderTopStyle' => 'dotted');
    public $styleCell_borderBottom_dotted = array('borderBottomSize' => 6, 'borderBottomColor' => 'black', 'borderBottomStyle' => 'dotted');
    public $styleCell_borderLeft_dotted = array('borderLeftSize' => 6, 'borderLeftColor' => 'black', 'borderLeftStyle' => 'dotted');
    public $styleCell_borderRight_dotted = array('borderRightSize' => 6, 'borderRightColor' => 'black', 'borderRightStyle' => 'dotted');

    public $styleCell_borderBottom_none_grey = array('borderBottomSize' => 6, 'borderBottomColor' => '#D9D9D9');
    public $styleCell_borderTop_none = array('borderTopSize' => 6, 'borderTopColor' => 'white');
    public $styleCell_borderBottom_none = array('borderBottomSize' => 6, 'borderBottomColor' => 'white');
    public $styleCell_borderLeft_none = array('borderLeftSize' => 6, 'borderLeftColor' => 'white');
    public $styleCell_borderRight_none = array('borderRightSize' => 6, 'borderRightColor' => 'white');

    public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black', 'borderTopStyle' => 'single');
    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black', 'borderottomStyle' => 'single');
    public $styleCell_borderLeft = array('borderLeftSize' => 6, 'borderLeftColor' => 'black', 'borderLeftStyle' => 'single');
    public $styleCell_borderRight = array('borderRightSize' => 6, 'borderRightColor' => 'black', 'borderRightStyle' => 'single');

    public $styleCell_borderTop_12 = array('borderTopSize' => 12, 'borderTopColor' => 'black', 'borderTopStyle' => 'single');
    public $styleCell_borderBottom_12 = array('borderBottomSize' => 12, 'borderBottomColor' => 'black', 'borderBottomStyle' => 'single');
    public $styleCell_borderLeft_12 = array('borderLeftSize' => 12, 'borderLeftColor' => 'black', 'borderLeftStyle' => 'single');
    public $styleCell_borderRight_12 = array('borderRightSize' => 12, 'borderRightColor' => 'black', 'borderRightStyle' => 'single');

    public $styleCell_valignCenter = array('valign' => 'center');
    public $styleCell_alignCenter = array('align' => 'center');
    public $styleCell_alignRight = array('align' => 'right');

    public $styleFont = array('name' => 'Calibri');
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_green = array('color' => '70AD47');
    public $styleFont_red = array('color' => '#C00000');
    public $styleFont_grey = array('color' => '#AEAAAD');
    public $styleFont_blue = array('color' => '#4F81BD');

    public $styleBG_grey = array('bgColor' => '#D9D9D9');
    public $styleBG_DarkOrange = array('bgColor' => '#FABF8F');
    public $styleBG_BrightOrange = array('bgColor' => '#FDE9D9');

    public $styleFont_22 = array('size' => 22);
    public $styleFont_16 = array('size' => 16);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_11 = array('size' => 11);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_9 = array('size' => 9);
    public $styleFont_8 = array('size' => 8);
    public $styleFont_7 = array('size' => 7);

    public $oinstrumentation = null;

    public $tblprogram_row_h = 0.42;
    public $tbl_row_h = 0.42;

    public $cLibrary_TPC = '';
    public $cLibrary_Notes = '';
    public $cLibrary_Types = '';
    public $cLibrary_Catalogs = '';
    public $cLibrary_Publications = '';

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new reportTools_client();

        $this->oinstrumentation = new instrumentation_osr();

        $this->aPL2_cf = array('color' => '#00B050'); //RGB(0, 176, 80);
        $this->aPL3_cf = array('color' => '#007CB9'); //RGB(0, 124, 185);

        $this->nPD_id = -1;
    }

    public function collect(array $where = [])
    {

        // Get the POST data
        $this->postData = $this->getRequest()->getData();

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        // Collect the selected dates with all required data


        //$this->prepare_aprojects();

        return $this;
    }
    // Create the file and return the filename
    /*********************************************/
    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        //20211102 ONCUST-359
        // wenn es nur ein Projekt in der Auswahl gibt, dann
        //Frage ist es eigentlich auch mûÑglich, dass sich der Dokementenname vom Bericht dann im Projektnamen automatisch umûÊndert?
        // sonst "multiple projects"

        $count = 0;
        $project = '';
        foreach ($this->aprojects as $aproject) {
            $project = $aproject['project'];
            $project = str_replace('+', '_', $project);
            $project = str_replace(':', '_', $project);
            $project = str_replace('/', '_', $project);
            $project = str_replace('?', '_', $project);

            if (!empty($project)) {
                $fileName = $project;
                $count++;
            }
        }

        if ($count > 1) {
            $fileName = 'multiple_projects';
        } else {
            if (!empty($project)) {
                $fileName = $project;
            }
        }

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->getSettings()->setThemeFontLang(new Language(Language::FR_FR));

        $this->phpWord->setDefaultFontSize(8);
        $this->phpWord->setDefaultFontName('Arial');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $section = $this->addSection();

        $this->prepare_aprojects($section);

        $this->addHeader($section);
        $this->addFooter($section);



        $projects_no = sizeof($this->aprojects);

        $i = 0;
        foreach ($this->aprojects as $this->aproject) {

            $i++;
            $this->nPD_id = -1;

            $this->prepare_projectdata($section);

            $this->showProject($section);

            if ($i < sizeof($this->aprojects)) {
                $this->addPagebreak($section);
            }

        }
    }

    function addPagebreak($section)
    {
        //$section->addText('<w:br w:type="page"/>');
    }

    function addSection()
    {
        return $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.27),
                'marginLeft' => Converter::cmToTwip(1),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(1.2),
                'marginBottom' => Converter::cmToTwip(1.4))
        );
    }

    function addHeader($section)
    {

    }

    function addFooter($section)
    {

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(8.5)),
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(19))
                    )
            );


        $footer = $section->addFooter();
        $textrun = $footer->addTextRun($styleTabs);
        $textrun->addText("\t");

        // 20230325 ONCUST-1674
        // bitte den Satz « Le port des protections auditives individuelles ... ». aus der Fusszeile rausnehmen
        if (!($this->cReportType == 'fiche_technique') && !($this->cReportType == 'fiche_bibliotheque')) {
            $textrun->addText(htmlspecialchars('Le port des protections auditives individuelles est vivement recommandé en cas de surcharge sonore.'), array_merge($this->styleFont_bold, $this->styleFont_italic, $this->styleFont_7));
        }


        $date = new DateTime(); // current time

        $date->add(new DateInterval('PT2H')); // P2H means a period of 2 hours



        $textrun->addText("\t" . htmlspecialchars('édité le ' . $date->format('d/m/Y H') . 'h' . $date->format('i') . ' - '), array_merge($this->styleFont_7));

        //$section->addField($fieldType, [$properties], [$options], [$fieldText], [$fontStyle])
        $textrun->addField('PAGE', array(), array(), '', array_merge($this->styleFont_7));
    }

    function showProject($section)
    {
        if ($this->caption > '') {
            $section->addText(htmlspecialchars($this->caption), array_merge($this->styleFont_bold, $this->styleFont_12), $this->styleCell_alignCenter);
        }

        $section->addTextBreak();

        //getMinMaxDays


        //*** 21070216
        //*** Es werden nun doch wieder teilweise andere Farben benutzt:
        //*** Bsp. S. Anhang
        //*** Die Projekttypen Code LYR und SYM bleiben so blaugrau

        //*** Alle anderen Typen sollen im Kopf orange sein und der Rest (Daten, Programme, Planification) hellorange, vgl. Anhang:

        //*** 20170217
        //*** <tm> die Termine ohne Projekte sind noch grün bzw. hellgrün geblieben.
        //*** Alle auér Projekte vom Typ LUR oder SYM sollen auch Orange/hellorange sein.

        switch (true) {
            //*** MSR, OCL oder CC: orange
            //*** LYR oder SYM: grau
            case in_array($this->aproject['projecttype_code'], array('LYR', 'SYM')):
                $this->BGColor_Header = $this->styleBG_grey;
                $this->BGColor_PDs = $this->styleBG_grey;
                $this->BGColor_other = array();
                $this->borderColor_Header = '#D9D9D9';
                $this->borderColor_PDs = '#D9D9D9';
                break;

            default:

                $this->BGColor_Header = $this->styleBG_DarkOrange;
                $this->BGColor_PDs = $this->styleBG_BrightOrange;
                $this->BGColor_other = $this->styleBG_BrightOrange;
                $this->borderColor_Header = '#FDE9D9';
                $this->borderColor_PDs = '#FDE9D9';

                break;
        }

        /*
        *** 20170117
        *** Projekte Typ Code PP oder CC und Terminarten ohne Projekt: andere Formatierung
        *** - Planungsblock fällt weg, ebenso wie Zellen für Solisten, Kleidung etc.

        *** 20170216
        *** d) und für alle diese Termine (vom Projekt Typ CC, PP oder ohne Projekt) nicht den Zeitraum anzeigen: Production du 7 octobre 2016 au 10 juin 2017

         */
        $this->showFromto($section);



        $this->getTblDates($section);

        if (!$this->l_Empty_CC_PP) {
            $this->getTblInfo($section);
            $this->getTblProgram($section);
            $this->getTblPlanification($section);
        }
    }

    function showFromto($section)
    {
        if ($this->l_show_fromto and !$this->l_Empty_CC_PP) {

            $styleTabs =
                array(
                    'tabs' => array(
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(26.5))
                    )
                );

            $textrun = $section->addTextRun($styleTabs);

            //$minmax = $this->getMinMaxDays_short($this->aproject['adates']);
            $minmax = $this->getMinMaxDays($this->aproject['adates']);
            $textrun->addText(htmlspecialchars('Production du ' . $minmax), array_merge($this->styleFont_bold));
        }
    }

    function prepare_aprojects($section)
    {


        // nur AusgewûÊhlte Termine

        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);

        $this->aprojects = array();

        $this->ablocks = array();
        $this->aseason_ids = array();
        $aseasons = array();



        foreach ($this->adates_selected as $adate) {
            $season_id = $adate->season_id;
            $project_id = $adate->project_id;
            $planninglevel = (int)$adate->planninglevel;

            $sprojecttype = '';
            $sprojecttype_code = '';
            $projecttype_colour_id = 0;
            if ($adate->sproject && $adate->sproject->sprojecttype) {
                $projecttype_colour_id = (int)$adate->sproject->sprojecttype->colour_id;
                $sprojecttype_code = $adate->sproject->sprojecttype->code;

                $sprojecttype = $adate->sproject->sprojecttype->name .
                    ((!empty($adate->sproject->sprojecttype->name) && !empty($adate->sproject->sprojecttype->name2)) ? ' / ' : '') .
                    $adate->sproject->sprojecttype->name2;
            }

            if (!$this->l_show_PP_CC) {
                //!(NVL(ALLT(UPPER(ProjectType_Code)), '') == 'CC') AND " + ;
                //!(NVL(ALLT(UPPER(ProjectType_Code)), '') == 'PP')"
                if (mb_strtoupper($sprojecttype_code) == 'CC' || mb_strtoupper($sprojecttype_code) == 'PP') {
                    continue;
                }
            }

            //$section->addText("1.block1=".$adate->block1);
            if (!in_array((int)$adate->block1, $this->ablocks)) {
                //$section->addText("2.block1=".(int)$adate->block1);
                $this->ablocks[] = (int)$adate->block1;
            }

            if (!in_array($adate->sseason->name, $aseasons)) {
                $aseasons[] = $adate->sseason->name;
            }

            if (!key_exists($adate->season_id, $this->aseason_ids)) {
                $this->aseason_ids[$adate->season_id] = array(
                    'ablocks' => array()
                );
            }

            if (!in_array((int)$adate->block1, $this->aseason_ids[$adate->season_id]['ablocks'])) {
                $this->aseason_ids[$adate->season_id]['ablocks'][] = (int)$adate->block1;
            }

            $project = ($adate->sproject ? $adate->sproject->name : '');
            $project_code = $adate->seventtype->code;

            if ($project_id <= 0) {
                $project_id = -1 * $adate->id;
                $project = $adate->seventtype->name;
                $project_code = $adate->seventtype->code;
            }

            $k = $season_id . '#' . $project_id . '#' . $planninglevel;

            if (!array_key_exists($k, $this->aprojects)) {


                //print_r($sprojecttype_code);die;
                //print_r($adate->sproject->sprojecttype->code);die;
                //$sprojecttype_code = '';


                $this->aprojects[$k] = array(
                    'season_id' => $season_id,
                    'project_id' => $project_id,
                    'planninglevel' => $planninglevel,
                    'project' => $project,
                    'project_code' => $project_code,
                    'projecttype' => $sprojecttype,
                    'projecttype_code' => $sprojecttype_code,
                    'projecttype_colour_id' => $projecttype_colour_id,
                    'memo_1' => array()
                );
            }
        }
        $this->cBlocks = implode(', ', $this->ablocks);
        $this->cSeasons = implode(', ', $aseasons);


        //print_r($this->aprojects); die;
    }

    function prepare_conductors()
    {
        $conductor_id = $this->adate->conductor_id;

        if ($conductor_id > 0 && $this->adate->conductoraddress) {
            if (!array_key_exists($conductor_id, $this->aproject['aconductors'])) {
                $this->aproject['aconductors'][$conductor_id] = array(
                    'name' => $this->getLongName($this->adate->conductoraddress->name2, '', mb_strtoupper($this->adate->conductoraddress->name1)),
                    'caption' => 'Conductor',
                    'instrument' => 'direction',
                    'instrument_en' => 'conductor',
                    'adates' => array()
                );
            }
            $this->aproject['aconductors'][$conductor_id]['adates'][$this->adate->id] = $this->adate;
        }


    }

    function prepare_projectdata($section)
    {

        //print_r('XXnPD_id='.$this->nPD_id.'#');

        $this->nPlanningLevel = $this->aproject['planninglevel'];
        $this->cProject = $this->aproject['project'];
        $this->cProject_Code = $this->aproject['project_code'];
        $this->cProjectType = $this->aproject['projecttype'];

        $this->aproject['duties2_sum'] = 0;
        $this->aproject['adates'] = array();
        $this->aproject['apds'] = array();
        $this->aproject['apds_text_2'] = array();
        $this->aproject['apds_program'] = array();
        $this->aproject['aconductors'] = array();
        $this->aproject['asoloists'] = array();
        $this->aproject['aorchestras'] = array();
        $this->aproject['apersons'] = array();
        $this->aproject['aseries_dresses'] = array();
        $this->aproject['aworks'] = array();
        $this->aproject['nduration_total'] = 0;
        $this->aproject['intermission_no'] = 0;

        $this->aproject['ablock1s'] = array();
        $this->aproject['amemo_1'] = array();

        //*** 20170216
        $this->l_Empty_CC_PP = ($this->aproject['project_id'] <= 0 or (in_array($this->aproject['projecttype_code'], array('PP', 'CC'))));


        if ($this->l_Empty_CC_PP) {
            $awhere = ['Adates.id IN' => $this->postData['dataItemIds']];
            if ($this->aproject['project_id'] < 0) {
                $awhere = array_merge($awhere, ['Adates.id' => -1 * $this->aproject['project_id']]);
            } else {
                $awhere = array_merge($awhere, ['Adates.project_id' => $this->aproject['project_id']]);
            }
        } else {
            //***
            //20170206
            //*** für alle anderen Projekte alle Termine

            //#if($this->nPD_id>0) {
            //#    $awhere = [
            //#        'Adates.id' => $this->nPD_id
            //#    ];
            //#} else {
            $awhere = [
                'Adates.season_id' => $this->aproject['season_id'],
                'Adates.project_id' => $this->aproject['project_id'],
                ['OR' => ['planninglevel' => 1, 'Adates.id IN' => $this->postData['dataItemIds']]]
            ];
            //#}

        }

        // alle Termine des Projektes
        $adates_project = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

        $this->aproject['instrumentation_max'] = '';
        $count_memo_1 = 0;

        $this->part = 1;
        $this->aparts = array();

        //20220928
        // work_count für alle Termine durchlaufend

        $this->work_count_project = 0;

        // 20230112 ONCUST-1005
        // Programm für alle Termine, nicht nur für $this->nPD_id (fiche_technique)
        // Object $adates_project kannnicht sortiert werden, zuerst in array umwandeln
        foreach($adates_project as $adate) {
            $adates_project_ordered[] = $adate;
        }

        //20230823
        // Termine sortieren nach l_performance, damit Programm aus der Konzerten zuerst kommt
        uasort(
            $adates_project_ordered,
            function ($a, $b) {
                $a_order = ($a->seventtype && $a->seventtype->l_performance == 1 ? '1' : '2') . ($a->date_ ? $a->date_->format('Ymd') : '').($a->start_ ? $a->start_->format('H:i') : '');
                $b_order = ($b->seventtype && $b->seventtype->l_performance == 1 ? '1' : '2') . ($b->date_ ? $b->date_->format('Ymd') : '').($b->start_ ? $b->start_->format('H:i') : '');

                return strcmp($a_order, $b_order);
            }
        );
        foreach ($adates_project_ordered as $this->adate) {
            $this->prepare_program($section);
        }

        foreach ($adates_project as $adate) {

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            if ($this->aproject['project_id'] <= 0) {
                $l_performance = 1;
            }

            if ($l_performance == 1) {
                $this->oinstrumentation->date_id = $adate->id;
                $this->oinstrumentation->getInstrumentation();

                $this->aproject['instrumentation_max'] =
                    ($this->oinstrumentation->instrumentation_max > '' ? 'Max. ' : '') .
                    $this->oinstrumentation->instrumentation_max;
            }

            //*********
            $this->aproject['adates'][$adate->id] = $adate;


            $this->aproject['duties2_sum'] += $adate->duties2;
            //print_r('id='.$adate->id.'#duties2='.$adate->duties2.'#sum='.$this->aproject['duties2_sum'].'#');

            //20241210 ONCUST-4604
            //$adate->block1 = 0 soll auch gezählt werden
            //if ($adate->block1 > 0) {
                // Array index darf nicht eine Zahl sein
                $k_block1 = 'b' . $adate->block1;
                //$k_block1 = $adate->block1;
                //print_r($k.'Y1YY'.$k_block1,'ZZZ'.(array_key_exists($k_block1, $this->aproject['ablock1s'])?'true':'false').'Q1QQ');

                if (!array_key_exists($k_block1, $this->aproject['ablock1s'])) {
                    $this->aproject['ablock1s'][$k_block1] = array('block1' => $adate->block1, 'duties' => 0);

                    //print_r($k.'YYY'.$k_block1,'ZZZ'.(array_key_exists($k_block1, $this->aproject['ablock1s'])?'true':'false').'QQQ');
                }

                $this->aproject['ablock1s'][$k_block1]['duties'] += $adate->duties;
            //}

            $eventtype_code = ($adate->seventtype ? $adate->seventtype->code : '');
            if ($l_performance == 1 || $eventtype_code == 'ENR') {
                if ($this->nPD_id <= 0 || $this->nPD_id == $adate->id) {
                    //print_r('nPD_id='.$this->nPD_id.'#date_id'.$adate->id.'#');
                    $this->aproject['apds'][$adate->id] = $adate;
                }
            }
        }

        // 20230317 ONCUST-1563
        // ** keine PDs - alle anzeigen
        if(sizeof($this->aproject['apds']) == 0) {
            foreach($this->aproject['adates'] as $adate) {
                if ($this->nPD_id <= 0 || $this->nPD_id == $adate->id) {
                    $this->aproject['apds'][$adate->id] = $adate;
                }
            }
        }

        // Informationen aus allen PDs holen
        $count_dress = 0;
        foreach ($this->aproject['apds'] as $this->adate) {
            if ($this->adate->text_2 > '' and !in_array($this->adate->text_2, $this->aproject['apds_text_2'])) {
                $this->aproject['apds_text_2'][] = $this->adate->text_2;
            }

            if ($this->adate->memo_1 > '' and !array_key_exists($this->adate->memo_1, $this->aproject['amemo_1'])) {
                $this->aproject['memo_1'][$this->adate->memo_1] = array(
                    'memo_1' => $this->adate->memo_1,
                    'date_' => $this->adate->date_->format('Ymd')
                );
            }


            //************************

            //***
            //20181102
            //*** Neue Regel für Anzeige Feld " info/prod. (publ.) " = memo 1 hier rot umrahmt :
            //*** Bisher zeigst Du memo 1 aus dem entsprechenden Termin,
            //*** Nun bitte noch an erster Stelle den Inhalt von memo 1 aus dem ersten Konzerttermin hinzufügen
            //*** Beim Fiche Technique für den 4.11 . würde also noch memo 1 aus dem 1.11 . hinzukommen:


            $count_memo_1++;
            if ($count_memo_1 == 1) {
                if ($this->adate->memo_1 > '' and !array_key_exists($this->adate->memo_1, $this->aproject['amemo_1'])) {
                    $this->aproject['memo_1'][$this->adate->memo_1] = array(
                        'memo_1' => $this->adate->memo_1,
                        'date_' => $this->adate->date_->format('Ymd')
                    );
                }
            }

            if ($this->aproject['memo_1']) {
                uasort(
                    $this->aproject['memo_1'],
                    function ($a, $b) {
                        return strcmp($a['date_'], $b['date_']);
                    }
                );
            }

            $this->prepare_conductors();

            $this->prepare_orchestras();
            $this->prepare_persons();
            // 20230112 ONCUST-1005
            // Programm für alle Termine, nicht nur für $this->nPD_id (fiche_technique)
            //$this->prepare_program($section);


            $dress_id = $this->adate->dress_id;
            //$section->addText('dress_id='.$dress_id.'#'.$this->adate->id.'#'.$l_performance.'#'.$this->adate->sdress->name);

            //if ($dress_id > 0 && $this->adate->sdress && $l_performance == 1) {
            if ($dress_id > 0 && $this->adate->sdress) {



                foreach ($this->adate->adate_series as $adate_serie) {

                    $k = $dress_id . '_' . $adate_serie->serie_id;

                    $serie = $adate_serie->sseries->code;

                    if (!array_key_exists($k, $this->aproject['aseries_dresses'])) {
                        $count_dress++;
                        $this->aproject['aseries_dresses'][$k] = array(
                            'dress' => $this->adate->sdress->name,
                            'serie' => $serie
                        );
                    }
                }

                if ($count_dress == 0) {
                    $k = $dress_id . '_0';
                    if (!array_key_exists($k, $this->aproject['aseries_dresses'])) {
                        $this->aproject['aseries_dresses'][$k] = array(
                            'dress' => $this->adate->sdress->name,
                            'serie' => ''
                        );
                    }
                }

            }

            /*
            // 20230112 ONCUST-1005
            // Programm für alle Termine, nicht nur für $this->nPD_id (fiche_technique)
            $this->prepare_program($section);
            */
        }


        //Soloists erst nach allen PDs, sonst ist das Programm nicht komplett
        foreach ($this->aproject['apds'] as $this->adate) {
            $this->prepare_soloists();
        }

    }

    function prepare_orchestras()
    {
        $orchestra_id = $this->adate->orchestra_id;

        if ($orchestra_id > 0 && $this->adate->orchestraaddress) {
            if (!array_key_exists($orchestra_id, $this->aproject['aorchestras'])) {
                $this->aproject['aorchestras'][$orchestra_id] = array(
                    'name' => $this->adate->orchestraaddress->name1,
                    'caption' => '',
                    'instrument' => '',
                    'instrument_en' => '',
                    'adates' => array()
                );
            }

            $this->aproject['aorchestras'][$orchestra_id]['adates'][$this->adate->id] = $this->adate;
        }


    }

    function prepare_persons()
    {

        foreach ($this->adate->adate_persons as $arow) {

            $address_id = $arow->address_id;

            $person_id = $address_id;

            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');
            $addressfunction = ($arow->saddressfunctionitem ? $arow->saddressfunctionitem->name : '');

            $instrument_en = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name2 : '');
            $addressgroup_en = ($arow->saddressgroup ? $arow->saddressgroup->name2 : '');
            $addressfunction_en = ($arow->saddressfunctionitem ? $arow->saddressfunctionitem->name2 : '');

            //20220513 ONCUST-643
            //Is it possible to make all the instruments start with lowercase?

            $instrument = mb_strtolower($instrument);
            $instrument_en = mb_strtolower($instrument_en);

            $ag_f_i = $instrument;

            if(empty($ag_f_i)) {
                $ag_f_i = $addressgroup;
            }

            $ag_f_i_en = $instrument_en;
            if(empty($ag_f_i_en)) {
                $ag_f_i_en = $addressgroup_en;
            }

   
            //20220409
            //Can we make it so that if there is any notes under the other participants that it will override the Address group?
            // Sometimes the artists have many roles at the orchestra and itã??s tetious to change it every time.
            //
            /*
             * 			*** 20170504
			*** 7.	Andere TN
			*** Bitte hinter Instrument nicht noch die Funktion anzeigen
			*** (letzte Anfrage hierzu war, die Bemerkungsspalte anzuzeigen)
			*** zB 06.03.2016

			*** 20170613
			*** Bemerkungen aus andere TN bitte in Klammern anzeigen (ohne Komma nach Instrument) idem grid Solisten Bemerkungen
             * */

            if ($arow->notes > '') {
                $ag_f_i .= ' ('.$arow->notes.')';
                $ag_f_i_en .= ' ('.$arow->notes.')';
            }

            if ($address_id > 0) {
                if (!array_key_exists($address_id, $this->aproject['apersons'])) {
                    $this->aproject['apersons'][$address_id] = array(
                        'name' => $this->getLongName($arow->saddress->name2, '', $arow->saddress->name1),
                        'caption' => '',
                        'instrument' => $ag_f_i,
                        'instrument_en' => $ag_f_i_en,
                        'adates' => array()
                    );
                }
                $this->aproject['apersons'][$address_id]['adates'][$this->adate->id] = $this->adate;
            }
        }
    }

    function prepare_soloists()
    {
        $where = 'AdateWorks.date_id=' . $this->adate->id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'AdateworkSoloists.instrument_id',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name',
                'Sinstrinstruments.name2'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order2')
            ->distinct();

        foreach ($arows as $arow) {
            $artist_id = $arow->artist_id;
            $instrument_id = (int)$arow->instrument_id;
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            $instrument_name2 = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name2 : '');

            $artist_instrument_id = 'a'.$artist_id.'#'.$instrument_id;

            //*** 20161031
            //*** Werkbuchstabe  anzeigen, wo Solist mitwirkt
            //*** Bitte Namen der Adressgruppe CH nicht in Großbuchstaben

            //*** Solisten mit  - trennen (nicht untereinander, Leerzeichen vor und nach -)


            if ($arow->artist_id > 0) {
                if (!array_key_exists($artist_instrument_id, $this->aproject['asoloists'])) {

                    $soloist = $this->getLongName($arow->saddress->name2, '', mb_strtoupper($arow->saddress->name1));

                    if ($this->reporttools->is_CH($artist_id)) {
                        $soloist = $arow->saddress->name1;
                        $instrument = '';
                        $instrument_name2 = '';
                    }

                    $soloist .= ($arow->notes > '' ? ' (' . $arow->notes . ')' : '');

                    $program_soloists = $this->getProgram_Soloist($artist_id, $instrument_id);

                    $soloist .= ($program_soloists > '' ? ' (' . $program_soloists . ')' : '');
                    //$soloist .='#'.$artist_instrument_id;
                    $this->aproject['asoloists'][$artist_instrument_id] = array(
                        'name' => $soloist,
                        'caption' => 'Soloist',
                        'instrument' => mb_strtolower($instrument),
                        'instrument_en' => mb_strtolower($instrument_name2),
                        'adates' => array()
                    );
                }

                $this->aproject['asoloists'][$artist_instrument_id]['adates'][$this->adate->id] = $this->adate;
            }
        }
    }

    function prepare_program($section)
    {


        foreach ($this->adate->adate_works as $awork) {

            $kpart = 'k' . $this->part;

            if (!array_key_exists($kpart, $this->aparts)) {
                //print_r('part='.$kpart.'YYY');
//print_r('title1='.$awork->swork->title1);
//                print($this->aparts);
//                print('ZZZ');
                $this->aparts[$kpart] = array(
                    'part' => $this->part,
                    'aworks' => array(),
                    'aworks_non_intermission' => array(),
                    'datework_ids' => '',
                    'nduration_total' => 0
                );
            }

            $datework_id = $awork->id;
            $work_id = $awork->work_id;
            $nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);

            //$awork->swork->title1 = $work_count.'#'.$awork->swork->title1;
            $kwork_id = 'w' . $work_id;
            // alle Werke des Projektes
            if (!array_key_exists($kwork_id, $this->aproject['aworks'])) {

                //$awork->swork->title1 = $work_count.'#'.$awork->swork->title1;
                if ($awork->swork->l_intermission == 0) {
                    $this->work_count_project++;
                }
                $awork->work_count = $this->work_count_project;
                $awork->work_count2 = $this->work_count_project;

                //nochmal sortieren wegen l_encore
                $awork->order_dw =
                    ($awork->swork->l_encore == 1 ? '1' : '2') . // && Die BIS (zugaben mÃ¼ssen aber unbedingt ans Ende, d.h. nach dem "offiziellen" Programm
                    ($this->adate->seventtype->l_performance == 1 ? '1' : '2') .
                    $this->adate->date_->format('Ymd') .
                    sprintf('%010d', $awork->work_order);


                $this->aproject['nduration_total'] += $nduration; //#'.$nduration.'#'.$awork->duration;
                $this->aproject['aworks'][$kwork_id] = $awork;
                //print_r('kwork_id='.$kwork_id.'#datework_id='.$datework_id.'#');

                // alle Werke im Part
                if (!array_key_exists($kwork_id, $this->aparts[$kpart]['aworks'])) {
                    $this->aparts[$kpart]['nduration_total'] += $nduration; //#'.$nduration.'#'.$awork->duration;
                    $this->aparts[$kpart]['aworks'][$kwork_id] = $awork;
                    if ($awork->swork->l_intermission == 0) {
                        $this->aparts[$kpart]['aworks_non_intermission'][$kwork_id] = $awork;

                    }

                    //print_r('kpart='.$kpart.'#'.'kwork_id='.$kwork_id.'#datework_id='.$datework_id.'#');
                    //print_r('id='.$awork->id.'#'.'datework_ids='.$awork->datework_ids.'#');

                    $this->aparts[$kpart]['datework_ids'] .= ($this->aparts[$kpart]['datework_ids'] > '' ? ',' : '') . $datework_id;

                    //print_r($this->aparts[$kpart]['aworks']);
                }
            }

            /*

            uasort(
                $this->aparts[$kpart]['aworks'],
                function ($a, $b) {
                    return strcmp($a->order_dw, $b->order_dw);
                }
            );
             */

            if ($awork->swork->l_intermission == 1) {
                $this->part++;
            }
        }
    }

    function getTblInfo($section)
    {
        $section->addTextBreak();

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(7.25);
        $this->acol_widths[] = Converter::cmToTwip(5.5);
        $this->acol_widths[] = Converter::cmToTwip(6.75);

        $this->table_width = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->table_width += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.03),
            'cellMarginBottom' => Converter::cmToTwip(0.03),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'width' => $this->table_width,
            'unit' => TblWidth::TWIP
        );

        $aborder_l = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight_dotted
        );

        $aborder_m = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight_dotted
        );

        $aborder_r = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight
        );

        $table = $section->addTable($tableProperties);

        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));

        $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowSpan, $aborder_l));
        $this->getCondSoloPers($cell, $this->styleFont_bold);


        $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
        $this->getServices($cell);

        $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_r));
        $this->getSeriesDress($cell);

        //*** 20171219
        //*** Vor Texte_2 soll nun kommen:
        //*** Wenn services_2 = 1 :  dann " 1 " + " service annuel " als fester Text
        //*** Wenn services_2 >1: dann " 2 " + " services annuels " als fester Text


        $duties2 = $this->aproject['duties2_sum'];


        $aborder_l = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight_dotted
        );

        $aborder_m = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderRight_dotted,
            $this->styleCell_borderLeft_dotted,
        );

        $aborder_r = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight
        );

        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));

        $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowContinue, $aborder_l));

        $cell = $table->addCell($this->acol_widths[1] + $this->acol_widths[2], array_merge($this->cellColSpan2, $aborder_r));

        //20230120 ONCUST-1063
        //services annuels bitte auch anzeigen wenn =0
        //if ($duties2 <> 0) {
/*
        foreach($this->aproject['adates'] as $adate) {
            $cell->addText($adate->id.'#'.$adate->date_->format('d.m.Y').'#'.$adate->duties2);
        }
*/

        $this->aproject['duties2_sum'] += $adate->duties2;

            $textrun = $cell->addTextRun();
            $textrun->addText(htmlspecialchars(($duties2 == 1 ? 'service annuel' : 'services annuels') . ': '), $this->styleFont_bold);
            $textrun->addText(htmlspecialchars($duties2));
        //}

        foreach ($this->aproject['apds_text_2'] as $text_2) {
            $cell->addText(htmlspecialchars($text_2));
        }


        $aborder_l = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight_dotted,
        );

        $aborder_m = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight_dotted
        );

        $aborder_r = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight
        );
        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));

        $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowContinue, $aborder_l));
        $cell = $table->addCell($this->acol_widths[1] + $this->acol_widths[2], array_merge($this->cellColSpan2, $aborder_r));
        $this->getMemo1($cell);
    }

    function getTblProgram($section)
    {
        $section->addTextBreak();

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(1);
        $this->acol_widths[] = Converter::cmToTwip(6.25);
        $this->acol_widths[] = Converter::cmToTwip(10.95);
        $this->acol_widths[] = Converter::cmToTwip(1.3);

        $this->table_width = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->table_width += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.03),
            'cellMarginBottom' => Converter::cmToTwip(0.03),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'width' => $this->table_width,
            'unit' => TblWidth::TWIP
        );


        $aborder = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight
        );

        $table = $section->addTable($tableProperties);

        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));

        $cell = $table->addCell($this->table_width, array_merge($this->cellColSpan4, $aborder, $this->BGColor_Header));
        $cell->addText(htmlspecialchars('Programme'), array_merge($this->styleFont_bold, $this->styleFont_9), $this->styleCell_alignCenter);


        //*** 20180119
        //*** Fiche technique

        //if($this->nPD_id >0) {
        //***
        //SOrtierung soll aus dem Termin übernommen werden .
        //SELECT * FROM crsProgram_Project WHERE crsProgram_Project . Date_id == this . nPD_id ORDER BY DateWork_Work_Order INTO CURSOR crsProgram_Project_Selected_Dates READWRITE

        //*** Part für Fiche technique beschreiben

        // sonst aus allen terminen

        $aborder_l = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight_dotted
        );

        $aborder_m = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight_dotted
        );

        $aborder_r = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight
        );

        $nduration_total = 0;


        foreach ($this->aparts as $apart) {

            if ($this->l_show_parts && sizeof($apart['aworks_non_intermission']) > 1) {

                $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));


                $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));
                $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                $cell->addText(htmlspecialchars('Effectif max. pour la ' . $apart['part'] . ($apart['part'] == 1 ? 'ère' : 'ème') . ' partie'), array_merge($this->styleFont_bold));

                $this->oinstrumentation->date_id = 0;
                $this->oinstrumentation->datework_id = 0;
                $this->oinstrumentation->work_id = 0;
                $this->oinstrumentation->datework_ids = $apart['datework_ids'];
                $this->oinstrumentation->getInstrumentation();
                $this->oinstrumentation->datework_ids = '';

                //*** 20181102
                //*** Anzeige Besetzung max 1. Konzerthälfte und 2. Konzerthälfte wie beim max für das Konzert, d.h. die Streicher sollen auch hier am Ende kommen (warum auch immer sie das irgendwann mal anders wollten)
                //*** So: 4.4.4.3 / 9.3.5.1 / timp / 2 perc / 2 hrp / 14 extra // 13.11.9.7.6  statt 13.11.9.7.6 //4.4.4.3 / 9.3.5.1 / timp / 2perc / 2hrp / 14extra

                //*this.oTable.cells(this.oTable.nnum_of_rows,3).value = '\b ' + this.oInstrumentation.cStrings + ' //' + this.oInstrumentation.cHarmonie
                $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));

                //$cell->addText(htmlspecialchars($apart['datework_ids']), array_merge($this->styleFont_bold));
                $cell->addText(htmlspecialchars($this->oinstrumentation->effectiv), array_merge($this->styleFont_bold));
                $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
            }



            foreach ($apart['aworks'] as $awork) {
                //fiche_technique
                if ($this->nPD_id > 0) {
                    $l_played = false;

                    foreach ($this->aproject['apds'] as $adate) {
                        foreach ($adate->adate_works as $works) {
                            if ($awork->work_id == $works->work_id) {
                                $l_played = true;
                            }
                        }
                    }
                    if(!$l_played) {continue;}
                }

                $nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);
                $cduration = '';
                if ($nduration > 0) {
                    $cduration = ' (' . $nduration . "')";
                }

                $nduration_total += $nduration;

                $title_arr_prem = $awork->swork->title1 .
                    ($awork->arrangement > '' ? ' (' . $awork->arrangement . ')' : '') .
                    (($awork->sworkpremiere && $awork->sworkpremiere->name > '') ? ' - ' . $awork->sworkpremiere->name : '');

                // 20230317 ONCUST-1563
                // Komponisten Namen bitte in Großbuchstaben:
                $composer = ($awork->swork->scomposer ? trim($awork->swork->scomposer->firstname . ' ' . mb_strtoupper($awork->swork->scomposer->lastname)) : '');
                $nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);
                $cduration = '';
                if ($nduration > 0) {
                    $cduration = $nduration . "'";
                }



                $this->oinstrumentation->work_id = $awork->work_id;
                $this->oinstrumentation->datework_id = 0;
                $this->oinstrumentation->datework_ids = '';
                $this->oinstrumentation->date_id = 0;
                $this->oinstrumentation->getInstrumentation();

                $instrumentation_effectiv_w = '';
                if ($this->oinstrumentation->instrumentation > '') {
                    $instrumentation_effectiv_w = $this->oinstrumentation->effectiv;
                }
                //$instrumentation_effectiv_w = $awork->work_id.'#'.$instrumentation_effectiv_w;
                $this->oinstrumentation->datework_id = $awork->id;
                $this->oinstrumentation->work_id = 0;
                $this->oinstrumentation->date_id = 0;
                $this->oinstrumentation->getInstrumentation();

                $instrumentation_effectiv_dw = '';
                if ($this->oinstrumentation->instrumentation > '') {
                    $instrumentation_effectiv_dw = $this->oinstrumentation->effectiv;
                }


                $l_encore = $awork->l_encore;
                $work_count = $this->getColumnLetter($awork->work_count);

                if ($awork->swork->l_intermission == 1) {
                    $composer = '';
                    $work_count = '';
                    //$cduration = '';
                    //continue;
                }

//                if ($awork->swork->l_intermission == 0) {

                $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));


                $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));
                $cell->addText(htmlspecialchars($work_count), array_merge($this->styleFont_bold), $this->styleCell_alignCenter);

                if ($awork->l_encore == 1) {
                    $cell->addText(htmlspecialchars('BIS'), array_merge($this->styleFont_bold), $this->styleCell_alignCenter);
                }


                $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                $cell->addText(htmlspecialchars($composer), array_merge($this->styleFont_bold));

                $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                $cell->addText(htmlspecialchars($title_arr_prem), array_merge($this->styleFont_bold, $this->styleFont_italic));

                $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                $cell->addText(htmlspecialchars($cduration), array_merge(), $this->styleCell_alignCenter);

                if ($awork->swork->l_intermission == 1) {
                    continue;
                }

                /*                $aborder_l = array_merge(
                                    $this->styleCell_borderTop_none,
                                    $this->styleCell_borderBottom_none,
                                    $this->styleCell_borderLeft,
                                    $this->styleCell_borderRight_dotted
                                );

                                $aborder_m = array_merge(
                                    $this->styleCell_borderTop_none,
                                    $this->styleCell_borderBottom_none,
                                    $this->styleCell_borderLeft_dotted,
                                    $this->styleCell_borderRight_dotted
                                );

                                $aborder_r = array_merge(
                                    $this->styleCell_borderTop_none,
                                    $this->styleCell_borderBottom_none,
                                    $this->styleCell_borderLeft_dotted,
                                    $this->styleCell_borderRight
                                );
                */
                if ($this->oinstrumentation->soloinstr > '') {
                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('soliste(s)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->soloinstr), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));

                }

                if ($this->l_show_vocals) {
                    if ($this->oinstrumentation->vocals > '') {
                        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                        $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                        $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                        $cell->addText(htmlspecialchars('chœur(s)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                        $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                        $cell->addText(htmlspecialchars($this->oinstrumentation->vocals), array_merge());

                        $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                    }
                }
                // ***20170117
                // ***Zeile immer anzeigen
                // *IF !EMPTY(this.oInstrumentation.cHarmonie)
                // *** 20170216
                // *** Zeile "harmonie" in "effectif" umbenennen (alle Berichte) und
                // *** bitte in diese Zeile " effectif" auch nochmal die Anzahl der Str hinzufügen (in allen Berichten)

                $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                $cell->addText(htmlspecialchars('effectif'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                $cell->addText(htmlspecialchars($this->oinstrumentation->effectiv), array_merge());

                $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));

                //*** 20150624
                //*** Die Zeile " Cordes " muss in jedem Fall hier erscheinen, auch wenn sie noch nicht ausgefüllt ist.
                if ($this->l_show_strings) {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('cordes'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->strings), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                if ($this->oinstrumentation->texte > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('remarques (*)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->texte), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                if ($this->oinstrumentation->percussions > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('percussions'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->percussions), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                //*** 20160125
                //*** 2. Spalte: schreibe "claviers" statt "keyboards" (s. Anhang)
                //***clavier wird : clavier(s)

                if ($this->l_show_keyboards && $this->oinstrumentation->keyboards > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('clavier(s)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->keyboards), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }


                if ($this->l_show_extras && $this->oinstrumentation->extras > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('extra(s)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars($this->oinstrumentation->extras), array_merge());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                //*** 20150216
                //*** Diese 4 Zeilen sind neu
                //*** 20170117
                //*** (emerkung, wenn vorhanden) - bisher ist diese Zeile immer da
                if ($this->oinstrumentation->details > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));
                    //*** remarques sur l'effectif wird: note(s)
                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('note(s)'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $this->reporttools->addMemo($cell, $this->oinstrumentation->details, array());

                    //$cell->addText(htmlspecialchars('datework_id='.$this->oinstrumentation->datework_id), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }
                // *** 20170911
                // *** Linie mit Inhalt aus datesworks_notes soll unter die instrumentation_notes noch hinzugefügt werden :
                $notes = $this->oinstrumentation->notes;
                //$notes = 'XXXXX';
                if ($this->l_show_notes && $notes > '') {

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars('note(s) non publiques'), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $this->reporttools->addMemo($cell, $notes, array());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                if ($this->l_show_addInstrFields) {
                    $this->prepareLibrary($awork->work_id);


                    //*** 20160125
                    //*** 4. bei "matériel d'orchestre" die Edition hinzufügen
                    //*** es wäre dann so:
                    //*** Matériel complet   Kalmus   F.14
                    //*** pp                           Hamelle  F. 14
                    //*** Type / Edition / N° archive
                    //*** es wird eine Zeile pro Kombination ausgegeben


                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars("matériel d'orchestre"), array_merge($this->styleFont_underline), $this->styleCell_alignRight);


                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $this->reporttools->addMemo($cell, $this->cLibrary_TPC, array());


                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));

                    $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                    $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                    $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                    $cell->addText(htmlspecialchars("notes bibliothèque"), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                    $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                    $this->reporttools->addMemo($cell, $this->cLibrary_Notes, array());

                    $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));
                }

                // *** 20180125
                // *** TM2 was ist der Unterschied wischen Zeile effectif und Effectif oeuvre?
                // *** wenn Effectif Oeuvre die Standard- Instrumentation ist, dann bitte ganze Zeile löschen
                // *** dann reicht die Zeile effectif mit der Werkbesetzung des Werkes am Termin

                if ($this->l_show_effectiv_osr) {
                    if ($instrumentation_effectiv_w > '' && $instrumentation_effectiv_dw <> $instrumentation_effectiv_w) {
                        $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
                        $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

                        $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));
                        $cell->addText(htmlspecialchars("Effectif Œuvre"), array_merge($this->styleFont_underline), $this->styleCell_alignRight);

                        $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));
                        $cell->addText(htmlspecialchars($instrumentation_effectiv_w), array_merge());
                        //$cell->addText(htmlspecialchars($instrumentation_effectiv_dw), array_merge());
                        //$cell->addText(htmlspecialchars($awork->work_id), array_merge());
                        //$cell->addText(htmlspecialchars($awork->swork->title1), array_merge());


                        $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));

                    }
                }
            }
        }

        if ($this->l_show_effectiv) {

            //*** 20181102
            //*** effectif max kommt aus dem ersten Konzert, was nicht sein darf:
            //***
            //*** 20190520
            //*** Kannst Du in so einem Fall nicht die max Besetzung aller Werke des Projekts ausgeben?
            if ($this->cReportType == 'fiche_technique' && $this->nPD_id > 0) {

                //*** 20191003
                //*** Bericht soll max instrumentation pro Performance ausgeben.

                //*** 20191008
                //*** this.nPD_id>0
                $this->oinstrumentation->datework_ids = '';
                $this->oinstrumentation->season_id = 0;
                $this->oinstrumentation->project_id = 0;
                $this->oinstrumentation->datework_id = 0;
                $this->oinstrumentation->work_id = 0;
                $this->oinstrumentation->date_id = $this->nPD_id;
            } else {
                $this->oinstrumentation->season_id = $this->aproject['season_id'];
                $this->oinstrumentation->project_id = $this->aproject['project_id'];
            }

            //$this->oinstrumentation->prepareRow();

            $this->oinstrumentation->l_maxinstr = true;
            $this->oinstrumentation->getInstrumentation();
            $this->oinstrumentation->l_maxinstr = false;


            //*** 20161107
            //*** effectif max : bitte noch Anzahl Musiker hinzufügen: xx musiciens

            //*** 20161116
            //*** 8.	Gesamtdauer ganz unten rechts anzeigen (vgl Bild ganz oben 115')

            $aborder_l = array_merge(
                $this->styleCell_borderTop_dotted,
                $this->styleCell_borderBottom,
                $this->styleCell_borderLeft,
                $this->styleCell_borderRight_dotted
            );

            $aborder_m = array_merge(
                $this->styleCell_borderTop_dotted,
                $this->styleCell_borderBottom,
                $this->styleCell_borderLeft_dotted,
                $this->styleCell_borderRight_dotted
            );

            $aborder_r = array_merge(
                $this->styleCell_borderTop_dotted,
                $this->styleCell_borderBottom,
                $this->styleCell_borderLeft_dotted,
                $this->styleCell_borderRight
            );

            $table->addRow(Converter::cmToTwip($this->tblprogram_row_h));
            $cell = $table->addCell($this->acol_widths[0], array_merge($aborder_l));

            $cell = $table->addCell($this->acol_widths[1], array_merge($aborder_m));


            $textrun = $cell->addTextRun($this->styleCell_alignRight);

            $textrun->addText(htmlspecialchars("Effectif max : "), array_merge($this->styleFont_bold));
            $textrun->addText(htmlspecialchars(($this->oinstrumentation->nharmonie + $this->oinstrumentation->nstrings) . " musiciens"), array_merge());

            $cell = $table->addCell($this->acol_widths[2], array_merge($aborder_m));


            //$cell->addText(htmlspecialchars($this->cReportType.'#'.$this->nPD_id.'#'.$this->oinstrumentation->effectiv), array_merge());
//
            //4. bitte in der max effectif Zeile keine * und instrument_text (wie hier bei Klavier) ausgeben - die Details sollen nur oben bei den Werken direkt stehen
            //$cell->addText(htmlspecialchars($this->oinstrumentation->effectiv), array_merge());
            $cell->addText(htmlspecialchars($this->oinstrumentation->effectiv), array_merge());


            $cell = $table->addCell($this->acol_widths[3], array_merge($aborder_r));

            //*** 20170329
            //*** 10.	Wenn es 2 unterschiedliche Programme gibt, dann im Fiche Bibliothèque keine totale Dauer angeben, im Fiche technique passt es, da ja dort ein Blatt pro Konzert

            //$cell->addText(implode(',', $this->aproject['apds_program']).'#'.sizeof($this->aproject['apds_program']).'#'.$cReportType);
            if (sizeof($this->aproject['apds_program']) <= 1 or $this->cReportType == 'fiche_technique') {
                $cell->addText(htmlspecialchars($nduration_total . "'"), array_merge(), $this->styleCell_alignCenter);
            }
        }
    }

    function prepareLibrary($work_id)
    {

        $this->cLibrary_Notes = '';
        $this->cLibrary_TPC = '';
        $this->cLibrary_Types = '';
        $this->cLibrary_Catalogs = '';
        $this->cLibrary_Notes = '';
        $this->cLibrary_Publications = '';

        $Alibraries = TableRegistry::getTableLocator()->get('Alibraries');


        $arows = $Alibraries
            ->find('all')
            ->contain(
                [
                    'Publisheraddresses',
                    'Slibrarytypes',
                    'AlibrScores',
                    'Spublications' => ['Publisheraddresses'],
                    'Owneraddresses'
                ]
            )
            ->where(['Alibraries.work_id = ' => $work_id])
            ->orderAsc('Alibraries.catalog');

        //*** cLibrary_TPC
        //*** Type / Edition / N° archive

        $aTPC = array();
        $aLibraryTypes = array();
        $aLibrary_catalogs = array();
        $aLibrary_notes = array();
        $aLibrary_Publications = array();

        foreach ($arows as $arow) {

            $librarytype = '';
            $publication_publisher = '';
            $catalog = '';

            if ($arow->catalog) {
                $catalog = $arow->catalog;
            }
            if ($arow->slibrarytype) {
                $librarytype = $arow->slibrarytype->name;
            }

            if ($arow->spublication && $arow->spublication->publisheraddress) {
                $publication_publisher = $arow->spublication->publisheraddress->name1 . ($arow->spublication->publisheraddress->name2 > '' ? ', ' : '') . $arow->spublication->publisheraddress->name2;
            }

            if ($librarytype > '') {
                $TPC = trim(trim($librarytype . ' ' . $publication_publisher) . ' ' . $catalog);
                if (!in_array($TPC, $aTPC)) {
                    $aTPC[] = $TPC;
                }

                //*** cLibrary_Types
                if (!in_array($librarytype, $aLibraryTypes)) {
                    $aLibraryTypes[] = $librarytype;
                }
            }

            //*** cLibrary_Catalogs
            if ($catalog > '') {
                if (!in_array($catalog, $aLibrary_catalogs)) {
                    $aLibrary_catalogs[] = $catalog;
                }

            }

            //*** cLibrary_Notes
            if ($arow->notes > '') {
                if (!in_array($arow->notes, $aLibrary_notes)) {
                    $aLibrary_notes[] = $arow->notes;
                }

            }

            //*** cLibrary_Publications
            if ($publication_publisher > '') {
                if (!in_array($publication_publisher, $aLibrary_Publications)) {
                    $aLibrary_Publications[] = $publication_publisher;
                }
            }
        }


        $this->cLibrary_TPC = implode("\n", $aTPC);
        $this->cLibrary_Types = implode('; ', $aLibraryTypes);
        $this->cLibrary_Catalogs = implode('; ', $aLibrary_catalogs);
        $this->cLibrary_Notes = implode('; ', $aLibrary_notes);
        $this->cLibrary_Publications = implode('; ', $aLibrary_Publications);
    }

    function getTblPlanification($section)
    {
        $section->addTextBreak();

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(1.28);
        $this->acol_widths[] = Converter::cmToTwip(1.28);
        $this->acol_widths[] = Converter::cmToTwip(2.25);
        $this->acol_widths[] = Converter::cmToTwip(5.69);
        $this->acol_widths[] = Converter::cmToTwip(4.25);
        $this->acol_widths[] = Converter::cmToTwip(3.45);
        $this->acol_widths[] = Converter::cmToTwip(1.3);

        $this->table_width = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->table_width += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.03),
            'cellMarginBottom' => Converter::cmToTwip(0.03),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'width' => $this->table_width,
            'unit' => TblWidth::TWIP
        );

        $aborder = array_merge(
            $this->styleCell_borderTop,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight
        );

        $table = $section->addTable($tableProperties);

        $table->addRow(Converter::cmToTwip($this->tbl_row_h));

        $cell = $table->addCell($this->table_width, array_merge($this->cellColSpan7, $aborder, $this->BGColor_Header));
        $cell->addText(htmlspecialchars('Planification'), array_merge($this->styleFont_bold, $this->styleFont_9), $this->styleCell_alignCenter);


        //*** 20161209
        //*** 1.	Aktivitäten der Gruppe Code OSRsans grau
        //*** 2.	Performances ohne Endzeit
        //*** 3.	Nach Terminart adates_text  (nut allen Text vor / anzeigen (adates_text mit / hab ich aber noch nicht in Daten gesehen))
        //*** 4.	Bei Performances nach Terminart Code Serie
        //*** 5.	Werkbuchstaben in Klammern hinter Terminart (letzte Stelle)
        //*** 6.	Spalte 4 alles fett
        //*** 7.	 Spalte 5 = adates_notes
        //*** 8.	Spalte 6 = zusätzl. Aktivitäten
        //*** 9.
        //

        // *** 20170117
        // *** alle Termine
        // *** Termine des Projekttyps  CC oder PP, hier nur die Termine aus dem Filter anzeigen
        //
        $aborder_l = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft,
            $this->styleCell_borderRight_dotted
        );

        $aborder_m = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight_dotted
        );

        $aborder_r = array_merge(
            $this->styleCell_borderTop_dotted,
            $this->styleCell_borderBottom_dotted,
            $this->styleCell_borderLeft_dotted,
            $this->styleCell_borderRight
        );

        $cday_old = '#';

        // *** 20170117
        // *** alle Termine
        // *** Termine des Projekttyps  CC oder PP, hier nur die Termine aus dem Filter anzeigen

        $count = 0;
        foreach ($this->aproject['adates'] as $adate) {
            $count++;

            if (sizeof($this->aproject['adates']) == $count) {
                $aborder_l = array_merge(
                    $this->styleCell_borderTop_dotted,
                    $this->styleCell_borderBottom,
                    $this->styleCell_borderLeft,
                    $this->styleCell_borderRight_dotted
                );

                $aborder_m = array_merge(
                    $this->styleCell_borderTop_dotted,
                    $this->styleCell_borderBottom,
                    $this->styleCell_borderLeft_dotted,
                    $this->styleCell_borderRight_dotted
                );

                $aborder_r = array_merge(
                    $this->styleCell_borderTop_dotted,
                    $this->styleCell_borderBottom,
                    $this->styleCell_borderLeft_dotted,
                    $this->styleCell_borderRight
                );
            }

            $this->date_id = $adate->id;

            $cEventTypeGroup_Code = ($adate->seventtype && $adate->seventtype->seventtypegroup ? $adate->seventtype->seventtypegroup->code : '');

            $cday = substr($this->reporttools->getWeekday($adate->date_), 0, 3) . ' ' . $adate->date_->format('j') . '. ' . mb_strtolower($this->reporttools->getMonthName($adate->date_)) . ' ' . $adate->date_->format('Y');
            $planninglevel = $adate->planninglevel;
            $l_logic_1 = $adate->l_logic_1;
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

            if ($this->aproject['project_id'] <= 0) {
                $l_performance = 1;
            }
            /*
                        if ($cday_old == $cday) {
                            $cday_old = $cday;
                            $cday = '';
                        } else {
                            $cday_old = $cday;
                        }
            */

            $start = '';
            if ($adate->start_) {
                $start = $adate->start_->format('H') . 'h' . $adate->start_->format('i');
            }

            $end = '';
            if ($adate->end_) {
                $end = $adate->end_->format('H') . 'h' . $adate->end_->format('i');
            }

            $start_end = $start;


            //*** 20170206
            //*** Uhrzeiten immer mit Minuten anzeigen: 10h00 statt 10h
            //*** 20170117
            //*** Keine Enduhrzeit bei Projekttyp Code CC
            if ($l_performance == 0 && !($cEventTypeGroup_Code == 'CC')) {
                $start_end .= ($end > '' ? ' - ' : '') . $end;
            }

            $location = $this->getLocation($adate, true);
            $ShortProgram = $this->getShortProgram($adate);

            //*** 20170206
            //*** Termine der Gruppe Code OSR fett
            //*** bitte nicht Uhrzeit, adates_notes und zusätzl. Aktivitäten fett machen, wenn es eine Performance ist.

            //*** 20170216
            //*** Ich hatte nochmal nachgefragt, wegen fett oder nicht fett aller Terminarten in 4. Spalte im Teil Planification:
            //*** Bitte zusätzlich auch die Terminarten der Gruppe Code LYR fett machen

            //*** 20170217
            //*** verdammt, hab ich verwechselt: den Code LYR gibt es nur bei Projekttypen aber nicht bei Terminartengruppen. Kannst Du es ändern und auf Code GT gehen für das Fett machen?

            $abold_perf = array();
            if ($l_performance == 1 || $cEventTypeGroup_Code == 'OSR' || $cEventTypeGroup_Code == 'GT') {
                $abold_perf = $this->styleFont_bold;
            }

            //*** 20170117
            //*** In der Schedule-Tabelle sollten die Terminarten der Gruppe mit Code OSRsans ausgegrauit werden:

            //*** 20170216
            //*** Grau (und nicht fett) werden sollen noch die Termine der Gruppe AdC (zusätzlich zu den Terminen der Gruppe OSRsans)

            //*** 20170329
            //*** 5.	Abschnitt : Planification
            //*** Diejenigen Terminarten, die im Moment grau angezeigt werden, sollen nun in Schriftart Arial Narrow sein
            //*** (brauchst Du die genaue Definition dieser Terminarten nochmal?)

            //*** 20170412
            //*** sorry und nicht mehr grau sondern schwarz

            $afont_style = array();
            if (mb_strtoupper($cEventTypeGroup_Code) == mb_strtoupper('OSRsans') || mb_strtoupper($cEventTypeGroup_Code) == mb_strtoupper('AdC')) {
                $afont_style = array_merge($afont_style, array('name' => 'Arial Narrow'));
            }

            //*** 20170329
            //*** 7.	Bericht Fiche Bibliotheque
            //*** Wenn in den zusätzlichen Daten Logik 1 Ja angeklickt ist, dann Terminart in rot im Terminpla (Planification) anzeigen

            if ($this->cReportType == 'fiche_bibliotheque' && $l_logic_1 == 1) {
                $afont_style = array_merge($afont_style, array('color' => 'red'));
            }

            //*** 20170704
            //*** 1.	Berichte werden auch für Termine aus verschiedenen Niveaus benutzt in Dates: Wunsch: alle Termine aus Niveau 2 sollen in grün angezeigt werden (so wie bei Bible)
            if ($planninglevel == 2) {
                $afont_style = array_merge($afont_style, $this->aPL2_cf);
            }
            if ($planninglevel == 3) {
                $afont_style = array_merge($afont_style, $this->aPL3_cf);
            }

            $table->addRow(Converter::cmToTwip($this->tbl_row_h));

            if ($cday_old <> $cday) {
                $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowSpan, $aborder_l));
                $cell->addText(htmlspecialchars(substr($this->reporttools->getWeekday($adate->date_), 0, 3) . '.'), array_merge());

                $cell = $table->addCell($this->acol_widths[1], array_merge($this->cellRowSpan, $aborder_m));
                $cell->addText(htmlspecialchars($adate->date_->format('j') . ' ' . mb_strtolower($this->reporttools->getMonthName_Short($adate->date_))), array_merge());
            } else {
                $cell = $table->addCell($this->acol_widths[0], array_merge($this->cellRowContinue, $aborder_l));
                $cell = $table->addCell($this->acol_widths[1], array_merge($this->cellRowContinue, $aborder_m));
            }
            $cday_old = $cday;

            $cell = $table->addCell($this->acol_widths[2], array_merge($this->cellRowSpan, $aborder_m));
            $cell->addText(htmlspecialchars($start_end), array_merge($afont_style), $this->styleCell_alignCenter);

            $cell = $table->addCell($this->acol_widths[3], array_merge($this->cellRowSpan, $aborder_m));
            $cell->addText(htmlspecialchars(
                trim(($adate->seventtype ? $adate->seventtype->name : '') . ' ' . $adate->text . ($l_performance == 1 ? ' ' . $this->getSeries_Code($adate) : '')) .
                ($ShortProgram > '' ? ' (' . $ShortProgram . ')' : '')
            ), array_merge($afont_style, $abold_perf));

            $cell = $table->addCell($this->acol_widths[4], array_merge($this->cellRowSpan, $aborder_m));
            $this->reporttools->addMemo($cell, $adate->notes, array_merge($afont_style));

            //*** 20170206
            //*** Spalte 6 zusätzl. Aktivitäten
            //*** Trennung mehrerer Aktivitäten mit Semikolon:
            //*** " direct+UER ; dédicace après (Très Classic) "

            //*** Keine Bindestrich zwischen Name und Text:
            //*** So " différé+UER 18/1 " statt " différé+UER - 18/1 "

            //*** 20171002
            //*** jede zusätzl. Aktivität bitte in neuer Zeile anzeigen
            //*this.oTable.cells(this.oTable.nnum_of_rows,6).value = getAddactivities(this.nDate_id, '; ') && \par

            $cell = $table->addCell($this->acol_widths[5], array_merge($this->cellRowSpan, $aborder_m));
            $this->getAddactivities($cell, $adate, array_merge($afont_style));

            $cell = $table->addCell($this->acol_widths[6], array_merge($this->cellRowSpan, $aborder_r));
            $cell->addText(htmlspecialchars($location), array_merge($afont_style), $this->styleCell_alignCenter);
        }
    }

    function getCondSoloPers($cell, $styleText = array(), $styleCell = array())
    {

        $apersons = array_merge($this->aproject['aconductors'], $this->aproject['asoloists'], $this->aproject['apersons']);

        foreach ($apersons as $aperson) {
            $textrun = $cell->addTextRun($styleCell);
            $textrun->addText(htmlspecialchars($aperson['name']), array_merge($styleText));


            if ($aperson['instrument'] > '') {
                //$textrun->addText(', ' . htmlspecialchars(htmlentities($aperson['instrument'], ENT_COMPAT | ENT_HTML401, "UTF-8")), array_merge($styleText));
                $textrun->addText(', ' . htmlspecialchars($aperson['instrument']), array_merge($styleText));


                /*// var_dump
                ob_start();
                var_dump($aperson['instrument']);
                $debug_dump = ob_get_clean();
                $cell->addText($debug_dump);
*/
                //$textrun->addText(', ' . var_dump($aperson['instrument']), array_merge($styleText));
            }


        }
    }

    function getServices($cell)
    {
        //*** Services per Block1
        //*** services : 4/pér5 + 2/pér6

        //*** 20170206
        //*** services: einfügen und danach die Anzahl der Dienste pro Periode so anzeigen wie in der Bibel (und nicht wie im fiche technique ursprünglich, sorry)
        //*** Nach diesem Muster:
        //*** pér. 3 : 0 serv. und nicht 0/pér3

        //** 20170216
        //*** alle Termine

        $cblock1 = '';

        foreach ($this->aproject['ablock1s'] as $block1 => $ablock) {

            $cblock1 .= ($cblock1 > '' ? ' / ' : '') .
                (sizeof($this->aproject['ablock1s']) > 1 ? 'pér. ' . $ablock['block1'] . ': ' : '') .
                $ablock['duties'] . ' serv.';

        }
        //*** 20170207
        //*** bitte hier noch fett "services" einfügen
        if ($cblock1 > '') {
            $textrun = $cell->addTextRun();
            $textrun->addText(htmlspecialchars('services: '), $this->styleFont_bold);
            $textrun->addText(htmlspecialchars($cblock1));
        }
    }


    function getMemo1($cell)
    {

        //***
        //20181102
        //*** Neue Regel für Anzeige Feld " info/prod. (publ.) " = memo 1 hier rot umrahmt :
        //*** Bisher zeigst Du memo 1 aus dem entsprechenden Termin,
        //*** Nun bitte noch an erster Stelle den Inhalt von memo 1 aus dem ersten Konzerttermin hinzufügen
        //*** Beim Fiche Technique für den 4.11 . würde also noch memo 1 aus dem 1.11 . hinzukommen:

        foreach ($this->aproject['memo_1'] as $amemo_1) {
            $this->reporttools->addMemo($cell, $amemo_1['memo_1']);
        }
    }

    function getSeriesDress($cell)
    {
        $styleTabs =
            array(
                'tabs' => array(
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.25))
                )
            );

        //*** 20170206
        //*** Wenn Kleidung überall gleich ist, dann ohne Seriencode nur einmal anzeigen

        $count = 0;
        foreach ($this->aproject['aseries_dresses'] as $aseries_dress) {
            $count++;
            if ($count > 1) {

            }

            $textrun = $cell->addTextRun($styleTabs);
            if ($count == 1) {
                $textrun->addText(htmlspecialchars('tenue: '), array_merge($this->styleFont_bold));
            }
            // 20230317
            // Kleidung: Tabulator wegnehmen und nur ein Leerzeichen nach dem Doppelpunkt
            //$textrun->addText("\t");

            if (sizeof($this->aproject['aseries_dresses']) > 1) {
                $textrun->addText(htmlspecialchars($aseries_dress['serie']) . ' ');
            }
            $textrun->addText(htmlspecialchars($aseries_dress['dress']) . ' ');


        }

    }

    function getTblDates($section)
    {

        if (!$this->l_Empty_CC_PP) {
            //*** 20170227
            //*** Dritte Spalte im Kopf nun auch zentrieren und Spalte auf 1.55 verkleinern….
            //*** Und 1. Spalte auf 1.28 cm (Statt 1,25)

            //*** 20170329
            //*** b)	insgesamt soll die Breite aller Spalten nicht 19.50 überschreiten
            //*** daher :         1. Zeile, wo Liste 20 steht: 19.50 (Statt 19.53)
            //*** letzte Spalte (wie die Werkbuchstaben drin stehen: 5.72 (Statt 5.75):

            //*7.25 5.5 6.75

            $this->acol_widths = array();
            $this->acol_widths[] = Converter::cmToTwip(1.28);
            $this->acol_widths[] = Converter::cmToTwip(2.75);
            $this->acol_widths[] = Converter::cmToTwip(1.55);
            $this->acol_widths[] = Converter::cmToTwip(7.5 + 0.7);
            $this->acol_widths[] = Converter::cmToTwip(5.72);
            $cellColSpan = $this->cellColSpan5;
        } else {
            //*** 20170329
            //*** 2.	Bei Produktion Typ Code PP Spalten anpassen ??
            //*** (1.	Spalte) 2.56 cm ; (2. Spalte) 2.25 cm ; (3. Spaltel) 13.39 cm ; (letzte Spalte) 1.3 cm
            //*** 2017076
            //*** <tm> es passt doch noch nicht, bei Samstag gibt es noch einen Zeilenumbruch, bitte auf 2,7 setzen

            $this->acol_widths = array();
            $this->acol_widths[] = Converter::cmToTwip(2.7);
            $this->acol_widths[] = Converter::cmToTwip(2.25);
            $this->acol_widths[] = Converter::cmToTwip(13.35);
            $this->acol_widths[] = Converter::cmToTwip(1.3);

            $cellColSpan = $this->cellColSpan4;
        }

        $this->table_width = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->table_width += $col_width;
        }

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => $this->borderColor_Header,
            'cellMarginTop' => Converter::cmToTwip(0.03),
            'cellMarginBottom' => Converter::cmToTwip(0.03),
            'width' => $this->table_width,
            'unit' => TblWidth::TWIP
        );

        $this->table = $section->addTable($tableProperties);

        $this->table->addRow(Converter::cmToTwip($this->tbl_row_h));

        $styleCell = array();

        //*** 20170117
        //*** Projekte Typ Code PP oder CC und Terminarten ohne Projekt: andere Formatierung, vgl. Modell Rapport Période S. 3 Bsp
        //*** - Titel kursiv
        //*** IF !EMPTY(this.cProjectType_Code) AND INLIST(this.cProjectType_Code, 'PP', 'CC')


        $aitalic = array();
        if ($this->l_Empty_CC_PP) {
            $aitalic = $this->styleFont_italic;
        }

        //*** 20170117
        //*** erste Zeile kann andere Farbe haben

        $aborder = array_merge(
            $this->styleCell_borderTop_12,
            $this->styleCell_borderBottom_none_grey,
            $this->styleCell_borderLeft_12,
            $this->styleCell_borderRight_12
        );

        $cell = $this->table->addCell($this->table_width, array_merge($styleCell, $cellColSpan, $this->BGColor_Header, $aborder));
        //$cell = $this->table->addCell($this->table_width, array_merge($styleCell, $cellColSpan, $this->styleBG_grey, $aborder));


        $cell->addText(htmlspecialchars($this->cProject), array_merge($this->styleFont_bold, $aitalic, $this->styleFont_22), $this->styleCell_alignCenter);

        $cday_old = '#';
        $count_pds = 0;
        foreach ($this->aproject['apds'] as $adate) {
            $count_pds++;

            if ($this->nPD_id > 0 && $adate->id <> $this->nPD_id) {
                continue;
            }

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            if ($this->aproject['project_id'] <= 0) {
                $l_performance = 1;
            }

            $cday = substr($this->reporttools->getWeekday($adate->date_), 0, 3) . ' ' . $adate->date_->format('j') . '. ' . mb_strtolower($this->reporttools->getMonthName_Short($adate->date_)) . ' ' . $adate->date_->format('Y');

            if ($cday_old == $cday) {
                $cday_old = $cday;
//                $cday = '';
            } else {
                $cday_old = $cday;
            }

            $cEventTypeGroup_Code = ($adate->seventtype && $adate->seventtype->seventtypegroup ? $adate->seventtype->seventtypegroup->code : '');
            $ShortProgram = $this->getShortProgram($adate);

            // *** 20170206
            // *** Uhrzeiten immer mit Minuten anzeigen: 10h00 statt 10h
            $start = '';
            if ($adate->start_) {
                $start = $adate->start_->format('H') . 'h' . $adate->start_->format('i');
            }

            $start_end = $this->reporttools->getTime($adate);


            //*** 20170206
            //*** Termine der Gruppe Code OSR fett

            $styleFont = array();
            if ($l_performance == 1 or $cEventTypeGroup_Code == 'OSR') {
                $styleFont = $this->styleFont_bold;
            }

            //$this->styleCell_borderTop_dotted,
            //$this->styleCell_borderRight_dotted
            $aborder_l = array_merge(
                $this->styleCell_borderLeft_12
            );
            //$this->styleCell_borderTop_dotted,
            //$this->styleCell_borderLeft_dotted,
            $aborder_r = array_merge(
                $this->styleCell_borderRight_12
            );

            //$this->styleCell_borderTop_dotted,
            //$this->styleCell_borderLeft_dotted,
            //$this->styleCell_borderRight_dotted

            $aborder_m = array_merge();

            if ($count_pds == sizeof($this->aproject['apds'])) {
                $aborder_l = array_merge($aborder_l, $this->styleCell_borderBottom_12);
                $aborder_r = array_merge($aborder_r, $this->styleCell_borderBottom_12);
                $aborder_m = array_merge($aborder_m, $this->styleCell_borderBottom_12);

            } else {
                /*
                $aborder_l = array_merge($aborder_l, $this->styleCell_borderBottom_dotted);
                $aborder_r = array_merge($aborder_r, $this->styleCell_borderBottom_dotted);
                $aborder_m = array_merge($aborder_m, $this->styleCell_borderBottom_dotted);
                */
            }


            $this->table->addRow(Converter::cmToTwip($this->tbl_row_h));

            $styleCell = array_merge($this->BGColor_PDs);
            if (!$this->l_Empty_CC_PP) {

                $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $aborder_l));
                $cell->addText(htmlspecialchars($this->getSeries_Code($adate)), array_merge($styleFont));

                $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell, $aborder_m));
                $cell->addText(htmlspecialchars($cday), array_merge($styleFont));

                $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell, $aborder_m));
                $cell->addText(htmlspecialchars($start), array_merge($styleFont), $this->styleCell_alignCenter);

                $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell, $aborder_m));
                $cell->addText(htmlspecialchars($this->getLocation($adate, false)), array_merge($styleFont)); //, $this->styleCell_alignCenter

                //*** 20170216
                //*** Werkbuchstaben ganz rechts ausrichten
                $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell, $aborder_r));
                $cell->addText(htmlspecialchars($ShortProgram), array_merge($styleFont), $this->styleCell_alignRight);
            } else {
                $styleFont = array();

                $aborder_l = array_merge(
                    $this->styleCell_borderLeft_dotted,
                    ($count_pds == 1 ? $this->styleCell_borderTop_12 : $this->styleCell_borderTop_dotted),
                    $this->styleCell_borderRight_dotted,
                    $this->styleCell_borderBottom_dotted
                );
                $aborder_r = $aborder_l;
                $aborder_m = $aborder_l;

                $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $aborder_l));

                $cell->addText(htmlspecialchars($cday), array_merge($styleFont));

                //*** 20170216
                //*** bitte Enduhrzeit noch einfügen

                $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell, $aborder_m));
                $cell->addText(htmlspecialchars($start_end), array_merge($styleFont), $this->styleCell_alignCenter);

                //*** 20170117
                //*** Termine des Projekttyps  CC oder PP
                //*** Linker Zellenteil: Terminart - adates_text - adates_notes

                //*** 20170206
                //*** Projekte vom Type Code PP oder CC und Termine ohne Projekt sind anders formatiert.

                //*** Es fehlt nun der Ort (an dessen Stelle steht nun adates_text
                //*** Ich weiß, Du hast versucht, die Formatierung soweit es ging beizubehalten. Wo packen wir ihn am besten hin? Adates_text weiter nach rechts und zusätzl. Spalte nach Uhrzeit
                //*** Oder Statt dem Code Serie in die erste Spalte (dann aber mit Name 3, da stehen die Abkürzungen drin)
                //*** <sj> ok, Spalte1 mit Location.Name3

                //*** 20170207
                //*** Bei Terminen ohne Projekt: bitte Bindestrich zwischen Terminart und adates_text wegnehmen, würde dann machen "Soirée de Noel …"

                $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell, $aborder_m));
                $textrun = $cell->addTextRun();

                switch (true) {
                    case $this->cProject_Code == 'PP':
                        //*** Format wie Termine ohne Projekt
                        //*** Aber statt adates_notes
                        //*** Memo_1 zusätzl. Felder

                        //$memo_1 = prepare_HTTP($adate->memo_1);
                        $memo_1 = $adate->memo_1;

                        //$cell->addText(htmlspecialchars($this->getLocation($adate, true)), array_merge($styleFont), $this->styleCell_alignCenter);

                        $textrun->addText(htmlspecialchars($adate->seventtype->name), array_merge($styleFont));
                        if ($adate->text > '') {
                            $textrun->addText(htmlspecialchars(' ' . $adate->text), array_merge($styleFont));
                        }

                        if ($adate->duties2 > 0) {
                            $duties2 = $adate->duties2 . ($adate->duties2 == 1 ? 'service annuel' : 'services annuels');

                            $textrun->addText(htmlspecialchars(' ' . $duties2), array_merge($styleFont));
                        }

                        if ($adate->text_2 > '') {
                            $textrun->addText(htmlspecialchars(' ' . $adate->text_2), array_merge($styleFont));
                        }

                        if ($memo_1 > '') {
                            $textrun->addTextBreak();
                            $this->reporttools->addMemo_textrun($textrun, $memo_1, $styleFont);
                        }

                        break;
                    default:
                        // 4. bei den orangenen Projekten (keine Ahnung mehr, wie Du die klassifizierst) sollen an der rot markierten Stelle die Daten aus Terminart, Adates_text und adates_notes
                        // in einer Zeile getrennt mit Leerzeichen ausgegeben werden.
                        $textrun->addText(htmlspecialchars($adate->seventtype->name), array_merge($styleFont));
                        if ($adate->text > '') {
                            $textrun->addText(' ');
                            $textrun->addText(htmlspecialchars($adate->text), array_merge($styleFont));
                        }

                        if ($adate->notes > '') {
                            //$textrun->addTextBreak();
                            $textrun->addText(' ');
                            $this->reporttools->addMemo_textrun($textrun, ' ' . $adate->notes, $styleFont);
                        }
                        break;
                }

                $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell, $aborder_r));
                $cell->addText(htmlspecialchars($this->getLocation($adate, true)), array_merge($styleFont), $this->styleCell_alignCenter);

            }
        }
    }

    function getShortProgram($adate, $separator = ', ')
    {
        // *** Beim Vergleich mit FPD
        // *** für Aktivitäten #Performance und #salle philharmonique Liège, keine Buchstaben für die gespielten Werke anzeigen!!!!!

        // *** Neu: Für Termine Code GEN generell die Werkbuchstaben weglassen,
        // *** d.h. bei allen Terminen außerhalb des Salle Philharmonique werden Werkbuchstaben weggelassen und
        // *** grundsätzlich bei Generalproben,
        // *** Ausnahme: bei Konzerten werden Werkbuchstaben immer angezeigt, egal welcher Ort

        // *		this.getProgram_Project()

        /*		SELECT * ;
                FROM crsDateWorks_Project;
                WHERE NVL(Date_id, 0) == this.nDate_id ;
                ORDER BY Work_Order;
                INTO CURSOR crsProgram_Date READWRITE
        */
        // *** 20150623
        // *** 17.	Bisher machen wir es nur für die Produktionen Gruppe Lyrique : das sind Opern, also gibt es nur ein Werk und wir vergeben keinen Buchstaben.
        // *** Könnten wir zusätzlich nach festlegen, wenn Produktion nur 1 Werk, dann erscheint Werk ohne Buchstabe in Splate H und in Wochentagzelle?

        // *** 20161107
        // *** Werkreihenfolge: die einfachen / weglassen und nur Pause anzeigen mit //:

        // *** 20161116
        // *** 9.	Bei Projekten mit nur einem Werk, bitte doch nur A anzeigen:
        // *		IF RECCOUNT("crsProgram_Project")<=1
        // *			RETURN ''
        // *		ENDIF

        $program = '';

        foreach ($this->aproject['aworks'] as $awork) {
            $program.=','.$this->getColumnLetter($awork->work_count);

        }
        $program .='#';

        $aprogram = array();
        // *		ll_after_intermission = .F.
        foreach ($adate->adate_works as $works) {

            $work_id = $works->work_id;

            if ($works->swork->l_intermission == 0) {

                foreach ($this->aproject['aworks'] as $awork) {


                    if ($work_id == $awork->work_id) {
                        //$program .= '#'.$awork->work_count.'x'.$awork->order_dw.'#';

                        $work = $this->getColumnLetter($awork->work_count);
                    }
                }

            } else {
                //***
                //20181103
                //*** // für Pausen wegnehmen bei work_ID = 11125 und wenn es nur ein anderes reguläres Werk gibt
                //*** Hier gibt es ein Werk, das heißt "ohne Pause" einfach damit man anzeigen kann, dass es keine Pause gibt .
                //*** Kannst Du hierfür die // rausnehmen?
                //*** Bei den anderen Pausenwerken(20min Pause, 30min Pause) ist es gut wenn die // bleiben, dann sieht man, es gibt eine Pause

                $work = '//'; // && 20151119 '/'
                if ($work_id == 11125) {
                    $work = '';
                }
            }

            if(!empty($work) && !in_array($work, $aprogram)) {
                $aprogram[] = $work;
            }

            //$program .= ($program > '' ? ' ' : '') . $work;

            //*** 20161031
            //*** Leerzeichen vor und nach /
//*			lcProgram = ;
//*				lcProgram + IIF(!EMPTY(lcProgram), IIF(ll_after_intermission, '', ' ') + '/' + IIF(ll_intermission == 0, ' ', ''), '') + lcWork

            //*			ll_after_intermission = ll_intermission == 1
        }


        /*foreach ($this->aproject['aworks'] as $awork) {
            $program .= $awork->work_count.'x'.$awork->order_dw.'#';
        }
        */
        //$program .= ($program > '' ? ' ' : '') . $work;
        $program = implode(' ',$aprogram);
        return $program;

    }

    function getSeries_Code($adate, $separator = ', ')
    {

        $series = '';

        foreach ($adate->adate_series as $adate_serie) {
            $serie = $adate_serie->sseries->code;
            if ($serie > '') {
                $series .= ($series > '' ? $separator : '') . $serie;
            }
        }
        return $series;
    }

    function getLocation($adate, $l_name3 = false)
    {

        //*** 20170206
        //*** Name 1 im Kopf verwenden:

        //*** 20170216
        //*** Bitte im Kopf noch Ort hinzufügen:
        $location = '';
        if($adate->locationaddress) {
            $location = $adate->locationaddress->name1;
            $location .= ($adate->locationaddress->place > '' ? ', ' : '') . $adate->locationaddress->place;
            if ($l_name3 && $adate->locationaddress->name3 > '') {
                $location = $adate->locationaddress->name3;
            }
        }

        return $location;
    }

    public function addProgram($adate)
    {
        //print_r($adate->adate_works);die;
        if (sizeof($adate->adate_works) == 0) {
            return;
        }

        $this->table->addRow(Converter::cmToTwip($this->tbl_row_h));

        foreach ($adate->adate_works as $awork) {
            $nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);
            $cduration = $nduration . "'";

            $title = (!empty($awork->title2) ? $awork->title2 : $awork->swork->title1);
            // 20230317 ONCUST-1563
            // Komponisten Namen bitte in Großbuchstaben:
            $composer = $this->getShortName($awork->swork->scomposer->firstname, '', mb_strtoupper($awork->swork->scomposer->lastname));
            if ($awork->swork->l_intermission == 1) {
                $work = $title;
            } else {
                $work = $composer . ' ' . $title;
            }

            $styleCell = array();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($work), array_merge($this->styleFont, $this->styleFont_bold));
            $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));


            $cell->addText($work);
        }
    }

    function getProgram($cell)
    {
        //20220513 ONCUST-643
        //Can we add space between here:

        $cell->addText('', array_merge($this->styleFont));

        $textrun = $cell->addTextRun();

        // 20220409
        //// Can we put in the total music time here?
        $cduration_total = ' (' . $this->aproject['nduration_total'] . "')";
        $textrun->addText(htmlspecialchars('EfnisskrûÀ ûÙ tû°nleikarûÑû¯') . $cduration_total, array_merge($this->styleFont, $this->styleFont_underline, $this->styleFont_11));
        $textrun->addText(htmlspecialchars(' ' . 'Program in concert order:'), array_merge($this->styleFont, $this->styleFont_underline, $this->styleFont_grey, $this->styleFont_11));
        $textrun->addTextBreak;

        //20220420 ONCUST-643
        //Can we add the instrumentation after each work? And make it blue to make it more clear.

        foreach ($this->aproject['aworks'] as $awork) {
            $textrun = $cell->addTextRun();
            $textrun->addText(htmlspecialchars($awork['composer_21']), array_merge($this->styleFont, $this->styleFont_bold, $this->styleFont_11));
            $textrun->addText(htmlspecialchars(' ' . $awork['title']), array_merge($this->styleFont, $this->styleFont_11));
            $textrun->addText(htmlspecialchars($awork['cduration']), array_merge($this->styleFont, $this->styleFont_11));

            //20220420 ONCUST-643
            //Also can we reduce the font size of the instrumentation down to 8p?
            //DEsweiteren mûÑchten Sie die anstatt der blauen Schriftrfarbe die graue Schriftfarbe der Instrumentierung
            $textrun->addText(htmlspecialchars(' ' . $awork['instrumentation']), array_merge($this->styleFont, $this->styleFont_8, $this->styleFont_grey));

        }

        $textrun = $cell->addTextRun();
        $textrun->addText(htmlspecialchars($this->aproject['instrumentation_max']), array_merge($this->styleFont, $this->styleFont_8, $this->styleFont_italic));
    }

    public function getShortName($tname2 = '', $tname5 = '', $tname1 = '', $format = '251')
    {

        $name = trim($tname2);

        $name2 = substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : '');

        while (strpos(' ', $name) !== false) {
            $name = trim(substr($name, 0, strpos(' ', $name)));

            $name2 = trim($name2 . ' ' . substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : ''));
        }

        switch (true) {
            case $format == '251':
                $name = trim($name2 . ' ' . trim(trim($tname5) . ' ' . trim($tname1)));
                break;

            default:
                $name = trim(trim($tname5) . ' ' . trim($tname1)) .
                    (!empty($name2) ? ', ' : '') . $name2;
                break;
        }

        return $name;
    }

    function getDresses($cell)
    {
        foreach ($this->aproject['adresses'] as $sdress) {
            $cell->addText(htmlspecialchars('KlûÎû¯naû¯ur Sû?: ' . $sdress->name), array_merge($this->styleFont, $this->styleFont_italic, $this->styleFont_11));
            $cell->addText(htmlspecialchars('Dress for ISO: ' . $sdress->name2), array_merge($this->styleFont, $this->styleFont_italic, $this->styleFont_grey, $this->styleFont_11));
        }
    }

    public function getLongName($tname2 = '', $tname5 = '', $tname1 = '')
    {
        $name = trim(trim($tname2 . ' ' . $tname5) . ' ' . $tname1);
        return $name;
    }

    public function addMemo_notes($section, $text, $styleFont = array(), $stylePar = array())
    {
        $textlines = explode("\n", $text);

        foreach ($textlines as $line) {
            //Is it possible to make it so that if there is a // then what follows would be gray?

            $line = str_replace('//', chr(1), $line);
            $line = str_replace('/', chr(1), $line);

            $line1 = $line;
            $line2 = '';

            if (strpos($line, chr(1)) !== false) {
                $line1 = substr($line, 0, strpos($line, chr(1)));
                $line2 = substr($line, strpos($line, chr(1)) + 1);
            }

            $textrun = $section->addTextRun();

            $textrun->addText(htmlspecialchars($line1), $styleFont, $stylePar);
            if ($line2 > '') {
                $textrun->addText(' ' . htmlspecialchars($line2), array_merge($styleFont, $this->styleFont_grey), $stylePar);
            }
            $textrun->addTextBreak;
        }
    }

    function getMinMaxDays_short($adates)
    {
        $minmax = '';

        $ayears = array();
        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if ($count == 1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates) > 0) {
            $m_min = mb_strtolower($this->reporttools->getMonthName($date_min));
            $d_min = $date_min->format('d');

            $m_max = mb_strtolower($this->reporttools->getMonthName($date_max));
            $d_max = $date_max->format('d');

            $minmax =
                $d_min . ' ' .
                ($m_min <> $m_max ? $m_min . ' ' : '') .
                'au ' .
                $d_max . ' ' . $m_max;
        }

        return $minmax;
    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $ayears = array();
        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if ($count == 1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if ($count>0) {
            $m_min = mb_strtolower($this->reporttools->getMonthName($date_min));
            $d_min = $date_min->format('d');
            $y_min = $date_min->format('Y');

            $m_max = mb_strtolower($this->reporttools->getMonthName($date_max));
            $d_max = $date_max->format('d');
            $y_max = $date_max->format('Y');

            $minmax =
                $d_min . '.' .
                ($m_min <> $m_max ? $m_min . '.' : '') .
                ($y_min <> $y_max ? $y_min : '') .
                ' au ' .
                $d_max . '.' . $m_max . '.' . $y_max;

            $minmax =
                $d_min . ' ' .
                ($m_min <> $m_max ? $m_min . ' ' : '') .
                ($y_min <> $y_max ? ' '.$y_min : '') .
                'au ' .
                $d_max . ' ' . $m_max.' '.$y_max;
        }

        return $minmax;
    }

    function getColumnLetter($col): string
    {
        $col--;

        $letter = '';

        if (($col - ($col % 26)) / 26 > 0) {
            $letter = chr(64 + ($col - ($col % 26)) / 26);
        }

        $letter = $letter . chr(65 + ($col % 26));

        return $letter;
    }

    function getAddactivities($cell, $adate, $astyle = array())
    {
        //*** 20170206
        //*** Keine Bindestrich zwischen Name und Text:
        //*** So " différé+UER 18/1 " statt " différé+UER - 18/1 "

        foreach ($adate->adate_activities as $aaddact) {
            $addact = $aaddact->seventtype->name . ' ' . $aaddact->text;
            $cell->addText(htmlspecialchars($addact), array_merge($astyle));
        }
    }


    function getProgram_Soloist($artist_id, $instrument_id)
    {

        //*** Beim Vergleich mit FPD
        //*** für Aktivitäten #Performance und #salle philharmonique Liège, keine Buchstaben für die gespielten Werke anzeigen!!!!!

        //*** Neu: Für Termine Code GEN generell die Werkbuchstaben weglassen,
        //*** d.h. bei allen Terminen außerhalb des Salle Philharmonique werden Werkbuchstaben weggelassen und
        //*** grundsätzlich bei Generalproben,
        //*** Ausnahme: bei Konzerten werden Werkbuchstaben immer angezeigt, egal welcher Ort

        $program = '';

        foreach ($this->aproject['aworks'] as $awork) {

            $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

            /*$where = [
                'Adates.season_id'=>$this->aproject['season_id'],
                'Adates.project_id'=>$this->aproject['project_id'],
                'Adates.planninglevel'=>$this->aproject['planninglevel'],
                'AdateworkSoloists.artist_id'=>$artist_id,
                'AdateWorks.work_id'=>$awork['work_id']
            ];
'*/
            $cdate_ids = '-1';
            foreach ($this->aproject['apds'] as $adate) {
                $cdate_ids .= ','.$adate->id;
            }

            $where =
                'AdateWorks.date_id IN ('.$cdate_ids.')'.' AND '.
                'AdateworkSoloists.artist_id='.$artist_id.' AND '.
                'AdateworkSoloists.instrument_id='.$instrument_id.' AND '.
                'AdateWorks.work_id='.$awork['work_id'];

            $arows = $AdateworkSoloistsTable
                ->find('all')
                ->select([
                    'AdateworkSoloists.id', 'AdateworkSoloists.artist_id',
                    'AdateWorks.work_id'
                ])
                ->contain([
                    'AdateWorks'
                ])
                ->where($where);

            //print_r($arows);die;

            $crow = '';
            foreach($arows as $arow) {
                $crow .= '#XXX#'.$arow->id.'#'.$arow->artist_id.'#'.$arow->adate_work->work_id.'#';
            }

            //$program.=$crow;

            if($crow>'') {
                $work_count = $this->getColumnLetter($awork->work_count);

                //$work_count = $work_count.$crow;

                if ($awork->swork->l_intermission == 1) {
                    $work_count = '';
                }

                //$work_count.='#'.$where;
                if (!empty($work_count)) {
                    $program .= ($program > '' ? ',' : '') . $work_count;
                }
            }
        }

        //$program = 'XXXXX'.$program;
        return $program;
    }
}
