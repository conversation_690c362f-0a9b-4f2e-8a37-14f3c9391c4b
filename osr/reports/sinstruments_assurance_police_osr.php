<?php
/*
20220402 ONCUST-815
OSR Assurance police musiciens

bitte den Filter für den Bericht umstellen und nur diejenigen Instrumente ausgeben, wo l_versichert=1.



<PERSON>te kontrollieren, dass der Zeitraum Spalte mit dem abgefragten Zeitraum des Berichts zusammenpasst.

Normalerweise wird abgefragt 1.9.xxxx bis 31.8.xxxx hier müsste dann also 365 Tage stehen.
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use \App\Reports\ReportSpreadsheet;


//use \App\Reports\ReportRtf;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;

use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

use App\Model\Table\AdateworkMovementsTable;
use Customer\osr\reports\ReportTools_client;
use Customer\osr\reports\instrumentation_osr;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;

class sinstruments_assurance_police_osr extends ReportSpreadsheet
{
    private $user = '';

    private $postData = null;
    private $reporttools = null;

    private $ainstruments_selected = null;
    private $ainstrument = null;
    private $instrument_id = 0;
    private $template_start = '';
    private $template_end  = '';

    private $nCN_Artist_name1 = 1;
    private $nCN_Artist_name2 = 2;
    private $nCN_Artist_sex = 3;

    private $nCN_Owner_name1 = 4;
    private $nCN_Owner_name2 = 5;

    private $nCN_Instrument = 4;
    private $nCN_Amount = 5;
    private $nCN_From = 6;
    private $nCN_To = 7;
    private $nCN_Days = 8;
    private $nCN_Taux = 9;
    private $nCN_Primes_annuelles = 10;
    private $nCN_Primes_prorata = 11;
    private $nCN_Insurance_Text = 12;

    private $nMaxColNum = 13;
    private $maxrownum = 0;

    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();

    private $borders_medium_top = array();
    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    protected $templates = [
        [
            'name' => 'sinstruments_assurance_police_osr_template',
            'file' => 'sinstruments_assurance_police_osr_template.php',
            'jsFile' => 'sinstruments_assurance_police_osr_template.js',
            'fileLocationType' => 'direct'
        ]
    ];
    function initialize()
    {
        parent::initialize();

        $this->template_filename = CUSTOMER_REP_DIR . 'sinstruments_assurance_police_osr.xlsx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        $this->prepare_borders();

    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $this->postData = $this->getRequest()->getData();

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $awhere = ['Sinstruments.id IN ' => $this->postData['dataItemIds'], 'l_insured'=>1];

        $this->ainstruments_selected = $this->model
            ->find('all')
            ->select()
            ->contain([
                'Artistaddresses' => ['SaddressPersdata' => ['SaddresspersdataBanks'=>['Sbanks'=>['Saddresses']]]],
                'Owneraddresses' => ['SaddressPersdata' => ['SaddresspersdataBanks'=>['Sbanks'=>['Saddresses']]]],
                'SinstrumentsInsurancevalues' =>
                    function (Query $query) {
                        return $query
                            ->orderDesc('SinstrumentsInsurancevalues.date_');
                    },
            ])
            ->where($awhere)
            ->order(['Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC'])
            ->all();
//print_r($this->ainstruments_selected); die;
        //$this->postData['formData']['template_start']
        //$this->postData['formData']['template_end']

        return $this;
    }

    public function write_sheets($view = null, $layout = null) {
        $count=1;

        $this->template_start =  Date::parse($this->postData['formData']['template_start']);
        if(empty($this->template_start)) {
            $this->template_start = time();
        }

        $this->template_end = Date::parse($this->postData['formData']['template_end']);
        if(empty($this->template_end)) {
            $this->template_end = time();
        }

        $this->ospreadsheet->setActiveSheetIndex($count-1);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        //$this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

        $caption = 'Mutations dès le '.
            Date::parse($this->postData['formData']['template_start'])->format('Ymd');

        $this->sheet->setTitle($caption);

        $this->write_sheet();

    }

    function write_sheet()
    {
        $this->row = 1;

        foreach ($this->ainstruments_selected as $ainstrument) {
            $instrument_id = $ainstrument->id;

            $instrument = trim($ainstrument->name . ' ' . $ainstrument->description);
            //$instrument = htmlspecialchars($ainstrument->name . ' ' . $ainstrument->description);
            //$instrument = str_replace('&amp;', '"', $instrument);

            //*** 20140902
            //*** 1.	Falls das Instrument im abgefragten Zeitraum 2 Einträge hat > 0,00 CHF
            //*** (d.h. das Instrument wurde neu bewertet und hat nun einen veränderten Versicherungswert)
            //*** dann bitte die Zeile mit dem alten Wert auch durchstreichen, bisher machst Du es schon, wenn das Instrument ganz rausfällt und einen Wert 0,00CHF hat

            // alle insurances im ausgewählten Zeitraum
            $ainsurances = array();
            $amount = 0;
            $count_incurances_all = 0;
            $amount_last = 0;

            $effectiveDate = new DateTime('2099-12-01');
            $ed = new DateTime('2099-12-01');
            if($effectiveDate>$this->template_end) {
                //$effectiveDate = clone $this->template_end;
                $effectiveDate = $this->template_end;
            }

            //$this->row++;
            //$this->sheet->setCellValue($this->getColumnLetter(10) . ($this->row), $ed->format('d.m.Y'));
            //$this->sheet->setCellValue($this->getColumnLetter(11) . ($this->row), $effectiveDate->format('d.m.Y'));
            //$this->sheet->setCellValue($this->getColumnLetter(12) . ($this->row), $this->template_end);

            foreach ($ainstrument->sinstruments_insurancevalues as $insurancevalue) {
                /*
                                    $this->row++;
                                    //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1) . ($this->row), htmlspecialchars('from'));
                                    $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), '111');
                                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+1) . ($this->row), $insurancevalue->date_);
                                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+2) . ($this->row), $this->template_start);
                                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+3) . ($this->row), $this->template_end);
                                    /**/
                //if ($insurancevalue->date_ >= $this->template_start and $insurancevalue->date_ <= $this->template_end) {
                if ($this->template_end>=$insurancevalue->date_ && $this->template_start <= $effectiveDate) {
                    // insurances ohne Wert werden mitgezählt
                    $count_incurances_all++;
                    // nur insurances mit Wert anzeigen
                    //if ($insurancevalue > 0) {
                    $ainsurance['amount'] = $insurancevalue->amount;
                    $ainsurance['from'] = $insurancevalue->date_;
                    $ainsurance['to'] = $effectiveDate;
                    $ainsurance['text'] = $insurancevalue->text;

                    $ainsurances[$count_incurances_all] = $ainsurance;

                    /*
                    $this->row++;
                    //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1) . ($this->row), htmlspecialchars('from'));
                    $this->sheet->setCellValue($this->getColumnLetter(1) . ($this->row), '222');
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+1) . ($this->row), $insurancevalue->date_);
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+2) . ($this->row), $ainsurance['from']);
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1+3) . ($this->row), $ainsurance['to']);
                    /**/
                }
                $effectiveDate = $insurancevalue->date_;
            }

            $count_insurances_active = 0;

            // nach Datum
            $ainsurances = array_reverse($ainsurances);
            foreach ($ainsurances as $ainsurance) {
                $count_insurances_active++;


                // nur insurances mit Wert anzeigen oder auch 0, wenn danach eine weitere Versicherung besteht
                if ($ainsurance['amount'] == 0 and $amount_last == 0) {
                    continue;
                }

                $this->row++;
                if ($ainstrument->artistaddress) {
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name1) . ($this->row), htmlspecialchars($ainstrument->artistaddress->name1));
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_name2) . ($this->row), htmlspecialchars($ainstrument->artistaddress->name2));

                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Artist_sex) . ($this->row), htmlspecialchars($ainstrument->artistaddress->saddress_persdata->sex));
                }

                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Instrument) . ($this->row), $instrument);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Amount) . ($this->row), $ainsurance['amount']);

                $from = $ainsurance['from'];
                if($this->template_start>$from) {
                    $from = $this->template_start;
                }

                $to = $ainsurance['to'];
                //if($this->template_start>$from) {
                if($to>$this->template_end) {
                    $to = $this->template_end;
                }

                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_From) . ($this->row), $from->format('d.m.Y'));
                if($ainsurance['to']) {
                    $this->sheet->setCellValue($this->getColumnLetter($this->nCN_To) . ($this->row), $to->format('d.m.Y'));
                }

                //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_To) . ($this->row), $ainsurance['to']);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Days) . ($this->row), '=DAYS360('.$this->getColumnLetter($this->nCN_From) . ($this->row).','.$this->getColumnLetter($this->nCN_To) . ($this->row).')');
                //$this->sheet->setCellValue($this->getColumnLetter($this->nCN_Days) . ($this->row), '=('.$this->getColumnLetter($this->nCN_To) . ($this->row).'-'.$this->getColumnLetter($this->nCN_From) . ($this->row).')+1');

                //lcText = NVL(this.getEffectiveValue("Instrument_id", lnInstrument_id, "sinstrument_insurcats", 'text', 'date_', effective_date, 'LOWER(sInstrumentInsurCats.Name) = "primes annuelles"'), '')
				//xcel.ActiveSheet.Cells(lnRow, 9).value = VAL(STRTRAN(lcText,',','.')) && '.' nur für SUISS


                $v = $this->getEffectiveValue("instrument_id", $instrument_id, "SinstrumentsInsurcats", 'text', 'date_', $ainsurance['from']);
                $taux = (float)(str_replace(',', '.', $v));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Taux) . ($this->row), $taux);

                //*** =RUNDEN((E2*I2/1000);1)
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row), '=ROUND(('.$this->getColumnLetter($this->nCN_Amount) . ($this->row).'*'.$this->getColumnLetter($this->nCN_Taux) . ($this->row).'/1000),1)');

                //*** =RUNDEN((J2*H2/360);1)
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Primes_prorata) . ($this->row), '=ROUND(('.$this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row).'*'.$this->getColumnLetter($this->nCN_Days) . ($this->row).'/360),1)');

                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Insurance_Text) . ($this->row), $ainsurance['text']);

                //$this->sheet->setCellValue($this->getColumnLetter(15) . ($this->row), $count_insurances_active);
                //$this->sheet->setCellValue($this->getColumnLetter(16) . ($this->row), sizeof($ainsurances));

                if($count_insurances_active<sizeof($ainsurances)) {

                    $styleArray = array('font' => array('strikethrough' => true));
                    for ($i=1; $i<=$this->nMaxColNum; $i++) {
                        $this->sheet->getStyle($this->getColumnLetter($i) . ($this->row))->applyFromArray($styleArray);
                    }
                }
            }
        }
        $this->sheet->getStyle($this->getColumnLetter(1) . (1) . ':' . $this->getColumnLetter($this->nMaxColNum) . ($this->row))->applyFromArray($this->borders_thin_all);

        //*** 20170901
        $this->row++;
        $styleArray = array('font' => array('bold' => true));
        for ($i=$this->nCN_Primes_annuelles; $i<=$this->nCN_Primes_prorata; $i++) {
            $this->sheet->getStyle($this->getColumnLetter($i) . ($this->row))->applyFromArray($styleArray);
        }

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row) . ':' . $this->getColumnLetter($this->nCN_Primes_prorata) . ($this->row))->applyFromArray($this->borders_thin_all);

        $this->sheet->getStyle($this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row) . ':' . $this->getColumnLetter($this->nCN_Primes_prorata) . ($this->row))
            ->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('C0C0C0');

        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row), '=SUM('.$this->getColumnLetter($this->nCN_Primes_annuelles) . (2).':'.$this->getColumnLetter($this->nCN_Primes_annuelles) . ($this->row-1).')');
        $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Primes_prorata) . ($this->row), '=SUM('.$this->getColumnLetter($this->nCN_Primes_prorata) . (2).':'.$this->getColumnLetter($this->nCN_Primes_prorata) . ($this->row-1).')');

	/*
				oExcel.ActiveSheet.Cells(lnRow, 6).value = IIF(effective_date<this.ddatestart, this.ddatestart, NVL(effective_date, CTOT('')))
				oExcel.ActiveSheet.Cells(lnRow, 7).value = IIF(effective_date_end>this.ddatestop, this.ddatestop, NVL(effective_date_end, CTOT('')))
				oExcel.ActiveSheet.Cells(lnRow, 8).value = '=DAYS360(' + getColumnLetter(6) + ALLT(STR(lnRow)) + ',' + getColumnLetter(7) + ALLT(STR(lnRow)) + ')'
				lcText = NVL(this.getEffectiveValue("Instrument_id", lnInstrument_id, "sinstrument_insurcats", 'text', 'date_', effective_date, 'LOWER(sInstrumentInsurCats.Name) = "primes annuelles"'), '')
				oExcel.ActiveSheet.Cells(lnRow, 9).value = VAL(STRTRAN(lcText,',','.')) && '.' nur für SUISS

    *** =RUNDEN((E2*I2/1000);1)
				oExcel.ActiveSheet.Cells(lnRow, 10).value = ;
					'=ROUND((' + getColumnLetter(this.ncolnum_insurancevalue) + ALLT(STR(lnRow)) + '*' + getColumnLetter(this.ncolnum_taux) + ALLT(STR(lnRow)) + '/1000),1)'

                    *** =RUNDEN((J2*H2/360);1)
				oExcel.ActiveSheet.Cells(lnRow, 11).value = ;
					'=ROUND((' + getColumnLetter(this.ncolnum_primesannuelles) + ALLT(STR(lnRow)) + '*' + getColumnLetter(this.ncolnum_nombres_de_jours) + ALLT(STR(lnRow)) + '/360),1)'

				oExcel.ActiveSheet.Cells(lnRow, 12).value = NVL(ALLT(crsInsurances_Instr_Active.Text), '')

            $start = ($ainstrument->start_ ? $ainstrument->start_->format('H:i'): '');

            $location = $ainstrument->locationaddress->name1.
                (!empty($location) && !empty($ainstrument->locationaddress->name1) ? ', ' : '') .
                $ainstrument->locationaddress->place;

            $eventtype = $ainstrument->seventtype->name;
            $conductor = $this->reporttools->getLongName($ainstrument->conductoraddress->name2, '', $ainstrument->conductoraddress->name1);

            foreach($ainstrument->ainstrument_works as $datework) {
                if($datework->swork->l_intermission==1) {continue;}

                //20220518 ONCUST-639
                $premiere = ($datework->sworkpremiere ? $datework->sworkpremiere->name : '');

                $title = ($datework->title2>'' ? $datework->title2 : $datework->swork->title1);
                $composer = trim($datework->swork->scomposer->firstname.' '.$datework->swork->scomposer->lastname);
                //$nduration = (int)substr($datework->duration, 0, 2) * 60 + (int)substr($datework->duration, 3, 2);

                $asoloists = $this->reporttools->getSoloists_dw($datework->id);
                $soloists = '';

                $this->row++;
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Composer) . ($this->row), htmlspecialchars($composer));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Title) . ($this->row), htmlspecialchars($title));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Date) . ($this->row), htmlspecialchars($ainstrument->date_->format('d.m.Y')));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Premiere) . ($this->row), htmlspecialchars($premiere));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Eventtype) . ($this->row), htmlspecialchars($eventtype));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Location) . ($this->row), htmlspecialchars($location));
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Conductor) . ($this->row), htmlspecialchars($conductor));
                $this->sheet->getStyle($this->getColumnLetter($this->nCN_Soloists) . ($this->row))->getAlignment()->setWrapText(true);
                $this->sheet->setCellValue($this->getColumnLetter($this->nCN_Soloists) . ($this->row), htmlspecialchars($soloists));
            }
            */
    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                ],
            ],
        ];

    }


    //getEffectiveValue("Instrument_id", lnInstrument_id, "sinstrument_insurcats", 'text', 'date_', effective_date, 'LOWER(sInstrumentInsurCats.Name) = "primes annuelles"'), '')
    function getEffectiveValue($whereField, $whereValue, $table, $field, $effectiveDateField, $date)
    {

        $arows = TableRegistry::getTableLocator()->get($table)
            ->find('all')
            ->contain(['SinstrumentInsurcats'])
            ->where($whereField . " = " . $whereValue. " and LOWER(Sinstrumentinsurcats.name) = 'primes annuelles'")
            ->order([$effectiveDateField => 'ASC']);

        $v = 0;
       // print_r($arows);die;

        foreach ($arows as $arow) {
            //print_r($arow);die;

                if ($arow[$effectiveDateField] <= $date) {
                    $v = $arow[$field];
                }
//            foreach($arow->Sinstrumentinsurcats)
            //if (strtolower($arow->Sinstrumentinsurcats->name) == 'primes annuelles') {
        //}
            return $v;
        }
    }
}
