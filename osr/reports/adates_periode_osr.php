<?php
set_time_limit(0);

ini_set('max_execution_time', 300);
ini_set('request_terminate_timeout', 300);

//phpinfo(); die;
/*
OSR Periode
*/

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use Cake\ORM\TableRegistry;

require_once('adates_periode_common_osr.php');

class adates_periode_osr extends cls_periode_common
{
    function initialize()
    {
        parent::initialize();

        $this->cReportType = 'periode';

        //*** 20170206
        $this->l_show_effectiv = false;
        $this->l_show_effectiv_osr = false;

        $this->caption = '';
        //*** 20170216
        //*** <PERSON><PERSON>e Clavier, extras und Chœur bei OSR Periode weglassen (nicht so bei fiche techn. Oder bibliotheque)

        //*** 20170329
        //*** 6.	<PERSON><PERSON>t " Période "
        //*** Wieder Meinungsänderung…
        //*** <PERSON><PERSON> für " Chœurs ", clavier und instruments extra wieder einfügen (wie im Fiche Techn. Oder Bibliotheque) vgl Bild
        //*** Bsp. 16/17,  Liste 03

        //*poImport.l_show_Vocals = .F.
        //*poImport.l_show_Keyboards = .F.
        //*poImport.l_show_Extras = .F.
    }

    function addSection() {
        return $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(0.5),
                'marginLeft' => Converter::cmToTwip(1),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(2),
                'marginBottom' => Converter::cmToTwip(1.4))
        );
    }


    function addHeader($section) {

        //*** 20161209
        //*** Wenn nur ein Projekt, dann Start-End des Projektes, ansonsten
        //*** Start-End der gewählten Blocks
        //*** Anzeige Kopfzeile: wenn Filter auf Periode 1 + Projekt, dann Datum du xx au xx des Projekts angeben (nicht der Periode),
        //*** wenn Filter nur auf Periode (bloc1) dann von bis der Periode anzeigen, geht das?

        if(is_array($this->aprojects) && sizeof($this->aprojects)==1) {

            $aproject = reset($this->aprojects);
            $awhere = [
                'Adates.season_id' => $aproject['season_id'],
                'Adates.project_id' => $aproject['project_id']
            ];

            //print_r($aproject['season_id'].'#'.$aproject['project_id'].'#');

            $adays = TableRegistry::getTableLocator()->get('Adates')
                ->find('all')
                ->where($awhere)
                ->order('Adates.date_');

        } else {
            $where = '';
            //$awhere = [];

            foreach($this->aseason_ids as $season_id=>$aseason) {
                foreach($aseason['ablocks'] as $block) {

                    $where .= ($where>'' ? ' OR ' : ''). '(Adays.season_id='.$season_id.' AND Adays.block1='.$block.')';
                    /*$awhere = array_merge(
                        $awhere,
                        ['OR' => ['Adays.season_id'=>$season_id, 'Adays.block1' => $block]]
                    );
                    */
                }
            }

            if(empty($where)) {
                //$where = '1=1';
                $where = 'Adays.season_id>0';
            }

            $adays = TableRegistry::getTableLocator()->get('Adays')
                ->find('all')
                ->where($where)
                ->order('Adays.date_');

        }


//print_r($adays); die;
        //$minmax = 'Du ' .$this->getMinMaxDays_short($adays);
        //$minmax = 'Du ' .$x.'#'.$this->getMinMaxDays($adays);
        $minmax = 'Du ' .$this->getMinMaxDays($adays);



        $header = $section->addHeader();

        //$section->addImage(CUSTOMER_REP_DIR. 'Sinfo_logo_ISL_prent_RGB.jpg', array('width'=>Converter::cmToPoint(3.55),'align'=>'center'));

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(1);
        $this->acol_widths[] = Converter::cmToTwip(16);
        $this->acol_widths[] = Converter::cmToTwip(2);

        $this->table_width = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->table_width += $col_width;
        }

        $borderSize = 6;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.03),
            'cellMarginBottom' => Converter::cmToTwip(0.03),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'width' => $this->table_width,
            'unit' => TblWidth::TWIP
        );

        $table = $header->addTable($tableProperties);

        $table->addRow(Converter::cmToTwip(0.6));

        $this->cBlocks = implode(', ', $this->ablocks);
        $cell = $table->addCell($this->acol_widths[0], array_merge());
        $cell->addImage(CUSTOMER_REP_DIR. 'adates_periode_osr.jpg', array('width'=>Converter::cmToPoint(0.6),'align'=>'left'));
        $cell = $table->addCell($this->acol_widths[1], array_merge());
        $cell->addText(htmlspecialchars('Période ' . $this->cBlocks), array_merge($this->styleFont_16));
        $cell->addText(htmlspecialchars($minmax), array_merge($this->styleFont_10));
        $cell = $table->addCell($this->acol_widths[2]);

        $cell->addText(htmlspecialchars($this->cSeasons), array_merge($this->styleFont_10));



    }
}
