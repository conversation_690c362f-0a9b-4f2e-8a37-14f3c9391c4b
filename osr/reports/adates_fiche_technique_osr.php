<?php
set_time_limit(0);

/*
OSR Programme
*/

require_once('adates_periode_common_osr.php');


class adates_fiche_technique_osr extends cls_periode_common
{
    function initialize()
    {
        parent::initialize();

        $this->caption = 'Fiche technique OSR';
        $this->cReportType = 'fiche_technique';

        //***
        $this->l_show_fromto = false;
        $this->l_show_parts = true;
		//*** 20170911
        //*		this.l_show_notes = .T.
		//***

		//*** 20180125
        $this->l_show_strings = false;
        $this->l_show_notes = false;

		//*** 20180206
        $this->l_show_effectiv_osr = false;
   }
    function addPagebreak($section) {
        $section->addPageBreak();
    }

    function showProject($section)
    {
        // *** 20161212
        // *** fiche_bibliotheque und fiche_technique immer pro Konzert oder einzelne Termine, wenn es keine Aufführungen gibt

        // ***1.	Grundsätzlich ein Blatt pro Konzerttermin ausgeben
        // *** 20150624
        // *** 2.	Produktion Liste 04 ist eine Aufnahme, d.h. es gibt kein Konzert.;
        // *** In dem Fall müsstest Du bitte alle Werkinfos aus allen Terminen raussuchen. (Ich kann nicht versprechen, dass Du alle Infos im 1. Termin findest)

        // *** 20170704
        // *** PlanningLevel nicht mehr berücksichtigen

        // *** Projekte(Termine) mit Aufführung


        //$this->prepare_projectdata();
        $apds = $this->aproject['apds'];

        if(sizeof($apds)>0) {
            // array kopieren

            //$apds = array_merge($this->aproject['apds']);

            $i = 0;
            foreach($apds as $adate) {

                $this->nPD_id = $adate->id;

                if( in_array($adate->id, $this->postData['dataItemIds'])) {
                    $i++;

                    // diesmal mit $this->nPD_id
                    if($i>1) {
                        $this->addPagebreak($section);
                    }

                    $this->prepare_projectdata($section);


                    parent::showProject($section);

                    $this->showKonzertdatum($section);
                }
            }

        } else {
            parent::showProject($section);
            $this->showKonzertdatum($section);
        }
    }

    function showKonzertdatum($section) {
        //*** 20181102
        //*** Links zentriert, jeweils vom Konzertdatum Jahr-Monat-Tag_OSR_FT_Code Série(1. Serie, die vorhanden)_Name 3 (des Spielorts)
        //*** So:
        //*** 2019-04-09_OSR_FR_TOUR2_SH Tokyo

        //*** 20210330
        //* keine Aifführungen

        $location = '';
        $cdate = '';

        if(sizeof($this->aproject['apds'])>0) {
            $afpd = reset($this->aproject['apds']);

            $location = $this->getLocation($afpd, true);

            if($afpd->date_) {
                $cdate = $afpd->date_->format('Y-m-d');
            }
        }

		if($this->nPD_id == 0) {
            $adate = reset($this->aproject['adates']);
		} else {
            $adate = $this->aproject['apds'][$this->nPD_id];
        }

        $series = $this->getSeries_Code($adate, '_');

		//*** 20190204
		//*** 2.	Bitte aus FR FT machen (für fiche technique)
		//*'_OSR_FR' +

        $info =
            $cdate.
			'_OSR_FT' .
            ($series>'' ? '_' : '').$series.
            ($location>'' ? '_' : '').$location;

        $section->addTextgBreak();
        $section->addTextgBreak();
        $section->addText($info);
    }
}
