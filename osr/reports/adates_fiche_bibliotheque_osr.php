<?php
set_time_limit(0);

/*
20220720

*** 20161212
*** fiche_bibliotheque und fiche_technique immer pro Konzert oder einzelne Termine, wenn es keine Aufführungen gibt
*/

require_once('adates_periode_common_osr.php');


class adates_fiche_bibliotheque_osr extends cls_periode_common
{
    function initialize()
    {
        parent::initialize();

        $this->cReportType = 'fiche_bibliotheque';
        // 20220816
        // 20230325
        $this->l_show_effectiv_osr = true;
        //*** 20170424
        $this->l_show_addInstrFields = true;

        $this->l_show_notes = true;

        $this->caption = 'Fiche bibliothèque OSR';


		//RETURN '{\b\fs24\qc ' + 'Fiche bibliothèque OSR' + '\par\par}' + lcProjectInfo
    }

}
