<?php


//namespace App\Reports\Tools;
namespace Customer\osr\reports;

use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use App\Model\Table\AdateworkSoloistsTable;

/**
 * Class ReportTools_client
 * @package App\Reports\Tools
 *
 * bereitet kundenspetifische Daten vor, die in mehreren Berichten benutzt werden können
 */
class ReportTools_client
{
    public $user = '';
    public $user_email = '';
    public $user_phone = '';
    public $user_mobile = '';

    private $adates_selected = array();

    public function __construct()
    {
        $this->initUser();
    }

    function initUser()
    {
        $this->user = $_SESSION['Auth']['User']['sh'];
        $this->user_email = '';
        $this->user_phone = '';
        $this->user_mobile = '';

        $where = "TRIM(UPPER(IFNULL(Opasusers.sh,''))) + '#' = '" . strtoupper($this->user) . "#' ";

        $opasusers = TableRegistry::getTableLocator()->get('Opasusers')
            ->find()
            ->select(['Opasusers.id', 'Opasusers.sh', 'Opasusers.name', 'Opasusers.fullname', 'Opasusers.address_id'])
            ->where($where)
            ->first();

        $address_id = ($opasusers->address_id ? $opasusers->address_id : 0);
        $this->user = ($opasusers->fullname ? $opasusers->fullname : '');
        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' . $address_id)
            ->toArray();

        foreach ($aaddressnumbers as $anumber) {

            if ($anumber->snumbertype->l_phone == 1) {
                $this->user_phone = ($anumber->number_ ? $anumber->number_ : '');
            }
            if ($anumber->snumbertype->l_email == 1) {
                $this->user_email = ($anumber->number_ ? $anumber->number_ : '');
            }
            if ($anumber->snumbertype->l_mobile == 1) {
                $this->user_mobile = ($anumber->number_ ? $anumber->number_ : '');
            }
        }
    }

    /**
     * @param $adates
     * @return string
     * listet Tage aus der AUswahl im Format
     * 12./13./14. Juni 2016
     */
    function getDays($adates)
    {

        $ayears = array();
        foreach ($adates as $date) {
            $y = $date->date_->format('Y');
            $m = $date->date_->format('m');
            $d = $date->date_->format('d');

            $ayears[$y]['amonths'][$m]['adays'][$d] = $date->date_;
        }

        $cyears = '';
        foreach ($ayears as $year => $ayear) {


            $cmonths = '';
            foreach ($ayear['amonths'] as $month => $amonth) {
                $cdays = '';
                foreach ($amonth['adays'] as $day => $date_) {

                    //$weekday = self::getWeekday($date_);
                    $weekday = '';

                    $cdays .= (!empty($cdays) ? ' | ' : '') . trim($weekday . ' ' . $day) . '.';
                }

                $cmonths .= (!empty($cmonths) ? ', ' : '') . $cdays . ' ' . self::getMonthName($date_);
            }


            $cyears .= (!empty($cyears) ? ', ' : '') . $cmonths . ' ' . $year;
        }

        return $cyears;
    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $ayears = array();
        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if ($count == 1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates) > 0) {
            $y_min = $date_min->format('Y');
            $m_min = $date_min->format('m');
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $date_max->format('m');
            $d_max = $date_max->format('d');

            $minmax =
                $d_min . '.' .
                ($m_min <> $m_max ? $m_min . '.' : '') .
                ($y_min <> $y_max ? $y_min : '') .
                ' au ' .
                $d_max . '.' . $m_max . '.' . $y_max;
        }

        return $minmax;
    }

    function getWeekday($date)
    {
        // N - Numerische Repräsentation des Wochentages gemäß ISO-8601 (in PHP 5.1.0 hinzugefügt) 1 (für Montag) bis 7 (für Sonntag)

        if (is_null($date)) {
            $dow = 0;
        } else {
            $dow = $date->format('N');
        }

        switch ($dow) {
            case 1:
                return 'lundi';
                break;
            case 2:
                return 'mardi';
                break;
            case 3:
                return 'mercredi';
                break;
            case 4:
                return 'jeudi';
                break;
            case 5:
                return 'vendredi';
                break;
            case 6:
                return 'samedi';
                break;
            case 7:
                return 'dimanche';
                break;
            default:
                return '';
                break;
        }
    }

    function getMonthName($date = null, $m = 0)
    {
        // n - Monatszahl, ohne führende Nullen 1 bis 12
        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }

        switch ($m) {
            case 1:
                return 'janvier';
                break;
            case 2:
                return 'février';
                break;
            case 3:
                return 'mars';
                break;
            case 4:
                return 'avril';
                break;
            case 5:
                return 'mai';
                break;
            case 6:
                return 'juin';
                break;
            case 7:
                return 'juillet';
                break;
            case 8:
                return 'août';
                break;
            case 9:
                return 'septembre';
                break;
            case 10:
                return 'octobre';
                break;
            case 11:
                return 'novembre';
                break;
            case 12:
                return 'décembre';
                break;
            default:
                return $m;
                break;
        }

    }

    function getMonthName_Short($date = null, $m = 0)
    {
        // n - Monatszahl, ohne führende Nullen 1 bis 12
        //*** 20161107 OSR
        //*** Abkürzung der Monate so: Janv., Fév., Mars, Avr., Mai, Juin, Juil., Août, Sept., Nov., Déc. (kannst Du, wenn es gekoppelt ist auch in anderen Berichten so machen)

        //*** 20181103
        //*** Wochentage und Monatsnamen immer klein schreiben.
        //*** Kannst Du das zentral einstellen, oder soll ich Dir alle Stellen anzeigen, wo Wochen/Monatsnamen vorkommen?


        if (is_null($date)) {
        } else {
            $m = $date->format('n');
        }


        switch ($m) {
            case 1:
                return 'janv.';
                break;
            case 2:
                return 'fév.';
                break;
            case 3:
                return 'mars';
                break;
            case 4:
                return 'avr.';
                break;
            case 5:
                return 'mai';
                break;
            case 6:
                return 'juin';
                break;
            case 7:
                return 'juil.';
                break;
            case 8:
                return 'août';
                break;
            case 9:
                return 'sept.';
                break;
            case 10:
                return 'oct.';
                break;
            case 11:
                return 'nov.';
                break;
            case 12:
                return 'déc.';
                break;
            default:
                return $m;
                break;
        }
    }

    public function getTime($adate, $separator = 'h', $l_show_end=true)
    {
        //*** 20170206 OSR
        //*** Uhrzeiten immer mit Minuten anzeigen: 10h00 statt 10h

        $start = '';
        $end = '';

        if ($adate->start_) {
            $start = $adate->start_->format('H').$separator.$adate->start_->format('i');
        }
        if ($adate->end_) {
            $end = $adate->end_->format('H').$separator.$adate->end_->format('i');
        }
        if ($start == '00'.$separator.'00') {
            $start = '';
        }
        if ($end == '00'.$separator.'00') {
            $end = '';
        }

        $startend = $start . (!empty($end) ? ' - ' : '') . $end;

        return $startend;
    }

    public function addMemo($section, $text, $styleFont = array(), $stylePar = array())
    {
        $textlines = explode("\n", $text);
        foreach ($textlines as $line) {
            $section->addText(htmlspecialchars($line), $styleFont, $stylePar);
        }
    }

    public function addMemo_textrun($section, $text, $styleFont = array(), $stylePar = array())
    {
        $count = 0;
        $textlines = explode("\n", $text);
        foreach ($textlines as $line) {
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line), $styleFont, $stylePar);
        }
    }

    function prepareData_selected($postData, $model, $awhere=array())
    {
        if(sizeof($awhere)==0) {
            $awhere = ['Adates.id IN' => $postData['dataItemIds']];
        }

        $this->adates_selected = $model
            ->find('all')
            ->select()
            ->contain([
                'Sdatestatuses',
                'Sdresses',
                'Sseasons',
                'Sprojects' => ['Sprojecttypes'],
                'Seventtypes' => ['Seventtypegroups'],
                'Locationaddresses' => ['Scountries'],
                'Orchestraaddresses',
                'Conductoraddresses',
                'AdateWorks' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sworks' => ['Scomposers'],
                                'AdateworkSoloists' => ['Sinstrinstruments', 'Saddresses'],
                                'Sworkpremieres'
                            ])
                            ->orderAsc('AdateWorks.work_order');
                    },
                'AdatePersons' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Saddresses',
                                'Saddressgroups',
                                'Sinstrinstruments',
                                'Saddressfunctionitems'
                            ])
                            ->orderAsc('AdatePersons.person_order');
                    },
                'AdateActivities' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Seventtypes' => ['Seventtypegroups']
                            ])
                            ->orderAsc('AdateActivities.activity_order');
                    },
                'AdateSeries' =>
                    function (Query $query) {
                        return $query
                            ->contain([
                                'Sseries'
                            ])
                            ->orderAsc('Sseries.serie_order');
                    }
            ])
            ->where($awhere)
            ->order(['Adates.date_' => 'ASC', 'Adates.start_' => 'ASC'])
            ->all();

        /*

        */
        foreach ($this->adates_selected as $k => $adate) {
            $this->setProgramBesetzungsCode($adate);

        }

        return $this->adates_selected;
    }

    function getProgramCode($adate) {

        $project_id = $adate->project_id;
        $conductor_id = $adate->conductor_id;
        $programCode = $project_id.'#'.$conductor_id;
        //$programCode = $conductor_id;

        foreach ($adate->adate_works as $works) {
            $programCode .= '#'. $works->swork->id;
        }
        return $programCode;
    }

    function setProgramBesetzungsCode($adate)
    {

        $conductor_id = $adate->conductor_id;
        $programCode = $conductor_id;
        $besetzungscode = '';

        foreach ($adate->adate_works as $works) {
            $programCode .= '#' . $works->swork->id;

            $besetzungscode .= ($besetzungscode>'' ? '#' : '').
                (int)$works->violin1.' '.
                (int)$works->violin2.' '.
				(int)$works->viola.' '.
				(int)$works->cello.' '.
				(int)$works->bass.' '.

				(int)$works->flute.' '.
				(int)$works->oboe.' '.
				(int)$works->clarinet.' '.
				(int)$works->bassoon.' '.

				(int)$works->horn.' '.
				(int)$works->trumpet.' '.
				(int)$works->trombone.' '.
				(int)$works->tuba.' '.

				(int)$works->timpani.' '.
				(int)$works->percussion.' '.
				(int)$works->harp.' '.
				(int)$works->keyboard.' '.
				(int)$works->extra.' '.
				(int)$works->vocals;
        }

        $adate->programcode = $programCode;
        $adate->besetzungscode = $besetzungscode;
    }

    function getNumbers_by_type($address_id = -1, $numbertype = '')
    {
        $aaddressnumbers = TableRegistry::getTableLocator()->get('SaddressNumbers')
            ->find('all')
            ->contain('Snumbertypes', function (Query $query) {
                return $query->select([
                    'Snumbertypes.l_phone',
                    'Snumbertypes.l_mobile',
                    'Snumbertypes.l_email',
                    'Snumbertypes.name'
                ]);
            })
            ->where('address_id = ' . $address_id)
            ->toArray();

        $numbers = '';

        foreach ($aaddressnumbers as $anumber) {

            if (
                $anumber->number_ &&
                (
                    $anumber->snumbertype->l_phone == 1 && strtolower($numbertype) == 'l_phone' ||
                    $anumber->snumbertype->l_email == 1 && strtolower($numbertype) == 'l_email' ||
                    $anumber->snumbertype->l_mobile == 1 && strtolower($numbertype) == 'l_mobile'
                )
            ) {
                $numbers .= ($numbers > '' ? ', ' : '') . $anumber->number_;
            }
        }
        return $numbers;
    }

    public function getLongName($tname2 = '', $tname5 = '', $tname1 = '')
    {
        $name = trim(trim($tname2 . ' ' . $tname5) . ' ' . $tname1);
        return $name;
    }

    public function getSoloists(int $date_id)
    {

        $where = 'AdateWorks.date_id=' . $date_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order2')
            ->distinct();

        return $arows;
    }

    public function getSoloists_dw(int $datework_id)
    {

        $where = 'AdateWorks.id=' . $datework_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order')
            ->distinct();

        return $arows;
    }

    public function getShortName($tname2 = '', $tname5 = '', $tname1 = '', $format = '251')
    {

        $name = trim($tname2);

        $name2 = substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : '');

        while (strpos(' ', $name) !== false) {
            $name = trim(substr($name, 0, strpos(' ', $name)));

            $name2 = trim($name2 . ' ' . substr($name, 0, 1) . (!empty(substr($name, 0, 1)) ? '.' : ''));
        }

        switch (true) {
            case $format == '251':
                $name = trim($name2 . ' ' . trim(trim($tname5) . ' ' . trim($tname1)));
                break;

            default:
                $name = trim(trim($tname5) . ' ' . trim($tname1)) .
                    (!empty($name2) ? ', ' : '') . $name2;
                break;
        }

        return $name;
    }

    function getSeries($adates, $separator="\n")
    {
        $aseries = array();

        foreach ($adates as $date_id => $adate) {
            foreach ($adate->adate_series as $adate_serie) {
                $serie = $adate_serie->sseries->name;
                if ($serie > '') {
                    if (!in_array($serie, $aseries)) {
                        $aseries[] = $serie;
                    }
                }
            }
        }

        return implode($separator, $aseries);
    }

    function getSeries_code($adate)
    {
        $aseries = array();

        foreach ($adate->adate_series as $adate_serie) {

            $serie = $adate_serie->sseries->code;
            if ($serie > '') {
                if (!in_array($serie, $aseries)) {
                    $aseries[] = $serie;
                }
            }
        }


        return implode(", ", $aseries);
    }

    public function is_CH(int $address_id)
    {
        $where = ['SaddressAddressgroups.address_id'=>$address_id, 'Saddressgroups.code'=>'CH'];

        $SaddressAddressgroupsTable = TableRegistry::getTableLocator()->get('SaddressAddressgroups');

        $arows = $SaddressAddressgroupsTable
            ->find('all')
            ->select([
                'SaddressAddressgroups.id'
            ])
            ->contain([
                'Saddressgroups'
            ])
            ->where($where);

        $crow = '';
        foreach($arows as $arow) {
            $crow .= '#XXX#'.$arow->id;
        }
        return $crow>'';
    }

    function RGB2HTML($rgb)
    {
        $blue = ($rgb - $rgb % (256 * 256)) / (256 * 256);
        $rgb = $rgb % (256 * 256);
        $green = ($rgb - $rgb % (256)) / 256;
        $red = $rgb % 256;
        return $this->ITSS(dechex($red), 2) . $this->ITSS(dechex($green), 2) . $this->ITSS(dechex($blue), 2);
    }
    function ITSS($ii, $len)
    {
        while (strlen($ii) < $len) {
            $ii = '0' . $ii;
        }
        return $ii;
    }
/*
    function prepare_HTTP($string)
    {
        //*** 20201130
        //*** Egal wo in OPAS: sobald etwas mit https:// anfängt bis zum nächsten Leerzeichen, müsstest Du es als Hyperlink ausgeben.

        $string_new = $string;
        if (strpos($string, 'http') !== false) {

            $http = substr($string, strpos($string, 'http')+1);

            $string_new = substr($string, 0, strpos($string, 'http'));

            $string = ;

            if AT(' ', lcHttp) > 0
            lcHttp = SUBSTR(lcHttp, 1, AT(' ', lcHttp) - 1)
                tcString = SUBSTR(tcString, AT(' ', lcHttp))
            endif

            tcString = SUBSTR(tcString, AT('http', tcString))

            lcHttp = '{\field{\*\fldinst HYPERLINK "' + lcHttp + '"}{\fldrslt ' + lcHttp + '}}'

            lcString_new = lcString_new + lcHttp + tcString

                $section->addLink(
                    'The Indian Wire',
                    [
                        'url'=> 'link href...'
        'font'=> 'font name...'
        'color'=> 'text color...'

    ]
);

        endif



        return lcString_new


    }
*/
}
