<?php
/*
20230505 ONCUST-1673
OSR Tableaux de service musiciens
*/
require_once('adates_bible_osr.php');

class adates_service_musiciens_osr extends adates_bible_osr
{
    function initialize()
    {
        parent::initialize();

        $this->reporttype = 'tableaux';
        $this->caption_prefix = 'Tableau de service musiciens - ';



        //*** Größe Logo:
        //*** -Höhe: 2,12cm
        //*** -Breite: 1,38cm

        $this->logo_h = 2.12;
        $this->logo_w = 1.38;

        $this->nCN_Projects = 0;
        $this->nMaxColNum = 8;
    }

    public function check_eventtype() {
        $check_eventtype = parent::check_eventtype();

        /*
        *** 20150706
        *** skippst Du alle Termine ohne Projekt? Bitte auch Termine ohne Projekt im Auswahlzeitraum anzeigen (sorry, kann sein, dass wir das mal anders gesagt haben 9)
	        *** INNER JOIN crsDates_Season_All ON crsDates_Season_All.Date_id == crsData_Ext.Date_id;

	        *** 20160928
        *** in den Zellen soll wegfallen
        *** a) zusätzl. Aktivitäten
        *** b) alle Termine der Gruppe "Autour des concerts" Code: AdC
        *** c) alle Termine aus Terminartgruppe "Audiovisuel" Code AV
        *** d) ebenso alle Termine der Terminartgruppe Code PEDsans weglassen
        *** e) alle Termine des Produktionstyp "OCL" weglassen

        *** 20161005
        *** nicht anzeigen:
            *** -alle Termine der Gruppe "Autour des concerts" (Code AdC)
            *** alle Termine der Gruppe "Sans l'orchestre" (Code OSRsans) (=neue Gruppe, gerade angelegt)
            *** - Ort bei Terminart "Raccord" (Generalprobe)
        *** - Ort bei Terminart "raccord ANNULÉ"
        *** - Projekte des Typs Code " OCL "

        *** 20170704
        *** 1.	Osr nutzt niveau 3 um anzuzeigen, ob der Saal verfügbar ist oder nicht, normalerweise haben die Termine "salle résa x" kein Projekt.
	        *** Wenn der Saal aber okkupiert ist, weil ein anderes Orchester da ist, dann gibt es schon ein Projekt und dann fehlen leider die Termine "salle résa x" im Bericht.
	        *** zB diese 2 Termine: 06./07.12.2017 Niveau 3
        *** d.h. bitte unabhängig vom Projekttyp alle Termine aus Niveau 3 im Bericht "Réservation de salles" anzeigen


        */
        if($check_eventtype) {

            $eventtypegroup_code = '';
            if(isset($this->adate->seventype->seventtypegroup)) {
                $eventtypegroup_code = $this->adate->seventype->seventtypegroup->code;
            }

            $eventtype = '';
            if(isset($this->adate->seventype)) {
                $eventtype = $this->adate->seventype->name;
            }

            $projecttype_code = '';
            if(isset($this->adate->sproject->sprojecttype)) {
                $projecttype_code = $this->adate->sproject->sprojecttype->code;
            }

            if(
                mb_strtoupper($eventtypegroup_code) == mb_strtoupper('AdC') ||
                mb_strtoupper($eventtypegroup_code) == mb_strtoupper('AV') ||
                mb_strtoupper($eventtypegroup_code) == mb_strtoupper('AdC') ||
                mb_strtoupper($eventtypegroup_code) == mb_strtoupper('OSRsans') ||
                mb_strtoupper($eventtype) == mb_strtoupper('raccord') ||
                mb_strtoupper($eventtype) == mb_strtoupper('raccord ANNULÉ') ||
                (mb_strtoupper($projecttype_code) == mb_strtoupper('OCL') && $this->adate->planninglevel<>3)
            ) {
                $check_eventtype = false;
            }
        }
        return $check_eventtype;
    }

    function setMargin_Widths() {
        /*
        *** Ränder:
        *** -Oben, Unten und rechts: 0,5
        *** -Links: 0,2

        *** 20161005
        *** Seite einrichten:
		*** - Rand : alle auf 0.2
        *** - Seite : zoom 40%
         *
         */

        parent::setMargin_Widths();

        $this->sheet->getPageMargins()->setTop(0.2/2.5);
        $this->sheet->getPageMargins()->setRight(0.2/2.5);
        $this->sheet->getPageMargins()->setLeft(0.2/2.5);
        $this->sheet->getPageMargins()->setBottom(0.2/2.5);
        $this->sheet->getPageMargins()->setHeader(0/2.5);
        $this->sheet->getPageMargins()->setFooter(0/2.5);

        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_First))->setWidth(7+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Monday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Tuesday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Wednesday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Thursday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Friday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Saturday))->setWidth(50.78+0.71);
        $this->sheet->getColumnDimension($this->getColumnLetter($this->nCN_Sunday))->setWidth(41.44+0.71);


        $this->weekday_height = 82.5;
        // *** 20161018
        // *** die Höhe der Zeilen 2,6,10 und 14  soll 18.00 sein.
        $this->weekheader_height = 18;// &&20.40&&26.25
        $this->firstrow_height = 30; //41.25

    }
}
