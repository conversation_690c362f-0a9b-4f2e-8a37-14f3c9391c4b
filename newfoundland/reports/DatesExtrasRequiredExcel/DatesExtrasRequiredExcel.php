<?php

namespace Customer\newfoundland\reports\DatesExtrasRequiredExcel;

use App\Reports\Report;
use Cake\Core\Configure;

use Customer\fasutilities\reports\OnSpreadsheet\OnSpreadsheet;

use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\StoredInstrumentationQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/*
 * Report used to show max instrumentation required for a set of Projects,
 *  and how many additional musicians the orchestra will need to hire, based on the
 *   the orchestra's core size
 */

class DatesExtrasRequiredExcel extends Report
{
    //    Change these to the preferred color for the client
    const PRIMARY_COLOR = '58101b';  // OPAS Next Darkest Red
    const SECONDARY_COLOR = 'ecefe0';

    //    Change these values to account for heading info in top rows
//      text in rows  1 or 2. If kept as below, header is row
//      1 and data block starts at row 2
    const HEADER_ROW = 5;
    const DATA_ROW = 8;
//     as currently configured...
//  Column A has Project name
//  Column B has first concert date
//  Instrumentation starts in column C
//  Column V is the right-most column
    const FINAL_COLUMN = 'V';

    /**
     * Helper class for selected date records
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for stored Instrumentations
     * @var StoredInstrumentationQueryHelper
     */
    private $storedInstrumentationQueryHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;


    public function initialize()
    {
        $this->dateQueryHelper = new DateQueryHelper();
        $this->storedInstrumentationQueryHelper = new StoredInstrumentationQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();
    }

    /*********************************************/
    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * @param array $where
     * @return $this|Report
     * INCLUDE venue, conductor, orchestra addresses
     * Future versions of this report may render different events on different days,
     *   but for now, use only One event in case the user selects more than one in OPAS Next
     */
    public function collect(array $where = [])
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray(0);

        return $this;
    }

    /**
     * @param null $view
     * @param null $layout
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function render($view = null, $layout = null): string
    {
        $fileExtension = '.xlsx';
        $spreadsheet = new OnSpreadsheet();
        $repFileText = $this->getRepFileParams();

//        report date/time settings from client settings.php
        $reportDateFormat = Configure::read('Formats.date');

//        +++++ DOCUMENT PROPERTIES ++++++
//        In most use cases, the user will format the spreadsheet for printing
//          so those properties are omitted
        $spreadsheet->getProperties()
            ->setCreator(htmlspecialchars(Configure::read('CustomerSettings.name')))
            ->setTitle($repFileText['report_title']);
        $defaultFont = 'Calibri';
        $defaultFontSize = 11;

//        GET CORE ORCHESTRA SIZE from STORED INSTRUMENTATION
        $instrumentations = $this->storedInstrumentationQueryHelper->getStoredInstrumentations()->getQuery()->toArray();
        foreach ($instrumentations as $instrumentation) {
            if ($instrumentation['l_owninstrumentation'] == 1) {
                $coreInstrumentation = $instrumentation;
            }
        }

//        RENDER TITLE and ORCHESTRA'S CORE INSTRUMENTATION LINE
//        Manually place any title lines above the Header Row constant. Format Title Cells here to keep cell Addresses easier to manage
        $spreadsheet->getActiveSheet()->setCellValue('A1', Configure::read('CustomerSettings.name'));
        $spreadsheet->getActiveSheet()->getStyle('A1')
            ->getFont()
            ->setName($defaultFont)
            ->setSize(14)
            ->setBold(true);
        $spreadsheet->getActiveSheet()->setCellValue('A2', $repFileText['report_title']);
        $spreadsheet->getActiveSheet()->getStyle('A2')
            ->getFont()
            ->setName($defaultFont)
            ->setSize(12)
            ->setBold(true);
        $spreadsheet->getActiveSheet()->setCellValue('A3', $repFileText['printed'] . ' ' . date('l, F j, Y'));

        $this->renderInstrumentationHeading($spreadsheet, $repFileText);
        $this->renderCoreInstrumentation($spreadsheet, $coreInstrumentation, $repFileText);


// GET PROJECTS
        $projects = $this->getProjects($this->datesResult);
        $numberOfProject = sizeof($projects);
        $row = self::DATA_ROW;

//       Loop through each project
        foreach ($projects as $project) {
//            identify the Anchor Date for the project and get the max instrumentation
            $anchorDate = $this->getAnchorDate($project);

            if ($anchorDate !== '') {
                $projectMaxArray = [];
                $musiciansNeededArray = [];
                $this->instrumentation->addInstrumentationString($anchorDate);

//                Create Arrays for MAx Required and difference from client's core orchestra
                $projectMaxArray[] = array(
                    $project['name'] . ' ' . $repFileText['max'] . ': ',
                    $anchorDate['date_']->format($reportDateFormat),

                    $anchorDate['max_array']['flute']['number'],
                    $anchorDate['max_array']['oboe']['number'],
                    $anchorDate['max_array']['clarinet']['number'],
                    $anchorDate['max_array']['bassoon']['number'],

                    $anchorDate['max_array']['horn']['number'],
                    $anchorDate['max_array']['trumpet']['number'],
                    $anchorDate['max_array']['trombone']['number'],
                    $anchorDate['max_array']['tuba']['number'],

                    $anchorDate['max_array']['timpani']['number'],
                    $anchorDate['max_array']['percussion']['number'],
                    $anchorDate['max_array']['harp']['number'],
                    $anchorDate['max_array']['keyboard']['number'],
                    $anchorDate['max_array']['extra']['number'],

                    $anchorDate['max_array']['violin1']['number'],
                    $anchorDate['max_array']['violin2']['number'],
                    $anchorDate['max_array']['viola']['number'],
                    $anchorDate['max_array']['cello']['number'],
                    $anchorDate['max_array']['bass']['number'],

                    $anchorDate['max_instrumentation_grid_name']['keyboards'],
                    $anchorDate['max_instrumentation_grid_name']['extras'],

                );
                $musiciansNeededArray[] = array(
                    $repFileText['additional'],
                    ' ',
                    $anchorDate['max_array']['flute']['number'] - $coreInstrumentation['flute'] > 0 ? $anchorDate['max_array']['flute']['number'] - $coreInstrumentation['flute'] : 0,
                    $anchorDate['max_array']['oboe']['number'] - $coreInstrumentation['oboe'] > 0 ? $anchorDate['max_array']['oboe']['number'] - $coreInstrumentation['oboe'] : 0,
                    $anchorDate['max_array']['clarinet']['number'] - $coreInstrumentation['clarinet'] > 0 ? $anchorDate['max_array']['clarinet']['number'] - $coreInstrumentation['clarinet'] : 0,
                    $anchorDate['max_array']['bassoon']['number'] - $coreInstrumentation['bassoon'] > 0 ? $anchorDate['max_array']['bassoon']['number'] - $coreInstrumentation['bassoon'] : 0,

                    $anchorDate['max_array']['horn']['number'] - $coreInstrumentation['horn'] > 0 ? $anchorDate['max_array']['horn']['number'] - $coreInstrumentation['horn'] : 0,
                    $anchorDate['max_array']['trumpet']['number'] - $coreInstrumentation['trumpet'] > 0 ? $anchorDate['max_array']['trumpet']['number'] - $coreInstrumentation['trumpet'] : 0,
                    $anchorDate['max_array']['trombone']['number'] - $coreInstrumentation['trombone'] > 0 ? $anchorDate['max_array']['trombone']['number'] - $coreInstrumentation['trombone'] : 0,
                    $anchorDate['max_array']['tuba']['number'] - $coreInstrumentation['tuba'] > 0 ? $anchorDate['max_array']['tuba']['number'] - $coreInstrumentation['tuba'] : 0,

                    $anchorDate['max_array']['timpani']['number'] - $coreInstrumentation['timpani'] > 0 ? $anchorDate['max_array']['timpani']['number'] - $coreInstrumentation['timpani'] : 0,
                    $anchorDate['max_array']['percussion']['number'] - $coreInstrumentation['percussion'] > 0 ? $anchorDate['max_array']['percussion']['number'] - $coreInstrumentation['percussion'] : 0,
                    $anchorDate['max_array']['harp']['number'] - $coreInstrumentation['harp'] > 0 ? $anchorDate['max_array']['harp']['number'] - $coreInstrumentation['harp'] : 0,
                    $anchorDate['max_array']['keyboard']['number'] - $coreInstrumentation['keyboard'] > 0 ? $anchorDate['max_array']['keyboard']['number'] - $coreInstrumentation['keyboard'] : 0,
                    $anchorDate['max_array']['extra']['number'] - $coreInstrumentation['extra'] > 0 ? $anchorDate['max_array']['extra']['number'] - $coreInstrumentation['extra'] : 0,

                    $anchorDate['max_array']['violin1']['number'] - $coreInstrumentation['violin1'] > 0 ? $anchorDate['max_array']['violin1']['number'] - $coreInstrumentation['violin1'] : 0,
                    $anchorDate['max_array']['violin2']['number'] - $coreInstrumentation['violin2'] > 0 ? $anchorDate['max_array']['violin2']['number'] - $coreInstrumentation['violin2'] : 0,
                    $anchorDate['max_array']['viola']['number'] - $coreInstrumentation['viola'] > 0 ? $anchorDate['max_array']['viola']['number'] - $coreInstrumentation['viola'] : 0,
                    $anchorDate['max_array']['cello']['number'] - $coreInstrumentation['cello'] > 0 ? $anchorDate['max_array']['cello']['number'] - $coreInstrumentation['cello'] : 0,
                    $anchorDate['max_array']['bass']['number'] - $coreInstrumentation['bass'] > 0 ? $anchorDate['max_array']['bass']['number'] - $coreInstrumentation['bass'] : 0
                );
            }
//            RENDER MAX INSTRUMENTATION, ADDITIONAL REQUIRED and Blank Row
//            everything is rendered as a single row array to make future customizations and layouts easier
            if ($anchorDate !== '') {
                $spreadsheet->getActiveSheet()->fromArray($projectMaxArray, null, 'A' . $row, true);
                $row = $row + 1;
                $spreadsheet->getActiveSheet()->fromArray($musiciansNeededArray, null, 'A' . $row, true);
                $row++;
                $row++;
            }

        }


//        +++ FORMAT SPREADSHEET ++++
        $this->formatSheet($row, 'U', $repFileText, $spreadsheet, $defaultFont, $defaultFontSize);

        $fileName = $this->_createFileName();
        $path = ROOT_REP_IN_DIR . $fileName . $fileExtension;
        $writer = new Xlsx($spreadsheet);
        $writer->save($path);

        return $fileName . $fileExtension;
    }

    private function getProjects($datesResult): array
    {
//  Create array of unique projects; then sort by criteria client wants.
//        Default sort is project order, then name
        $projectArray = [];
        foreach ($datesResult as $dateResult) {
            $projectArray[] = array(
                'id' => $dateResult['project_id'],
                'order' => $datesResult['sproject']['project_order'],
                'name' => $dateResult['sproject']['name']
            );
        }
        $projectArray = array_unique($projectArray, SORT_REGULAR);

        /**
         * @see https://stackoverflow.com/a/2699159
         */
        usort(
            $projectArray,
            function ($a, $b) {
                return $a['order'] . $a['name'] <=> $b['order'] . $b['name'];
            }
        );

        return $projectArray;
    }

    private function getAnchorDate($project)
    {
//        for First Concert with repertoire in the Project, return the adates OBJECT

        foreach ($this->datesResult as $date) {

            if ($date['project_id'] == $project['id'] && $date['seventtype']['l_performance'] == 1 && !empty($date['adate_works'])) {
                return $date;
            }
        }
        return '';
    }

    private function renderInstrumentationHeading($spreadsheet, $repFileText)
    {
        $instHeaderArray = array(
            $repFileText['fl'] ?? __('fl'),
            $repFileText['ob'] ?? __('ob'),
            $repFileText['cl'] ?? __('cl'),
            $repFileText['bn'] ?? __('bn'),
            $repFileText['hn'] ?? __('hn'),
            $repFileText['tp'] ?? __('tp'),
            $repFileText['tb'] ?? __('tb'),
            $repFileText['tu'] ?? __('tu'),

            $repFileText['tmp'] ?? __('timp'),
            $repFileText['perc'] ?? __('prc'),
            $repFileText['harp'] ?? __('harp'),
            $repFileText['kybd'] ?? __('kbd'),
            $repFileText['extra'] ?? __('ext'),

            $repFileText['v1'] ?? __('vl1'),
            $repFileText['v2'] ?? __('vl2'),
            $repFileText['va'] ?? __('vla'),
            $repFileText['vc'] ?? __('cel'),
            $repFileText['db'] ?? __('bas'),

            $repFileText['key_grid'] ?? __('swork_keyboards'),
            $repFileText['extra_grid'] ?? __('swork_extras'),
        );
        $spreadsheet->getActiveSheet()->fromArray($instHeaderArray, null, 'C' . self::HEADER_ROW, true);
    }

    private function renderCoreInstrumentation($spreadsheet, $coreInstrumentation, $repFileText)
    {
        $coreInstArray = array(
            $repFileText['coreLabel'],
            '',

            $coreInstrumentation['flute'] ?? 0,
            $coreInstrumentation['oboe'] ?? 0,
            $coreInstrumentation['clarinet'] ?? 0,
            $coreInstrumentation['bassoon'] ?? 0,
            $coreInstrumentation['horn'] ?? 0,
            $coreInstrumentation['trumpet'] ?? 0,
            $coreInstrumentation['trombone'] ?? 0,
            $coreInstrumentation['tuba'] ?? 0,

            $coreInstrumentation['timpani'] ?? 0,
            $coreInstrumentation['percussion'] ?? 0,
            $coreInstrumentation['harp'] ?? 0,
            $coreInstrumentation['keyboard'] ?? 0,
            $coreInstrumentation['extra'] ?? 0,

            $coreInstrumentation['violin1'] ?? 0,
            $coreInstrumentation['violin2'] ?? 0,
            $coreInstrumentation['viola'] ?? 0,
            $coreInstrumentation['cello'] ?? 0,
            $coreInstrumentation['bass'] ?? 0
        );
        $spreadsheet->getActiveSheet()->fromArray($coreInstArray, null, 'A' . (self::HEADER_ROW + 1), true);
    }

    private function formatSheet($row, $finalColumn, $repFileText, $spreadsheet, $defaultFont, $defaultFontSize)
    {
        $finalRow = $row - 1;
//        because the formatting stops at column !== last column, we need to move the 'final column'
//        one column to the right to include the actual last column with data in it
        $finalColumn = ++$finalColumn;

//         SET COLUMN WIDTHS FOR non-instrumentation columns
        foreach (array('A', 'U', 'V') as $col) {
            $spreadsheet->getActiveSheet()->getColumnDimension($col)->setWidth(25);
        }
//        SET COLUMN WIDTH FOR date column
        $spreadsheet->getActiveSheet()->getColumnDimension('B')->setWidth(15);

//        FORMAT INSTRUMENTATION HEADER ROW
        $spreadsheet->getActiveSheet()
            ->getStyle('A' . self::HEADER_ROW . ':' . $finalColumn . self::HEADER_ROW)
            ->applyFromArray(
                $this->getHeaderFormat($defaultFont, $defaultFontSize)
            );
        $spreadsheet->getActiveSheet()
            ->getStyle('C' . self::HEADER_ROW . ':' . 'T' . self::HEADER_ROW)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

//        BOLD PROJECT NAMES and Set "Additional Needed" to color for ease of reading
//        $phpColor = new PHPExcel_Style_Color();
//        $phpColor->setRGB('FF0000');
        for ($row = self::DATA_ROW; $row <= $finalRow; $row++) {
            if ($spreadsheet->getActiveSheet()->getCell('A' . $row)->getValue() == $repFileText['additional']) {
                $spreadsheet->getActiveSheet()->getStyle('C' . $row . ':' . 'T' . $row)
                    ->applyFromArray($this->getAdditionalFormat($defaultFont, $defaultFontSize));
            } else {
                $spreadsheet->getActiveSheet()->getStyle('A' . $row)
                    ->getFont()
                    ->setName($defaultFont)
                    ->setSize($defaultFontSize)
                    ->setBold(true);
            }
        };

//  DROP IN VERTICAL BORDERS where client wants - currently after Winds, Brass , Extra and Bass
        foreach (array('F', 'J', 'O', 'T') as $col) {
            $spreadsheet->getActiveSheet()
                ->getStyle($col . self::DATA_ROW . ':' . $col . $finalRow)
                ->applyFromArray($this->getBorderStyle());
        }

//       +++++ Freeze Panes +++++
        $spreadsheet->getActiveSheet()->freezePane('C' . self::DATA_ROW);

//        return to top of sheet
        $spreadsheet->getActiveSheet()->setSelectedCells('A1');
    }

    protected function getHeaderFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => self::PRIMARY_COLOR) //
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            )
        );
    }

    protected function getAdditionalFormat($defaultFont, $defaultFontSize)
    {
        return array(

            'fill' => array(
                'fillType' => Fill::FILL_SOLID,
                'startColor' => array('rgb' => self::SECONDARY_COLOR) //
            ),

            'font' => array(
                'name' => $defaultFont,
                'bold' => true,
//                'color' => array('rgb' => 'FFFFFF'),
//              'italic' => true,
                'size' => $defaultFontSize
            )
        );
    }

    protected function getBorderStyle()
    {
        return array(
            'borders' => [

                'right' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => array('argb' => self::PRIMARY_COLOR)
                ]


            ]
        );
    }
}
