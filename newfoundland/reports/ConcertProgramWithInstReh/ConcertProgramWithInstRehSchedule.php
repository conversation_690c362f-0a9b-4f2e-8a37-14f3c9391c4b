<?php

namespace Customer\newfoundland\reports\ConcertProgramWithInstReh;

use Cake\Core\Configure;

use Customer\fasutilities\reports\utility\DateQueryHelper;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

/**
 * Render the bottom, schedule table - each event in the Project + Season
 * Date | Time | Event + text | Venue | Program, Notes, etc
 */
class  ConcertProgramWithInstRehSchedule
{

    const COL_1_WIDTH = 0.25; // indent
    const COL_2_WIDTH = 1.65;
    const COL_3_WIDTH = 2.45;
    const COL_4_WIDTH = 3.00;

    /**
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(
        array $scheduleDates,
        array $composersWithMultipleWorks,
        Section $pageSection,
        $repFileText
    ) {
        $this->dateQueryHelper = new DateQueryHelper();

        $this->scheduleDates = $scheduleDates;
        $this->pageSection = $pageSection;
        $this->composersWithMultipleWorks = $composersWithMultipleWorks;
//        labels from the report .rep file
        $this->repFileText = $repFileText;

        $this->reportStyles = new ConcertProgramWithInstRehStyles();
    }

    public function renderSchedule()
    {
        //        date and time formats from rep file or settings.php
        $scheduleTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');

        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $defaultCell = $this->reportStyles->defaultCell;

        $days = $this->getCalendarDays($this->scheduleDates);

        $this->renderHeaderTable($defaultFontBold, $defaultParagraph, $this->repFileText, $defaultCell, $this->reportStyles->shadedCell);

        $calendarDay = '';
        foreach ($days as $day) {
//      Render Day outside the table, then events within the table
            if ($day != $calendarDay) {
                $this->pageSection->addText(
                    $day->format('F, l j'),
                    $this->reportStyles->defaultFontBold,
                    $defaultParagraph
                );
            }
            $scheduleTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);

            foreach ($this->scheduleDates as $scheduleDate) {
                if ($scheduleDate['date_'] == $day) {
//          Fetch elements for the schedule row: time, event, reh order
                    if (!is_null($scheduleDate['start_']) && trim($scheduleDate['start_'] != '')) {
                        $eventTime = $scheduleDate['start_']->format($scheduleTimeFormat);
                        if (!is_null($scheduleDate['end_']) && trim($scheduleDate['end'] != '')) {
                            $eventTime .= ' - ' . $scheduleDate['end_']->format($scheduleTimeFormat);
                        }
                    } else {
                        $eventTime = '';
                    }

                    $activity = $scheduleDate['seventtype']['name'];
                    if (!is_null($scheduleDate['text']) && trim($scheduleDate['text']) !== '') {
                        $activity .= ' - ' . $scheduleDate['text'];
                    }
                    if ($scheduleDate['location_id'] > 0) {
                        if (!is_null($scheduleDate['locationaddress']['code']) && trim(
                                $scheduleDate['locationaddress']['code']
                            ) !== '') {
                            $venueName = $scheduleDate['locationaddress']['code'];
                        } else {
                            $venueName = trim(
                                $scheduleDate['locationaddress']['name2'] . ' ' . $scheduleDate['locationaddress']['name1']
                            );
                        }
                        $activity .= ' / ' . $venueName;
                    }

//          Fetch Rehearsal Order from query helper because we need the soloist
                    $rehOrderArray = [];
                    $rehWorks = $this->dateQueryHelper->getDateWorksForDateID($scheduleDate['id'])->getQuery()->toArray(
                    );
                    foreach ($rehWorks as $rehWork) {
                        $workTitle = $rehWork['swork']['scomposer']['lastname'];
                        if (in_array(
                                $rehWork['swork']['composer_id'],
                                $this->composersWithMultipleWorks,
                                false
                            ) == true) {
                            if (!is_null($rehWork['title3']) && trim($rehWork['title3']) !== '') {
                                $workTitle .= ': ' . $rehWork['title3'];
                            } elseif (!is_null($rehWork['title2']) && trim($rehWork['title2']) !== ''

                            ) {
                                $workTitle .= ': ' . $rehWork['title2'];
                            } else {
                                $workTitle .= ': ' . $rehWork['swork']['title1'];
                            }
                        }
//            check for soloist or choir
                        (bool)$hasSoloist = false;
                        (bool)$hasChoir = false;
                        $soloistTag = '';
                        if (!empty($rehWork['adatework_soloists'])) {
                            foreach ($rehWork['adatework_soloists'] as $rehWorkSoloist) {
                                $instSysSection = $rehWorkSoloist['sinstrinstrument']['sinstrsection']['syssection_id'];

                                if ($instSysSection > 0 && $instSysSection != 19) {
                                    $hasSoloist = true;
                                }
                                if ($instSysSection == 19) {
                                    $hasChoir = true;
                                }

                                if ($hasSoloist == true && $hasChoir == true) {
                                    $soloistTag = $this->repFileText['soloChoirTag'];
                                }
                                if ($hasSoloist == true && $hasChoir == false) {
                                    $soloistTag = $this->repFileText['soloTag'];
                                }
                                if ($hasSoloist == false && $hasChoir == true) {
                                    $soloistTag = $this->repFileText['choirTag'];
                                }
                                $workTitle .= ' ' . $soloistTag;
                            }
                        }
                        $rehOrderArray[] = trim($workTitle) ;
                    }
                    $rehWorkOutput = implode('; ',  array_unique($rehOrderArray) );


// RENDER SCHEDULE TABLE
                    $scheduleTable->addRow();
                    $scheduleTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $defaultCell)
                        ->addText('', $defaultFont, $defaultParagraph);
                    $scheduleTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->bottomBorderCell
                    )
                        ->addText($eventTime, $defaultFont, $defaultParagraph);
                    $scheduleTable->addCell(
                        Converter::inchToTwip(self::COL_3_WIDTH),
                        $this->reportStyles->bottomBorderCell
                    )
                        ->addFormattedText($activity, $defaultFont, $defaultParagraph);
                    $scheduleTable->addCell(
                        Converter::inchToTwip(self::COL_4_WIDTH),
                        $this->reportStyles->bottomBorderCell
                    )
                        ->addFormattedText($rehWorkOutput, $defaultFont, $defaultParagraph);
                }
            }
            $this->pageSection->addTextBreak(1, $this->reportStyles->movementFont, $defaultParagraph);


            $calendarDay = $day;
        }
    }

    protected function getCalendarDays($scheduleDates): array
    {
        $calendarDays = [];
        foreach ($scheduleDates as $scheduleDate) {
            $calendarDays[] = $scheduleDate['date_'];
        }
        return array_unique($calendarDays);
    }

    protected function renderHeaderTable($defaultFontBold, $defaultParagraph, $repFileText, $defaultCell, $shadedCell)
    {
        $headerTable = $this->pageSection->addTable($this->reportStyles->defaultTableStyle);
        $headerTable->addRow();
        $headerTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH),$defaultCell)
            ->addText($repFileText[''], $defaultFontBold, $defaultParagraph);
        $headerTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $shadedCell)
            ->addText($repFileText['date'], $defaultFontBold, $defaultParagraph);
        $headerTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $shadedCell)
            ->addText($repFileText['event'], $defaultFontBold, $defaultParagraph);
        $headerTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $shadedCell)
            ->addText($repFileText['repertoire'], $defaultFontBold, $defaultParagraph);
    }

}
