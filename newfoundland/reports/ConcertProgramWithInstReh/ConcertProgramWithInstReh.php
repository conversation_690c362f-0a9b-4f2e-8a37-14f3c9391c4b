<?php

namespace Customer\newfoundland\reports\ConcertProgramWithInstReh;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Carbon\Carbon;

use Cake\Core\Configure;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DateWorkQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\reports\utility\ComposerQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;

/*
 * Standard Concert Program WITH INSTRUMENTATION
 * Allow for different instrumentation outputs based on regional preference
 *
 * Each project + program number composition -  Conductor, date-work conductor, soloists, works and movements
 * This version omits composer dates and puts work duration in place of Composition Dates. Instrumentation below
 *    each work entry. Margins are smaller and table re-sized to allow for more room for instrumentation
 *
 * Allow for many client customization for time/duration formats (regional preferences, etc.)
 *   TO SPELL OUT "  2 hours 12 minutes"
 *   use Carbon\CarbonInterval;
 *   CarbonInterval::seconds($totalDur)->cascade()->forHumans();
 *
 * TO DO: create a wizard that allows user to select detailed/standard instrumentation and how to output
 *    date-work instrumentation grids
 */

class ConcertProgramWithInstReh extends ReportWord
{
//    table is 7.25 in total width
    const COL_1_WIDTH = 2.15;
    const COL_2_WIDTH = 4.35;
    const COL_3_WIDTH = 0.75;

    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * Helper class for the compositions on each concert
     *
     * @var DateWorkQueryHelper
     */
    private $dateWorkQueryHelper;

    /**
     * @var ComposerQueryHelper
     */
    private $composerQueryHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->dateWorkQueryHelper = new DateWorkQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->composerQueryHelper = new ComposerQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        // Set report font, table and paragraph styles
        $this->reportStyles = new ConcertProgramWithInstRehStyles();
    }

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }

    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Date records selected by user
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

//    Typically the use case is to run this report for one project at a time. However, the user could run it for
//      more than one. this function breaks the selected dates down into each unique season:project:program number set
    private function getProjects(): array
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concert Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all representative conductor, soloist, title, works, etc. information
    private function getAnchorDate(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
        return '';
    }

    private function renderReport()
    {
//        Set report formats based on client region and preferences
        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
        $topDateFormat = Configure::read('Formats.programHeaderDate') ?? Configure::read('Formats.date');
        $topTimeFormat = Configure::read('Formats.programHeaderTime') ?? Configure::read('Formats.time_short');
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.5),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.60),
                'breakType' => 'continuous',
            ]
        );

//        for Dates selected by User, loop through Season+Project+ProgramNumber, putting a page break between each
        $p = 0;
        foreach ($this->getProjects() as $project) {
            if ($p > 0) {
                $pageSection->addPageBreak();
            }

//            Anchor Date - date Object
            $concertDate = $this->getAnchorDate($project);
//            prevent error from instrumentation helper if user does not select any concerts
            if (!empty($concertDate)) {
                $this->instrumentation->addInstrumentationString($concertDate);
            }


//            Top section of report prints all concert dates in the Season+Project+ProgramNumber set.
//            If all concerts take place in the same venue, venue name is below concert dates, otherwise after concert date
            foreach ($this->datesResult as $headerDate) {
                if ($headerDate['season_id'] . ':' . $headerDate['project_id'] . ':' . $headerDate['programno'] === $project
                    && $headerDate['seventtype']['l_performance'] == 1) {
                    $printConcertDate = $headerDate['date_']->format($topDateFormat)
                        . ' ' . __('at') . ' ' . $headerDate['start_']->format($topTimeFormat);

                    if ($headerDate['location_id'] > 0) {
                        $headerVenue = $this->getHeaderVenues($project);
//                   if $headerVenue is empty, then there is more than one concert venue so append venue to each concert date
                        if (strlen(trim($headerVenue)) == 0) {
                            $headerVenueName = $headerDate['locationaddress']['name2'] . ' ' . $headerDate['locationaddress']['name1'];
                            $printConcertDate .= ' - ' . trim(htmlspecialchars($headerVenueName));
                        }
                    } else {
                        $headerVenue = '';
                    }

//              render Header Dates
                    $pageSection->addText($printConcertDate, $this->reportStyles->dateHeaderFont, $defaultParagraph);
                }
            }
//              If all concerts take place in the same venue, print that venue under the final concert date
            if (strlen(trim($headerVenue)) != 0) {
                $pageSection->addFormattedText($headerVenue, $this->reportStyles->dateHeaderFont, $defaultParagraph);
            }
            $pageSection->addTextBreak(1, $defaultFont, $this->reportStyles->defaultParagraphCenter);

//               +++ use ANCHOR DATE to render all other information +++
            $orchestraName = trim(
                $concertDate['orchestraaddress']['name2'] . ' ' . $concertDate['orchestraaddress']['name1']
            );
            $pageSection->addFormattedText($orchestraName ?? '', $this->reportStyles->orchestraFont, $defaultParagraph);

            $projectName = $concertDate['sproject']['name'];
            if (!is_null($concertDate['programno']) && (string)trim($concertDate['programno']) !== '') {
                $projectName .= ' ';
                $projectName .= $repFileText['programNo_Prefix'] ?? __('adates.programno');
                $projectName .= ' ' . mb_strtoupper($concertDate['programno']);
            }
            $pageSection->addFormattedText($projectName, $this->reportStyles->projectFont, $defaultParagraph);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

//                   +++ RENDER HEADER PERSONNEL and CONCERT TITLE
            $this->renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph);
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            if (!is_null($concertDate['programtitle']) && trim($concertDate['programtitle']) !== '') {
                $pageSection->addFormattedText(
                    $concertDate['programtitle'],
                    $this->reportStyles->dateHeaderFont,
                    $this->reportStyles->defaultParagraphCenter
                );
            }
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);


//          ++++  RENDER  CONCERT PROGRAM ++++
            $firstHalfDur = $totalDur = $intDur = 0;
            $programTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $dateWorks = $this->dateWorkQueryHelper->getDateWorksForDateID($concertDate['id'])
                ->withMovements()
                ->withDateWorkConductor()
                ->withInstrumentation()
                ->getQuery()
                ->toArray();
            $this->instrumentation->addInstrumentationStringToWork($dateWorks);
            $composer = 0;
            $intOrder = $this->getIntermissionOrder($dateWorks);
            foreach ($dateWorks as $dateWork) {
                if ($dateWork['l_encore'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                    $programTable->addRow();
                    $leftColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_1_WIDTH),
                        $this->reportStyles->leftColumnCell
                    );
                    $centerColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->centerColumnCell
                    );
                    $rightColumnCell = $programTable->addCell(
                        Converter::inchToTwip(self::COL_3_WIDTH),
                        $this->reportStyles->defaultCell
                    );

//                            RENDER COMPOSER and DATES - suppress composer name if same composer as previous work
                    if ($dateWork['swork']['l_intermission'] == 0
                        && $dateWork['swork']['composer_id'] != $composer) {
                        $composerName = $this->composerQueryHelper->getComposerName(
                            $dateWork['swork']['scomposer']['lastname'],
                            $dateWork['swork']['scomposer']['firstname'],
                            1
                        );
                        $composerName .= $dateWork['arrangement'] ? ' (' . $dateWork['arrangement'] . ')' : '';
                        $composerDates = $this->composerQueryHelper->getComposerDates(
                            $dateWork['swork']['scomposer']['birthyear'],
                            $dateWork['swork']['scomposer']['deathyear']
                        );
                    } else {
                        $composerName = '';
                        $composerDates = '';
                    }

                    $leftColumnCell->addFormattedText(
                        mb_strtoupper($composerName),
                        $defaultFontBold,
                        $this->reportStyles->defaultParagraphRight
                    );
//                            if (!empty($composerDates)) {
//                                $leftColumnCell->addText(
//                                    $composerDates,
//                                    $defaultFont,
//                                    $this->reportStyles->defaultParagraphRight
//                                );
//                            }


//                       RENDER WORK TITLE -- first of print title or master title. TextRun needed to
//                            render different font style for premiere
                    $workTitle = $centerColumnCell->addTextRun($defaultParagraph);
                    if (!empty(trim($dateWork['title2']))) {
                        $dateWorkTitle = $dateWork['title2'];
                    } else {
                        $dateWorkTitle = $dateWork['swork']['title1'];
                    }
                    $workTitle->addFormattedText($dateWorkTitle, $defaultFont, $defaultParagraph);
                    $premiere = $dateWork['Sworkpremieres']['name'] ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                            'premiere'
                        ) : ' ';
                    $workTitle->addText($premiere, $this->reportStyles->defaultFontItalic, $defaultParagraph);


//                    Loop through Movements
                    $dateWorkMovements = $dateWork['adatework_movements'];
                    usort(
                        $dateWorkMovements,
                        function ($a, $b) {
                            return $a['movement_order'] <=> $b['movement_order'];
                        }
                    );

                    foreach ($dateWork['adatework_movements'] as $dateWorkMovement) {
                        if (is_null($dateWorkMovement['duration']) || (string)trim(
                                $dateWorkMovement['duration']
                            ) == '') {
                            $dateWorkMovementDuration = '00:00:00';
                        } else {
                            $dateWorkMovementDuration = str_replace('_', '0', $dateWorkMovement['duration']);
                        }
                        $carbonWorkDur = Carbon::createFromFormat('H:i:s', $dateWorkMovementDuration);
                        $dateWorkMovementSeconds = $carbonWorkDur->secondsSinceMidnight();
                        $dateWorkMvtDur = gmdate(
                            $this->getDurationFormat($dateWorkMovementSeconds),
                            $dateWorkMovementSeconds
                        );

//                        If client wants the movement duration rendered, use this:
                        if ($dateWorkMovementSeconds > 0) {
                            $movementOutput = trim($dateWorkMovement['name']) . "\t" . $dateWorkMvtDur;
                        } else {
                            $movementOutput = trim($dateWorkMovement['name']);
                        }


                        if (strlen(trim($dateWorkMovement['name'])) > 0) {
                            $centerColumnCell->addFormattedText(
                                $movementOutput,
                                $this->reportStyles->movementFont,
                                $this->reportStyles->movementParagraph
                            );
                        }
                    }


//                            +++ RENDER DATE-WORK CONDUCTORS and SOLOISTS +++
//                            Fetch soloists (array)
                    $soloists = $this->dateWorkQueryHelper->getSoloistsForDateWorkID($dateWork['id'])->getQuery(
                    )->toArray();

//                          Fetch date-work conductor as string
                    if ($dateWork['conductor_id'] > 0) {
                        $dateWorkConductor = $this->dateWorkQueryHelper->getDateWorkConductorAsString(
                            $soloists,
                            $dateWork['conductor_id'],
                            0,
                            0,
                            0,
                            ', ',
                            ';'
                        );
                        $centerColumnCell->addText(
                            $dateWorkConductor,
                            $defaultFontBold,
                            $this->reportStyles->defaultParagraphCenter
                        );
                    }

                    $dateWorkSoloists = $this->dateWorkQueryHelper->getDateWorkSoloistsAsString(
                        $soloists,
                        $dateWork['conductor_id'] ?? 0,
                        '</w:t><w:br/><w:t>',
                        0,
                        0,
                        ', '
                    );
                    $centerColumnCell->addText(
                        $dateWorkSoloists,
                        $defaultFontBold,
                        $this->reportStyles->defaultParagraphCenter
                    );

//                    RENDER DATE-WORK INSTRUMENTATION
                    if (!empty($dateWorkSoloists)) {
                        $centerColumnCell->addText('', $defaultFont, $defaultParagraph);
                    }
                    if ($dateWork['swork']['l_intermission'] == 0) {
                        $centerColumnCell->addFormattedText(
                            $dateWork['instrumentation_detail'],
                            $defaultFont,
                            $defaultParagraph
                        );
                    }

//                            Work DURATION
                    if (is_null($dateWork['duration']) || (string)trim($dateWork['duration']) == '') {
                        $dateWorkDuration = '00:00:00';
                    } else {
                        $dateWorkDuration = str_replace('_', '0', $dateWork['duration']);
                    }
                    $carbonWorkDur = Carbon::createFromFormat('H:i:s', $dateWorkDuration);
                    $dateWorkSeconds = $carbonWorkDur->secondsSinceMidnight();
                    $dateWorkDuration = gmdate($this->getDurationFormat($dateWorkSeconds), $dateWorkSeconds);

//                            KEEP THIS in case client wants work durations rendered in whole minutes
//                            $workDurMinutes = round($carbonWorkDur->secondsSinceMidnight() / 60) ;

                    if ($dateWork['work_order'] < $intOrder) {
                        $firstHalfDur += $carbonWorkDur->secondsSinceMidnight();
                    }
                    if ($dateWork['work_order'] == $intOrder) {
                        $intDur = $carbonWorkDur->secondsSinceMidnight();
                    }
                    $totalDur += $carbonWorkDur->secondsSinceMidnight();
                    $rightColumnCell->addText(
                        $dateWorkDuration,
                        $defaultFont,
                        $this->reportStyles->defaultParagraphRight
                    );


//                          RENDER EMPTY ROW between works
                    $programTable->addRow();
                    $programTable->addCell(
                        Converter::inchToTwip(self::COL_1_WIDTH),
                        $this->reportStyles->leftColumnCell
                    )
                        ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
                    $programTable->addCell(
                        Converter::inchToTwip(self::COL_2_WIDTH),
                        $this->reportStyles->centerColumnCell
                    )
                        ->addText('', $defaultFont, $this->reportStyles->defaultParagraphCenter);
                    $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH))
                        ->addText('', $defaultFont, $defaultParagraph);
                }

                $composer = $dateWork['swork']['composer_id'];
            }


//            ++++ RENDER MAX INSTRUMENTATION ++++

//            Blank Row to separate Max Instrumentation
            $programTable->addRow();
            $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $this->reportStyles->defaultParagraphRight);
            $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCell)
                ->addFormattedText('', $defaultFont, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);


            $programTable->addRow();
            $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCell)
                ->addText('Total', $defaultFontBold, $this->reportStyles->defaultParagraphRight);

            $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCell)
                ->addFormattedText($concertDate['max_instrumentation'], $defaultFontBold, $defaultParagraph);
            $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCell)
                ->addText('', $defaultFont, $defaultParagraph);

            //MAX INSTRUMENTATION GRIDS
            $maxGridLabel = array(
                __('keyboard'),
                __('percussion'),
                __('extra'),
                __('vocal'),
                __('solo')
            );
            $maxGridContents = array(
                $concertDate['max_instrumentation_grid_name']['keyboards'],
                $concertDate['max_instrumentation_grid_name']['percussions'],
                $concertDate['max_instrumentation_grid_name']['extras'],
                $concertDate['max_instrumentation_grid_name']['vocals'],
                $this->getSoloistInstruments($concertDate['id'])
            );
            for ($g = 0; $g < 5; $g++) {
                //            only output  if there is content
                if (!is_null($maxGridContents[$g]) && (string)trim($maxGridContents[$g]) != '') {
                    $programTable->addRow();
                    $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCell)
                        ->addText($maxGridLabel[$g], $defaultFont, $this->reportStyles->defaultParagraphRight);
                    $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCell)
                        ->addFormattedText($maxGridContents[$g], $defaultFont, $defaultParagraph);
                    $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCell)
                        ->addText('', $defaultFont, $defaultParagraph);
                }
            }


//            +++ RENDER CONCERT DURATION TOTALS +++
            $secondHalfDur = $totalDur - $firstHalfDur - $intDur;
            $totalMusicDur = $totalDur - $intDur;

            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);
            $pageSection->addText(
                "\t" . $repFileText['first_half'] . "\t" . gmdate(
                    $this->getDurationFormat($firstHalfDur),
                    $firstHalfDur
                ),
                $defaultFont,
                $this->reportStyles->totalsParagraph
            );
            $pageSection->addText(
                "\t" . $repFileText['second_half'] . "\t" . gmdate(
                    $this->getDurationFormat($secondHalfDur),
                    $secondHalfDur
                ),
                $defaultFont,
                $this->reportStyles->totalsParagraph
            );
            $pageSection->addText(
                "\t" . $repFileText['music'] . "\t" . gmdate(
                    $this->getDurationFormat($totalMusicDur),
                    $totalMusicDur
                ),
                $defaultFont,
                $this->reportStyles->totalsParagraph
            );
            $pageSection->addText(
                "\t" . $repFileText['total'] . "\t" . gmdate($this->getDurationFormat($totalDur), $totalDur),
                $defaultFontBold,
                $this->reportStyles->totalsParagraph
            );

//            RENDER REHEARSAL SCHEDULE for PROJECT
            $pageSection->addTextBreaK(2, $defaultFontBold, $defaultParagraph);
            $pageSection->addText(
                $repFileText['schedule'],
                $this->reportStyles->orchestraFont,
                $this->reportStyles->defaultParagraphCenter
            );
            $composersWithMultipleWorks = $this->getComposersWithMoreThanOneWork($concertDate);
            $scheduleDates = $this->getScheduleDates($concertDate['season_id'], $concertDate['project_id']);
            $rehearsalSchedule = new ConcertProgramWithInstRehSchedule(
                $scheduleDates,
                $composersWithMultipleWorks,
                $pageSection,
                $repFileText
            );
            $rehearsalSchedule->renderSchedule();

            $p++;
        }
    }

    public function getHeaderVenues($seasonProjectCombo)
    {
        $headerVenues = [];
        foreach ($this->datesResult as $venueDate) {
            if ($venueDate['season_id'] . ':' . $venueDate['project_id'] . ':' . $venueDate['programno'] == $seasonProjectCombo
                && $venueDate['seventtype']['l_performance'] == 1) {
//            entire address array is fetched in case the venue city, address or other info is needed
                $headerVenues[] = trim(
                    htmlspecialchars(
                        $venueDate['locationaddress']['name2']
                        . ' ' . $venueDate['locationaddress']['name1']
                    )
                );
            }
        }
        $venueCount = sizeof(array_unique($headerVenues));
//        if there is only one venue, return that one name as a string and it will be displayed UNDER the concert dates
        if ($venueCount == 1) {
            return implode('', array_unique($headerVenues));
        }
        return '';
    }

    public function renderHeaderPersonnel($concertDate, $pageSection, $defaultFont, $defaultParagraph)
    {
        $mainConductor = $this->datePerformerHelper->getConductor(
            $concertDate['id'],
            $concertDate['conductor_id'],
            0,
            0,
            ', ',
            '; ',
            0,
            true
        );
        if (!empty($mainConductor)) {
            $pageSection->addText($mainConductor, $this->reportStyles->defaultFontBold, $defaultParagraph);
        }

//      function returns date-work conductors in a single text string
        $dateWorkConductors = $this->datePerformerHelper->getDateWorkConductors(
            $concertDate['id'],
            '</w:t><w:br/><w:t>',
            0,
            0,
            0,
            ', ',
            '; ',
            0,
            0,
            true
        );
        if (!empty($dateWorkConductors)) {
            $pageSection->addText(
                $dateWorkConductors,
                $this->reportStyles->defaultFontBold,
                $defaultParagraph
            );
        }

//                    function returns soloists in a single string; if the soloist is also the main conductor or
//                    a date-work conductor, that soloist is omitted
        $mainSoloist = $this->datePerformerHelper->getSoloists(
            $concertDate['id'],
            $concertDate['conductor_id'],
            '~~',
            0,
            0,
            ', ',
            true,
            ' - ',
            ' / ',
            0
        );
        if (!empty($mainSoloist)) {
            $soloistArray = explode('~~', $mainSoloist);
            foreach ($soloistArray as $soloist) {
                $pageSection->addFormattedText($soloist, $defaultFont, $defaultParagraph);
            }
        }
    }

    public function getIntermissionOrder($dateWorks): int
    {
        foreach ($dateWorks as $dateWork) {
            if ($dateWork['swork']['l_intermission'] == 1) {
                return $dateWork['work_order'];
            }
        }
        return 0;
    }

    public function getDurationFormat($duration): string
    {
        if ($duration > 3600) {
            $durationFormat = 'g:i:s';
        } else {
            $durationFormat = 'i:s';
        }
        return $durationFormat;
    }

    public function getSoloistInstruments($dateID)
    {
        $dateSoloistInstruments = [];
        $concertSoloists = $this->dateQueryHelper->getSoloistsForDateID($dateID)->getQuery()->toArray();
        foreach ($concertSoloists as $concertSoloist) {
            $dateSoloistInstruments[] = mb_strtolower($concertSoloist['sinstrinstrument']['name']);
        }
        return implode('; ', array_unique($dateSoloistInstruments));
    }

    /*
 *   For the SCHEDULE portion of the report  we  strip out performances
 */
    private function getScheduleDates($seasonId, $projectId)
    {
        $scheduleDateArray = [];
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['seventtype']['l_performance'] == 0
                && $dateResult['season_id'] === $seasonId &&
                $dateResult['project_id'] === $projectId) {
                $scheduleDateArray[] = $dateResult;
            }
        }
        return array_unique(array_filter($scheduleDateArray));
    }

    //         COMPOSERS WITH MULTIPLE WORKS for Rehearsal orders
    private function getComposersWithMoreThanOneWork($concertDate): array
    {
        $composerIdArray = [];
        foreach ($concertDate['adate_works'] as $dateWork) {
            $composerIdArray[] = $dateWork['swork']['composer_id'];
        }

        $duplicateComposers = array_diff_assoc($composerIdArray, array_unique($composerIdArray));
        return $duplicateComposers;
    }
}
