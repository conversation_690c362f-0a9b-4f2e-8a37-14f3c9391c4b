<?php

namespace Customer\newfoundland\reports\SeasonPercReq;

use App\Reports\ReportWord;
use App\Reports\ReportsInterface;

use Cake\Core\Configure;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use Customer\fasutilities\reports\OnWord\OnWord;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Shared\Converter;
use ReflectionException;


/*
*  Report produces a single 4-columh table layout of season programs with percussion requirements
*  TG: 2025-O6 - Refactored to deprecate old Word Helpers and incorporate FormattedText
*/

class SeasonPercReq extends ReportWord
{
// Table width = 7.25 inches
    const COL_1_WIDTH = 1.45;
    const COL_2_WIDTH = 1.45;
    const COL_3_WIDTH = 2.54;
    const  COL_4_WIDTH = 1.81;


    /**
     * Helper class for the dates selected by the user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;
    private $datesResult;

    /**
     * Helper class for the conductor / soloist in report header
     *
     * @var DatePerformerHelper
     */
    private $datePerformerHelper;


    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    function initialize()
    {
        parent::initialize();

        // Set query helpers
        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();
        $this->instrumentation = new CustomerInstrumentation();

        // Set report font, table and paragraph styles
        $this->reportStyles = new SeasonPercReqStyles();
    }


    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * Get the DATE RECORDS selected by the user
     *
     * @param array $where
     * @return $this|ReportsInterface
     */
    public function collect(array $where = []): ReportsInterface // Date records selected by user
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->withAddresses()
            ->withInstrumentationGrids()
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }

    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }

    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    private function renderReport()
    {
//        Set report formats based on client region and preferences
        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
//        headers/labels from rep file
        $repFileText = $this->getRepFileParams();

//        default font and paragraph style, defined in Styles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultFontItalic = $this->reportStyles->defaultFontItalic;
        $defaultParagraph = $this->reportStyles->defaultParagraph;
        $borderCell = $this->reportStyles->borderCell;
        $headerCell = $this->reportStyles->headerCell;

        $pageSection = $this->onWord->addSection(
            [
                'paperSize' => $paperSize,
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.50),
                'marginBottom' => Converter::inchToTwip(0.75),
                'breakType' => 'continuous',
            ]
        );

// Loop through all selected Seasons
        $allSeasons = [];
        foreach ($this->datesResult as $dateResult) {
            $allSeasons[] = array(
                'id' => $dateResult['season_id'],
                'name' => $dateResult['sseason']['name']
            );
        }
        // Serialize, unique, then unserialize
        $uniqueSeasons = array_map('unserialize', array_unique(array_map('serialize', $allSeasons)));
        $s = 0;

        $pageSection->addText(
            $repFileText['reportTitle'],
            $this->reportStyles->titleFont,
            $this->reportStyles->defaultParagraphCenter
        );
        $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);

        foreach ($uniqueSeasons as $season) {
            if ($s > 0) {
                $pageSection->addTextBreak(1);
            }

//            RENDER HEADER FOR SEASON and Table Header
            $pageSection->addFormattedText(
                $season['name'],
                $this->reportStyles->seasonFont,
                $this->reportStyles->headerParagraph
            );
            $pageSection->addTextBreak(1, $this->reportStyles->smallFont, $defaultParagraph);

            $headerTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            $headerTable->addRow();
            $headerTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $headerCell)
                ->addText($repFileText['performances'], $defaultFontBold, $defaultParagraph);
            $headerTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $headerCell)
                ->addText($repFileText['project'] ?? __('adates.project_id'), $defaultFontBold, $defaultParagraph);
            $headerTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $headerCell)
                ->addText($repFileText['program'], $defaultFontBold, $defaultParagraph);
            $headerTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $headerCell)
                ->addText($repFileText['percussion'], $defaultFontBold, $defaultParagraph);


//        for Dates selected by User, loop through Season+Project+ProgramNumber; pass current Season
            $projectTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);
            foreach ($this->getProjects($season['id']) as $project) {
//            Anchor Date - date Object
                $concertDate = $this->getAnchorDate($project);
                $this->instrumentation->addInstrumentationString($concertDate);

//                RENDER EACH TABLE ROW
                $projectTable->addRow();

//              Column One - date range
                $perfDates = [];
                foreach ($this->datesResult as $event) {
                    if ($event['season_id'] . ':' . $event['project_id'] . ':' . $event['programno'] === $project
                        && $event['seventtype']['l_performance'] == 1) {
                        $perfDates[] = $event['date_'];
                    }
                }
                $perfDateRange = $this->dateQueryHelper->getFormattedDateRange(reset($perfDates), end($perfDates), 1);

                $projectTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $borderCell)
                    ->addText($perfDateRange, $defaultFont, $defaultParagraph);

//                COLUMN TWO - Project, Conductor Soloist
                $projectCell = $projectTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $borderCell);
                $projectCell->addFormattedText($concertDate['sproject']['name'], $defaultFontBold, $defaultParagraph);
                if (trim($concertDate['programno']) != '') {
                    $projectCell->addFormattedText(
                        $repFileText['programPrefix'] . ' ' . $concertDate['programno'],
                        $defaultFontBold,
                        $defaultParagraph
                    );
                }
                $conductor = $this->getConductor($concertDate);
                if (!empty(trim($conductor))) {
                    $projectCell->addFormattedText($conductor, $defaultFont, $defaultParagraph);
                }
                $soloists = $this->getSoloists($concertDate);
                if (!empty(trim($soloists))) {
                    $projectCell->addFormattedText($soloists, $defaultFont, $defaultParagraph);
                }

//                COLUMN THREE - Program Title, Repertoire
                $programCell = $projectTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $borderCell);
                if (!empty(trim($concertDate['programtitle']))) {
                    $programCell->addFormattedText($concertDate['programtitle'], $defaultFontItalic, $defaultParagraph);
                }

                $dateWorks = $concertDate['adate_works'];
                usort(
                    $dateWorks,
                    function ($a, $b) {
                        return $a['work_order'] <=> $b['work_order'];
                    }
                );
                $programTextRun = $programCell->addTextRun($defaultParagraph);
                foreach ($dateWorks as $dateWork) {
                    if ($dateWork['swork']['l_intermission'] == 0 && $dateWork['swork']['l_regular'] == 1) {
                        $programTextRun->addFormattedText(
                            $dateWork['swork']['scomposer']['lastname'] . ': ',
                            $defaultFontBold
                        );

                        if (!is_null($dateWork['title2']) && trim($dateWork['title2']) !== '') {
                            $workTitle = $dateWork['title2'];
                        } else {
                            $workTitle = $dateWork['swork']['title1'];
                        }

                        $workTitleContents = htmlspecialchars(str_replace('>', '<', $workTitle));
                        // break up the text into an array of text elements
                        $textLines = explode('&lt;', $workTitleContents);
                        // if the resulting array contains one value - no italics are used
                        // if the resulting array contains more than one value, all ODD key values should be put in italics
                        foreach ($textLines as $key => $textLine) {
                            if ($key % 2 == 0) {
                                $programTextRun->addText($textLine, $defaultFont);
                            } else {
                                $programTextRun->addText($textLine, $defaultFontItalic);
                            }
                        }

                        $premiere = trim(
                            $dateWork['Sworkpremieres']['name']
                        ) ? ' - ' . $dateWork['Sworkpremieres']['name'] . ' ' . __(
                                'premiere'
                            ) : ' ';
                        $programTextRun->addFormattedText($premiere, $defaultFontItalic);
                        $programTextRun->addTextBreak(1, $defaultFont);
                    }
                }


//                 COLUMN FOUR - Timpani and Percussion
                $percussionCell = $projectTable->addCell(Converter::inchToTwip(self::COL_4_WIDTH), $borderCell);
                $timpPercTextRun = $percussionCell->addTextRun($defaultParagraph);
                $timpPercTextRun->addText(__('sworks.timpani') . ': ', $defaultFontBold);
                $timpPercTextRun->addText($concertDate['maxTimp'], $defaultFont);
                $timpPercTextRun->addText(' ' . __('sworks.percussion') . ': ', $defaultFontBold);
                $timpPercTextRun->addText($concertDate['maxPerc'], $defaultFont);
                $timpPercTextRun->addTextBreak(1, $defaultFont);
                $timpPercTextRun->addFormattedText($concertDate['max_instrumentation_grid_name']['percussions'], $defaultFont);
            }

            $s++;
        }
    }


    private function getProjects($seasonId): array
    {
        $seasonProject = '';
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] != $seasonProject
                && $dateResult['season_id'] == $seasonId) {
                $projectArray[] = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
            }
            $seasonProject = $dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'];
        }
        return array_unique(array_filter($projectArray));
    }

//    Concert Programs can be repeated within each Season:Project:ProgramNumber. The Anchor Date is the first concert in
//      that set. The vast majority of the time it contains all representative conductor, soloist, title, works, etc. information
    private function getAnchorDate(string $project)
    {
        foreach ($this->datesResult as $dateResult) {
            if ($dateResult['season_id'] . ':' . $dateResult['project_id'] . ':' . $dateResult['programno'] === $project &&
                $dateResult['seventtype']['l_performance'] == 1) {
                return $dateResult;
            }
        }
        return '';
    }

    private function getConductor($dateResult): string
    {
        $mainConductor = $this->datePerformerHelper->getConductor(
            $dateResult['id'],
            $dateResult['conductor_id'],
            0,
            0,
            ', ',
            '; ',
            null,
            true

        );
        return $mainConductor ?? '';
    }

    private function getSoloists($dateResult): string
    {
        $dateSoloists = $this->datePerformerHelper->getSoloists(
            $dateResult['id'],
            $dateResult['conductor_id'],
            '; ',
            0,
            0,
            ', ',
            true,
            ' - ',
            '/',
            null
        );
        return $dateSoloists ?? '';
    }

}
