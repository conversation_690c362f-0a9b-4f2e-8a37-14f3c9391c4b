<?php

namespace Customer\newfoundland\reports\DatesSocanSubmission;

use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Tab;

class DatesSocanSubmissionStyles
{
    const OPAS_DARK = '58101B';  // OPAS Next Darkest Red
    const OPAS_MEDIUM = '9C2329';  // OPAS Next second-darkest Red
    const OPAS_RED = 'C73937';
    const OPAS_ORANGE = 'F55144';
    const OPAS_ORANGE_BG = 'FEF4F3'; // lighter tint for background color

    public $defaultFont;
    public $defaultFontBold;
    public $defaultParagraph;

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Aptos';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->headerFont = clone($this->defaultFontBold);
        $this->headerFont->setSize(12);

        $this->projectHeaderFont = clone($this->defaultFontBold);
        $this->projectHeaderFont->setSize(11);
        $this->projectHeaderFont->setUnderline('single');

        $this->orchestraFont = clone($this->defaultFont);
        $this->orchestraFont->setSize(18);
        $this->orchestraFont->setSmallCaps(true);

        $this->projectFont = clone($this->defaultFontBold);
        $this->projectFont->setSize(12);

        $this->soloistFont = clone($this->defaultFont);
        $this->soloistFont->setSize(12);

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setBold(true);
        $this->titleFont->setSize(14);
        $this->titleFont->setColor('901414');


    }

    /**
     * Define the styles for this section: Paragraph | Table | Cell
     */
    protected function getStyles()
    {
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->bottomBorderParagraph = array_merge(
            $this->defaultParagraphCenter,
            ['borderBottomSize' => 12, 'borderBottomColor' => '000000']
        );

        $this->movementParagraph = [
            'spaceAfter' => 0,
            'spacing' => 0,
            'indent' => 0.6  // indent is in HALF-INCHES
//            'tabs' => array(
//                new Tab('right', 6552, ' ') // 4.55 in
//            )
        ];

        // paragraph style for a footer if needed
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        $this->footerParagraphStyle = $phpWord->addParagraphStyle(
            'footerParagraphStyle',
            array(
                'alignment' => 'left',  // left; Right; Center; other options available
                'spaceBefore' => 100,
                'spaceAfter' => 0,
                'spacing' => 0,  // space between lines in twips
                'borderTopSize' => 6,
                'borderTopColor' => self::OPAS_DARK,
//                'borderColor' => "ded6d5",  // light grey top border
                'tabs' => array(
//                    new \PhpOffice\PhpWord\Style\Tab('left', 1550),
//                    new \PhpOffice\PhpWord\Style\Tab('center', 5040),
                    new \PhpOffice\PhpWord\Style\Tab('right', 10400),
                ),
            )
        );


//        ++++++ TABLE STYLES +++++
        $this->defaultTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => 18000, // 12.5 inches
            'cellMarginLeft' => Converter::inchToTwip(0.08),
            'cellMarginRight' => Converter::inchToTwip(0.08),
            'cellMarginTop' => 0,  // zero is standard for top/bottom in Word
            'cellMarginBottom' => 0  // zero is standard for top/bottom in Word
        ];

//        ++++++ CELL STYLES +++++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

        $this->shadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => 'ECEFE0'
        );

//        left-most column for composer name
        $this->leftColumnCell = array_merge(
            $this->defaultCell,
            [
                'cellMarginRight' => 460, // .18 inch
                'borderRightSize' => 6,
                'borderRightColor' => '899499',
                'borderRightStyle' => 'single'
            ]
        );

//        center column for work title and movements
        $this->centerColumnCell = array_merge(
            $this->defaultCell,
            [
                'cellMarginLeft' => 260 // .18 inch
            ]
        );

        $this->titleCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderBottomColor' => 'b3b6ba',
            'borderBottomSize' => 4
        );

        $this->programCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderTopSize' => 6,
            'borderTopColor' => self::OPAS_DARK,
            'bgColor' => self::OPAS_ORANGE_BG
        );


        $this->greyShadedCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'bgColor' => self::OPAS_ORANGE_BG,
            'borderTopColor' => self::OPAS_DARK,
            'borderTopSize' => 8,
        );

        $this->greyShadedCellLeftBorder = array_merge(
            $this->greyShadedCell,
            array(
                'borderLeftSize' => 6,
                'borderLeftColor' => self::OPAS_DARK
            )
        );

        $this->greyShadedCellRightBorder = array_merge(
            $this->greyShadedCell,
            array(
                'borderRightSize' => 6,
                'borderRightColor' => self::OPAS_DARK
            )
        );

        $this->greyShadedCellLeftRightBorder = array_merge(
            $this->greyShadedCell,
            array(
                'borderRightSize' => 6,
                'borderRightColor' => self::OPAS_DARK,
                'borderLeftSize' => 6,
                'borderLeftColor' => self::OPAS_DARK
            )
        );


    }

}
