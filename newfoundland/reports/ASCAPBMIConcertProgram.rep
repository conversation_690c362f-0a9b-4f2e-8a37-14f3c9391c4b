<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert Program</caption>
  <classfile>ASCAPBMIConcertProgram/ASCAPBMIConcertProgram.php</classfile>
  <classname>customer\newfoundland\reports\ASCAPBMIConcertProgram\ASCAPBMIConcertProgram</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>ASCAP / BMI - Concert Program Submission</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted ASCAP / BMI Concert Program for each Project</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </default>
    <australia>
      <title>ASCAP / BMI - Concert Program Submission</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted ASCAP / BMI Concert Program for each Project</notes>
      <programNo_Prefix>- Program</programNo_Prefix>
    </australia>
    <canada>
      <title>ASCAP / BMI - Concert Programme Submission</title>
      <notes>Run  the report for any set of PERFORMANCES. The report produces a formatted ASCAP / BMI Concert Programme for each Project</notes>
      <programNo_Prefix>- Programme</programNo_Prefix>
    </canada>
    <german>
      <title>Konzertprogramm</title>
      <notes>Führen Sie den Bericht für eine beliebige Anzahl von PERFORMANCES aus. Der Bericht erzeugt ein formatiertes Konzertprogramm für jedes Projekt</notes>
      <programNo_Prefix>- Programm</programNo_Prefix>
    </german>
  </text>
  <icon>rtf</icon>
</rep>
