<?php

namespace Customer\newfoundland\reports\DatesEventRosterByWorkLandscape;

use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;

class DatesEventRosterByWorkLandscapeProgram
{
//  Table total = 9.75 in
    const COL_1_WIDTH = 4.0;
    const COL_2_WIDTH = 5.0;
    const COL_3_WIDTH = 0.75;

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\newfoundland\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    /**
     * @var DatesEventRosterByWorkLandscapeStyles
     */
    private $reportStyles;

    private $eventId;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;


    public function __construct(int $eventId, Section $pageSection)
    {
        $this->eventId = $eventId;
        $this->pageSection = $pageSection;

        $this->dateQueryHelper = new DateQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->reportStyles = new DatesEventRosterByWorkLandscapeStyles();
    }

    public function renderConcertProgram()
    {
        $eventWorks = $this->dateQueryHelper->getDateWorksForDateID($this->eventId)
            ->withDateWorkInstrumentationGrids()
            ->getQuery()
            ->toArray();


        $programTable = $this->pageSection->addTable($this->reportStyles->programTableStyle);

        foreach ($eventWorks as $eventWork) {

//            This adds all the formatted instrumentation strings found in the CustomerInstrumentation class
            $this->instrumentation->addInstrumentationStringToWork($eventWork);

            $programTable->addRow();
            $workCell = $programTable->addCell(Converter::inchToTwip(self::COL_1_WIDTH), $this->reportStyles->defaultCellStyle);
            $instrumentationCell = $programTable->addCell(Converter::inchToTwip(self::COL_2_WIDTH), $this->reportStyles->defaultCellStyle);
            $durationCell = $programTable->addCell(Converter::inchToTwip(self::COL_3_WIDTH), $this->reportStyles->defaultCellStyle);

            if ($eventWork['swork']['l_regular'] == 1) {
                if ($eventWork['l_encore'] == 1) {
                    $encore = mb_strtoupper(__('encore')) . ' -- ';
                }
                if ($eventWork['swork']['l_intermission'] != 1) {
                    $composer = $eventWork['swork']['scomposer']['lastname'];
                    $composer .= ': ';
                } else {
                    $composer = '';
                }

                if (trim($eventWork['title2']) !== '') {
                    $workTitle = $eventWork['title2'];
                } else {
                    $workTitle = $eventWork['swork']['title1'];
                }
                $workTitle .=$eventWork['arrangement'] ? ' (' . $eventWork['arrangement'] . ')' : '';

//                the < > characters will turn the text to italics when addFormattedText is used
                $workTitle .= $eventWork['Sworkpremieres']['name'] ?
                    ' - <' . $eventWork['Sworkpremieres']['name'] . ' ' . __('premiere' . '>')
                    : ' ';
                $workTextRun = $workCell->addTextRun($this->reportStyles->programParagraph);
                $workTextRun->addFormattedText($encore ? $encore . ' ' : '', $this->reportStyles->programFont);
                $workTextRun->addFormattedText($composer , $this->reportStyles->programFontBold);
                $workTextRun->addFormattedText($workTitle, $this->reportStyles->programFont);

                if ($eventWork['swork']['l_intermission'] == 0) {
                    $instrumentation = $eventWork['instrumentation_detail'];
                } else {
                    $instrumentation = '';
                }

                $instrumentationCell->addFormattedText(
                    $instrumentation,
                    $this->reportStyles->programFont,
                    $this->reportStyles->programParagraph
                );

                $dateWorkDuration = $this->dateQueryHelper->getDurationAsInt($eventWork['duration']);
                $durationCell->addText(
                    $dateWorkDuration,
                    $this->reportStyles->programFont,
                    $this->reportStyles->defaultParagraphRight
                );

            }


        }


    }





}
