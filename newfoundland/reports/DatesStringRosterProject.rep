<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>String Rosters by Project (Excel)</caption>
  <classfile>DatesStringRosterProject/DatesStringRosterProject.php</classfile>
  <classname>customer\newfoundland\reports\DatesStringRosterProject\DatesStringRosterProject</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>String Rosters by Project (Excel)</title>
      <notes>Run for any set of Performances.  he report produces string assignments for each PROJECT</notes>
      <report_title>Projects with String Section Assignments</report_title>
      <daterange_format>1</daterange_format>
      <printed>Printed on:</printed>
    </default>
    <german>
      <title>Roster für Veranstaltungsmusiker</title>
      <notes>Führen Sie den Bericht für eine beliebige Anzahl von Aufgaben aus. Der Bericht erstellt einen Musiker-Dienstplan für jede Aktivität</notes>
      <report_title>Personnel Roster</report_title>
      <printed>Gedruckt am:</printed>
    </german>
  </text>
  <icon>excel</icon>
</rep>
