<?php

namespace Customer\newfoundland\reports\WeekCalendarLandscape;

use App\Reports\Report;
use App\Reports\ReportWord;

use Cake\Core\Configure;

use Customer\fasutilities\reports\utility\DateQueryHelper;

use Customer\fasutilities\reports\OnWord\OnWord;

use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Exception\Exception;
use PhpOffice\PhpWord\IOFactory;

use ReflectionException;

/* Report produces a Week Calendar with programs and dress below
 *  Landscape orientation
*/

class WeekCalendarLandscape extends ReportWord
{

//    this provides an evenly spaced 7-column table of approx 9.75 inches wide
    const COL_WIDTH = 1.39;
    const HALF_COL_WIDTH = 4.80;
    const LEFT_DRESS_COL = 1.75;
    const RIGHT_DRESS_COL = 4.00;

    /**
     * Helper class for the Events selected by user.
     *
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * Prepared array for the rendering
     *
     * @var array
     */
    private $datesResult;

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;


    public function initialize()
    {
        parent::initialize();

        $this->dateQueryHelper = new DateQueryHelper();

        $this->reportStyles = new WeekCalendarStyles();
    }

    /*********************************************/

    /* The getI18n() function is part of the REPORT class. It returns an array of all the .rep file elements within
        the <text></text> section of the .rep file. This allows for headings and other report text to output in a
        variety of languages
    */
    public function getRepFileParams()
    {
        $repFileParameters = $this->getI18n();
        return $repFileParameters;
    }


    /**
     * Get the EVENTS selected by the user
     *
     * @param array $where
     * @return $this|Report
     */
    public function collect(array $where = []): \App\Reports\ReportsInterface
    {
        $dateQuery = $this->dateQueryHelper
            ->getDates($this->getRequest()->getData()['dataItemIds'])
            ->getQuery();

        $this->datesResult = $dateQuery->toArray();

        return $this;
    }


    /**
     * Prepare the data and start rendering
     *
     * @throws ReflectionException
     */
    function fill_phpWord()
    {
        $this->onWord = new OnWord();

        $this->renderReport();
    }


    /**
     * @throws Exception
     */
    function save_phpWord(): string
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        $objWriter = IOFactory::createWriter($this->onWord);
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    private function renderReport()
    {
//        report title and column headings from .rep file
//        $weekColHeader = $repFileText['week_column'];
//        $svcColHeader = $repFileText['service_column'];
        $repFileText = $this->getRepFileParams();
        $programNoPrefix = $repFileText['pgm_prefix'];

//         Default font and paragraph styles created in WeekCalendarStyles class
        $defaultFont = $this->reportStyles->defaultFont;
        $defaultFontBold = $this->reportStyles->defaultFontBold;
        $defaultParagraph = $this->reportStyles->defaultParagraph;

        $paperSize = Configure::read('opasReports.paperFormat') ?? "Letter";
        $pageSection = $this->onWord->addSection(
            [
                'breakType' => 'continuous',
                'paperSize' => $paperSize,
                'orientation' => "landscape",
                'marginLeft' => Converter::inchToTwip(0.75),
                'marginRight' => Converter::inchToTwip(0.50),
                'marginTop' => Converter::inchToTwip(0.25),
                'marginBottom' => Converter::inchToTwip(0.50)
            ]
        );

//        All events selected by user
        $datesResult = $this->datesResult;

//        grab the first Season ID to pass to the getDaysForSeasonWeek function below
        $firstDate = reset($datesResult);
        $seasonID = $firstDate['season_id'];

//        fetch each Week among all dates selected by user
        $allWeeks = [];
        foreach ($datesResult as $dateResult) {
            $allWeeks[] = $dateResult['week'];
        }
        $allWeeks = array_filter(array_unique($allWeeks));

//       loop through each unique WEEK among those selected by User
        $w = 0;
        foreach ($allWeeks as $week) {
            $programArray = [];
            $weekPrograms = [];
            $dressForTheWeek = [];

//            output a page break between weeks
            if ($w > 0) {
                $pageSection->addText('<w:p><w:r><w:br w:type="page"/></w:r></w:p>', $defaultFont, $defaultParagraph);
            }

//                Define the 7 days in the Schedule Table
            $daysOfTheWeek = $this->dateQueryHelper->getDaysforSeasonWeek($seasonID, $week)->getQuery()->toArray();
            $firstDateOfWeek = reset($daysOfTheWeek);

//            RENDER PAGE HEADER
            $reportHeaderTable = $pageSection->addTable($this->reportStyles->defaultTableSytle);
            $reportHeaderTable->addRow();
            $leftCell = $reportHeaderTable->addCell(
                Converter::inchToTwip(self::HALF_COL_WIDTH),
                $this->reportStyles->defaultCell
            );
            $leftCell->addText(
                Configure::read('CustomerSettings.name'),
                $this->reportStyles->titleFont,
                $defaultParagraph
            );
            $leftCell->addText($repFileText['report_subtitle'], $defaultFont, $defaultParagraph);
            $leftCell->addText($repFileText['printed'] . ' ' . date('D M j, Y'), $defaultFont, $defaultParagraph);

            $rightCell = $reportHeaderTable->addCell(
                Converter::inchToTwip(self::HALF_COL_WIDTH),
                $this->reportStyles->defaultCell
            );
            $rightCell->addText(
                $repFileText['week'] . ' ' . $firstDateOfWeek['pweek'],
                $this->reportStyles->titleFont,
                $this->reportStyles->defaultParagraphRight
            );


            $pageSection->addText(' ', $defaultFont, $defaultParagraph);
            $calendarTable = $pageSection->addTable($this->reportStyles->calendarTableStyle);

//  RENDER HEADER ROW FOR SCHEDULE PORTION
            $calendarTable->addRow();
            foreach ($daysOfTheWeek as $headerDay) {
//                format calendar day according to region or client preferences
                $dowCell = $calendarTable->addCell(
                    Converter::inchToTwip(self::COL_WIDTH),
                    $this->reportStyles->topRowCell
                );
                $dayOutputTextRun = $dowCell->addTextRun($this->reportStyles->defaultParagraphCenter);
                $dayOutputTextRun->addText($headerDay['date_']->format('l'), $this->reportStyles->headerFont);
                $dayOutputTextRun->addTextBreak(1, $this->reportStyles->headerFont);
                $dayOutputTextRun->addText($headerDay['date_']->format('M j'), $this->reportStyles->headerFont);
            }

//  RENDER EVENTS ROW
            $calendarTable->addRow();
            foreach ($daysOfTheWeek as $weekDay) {
                $calendarCellStyle = $this->reportStyles->defaultCell;
                $dayCell = $calendarTable->addCell(Converter::inchToTwip(self::COL_WIDTH), $calendarCellStyle);

//                    Calendar Day info -  holiday, day text
                if (!empty($weekDay['sholyday']['name'])) {
                    $dayCell->addText(
                        htmlspecialchars($weekDay['sholyday']['name']),
                        $this->reportStyles->defaultFontItalic,
                        $this->reportStyles->defaultParagraphRight
                    );
                }
                if (!empty($weekDay['text_'])) {
                    $dayCell->addText(
                        htmlspecialchars($weekDay['text_']),
                        $this->reportStyles->dayTextFont,
                        $this->reportStyles->defaultParagraphRight
                    );
                }

//                    Events on that Day via the WeekCalendarEvents class
                foreach ($this->datesResult as $event) {
                    if ($event['date_']->format('Ymd') === $weekDay['date_']->format('Ymd')) {
                        $svcSum += $event['duties'];

                        if ($event['dress_id'] > 0) {
                            $dressForTheWeek[] = array(
                                'dress' => $event['sdress'],
                                'projectId' => $event['project_id'],
                                'projectName' => $event['sproject']['name'],
                                'date' => $event['date_']
                            );
                        }

                        $activity = new WeekCalendarEvents($event);
                        $activityOutput = $activity->getEventOutput($event, $repFileText);
                        if ($event['seventtype']['l_performance'] == 1) {
                            $dayCell->addText(
                                $activityOutput,
                                $this->reportStyles->defaultFontBold,
                                $defaultParagraph
                            );
                        } else {
                            $dayCell->addText(
                                $activityOutput,
                                $defaultFont,
                                $defaultParagraph
                            );
                        }

//                      if the event is a Performance, create an ad-hoc array with the info required to print programs for each month
//                      this array is sent to the MonthCalendarPrograms class
                        if ($event['seventtype']['l_performance'] == 1) {
                            $programArray['dateID'] = $event['id'];
                            $programArray['project'] = $event['sproject']['name'];
                            $programArray['pgmNo'] = $event['programno'];
                            $programArray['pgmTitle'] = $event['programtitle'];
                            $programArray['condID'] = $event['conductor_id'];
                            $programArray['orchID'] = $event['orchestra_id'];
                            $programArray['pweek'] = $event['pweek'];
                        }
                        $weekPrograms[] = $programArray;

                        // blank line between events
                        $dayCell->addText('', $defaultFont, $defaultParagraph);
                    }
                }
            }

            $weekSvc = $svcSum > 0 ? $svcSum : '';
            $pageSection->addText($repFileText['svc_prefix'] . $weekSvc, $defaultFont, $defaultParagraph);
            $pageSection->addText('', $defaultFont, $defaultParagraph);

            $programTable = new WeekCalendarPrograms(
                array_unique(array_filter($weekPrograms), SORT_REGULAR),
                $pageSection,
                $repFileText
            );

//            send the Program Number prefix from the report .rep file for rendering in project header
            $programTable->render($programNoPrefix);


//      +++ RENDER DRESS CODE +++
//             This can be made more efficient, but it allows output of each Project + Dress and dates used.
//            Same project can have different dress on non-consecutive days
            $pageSection->addTextBreak(1, $defaultFont, $defaultParagraph);
            $pageSection->addText($repFileText['dress'], $defaultFontBold, $this->reportStyles->topBorderParagraph);

            if (!empty($dressForTheWeek)) {
                $dressTable = $pageSection->addTable($this->reportStyles->defaultTableStyle);

// sort dress by Project Name
                usort(
                    $dressForTheWeek,
                    function ($a, $b) {
                        return $a['projectName'] <=> $b['projectName'];
                    }
                );

//   GET EACH PROJECT and DRESS CODE Represented (remove duplicates where same dress is used more than once per project
                $dressAndProjectIds = [];
                foreach ($dressForTheWeek as $dress) {
                    $dressAndProjectIds[] = $dress['projectId'] . ':' . $dress['dress']['id'];
                }
                $dressAndProjectIds = array_unique($dressAndProjectIds);

//                Loop through each PROJECT and Dress used for same
                foreach ($dressAndProjectIds as $dressProjectId) {
//  Concatenate all dates on which each dress is uesd for this project
                    $dressDates = $this->getDressDates($dressProjectId, $dressForTheWeek);

                    $dressTable->addRow();
                    $dressCell = $dressTable->addCell(
                        Converter::inchToTwip(self::LEFT_DRESS_COL),
                        $this->reportStyles->borderCell
                    );
                    $menCell = $dressTable->addCell(
                        Converter::inchToTwip(self::RIGHT_DRESS_COL),
                        $this->reportStyles->defaultCell
                    );
                    $womenCell = $dressTable->addCell(
                        Converter::inchToTwip(self::RIGHT_DRESS_COL),
                        $this->reportStyles->defaultCell
                    );

                    $l_OutputDress = false; // output dress only once per Project
                    foreach ($dressForTheWeek as $dress) {
                        if ($dress['projectId'] . ':' . $dress['dress']['id'] == $dressProjectId && $l_OutputDress == false) {
                            $dressCodeTextRun = $dressCell->addTextRun($defaultParagraph);
                            $dressCodeTextRun->addText($dress['dress']['name'], $defaultFontBold);
                            $dressCodeTextRun->addTextBreak(1, $defaultFont);
                            $dressCodeTextRun->addText($dress['projectName'], $defaultFontBold);
                            $dressCodeTextRun->addTextBreak(1, $defaultFont);
                            $dressCodeTextRun->addText($dressDates, $defaultFont);

                            $menCell->addFormattedText(
                                $repFileText['men_prefix'] . ' ' . $dress['dress']['men'],
                                $defaultFont,
                                $defaultParagraph
                            );
                            $womenCell->addFormattedText(
                                $repFileText['women_prefix'] . ' ' . $dress['dress']['women'],
                                $defaultFont,
                                $defaultParagraph
                            );

                            $l_OutputDress = true;
                        }
                    }
                }
            }

            $w++;
        }
    }


    protected function getDressDates($dressProjectId, $dressForTheWeek): string
    {
        $dressDates = [];
        foreach ($dressForTheWeek as $dress) {
            if ($dress['projectId'] . ':' . $dress['dress']['id'] == $dressProjectId) {
                $dressDates[] = $dress['date']->format('D. M j');
            }
        }

        return implode('; ', $dressDates);
    }

}
