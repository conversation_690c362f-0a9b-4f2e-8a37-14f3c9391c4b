<?php

namespace Customer\newfoundland\reports\WeekCalendarLandscape;

use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Tab;

class WeekCalendarStyles
{
    const CELL_SHADING =  'ECEFE0';  //  Tan
    const PRIMARY_COLOR =  '58101B'; // OPAS dark red
//    const SECONDARY_COLOR =
//  const RED = 'ff1a1a';

    public $defaultFont;
    public $defaultFontBold;
    public $defaultParagraph;
    public $programParagraph;

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Calibri';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont);
        $this->defaultFont->setSize(10);

        $this->defaultFontBold = clone($this->defaultFont);
        $this->defaultFontBold->setBold(true);

        $this->defaultFontItalic = clone($this->defaultFont);
        $this->defaultFontItalic->setItalic(true);

        $this->defaultFontBoldUnderline = clone($this->defaultFontBold);
        $this->defaultFontBoldUnderline->setUnderline('single');

        $this->titleFont = clone($this->defaultFont);
        $this->titleFont->setBold(true);
        $this->titleFont->setSize(12);

        $this->programFont = clone($this->defaultFont);
        $this->programFont->setSize(8);

        $this->headerFont = clone($this->defaultFont);
        $this->headerFont->setBold(true);
        $this->headerFont->setSize(11);

        $this->dayTextFont = clone($this->defaultFont);
        $this->dayTextFont->setColor(self::PRIMARY_COLOR);

    }

    /**
     * Define the styles for the report - Paragraph | Table | Cell
     * this particular report has a crazy number of styles, required primarily to format the main Calendar table
     */
    protected function getStyles()
    {
//        +++ PARAGRAPHS +++
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0,  // space between lines in twips
        ];

        $this->defaultParagraphRight = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'right']
        );

        $this->defaultParagraphCenter = array_merge(
            $this->defaultParagraph,
            ['alignment' => 'center']
        );

        $this->programParagraph = array_merge(
            $this->defaultParagraph,
            [
                'spaceAfter' => 40,  // about .2 points in dialog
                'indent' => 1,
                'hanging' => 1,
                'tabs' => array(
                    new Tab('right', 9504, ' ') // 6.60 in
                )
            ]
);

        $this->topBorderParagraph = array_merge(
            $this->defaultParagraph,
            ['borderTopSize' => 6, 'borderTopColor' => self::PRIMARY_COLOR]
        );


//        used only in the one line under the main Calendar table
        $this->footerParagraph = array_merge(
            $this->defaultParagraph,
            [
                'tabs' => array(
                    new Tab('right', 13507, ' ') // 9.38 in
                )
            ]
        );

//        +++ TABLES +++

        $this->defaultTableStyle = array(
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115,
        );

        $this->calendarTableStyle = array(
            'borderSize' => 6,
            'borderColor' => '000000',
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115,
        );

        $this->programTableStyle = array(
            'borderTopSize' => 12,
            'borderTopColor' => self::PRIMARY_COLOR,
            'cellMarginLeft' => 115,  // 0.08 inches
            'cellMarginRight' => 115,
        );

//   +++ CELLS +++
        $this->defaultCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
        );

        $this->shadedCell = array_merge(
            $this->defaultCell,
            ['bgColor' => 'f7f6f0']  // beige
        );

        $this->leftColumnCell = array_merge(
            $this->shadedCell,
            [
                'borderLeftSize' => 6,
                'borderLeftColor' => '000000',
                'borderLeftStyle' => 'double',
                'valign' => 'center'
            ]
        );

        $this->rightColumnCell = array_merge(
            $this->shadedCell,
            [
                'borderRightSize' => 6,
                'borderRightColor' => '000000',
                'borderRightStyle' => 'double',
                'valign' => 'center'
            ]
        );

        $this->topRowCell = array_merge(
            $this->defaultCell,
            [
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
                'bgColor' => self::CELL_SHADING
            ]
        );

//        these two cells are used only One Time Each - in the upper right and left
//        corners of the Calendar Table to make the borders run around the top corners
        $this->upperLeftCell = array_merge(
            $this->shadedCell,
            [
                'borderLeftSize' => 6,
                'borderLeftColor' => '000000',
                'borderLeftStyle' => 'double',
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
            ]
        );
        $this->upperRightCell = array_merge(
            $this->shadedCell,
            [
                'borderRightSize' => 6,
                'borderRightColor' => '000000',
                'borderRightStyle' => 'double',
                'borderTopSize' => 6,
                'borderTopColor' => '000000',
                'borderTopStyle' => 'double',
            ]
        );

        $this->titleCell = array(
            'spaceAfter' => 0,
            'spacing' => 0,
            'borderBottomColor' => 'b3b6ba',
            'borderBottomSize' => 4
        );
    }

}
