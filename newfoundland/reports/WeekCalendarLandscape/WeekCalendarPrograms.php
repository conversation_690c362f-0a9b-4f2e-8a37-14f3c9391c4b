<?php

namespace Customer\newfoundland\reports\WeekCalendarLandscape;

use Carbon\Carbon;

use Customer\fasutilities\reports\utility\AddressQueryHelper;
use Customer\fasutilities\reports\utility\DateQueryHelper;
use Customer\fasutilities\reports\utility\DatePerformerHelper;
use Customer\fasutilities\utility\Instrumentation\CustomerInstrumentation;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use ReflectionException;

/*
 * This Class can be used to render the program for each WEEK of activities.
 * Establish the page section in the main report.
 * Paragraph / Font / Table styles are taken from the associated Style.php for the report
 * The main report passes the $weekPrograms array that contains key items for each Performance in the Month
 *
 */

class WeekCalendarPrograms

{
//    col width in INCHES - total table about 9.5 inches wide
    const LEFT_COL = 2.78;
    const RIGHT_COL = 6.95;

    /**
     * The PHPWord section to render the table
     *
     * @var \Customer\newfoundland\reports\OnWord\Element\Section
     */
    private $pageSection;

    /**
     * @var DateQueryHelper
     */
    private $dateQueryHelper;

    /**
     * @var AddressQueryHelper
     */
    private $addressHelper;

    /**
     * @var array DatePerformerHelper
     */
    private $datePerformerHelper;

    /**
     * @var CustomerInstrumentation
     */
    private $instrumentation;

    private $monthPrograms = [];

    /**
     * PHPWord
     *
     * @var $phpWord \PhpOffice\PhpWord\PhpWord
     */
    public $phpWord;
    public $onWord;
    private $reportStyles;

    public function __construct(array $weekPrograms, Section $pageSection, $repFileText)
    {
        $this->weekPrograms = $weekPrograms;
        $this->pageSection = $pageSection;
        $this->repFileText = $repFileText;

        $this->dateQueryHelper = new DateQueryHelper();
        $this->datePerformerHelper = new DatePerformerHelper();

        $this->addressHelper = new AddressQueryHelper();
        $this->instrumentation = new CustomerInstrumentation();

        $this->reportStyles = new WeekCalendarStyles();
    }


    public function render($programNoPrefix)
    {
        // anchor date is defined as each project + program number combination
        $anchorDate = '';

        foreach ($this->weekPrograms as $weekProgram) {
            /* $weekProgram keys:
            *   ['dateID'] ['project'] ['pgmNo'] ['pgmTitle'] ['condID'] ['orchID'] ['pweek']
            */

            $performerHeader = [];

            if ($weekProgram['project'] . $weekProgram['pgmNo'] != $anchorDate) {
                $programTable = $this->pageSection->addTable($this->reportStyles->programTableStyle);
                $programTable->addRow();

                $projectCell = $programTable->addCell(Converter::inchToTwip(self::LEFT_COL), $this->reportStyles->defaultCell);

                $programHeading = $weekProgram['project'];
                $programHeading .= $weekProgram['pgmNo'] ? ' - ' . $programNoPrefix . ' ' . $weekProgram['pgmNo'] : '';
                $projectCell->addText(
                    $programHeading,
                    $this->reportStyles->defaultFontBold,
                    $this->reportStyles->defaultParagraph
                );

                if (!empty($weekProgram['pgmTitle'])) {
                    $projectCell->addText(
                        htmlspecialchars($weekProgram['pgmTitle']),
                        $this->reportStyles->defaultFontItalic,
                        $this->reportStyles->defaultParagraph
                    );
                }

//                assemble the Conductor, Date-Work Conductors and Soloists. Implode and render in the format required by the report.
                $performerHeader[] = $this->datePerformerHelper->getConductor(
                    $weekProgram['dateID'],
                    $weekProgram['condID']
                );
                $performerHeader[] = $this->datePerformerHelper->getDateWorkConductors($weekProgram['dateID']);
                $performerHeader[] = $this->datePerformerHelper->getSoloists(
                    $weekProgram['dateID'],
                    $weekProgram['condID'],
                );
                $projectCell->addText(
                    implode('</w:t><w:br/><w:t>', array_filter($performerHeader)),
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph
                );
                $programCell = $programTable->addCell(Converter::inchToTwip(self::RIGHT_COL), $this->reportStyles->defaultCell);

//                fetch max instrumentation and pass the string to the program function so the max instrumentation
//                can be rendered after the last work on the program
                if($weekProgram['orchID']>0) {
                    $maxInstString = $this->getMaxInstrumentation($weekProgram['dateID']);
                } else {
                    $maxInstString = '';
                }
//              render the works on the program
                $this->getConcertProgram($weekProgram['dateID'], $programCell, $maxInstString);

//                blank line between programs
                $this->pageSection->addText(
                    '',
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->defaultParagraph
                );

                $anchorDate = $weekProgram['project'] . $weekProgram['pgmNo'];
            }
        }
    }

    public function getConcertProgram($dateID, $programCell, $maxInstString)
    {
//        in month and other schedules, there is usually insufficient space for work instrumentation, movements, etc
        $concertWorks = $this->dateQueryHelper->getDateWorksForDateID($dateID)
            ->withDateWorkInstrumentationGrids()
            ->getQuery()
            ->toArray();

        $totalDur = 0;
        foreach ($concertWorks as $concertWork) {
            $this->instrumentation->addInstrumentationStringToWork($concertWork);
            if ($concertWork['swork']['l_regular'] == 1) {
//                GET DATE WORK DURATION - format according to region or client customization
                if (is_null($concertWork['duration']) || (string)trim($concertWork['duration']) == '') {
                    $dateWorkDuration = '00:00:00';
                } else {
                    $dateWorkDuration = str_replace('_', '0', $concertWork['duration']);
                }
                $carbonWorkDur = Carbon::createFromFormat('H:i:s', $dateWorkDuration);
                $workDurMinutes = round($carbonWorkDur->secondsSinceMidnight() / 60);
                if ($concertWork['swork']['l_intermission'] == 0) {
                    if ($workDurMinutes > 59) {
                        $dateWorkDurOutput = "\t" . $carbonWorkDur->format('g:i:s');
                    } elseif ($workDurMinutes > 0) {
                        $dateWorkDurOutput = "\t" . $carbonWorkDur->format('i:s');
                    } else {
                        $dateWorkDurOutput = '';
                    }
                } else {
                    $dateWorkDurOutput = '';
                }

                if ($concertWork['l_encore'] == 1) {
                    $encore = mb_strtoupper(__('encore')) . ' -- ';
                }
                if ($concertWork['swork']['l_intermission'] != 1) {
                    $composer = mb_strtoupper($concertWork['swork']['scomposer']['lastname']);
                    $composer .= $concertWork['arrangement'] ? ' (' . $concertWork['arrangement'] . ')' : '';
                    $composer .= ':';
                } else {
                    $composer = '';
                }

                if (trim($concertWork['title2']) !== '') {
                    $workTitle = $concertWork['title2'];
                } else {
                    $workTitle = $concertWork['swork']['title1'];
                }

//                the < > characters will turn the text to italics when addFormattedText is used
                $workTitle .= $concertWork['Sworkpremieres']['name'] ?
                    ' - <' . $concertWork['Sworkpremieres']['name'] . ' ' . __('premiere' . '>')
                    : ' ';

                $programCell->addFormattedText(
                    trim($encore . ' ' . $composer . ' ' . $workTitle . $dateWorkDurOutput),
                    $this->reportStyles->defaultFont,
                    $this->reportStyles->programParagraph
                );

                if ($concertWork['swork']['l_intermission'] == 0) {
                    $programCell->addFormattedText(
                        "\t" . $concertWork['instrumentation_detail'],
                        $this->reportStyles->defaultFont,
                        $this->reportStyles->programParagraph
                    );
                }

                $totalDur += $carbonWorkDur->secondsSinceMidnight();
            }
        }
//             Max Instrumentation Row
        $programCell->addText('', $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
        $totalDurMintues =  round($totalDur / 60) ;
        if ($totalDurMintues > 59) {
            $durationFormat = 'g:i:s';
        } else {
            $durationFormat = 'i:s';
        }
        $programCell->addFormattedText(
            trim($this->repFileText['total'] . ' ' . $maxInstString) . "\t" . gmdate($durationFormat, $totalDur),
            $this->reportStyles->defaultFontBold,
            $this->reportStyles->programParagraph
        );

//        blank line to provide space before next Project
        $this->pageSection->addText(' ', $this->reportStyles->defaultFont, $this->reportStyles->defaultParagraph);
    }

    public function getMaxInstrumentation($dateID)
    {
        /* send $dateID to the getDates() function in dateQueryHelper and return the results as the first item in an array.
        *   this creates the object that CustomerInstrumentation.php expects to see and that works with
        *     Instrumentation functions.
        */
        $perfDate = $this->dateQueryHelper->getDates($dateID)->getQuery()->toArray()[0];
        $this->instrumentation->addInstrumentationString($perfDate);

        /* this report outputs the Total Instrumentation in an in-line format
        *   elements of the max instrumentation are fetched indvidually and assembled
         */

        $maxInstArray = [];
        $maxInstArray[] = $perfDate['max_WindsBrass'];
        $maxInstArray[] = $perfDate['max_TimpPerc'];
        $maxInstArray[] = $perfDate['max_Harp'];

        $maxKeyboard = $perfDate['max_Keyboard'];
        $maxKeyboard .= $perfDate['max_instrumentation_grid_code']['keyboards'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['keyboards'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxKeyboard;

        $maxExtra = $perfDate['max_Extra'];
        $maxExtra .= $perfDate['max_instrumentation_grid_code']['extras'] ?
            $this->instrumentation->getGridPrefix(
            ) . $perfDate['max_instrumentation_grid_code']['extras'] . $this->instrumentation->getGridSuffix() :
            '';
        $maxInstArray[] = $maxExtra;

        $maxInstArray[] = $perfDate['max_instrumentation_grid_name']['vocals'];
        $maxInstArray[] = $perfDate['max_Strings'];

        $maxInstString = '';
        $maxInstString .= implode($this->instrumentation->getGroupSeparator(), array_filter($maxInstArray));

        return $maxInstString;
    }
}
