<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert Program with Instrumentation / Reh. Schedule</caption>
  <classfile>ConcertProgramWithInstReh/ConcertProgramWithInstReh.php</classfile>
  <classname>customer\newfoundland\reports\ConcertProgramWithInstReh\ConcertProgramWithInstReh</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Concert Program with Instrumentation and Reh. Schedule</title>
      <notes>Run for any set of EVENTS in a Project. This report outputs the concert program to Word, with work and concert total instrumentation and duration. The bottom section includes the Rehearsal Schedule and rehearsal orders for the Project</notes>
      <first_half>First Half:</first_half>
      <second_half>Second Half:</second_half>
      <music>Total Music:</music>
      <total>Total Concert Duration:</total>
      <schedule>Rehearsal Schedule</schedule>
      <soloChoirTag>[with soloist and choir]</soloChoirTag>
      <soloTag>[with soloist]</soloTag>
      <choirTag>[with choir]</choirTag>
      <date>Time</date>
      <event>Activity / Venue</event>
      <repertoire>Rehearsal Order</repertoire>
    </default>
    <canada>
      <title>Concert Programme with Instrumentation and Reh. Schedule</title>
      <notes>Run for any set of EVENTS in a Project. This report outputs the concert programme to Word, with work and concert total instrumentation and duration. The bottom section includes the Rehearsal Schedule and rehearsal orders for the Project</notes>
      <first_half>First Half:</first_half>
      <second_half>Second Half:</second_half>
      <music>Total Music:</music>
      <total>Total Concert Duration:</total>
      <soloChoirTag>[with soloist and choir]</soloChoirTag>
      <soloistTag>[with soloist]</soloistTag>
      <choirTag>[with choir]</choirTag>
      <date>Time</date>
      <event>Activity / Venue</event>
      <repertoire>Rehearsal Order</repertoire>
    </canada>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
