<?php

namespace Customer\newfoundland\reports;

use PhpOffice\PhpWord\Element\Section;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Fill;
use ReflectionException;
use Cake\Core\Configure;


/*
 * JH 20250521 CALSeasonWeekCalendarTemplate is the template
 * 
 *
 */

class adates_season_week_calendar_styles
{
    const BORDER_COLOR = '000000';  // BLACK
    const WEEKDAY_ROW_COLOR = 'F2DBDB';   // rosa
    const WEEK_HEADER_COLOR = 'D6E3BC';   // green
    const DAY_NUMBER_COLOR = 'B6DDE8';  // blue

    public function __construct()
    {
        $this->getFonts();
        $this->getStyles();
    }

    protected function getFonts()
    {
        $reportFont = 'Century Gothic';

        $this->defaultFont = new Font();
        $this->defaultFont->setName($reportFont)
            ->setSize(8);

        $this->fontBold = clone($this->defaultFont);
        $this->fontBold->setBold();

        $this->fontItalic = clone($this->defaultFont);
        $this->fontItalic->setItalic();

        $this->fontBoldUnderline = clone($this->defaultFont);
        $this->fontBoldUnderline->setBold()
            ->setUnderline('single');

        $this->fontBold10 = clone($this->defaultFont);
        $this->fontBold10->setBold()
            ->setSize(10);

        $this->fontBold9 = clone($this->defaultFont);
        $this->fontBold9->setBold()
            ->setSize(9);

        $this->font9 = clone($this->defaultFont);
        $this->font9
            ->setSize(9);

        $this->fontBoldUnderline9 = clone($this->defaultFont);
        $this->fontBoldUnderline9->setBold()
            ->setSize(9)
            ->setUnderline('single');

        $this->headerFont = new Font();
        $this->headerFont
            ->setName($reportFont)
            ->setSize(12)
            ->setBold();

        $this->footerFont = new Font();
        $this->footerFont
            ->setName($reportFont)
            ->setSize(9);

        $this->fontBold11 = new Font();
        $this->fontBold11
            ->setName($reportFont)
            ->setBold()
            ->setSize(11);

    }

    /**
     * Define the styles for the report: Paragraph | Table | Cell
     */
    protected function getStyles()
    {

        //  ++++++ section/page dimensions +++++
        if (Configure::read('Formats.region') === 'North America') {
            $this->paperSize = "Letter";
            $this->formatForDateRange = 2;
            $pageWidthCm = 27.94;
            $pageHeightCm = 21.59;
        } else {
            $this->paperSize = "A4";
            $this->formatForDateRange = 4;
            $pageWidthCm = 29.7;
            $pageHeightCm = 21.0;
        }

        $this->pageOrientation = 'landscape';

        $this->pageTopBorderCm = 0.762;
        $this->pageBottomBorderCm = 0.762;
        $this->pageLeftBorderCm = 1.27;
        $this->pageRightBorderCm = 0.508;

        // size of the main table
        $this->mainTableWidthCm = $pageWidthCm
            - $this->pageLeftBorderCm
            - $this->pageRightBorderCm;
        $this->weekColumnWidthCm = 0.508;
        $this->dayColumnWidthCm = 2.616;
        $this->projectColumnWidthCm = $this->mainTableWidthCm
            - $this->weekColumnWidthCm
            - ($this->dayColumnWidthCm * 7);

        //  ++++++ PARAGRAPH STYLES +++++
        $this->defaultParagraph = [
            'spaceBefore' => 0,
            'spaceAfter' => 0,
            'spacing' => 0  // space between lines in twips
        ];


        //  ++++++ TABLE STYLES +++++
        $this->mainTableStyle = [
            'unit' => TblWidth::TWIP,
            'width' => Converter::cmToTwip($this->mainTableWidthCm),
            'cellMarginLeft' => Converter::cmToTwip(0.05),
            'cellMarginRight' => Converter::cmToTwip(0.05),
            'cellMarginTop' => Converter::cmToTwip(0.05),
            'cellMarginBottom' => Converter::cmToTwip(0.05)
        ];


        //        ++++++ CELL STYLES +++++
        $this->defaultCell = [
            'borderTopColor' => self::BORDER_COLOR,
            'borderTopSize' => 2,
            'borderBottomColor' => self::BORDER_COLOR,
            'borderBottomSize' => 2,
            'borderLeftColor' => self::BORDER_COLOR,
            'borderLeftSize' => 2,
            'borderRightColor' => self::BORDER_COLOR,
            'borderRightSize' => 2
        ];

        $this->defaultCellNoBottom = [
            'borderTopColor' => self::BORDER_COLOR,
            'borderTopSize' => 2,
            'borderLeftColor' => self::BORDER_COLOR,
            'borderLeftSize' => 2,
            'borderRightColor' => self::BORDER_COLOR,
            'borderRightSize' => 2
        ];

        $this->defaultCellNoTop = [
            'borderBottomColor' => self::BORDER_COLOR,
            'borderBottomSize' => 2,
            'borderLeftColor' => self::BORDER_COLOR,
            'borderLeftSize' => 2,
            'borderRightColor' => self::BORDER_COLOR,
            'borderRightSize' => 2
        ];

        $this->legendOuterCell = [];

        $this->legendMiddleCell = [
            'borderLeftColor' => self::BORDER_COLOR,
            'borderLeftSize' => 2,
            'borderRightColor' => self::BORDER_COLOR,
            'borderRightSize' => 2
        ];

    }


}
