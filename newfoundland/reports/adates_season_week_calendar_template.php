<?php

// namespace Customer\newfoundland\reports;

use App\Reports\ReportTemplate;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;

class adates_season_week_calendar_template extends ReportTemplate
{
    /**
     * @inheritdoc
     */
    protected $templateName = 'adates_season_week_calendar_template';

    /**
     * @inheritdoc
     */
    protected $fileLocationType = 'direct';

    public function getTemplateData(array $params = [])
    {

        $SseasonsTable = TableRegistry::getTableLocator()->get('Sseasons');

        $sseasons = $SseasonsTable->find('listSort', ['onlyActivated' => true]);

        if (Configure::read('Calendar.Weeks.FirstDayOfWeek') == 'Su') {
            $dayOfWeek = 7;
        } else {
            $dayOfWeek = 1;
        }

        return [
            'sseasons' => $sseasons,
            'curr_season_id' => $sseasons[0]['id'],
            'fdow' => $dayOfWeek
        ];

    }

    public function submit($formDataString = null)
    {
        parse_str($formDataString, $formDataArray);

        $this->data['formData'] = $formDataArray;

        return parent::submit($formDataString);
    }

}
