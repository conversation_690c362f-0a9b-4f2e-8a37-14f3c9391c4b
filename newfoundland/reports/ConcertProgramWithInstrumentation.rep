<rep>
  <form>adates</form>
  <source>adates</source>
  <caption>Concert Program with Instrumentation</caption>
  <classfile>ConcertProgramWithInstrumentation/ConcertProgramWithInstrumentation.php</classfile>
  <classname>customer\newfoundland\reports\ConcertProgramWithInstrumentation\ConcertProgramWithInstrumentation</classname>
  <lnodata>false</lnodata>
  <text>
    <default>
      <title>Concert Program with Instrumentation</title>
      <notes>Run for any set of PERFORMANCES in a Season. This report outputs the concert programme to Word, with work and concert total instrumentation and duration.</notes>
      <work_table_duration_minutes_postfix>'</work_table_duration_minutes_postfix>
      <work_table_duration_seconds_postfix>''</work_table_duration_seconds_postfix>
      <first_half>First Half:</first_half>
      <second_half>Second Half:</second_half>
      <music>Total Music:</music>
      <total>Total Concert Duration:</total>
    </default>
    <canada>
      <title>Concert Programme with Instrumentation</title>
      <notes>Run for any set of PERFORMANCES in a Season. This report outputs the concert programme to Word, with work and concert total instrumentation and duration.</notes>
      <work_table_duration_minutes_postfix>'</work_table_duration_minutes_postfix>
      <work_table_duration_seconds_postfix>''</work_table_duration_seconds_postfix>
      <first_half>First Half:</first_half>
      <second_half>Second Half:</second_half>
      <music>Total Music:</music>
      <total>Total Concert Duration:</total>
    </canada>
  </text>
  <rtf></rtf>
  <icon>rtf</icon>
</rep>
