{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Adates/edit.twig' %}

{% set edit = true %}
{% set disabled = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-calendar',
    'icon': 'fa-calendar',
    'headline': {
      0: __('adates') ~ ' ('~ __('adates.planninglevel') ~' '~ planninglevel ~')',
      1: __('view'),
      2: adate.text
    },
    'data': adate
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: currentController,
    1: {
      1: 'list',
      2: 'new',
      3: {'newcopy': {'link': "/"~currentController~"/newcopy/" ~ adate.id}},
      4: 'edit',
      5: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
    },
    2: adate.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-calendar', 1: __('view')} %}
{% endblock %}
