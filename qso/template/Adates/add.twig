{% extends '../Adates/add.twig' %}

{# TAB 1#}
{% block tab1 %}
  <div role="tabpanel" class="tab-pane {{ tab == 1 ? 'active' }}" id="basic">
    <header>
      {{ __('basic information') }}
    </header>
    <div class="">
      <div class="col col-xs-12">
        <fieldset>
          <div class="row">
            <section class="col col-12">
              {% embed '../Components/Datetime.twig' %}
                {% block datetime_component_fields %}
                  <!-- ko if: showTimeFrom -->
                  <div class="col col-3">
                    <label data-bind="text: timeFromLabel"></label>
                    <div class="form-group">
                      <div class="input-group">
                        <input class="form-control datetimepicker opas-datepicker" type="text" title=""
                               data-bind="
                                   enable: (hasEditRights() && !disabled),
                                   attr: {
                                     id: timeFromId,
                                     name: timeFromName,
                                     'data-format': timeFromFormat,
                                   },
                                   dateTimePicker: timeFromValue,
                                   event: {
                                      'dp.change': onTimeRefresh
                                   },
                                   'val': ''">
                        <span class="input-group-addon opas-datepicker-addon"><i class="far fa-clock"></i></span>
                      </div>
                    </div>
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showTimeTo -->
                  <div class="col col-3">
                    <label data-bind="text: timeToLabel"></label>
                    <div class="form-group">
                      <div class="input-group">
                        <input class="form-control datetimepicker opas-datepicker" type="text" title=""
                               name=""
                               data-bind="
                                   enable: (hasEditRights() && !disabled),
                                   attr: {
                                     id: timeToId,
                                     name: timeToName,
                                     'data-format': timeToFormat
                                   },
                                   dateTimePicker: timeToValue,
                                   event: {
                                      'dp.change': onTimeRefresh
                                   },
                                   'val': ''">
                        <span class="input-group-addon opas-datepicker-addon"><i class="far fa-clock"></i></span>
                      </div>
                    </div>
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showProductionWeek -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: pweekLabel"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showPweekValue(),
                               attr: {
                                 id: pweekId,
                                 name: pweekName
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showWeekday -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: weekdayLabel"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showWeekdayValue(),
                               attr: {
                                 id: weekdayId,
                                 name: weekdayName
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showWeek -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: weekLabel"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showWeekValue(),
                               attr: {
                                 id: weekId,
                                 name: weekName
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showMonth -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: monthLabel"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showMonthValue(),
                               attr: {
                                 id: monthId,
                                 name: monthName
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showYear -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: yearLabel"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showYearValue(),
                               attr: {
                                 id: yearId,
                                 name: yearName
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showBlock1 -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: block1Label"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showBlock1Value(),
                               attr: {
                                 id: block1Id,
                                 name: block1Name
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->

                  <!-- ko if: showBlock3 -->
                  <div class="input text col" style="padding-right: 0">
                    <label data-bind="text: block3Label"></label>
                    <input type="text" title=""
                           data-bind="
                               value: showBlock3Value(),
                               attr: {
                                 id: block3Id,
                                 name: block3Name
                               }"
                           style="width: 60px; height: 36px"
                           disabled="disabled">
                  </div>
                  <!-- /ko -->
                {% endblock %}
              {% endembed %}
              <datetime-widget params="
                            prefixId: 'adate',
                            planninglevel: {{ planninglevel }},
                            rights: '{{ Rights.get('adates-date_') | raw }}',
                            disabled: {{ (disabled == false) ? 'false' : 'true' }},
                            date: {
                              id: 'date_',
                              label: '{{ __('adates.date_') }} *',
                              format: '{{ Oform.readFormat('momentjsDate') }}',
                              value: '{{ (adate.date_ is empty) ? '' : adate.date_|date('Y-m-d H:i') }}',
                              dateValue: currentViewModel.date_
                            },
                            weekday: '{{ adate.weekday }}',
                            week: '{{ adate.week }}',
                            month: '{{ adate.month }}',
                            year: '{{ adate.year }}',
                            pweek: '{{ adate.pweek }}',
                            afterLoadCallback: 'self.root.loadedAdays(response)'
                            "></datetime-widget>
            </section>
          </div>

          <div class="row">
            <section class="col col-6">
              {{ Oform.input('season_id', {
                'class': 'select2',
                'data-bind': 'bindValue: seasonId',
                'data-listlength': sseasons|length,
                'label': __('adates.season_id'),
                'empty': '-',
                'options': sseasons,
                'disabled': (disabled) ? true : false
              }) | raw }}
            </section>
          </div>

          <div class="row">
            <section class="col col-2">
              <label>{{ __('adates.start_') }}</label>
              <div class="form-group">
                <div class="input-group">
                  {{ Oform.input('start_', {
                    'autocomplete': 'off',
                    'class': 'form-control datetimepicker opas-datepicker',
                    'data-bind': 'dateTimePicker: start_, event: {"dp.change": calculateDuration}',
                    'data-format': Oform.readFormat('momentjsTime'),
                    'data-keep-date': true,
                    'data-use-current': 'false',
                    'disabled': (disabled) ? true : false,
                    'label': false,
                    'templates': {'inputContainer': '{{content}}'},
                    'type': 'text',
                    'val': (adate.start_ is empty) ? '' : adate.start_|date('Y-m-d H:i'),
                  }) | raw }}
                  <span class="input-group-addon opas-datepicker-addon">
                    <i class="far fa-clock"></i>
                  </span>
                </div>
              </div>
            </section>

            <section class="col col-2">
              <label>{{ __('adates.end_') }}</label>
              <div class="form-group">
                <div class="input-group">
                  {{ Oform.input('end_', {
                    'autocomplete': 'off',
                    'class': 'form-control datetimepicker opas-datepicker',
                    'data-bind': 'dateTimePicker: end_, event: {"dp.change": calculateDuration}',
                    'data-format': Oform.readFormat('momentjsTime'),
                    'data-keep-date': true,
                    'data-use-current': 'false',
                    'disabled': (disabled) ? true : false,
                    'label': false,
                    'templates': {'inputContainer': '{{content}}'},
                    'type': 'text',
                    'val': (adate.end_ is empty) ? '' : adate.end_|date('Y-m-d H:i'),
                  }) | raw }}
                  <span class="input-group-addon opas-datepicker-addon">
                    <i class="far fa-clock"></i>
                  </span>
                </div>
              </div>
            </section>

            <section class="col col-2">
              <div class="input text">
                <label for="calculated-duration">{{ __('adates.duration') }}</label>
                <input disabled
                       maxlength="5"
                       id="calculated-duration"
                       class="inputmask-duration"
                       data-bind="value: calculatedDuration"/>
              </div>
            </section>

            <section class="col col-2">
              <label class="input">{{ __('adates.duration') }}</label>
              <div class="form-group">
                <div class="input-group">
                  {{ Oform.input('duration', {
                    'class': 'form-control inputmask-duration',
                    'data-bind': 'bindValue: duration, attr: {readonly: l_duration_lock() == 1}',
                    'disabled': (disabled) ? true : false,
                    'label': false,
                    'type': 'text',
                  }) | raw }}
                  <span class="input-group-addon" data-bind="click: {{ disabled ? 'true' : 'false' }} ? null : setDurationLock">
                      <i class="fas" data-bind="css: {'fa-unlock': l_duration_lock() == 0, 'fa-lock': l_duration_lock() == 1}"></i>
                  </span>
                </div>
              </div>
              {{ Oform.input('l_duration_lock', {
                'data-bind': 'bindValue: l_duration_lock',
                'disabled': (disabled) ? true : false,
                'type': 'hidden',
              }) | raw }}
            </section>

            <section class="col col-2">
              <label class="input">
                {{ Oform.input('duties', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.duties'),
                  'max': 99999,
                  'min': 0,
                  'step': 0.25,
                  'type': 'number',
                }) | raw }}
              </label>
            </section>
          </div>

          <div class="row">
            <section class="col col-4">
              {{ Oform.input('project_id', {
                'class': 'select2',
                'data-bind': "options: sprojects,
                  optionsText: 'name',
                  optionsValue: 'id',
                  optionsCaption: '-',
                  value: projectId,
                  event: {focus: onBeforeChangeProject, change: onChangeProject}",
                'disabled': (disabled) ? true : false,
                'label': __('adates.project_id'),
              }) | raw }}
            </section>

            <section class="col col-4">
              <label>
                <add-entity-component
                  params="{
                    optionText: 'name1Name2',
                    elementId: 'quick_add_conductor',
                    targetElementId: 'conductor-id',
                    targetUrl: '/saddresses/add'}">
                  {% if not disabled %}
                    <a href="#"
                       data-toggle="modal"
                       data-target="#modal_quick_add_conductor">{{ __('adates.conductor_id') }}</a>
                  {% else %}
                    <span>{{ __('adates.conductor_id') }}</span>
                  {% endif %}
                  {% cell 'Form::modalForm' {0: {
                    template: 'Saddresses/quick_add_conductor',
                    sinstrinstruments: sinstrinstruments,
                    saddressgroupsCon: saddressgroupsCon
                  }} %}
                </add-entity-component>
              </label>
              {{ Oform.input('conductor_id', {
                'class': 'select2',
                'data-bind': 'bindValue: conductorId',
                'disabled': (disabled) ? true : false,
                'empty': '-',
                'label': false,
                'options': conductoraddresses,
              }) | raw }}
            </section>
          </div>

          <div class="row">
            <section class="col col-4">
              {{ Oform.input('eventtype_id', {
                'class': 'select2',
                'data-bind': 'bindValue: eventtypeId, event: {change: function(model, event){updateTimeFields(model, event)}}',
                'disabled': (disabled) ? true : false,
                'empty': '-',
                'label': __('adates.eventtype_id'),
                'options': seventtypes,
              }) | raw }}
            </section>

            <section class="col col-4">
              <label>
                <add-entity-component
                  params="{
                    optionText: 'name1',
                    elementId: 'quick_add_orchestra',
                    targetElementId: 'orchestra-id',
                    targetUrl: '/saddresses/add'}">
                  {% if not disabled %}
                    <a href="#"
                       data-toggle="modal"
                       data-target="#modal_quick_add_orchestra">{{ __('adates.orchestra_id') }}</a>
                  {% else %}
                    <span>{{ __('adates.orchestra_id') }}</span>
                  {% endif %}
                  {% cell 'Form::modalForm' {0: {
                    template: 'Saddresses/quick_add_orchestra',
                    saddressgroupsOrc: saddressgroupsOrc
                  }} %}
                </add-entity-component>
              </label>
              {{ Oform.input('orchestra_id', {
                'class': 'select2',
                'data-bind': 'bindValue: orchestraId',
                'disabled': (disabled) ? true : false,
                'empty': '-',
                'label': false,
                'options': orchestraaddresses
              }) | raw }}
            </section>

            <section class="col col-4">
              <label class="input">
                {{ Oform.input('programtitle', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.programtitle'),
                }) | raw }}
              </label>
            </section>
          </div>

          <div class="row">
            <section class="col col-4">
              <label>
                <add-entity-component
                  params="{
                            optionText: 'venue',
                            elementId: 'quick_add_location',
                            targetElementId: 'location-id',
                            targetUrl: '/saddresses/add'}">
                  {% if not disabled %}
                    <a href="#"
                       data-toggle="modal"
                       data-target="#modal_quick_add_location">{{ __('adates.location_id') }}</a>
                  {% else %}
                    <span>{{ __('adates.location_id') }}</span>
                  {% endif %}
                  {% cell 'Form::modalForm' {0: {
                    template: 'Saddresses/quick_add_location',
                    saddressgroupsLoc: saddressgroupsLoc,
                    ssysaddressaccesscategories: ssysaddressaccesscategories
                  }} %}
                </add-entity-component>
              </label>
              {{ Oform.control('location_id', {
                'class': 'select2',
                'data-bind': "value: locationId,
                          options: locationaddresses,
                          optionsText: 'venue',
                          optionsValue: 'id',
                          optionsCaption: '-',
                          event: {'select2:selecting': onBeforeChangeLocation, 'select2:clearing': onBeforeClearLocation}",
                'data-listlength': locationaddresses|length,
                'disabled': disabled,
                'empty': '-',
                'label': false,
                'options': locationaddresses
              }) | raw }}
            </section>
            <section class="col col-4">
              {{ Oform.input('status_id', {
                'class': 'select2',
                'data-listlength': sdatestatuses|length,
                'label': __('adates.status_id'),
                'empty': '-',
                'options': sdatestatuses,
                'disabled': (disabled) ? true : false
              }) | raw }}
            </section>
            <section class="col col-4">
              <label class="input">
                {{ Oform.input('programno', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.programno'),
                }) | raw }}
              </label>
            </section>
          </div>
        </fieldset>

        <fieldset>
          <div class="row">
            <section class="col col-12">
              <label class="input">
                {{ Oform.input('text', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.text'),
                }) | raw }}
              </label>
            </section>
          </div>
          <div class="row">
            <section class="col col-12">
              <label class="input">
                {{ Oform.input('notes', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.notes'),
                }) | raw }}
              </label>
            </section>
          </div>
        </fieldset>

        <fieldset>
          <div class="row">
            <section class="col col-6">
              {{ Oform.input('dress_id', {
                'class': 'select2',
                'data-listlength': sdresses|length,
                'disabled': (disabled) ? true : false,
                'label': __('adates.dress_id'),
                'empty': '-',
                'options': sdresses,
              }) | raw }}
            </section>

            <section class="col col-6">
              <opas-tags
                params="controller: 'ADATES', tags: {{ tags|json_encode }}, selectedIds: {{ adate.tags|column('id')|json_encode }}, disabled: {{ disabled ? 'true' : 'false' }}"></opas-tags>
            </section>
          </div>
        </fieldset>

        <fieldset>
          {% if 'A' in accessRights.AdateSeries %}
            <div class="row">
              {% embed '../Components/Adates/adate_series.twig' %}{% endembed %}
              <adate-series-list-widget params="parent_id:{{ adate.id }}"></adate-series-list-widget>
            </div>
          {% endif %}

          {% if 'A' in accessRights.AdatePersons %}
            <div class="row">
              {% embed '../Components/Adates/adate_persons.twig' %}{% endembed %}
              <adate-persons-list-widget params="parent_id:{{ adate.id }}"></adate-persons-list-widget>
            </div>
          {% endif %}

          {% if 'A' in accessRights.AdateActivities %}
            <div class="row">
              {% embed '../Components/Adates/adate_activities.twig' %}{% endembed %}
              <adate-activities-list-widget params="parent_id:{{ adate.id }}"></adate-activities-list-widget>
            </div>
          {% endif %}
        </fieldset>


        <fieldset>
          <div class="row">
            <section class="col col-4">
              <label class="input">
                {{ Oform.input('pl', {
                  'disabled': true,
                  'label': __('adates.planninglevel'),
                  'val': (adate.planninglevel ?? planninglevel),
                }) | raw }}
                <input type="hidden" value="{{ adate.planninglevel ?? planninglevel }}" name="planninglevel">
              </label>
            </section>

            <section class="col col-4">
              {% element 'Snippets/toggle_yes_no' {
                'name': 'l_duties', 'disabled': disabled,
                'value': adate.l_duties, 'label': __('adates.l_duties')
              } %}
            </section>

            <section class="col col-4">
              {% element 'Snippets/toggle_yes_no' {
                'name': 'l_print_details', 'disabled': disabled,
                'value': adate.l_print_details, 'label': __('adates.l_print_details')
              } %}
            </section>
          </div>

          <div class="row">
            <section class="col col-4">
              {% element 'Snippets/toggle_yes_no' {
                'name': 'l_ticket_online', 'disabled': disabled,
                'value': adate.l_ticket_online, 'label': __('adates.l_ticket_online')
              } %}
            </section>
            <section class="col col-4">
              {% element 'Snippets/toggle_yes_no' {
                name: 'l_defaultaccounting', disabled: disabled,
                value: adate.l_defaultaccounting, label: __('adates.l_defaultaccounting')
              } %}
            </section>
            <section class="col col-4">
            </section>
          </div>

          <div class="row">
            <section class="col col-4">
              <label class="input">
                {{ Oform.input('code', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.code'),
                }) | raw }}
              </label>
            </section>
            <section class="col col-4">
              <label class="input">
                {{ Oform.input('abbreviation', {
                  'disabled': (disabled) ? true : false,
                  'label': __('adates.abbreviation'),
                }) | raw }}
              </label>
            </section>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
{% endblock %}
{# END TAB1 #}

{% block tab2 %}
  <div role="tabpanel" class="tab-pane {{ tab == 2 ? 'active' }}" id="program">
    <header>
      {{ __('adaysperiods.program_text') }}
    </header>
    <div>
      <div class="col col-xs-12">
        <fieldset>
          <div class="row">
            <section class="col col-6">
              {{ Oform.input('project-name', {
                'data-bind': 'value: projectName',
                'disabled': true,
                'label': __('adates.project_id'),
              }) | raw }}
            </section>
            <section class="col col-6">
              {{ Oform.input('conductor-name', {
                'data-bind': 'value: conductorName',
                'disabled': true,
                'label': __('adates.conductor_id'),
              }) | raw }}
            </section>
          </div>
          <div class="row">
            <section class="col col-6">
              {{ Oform.input('orchestra-name', {
                'data-bind': 'value: orchestraName',
                'disabled': true,
                'label': __('adates.orchestra_id'),
              }) | raw }}
            </section>
            <section class="col col-6">
              {{ Oform.input('eventtype-name', {
                'data-bind': 'value: eventtypeName',
                'disabled': true,
                'label': __('adates.eventtype_id'),
              }) | raw }}
            </section>
          </div>
        </fieldset>

        <fieldset>
          <div class="row">
            <section class="col col-6">
            </section>
          </div>
        </fieldset>

        <fieldset>
          <div class="row">
            <div class="row">
              {% block adateWorksComponent %}
                {% embed '../Components/Adates/adate_works.twig' %}{% endembed %}
              {% endblock %}
              <adate-works-list-widget params="parent_id:{{ adate.id }}"></adate-works-list-widget>
            </div>
          </div>
        </fieldset>
      </div>
    </div>
  </div>
{% endblock %}

