{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Adates/add.twig' %}

{% set edit = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-calendar',
    icon: 'fa-pen',
    headline: {
      0: __('adates') ~ ' ('~ __('adates.planninglevel') ~' '~ planninglevel ~')',
      1: __('edit'),
      2: adate.text
    },
    data: adate
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: currentController,
    1: {
      1: 'list',
      2: 'view',
      3: 'new',
      4: {'newcopy': {'link': "/"~currentController~"/newcopy/" ~ adate.id}},
      5: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
      6: {'save': {
        'disabled': true,
        'data-bind': 'enable: formChanged, event: {click: function(){ onBeforeSubmitForm('~adate.id~') }}'}},
      7: {'revert': {
        'disabled': true,
        'data-bind': "enable: formChanged, click: function(){uiService.showModal('confirmRevertForm')}"}}
    },
    2: adate.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-pen', 1: __('edit')} %}
{% endblock %}

{% block additional_form_fields %}
  <div class="hidden" data-bind="foreach: selectedRelatives">
    <input type="hidden" name="selectedRelatives[]" data-bind="value: $data"/>
  </div>
{% endblock %}

{% block dialogs %}
  {{ parent() }}

  {% cell 'Dialog::confirm' {0:{
    'id': 'confirmDeleteItem',
    'headline': __('delete record'),
    'message': __('delete record {0}?',adate.date_),
    'url': '/'~currentController~'/delete/' ~ adate.id
  }} %}

  {% cell 'Form::modalForm' {0: {
    'template': 'Adates/select_adate_relatives'
  }} %}

  {% cell 'Form::modalForm' {0: {
    'template': 'Adates/confirm_change_eventtype'
  }} %}
{% endblock %}
