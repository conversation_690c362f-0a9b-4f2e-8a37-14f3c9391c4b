{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Sworks/add.twig' %}

{% set edit = true %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-music',
    'icon': 'fa-pen',
    'headline': {0: __('sworks'), 1: __('edit'), 2: swork.name },
    'data': swork
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: 'sworks',
    1: {
      1: 'list',
      2: 'view',
      3: 'new',
      4: {'newcopy': {'link': "/sworks/newcopy/" ~ swork.id}},
      5: {'delete': {'data-bind': "click: function(){uiService.showModal('confirmDeleteItem')}"}},
      6: {'save': {
        'disabled': true,
        'data-bind': 'enable: formChanged, event: {click: submitForm}'}},
      7: {'revert': {
        'disabled': true,
        'data-bind': "enable: formChanged, click: function(){uiService.showModal('confirmRevertForm')}"}}
    },
    2: swork.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-pen', 1: __('edit')} %}
{% endblock %}

{% block dialogs %}
  {{ parent() }}

  {% cell 'Dialog::confirm' {0:{
    'id': 'confirmDeleteItem',
    'headline': __('delete record'),
    'message': __('delete record {0}?',swork.title1),
    'url': '/sworks/delete/' ~ swork.id
  }} %}
{% endblock %}
