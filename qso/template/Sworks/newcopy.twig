{% set path = constant('CUSTOMER_TPL_DIR') %}
{% extends path ~ 'Sworks/add.twig' %}

{% block pageheader %}
  {% cell 'Pageheader' {
    controller: currentController,
    menuicon: 'fa-music',
    'icon': 'fa-plus',
    'headline': {0: __('sworks'), 1: __('new+copy')}
  } %}
{% endblock %}

{% block pagemenu %}
  {% cell 'Pagemenu' {
    0: 'sworks',
    1: {
      1: 'list',
      2: 'new',
      3: {'save': {
        'disabled': true,
        'data-bind': 'enable: formChanged, event: {click: submitForm}'}},
      4: {'revert': {
        'disabled': true,
        'data-bind': "enable: formChanged, click: function(){uiService.showModal('confirmRevertForm')}"}}
    },
    2: swork.id
  } %}
{% endblock %}

{% block jarvis_header %}
  {% cell 'Jarviswidget::header' {0: 'fa-plus', 1: __('new+copy')} %}
{% endblock %}

{% block form_definition %}
  {{ Oform.create(swork, {
    'url': '/sworks/add',
    'name': 'SworksForm',
    'id': 'SworksForm',
    'class': 'smart-form',
    'method': 'post'
  }) | raw }}
{% endblock %}
