{% extends '../Scomposers/add.twig' %}

{% block tab1_content %}

  <div class="col col-xs-12 col-sm-6">
    <fieldset>
      <div class="row">
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('lastname', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.lastname'),
              }) | raw }}
          </label>
        </section>
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('firstname', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.firstname'),
              }) | raw }}
          </label>
        </section>
      </div>

      <div class="row">
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('name2', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.name2'),
              }) | raw }}
          </label>
        </section>
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('name3', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.name3'),
              }) | raw }}
          </label>
        </section>
      </div>

      <div class="row">
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('initials', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.initials'),
              }) | raw }}
          </label>
        </section>
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('sex', {
              'class': 'select2',
              'data-listlength': 3,
              'disabled': (disabled) ? true : false,
              'empty': '-',
              'label': __('scomposers.sex'),
              'options': {'M': 'M', 'F': 'F'},
              }) | raw }}
          </label>
        </section>
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('code', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.code'),
              }) | raw }}
          </label>
        </section>
      </div>

      <div class="row">
        <div class="col col-6">
          <div class="row">
            <section class="col col-4">
              {{ Oform.input('birthday', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 31,
                'min': 1,
                'step': 1,
                'label': __('scomposers.birthday'),
                'type': 'number'
              }) | raw }}
            </section>
            <section class="col col-4">
              {{ Oform.input('birthmonth', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 12,
                'min': 1,
                'step': 1,
                'label': __('scomposers.birthmonth'),
                'type': 'number'
              }) | raw }}
            </section>
            <section class="col col-4">
              {{ Oform.input('birthyear', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 9999,
                'min': 1,
                'step': 1,
                'label': __('scomposers.birthyear'),
                'type': 'number'
              }) | raw }}
            </section>
          </div>
        </div>
        <div class="col col-6">
          <div class="row">
            <section class="col col-4">
              {{ Oform.input('deathday', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 31,
                'min': 0,
                'step': 1,
                'label': __('scomposers.deathday'),
                'type': 'number'
              }) | raw }}
            </section>
            <section class="col col-4">
              {{ Oform.input('deathmonth', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 12,
                'min': 1,
                'step': 1,
                'label': __('scomposers.deathmonth'),
                'type': 'number'
              }) | raw }}
            </section>
            <section class="col col-4">
              {{ Oform.input('deathyear', {
                'disabled': (disabled) ? true : false,
                'label': false,
                'max': 9999,
                'min': 0,
                'step': 1,
                'label': __('scomposers.deathyear'),
                'type': 'number'
              }) | raw }}
            </section>
          </div>
        </div>
      </div>

      <div class="row">
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('birthplace', {
              'disabled': (disabled) ? true: false,
              'label': __('scomposers.birthplace'),
              }) | raw }}
          </label>
        </section>

        <section class="col col-4">
          <label class="input">
            {{ Oform.input('birthstate', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.birthstate'),
              }) | raw }}
          </label>
        </section>

        <section class="col col-4">
          <label class="input">
            {{ Oform.input('birthcountry_id', {
              'class': 'select2',
              'data-listlength': scountries|length,
              'disabled': (disabled) ? true : false,
              'empty': '-',
              'label': __('scomposers.birthcountry_id'),
              'options': scountries,
              }) | raw }}
          </label>
        </section>
      </div>

      <div class="row">
        <section class="col col-4">
          <label class="input">
            {{ Oform.input('deathplace', {
              'disabled': (disabled) ? true: false,
              'label': __('scomposers.deathplace'),
              }) | raw }}
          </label>
        </section>

        <section class="col col-4">
          <label class="input">
            {{ Oform.input('deathstate', {
              'disabled': (disabled) ? true : false,
              'label': __('scomposers.deathstate')
            }) | raw }}
          </label>
        </section>

        <section class="col col-4">
          <label class="input">
            {{ Oform.input('deathcountry_id', {
              'class': 'select2',
              'data-listlength': scountries|length,
              'disabled': (disabled) ? true : false,
              'empty': '-',
              'label': __('scomposers.deathcountry_id'),
              'options': scountries,
            }) | raw }}
          </label>
        </section>
      </div>

    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('notes', {
              'label': __('scomposers.notes'),
              'disabled': (disabled) ? true : false,
            }) | raw }}
          </label>
        </section>
        <section class="col col-6">
          <label class="input">
            {{ Oform.input('synonyms', {
              'label': __('scomposers.synonyms'),
              'disabled': (disabled) ? true : false,
            }) | raw }}
          </label>
        </section>
      </div>
    </fieldset>

    <fieldset>
      <div class="row">
        <section class="col col-6">
          {{ Oform.input('address_id', {
            'class': 'select2',
            'disabled': (disabled) ? true : false,
            'label': __('scomposers.address_id'),
            'options': {'':'', (scomposer.saddress.id): scomposer.saddress.name1Name2},
            'type': 'select',
          }) | raw }}
        </section>
        <section class="col col-3">
          <label class="toggle">
            <input type="hidden" value="0" name="l_activated"/>
            <input type="checkbox" value="1" {{ (disabled) ? 'disabled' : '' }}
                   name="l_activated" {{ scomposer.l_activated ? 'checked' : '' }} />
            <i data-swchon-text="{{ __('yes') }}"
               data-swchoff-text="{{ __('no') }}"></i> {{ __('activated') }}
          </label>
        </section>
      </div>
    </fieldset>
  </div>


  <div class="col col-xs-12 col-sm-6">

    {#ACCORDION 2#}
    {% embed '../Components/Scomposers/scomposer_residents.twig' with {'rights': 'A'} %}
    {% endembed %}
    <scomposer-residents-list-component params="parent_id: {{ scomposer.id }}">
      <span>{{ __('loading') }} ...</span>
    </scomposer-residents-list-component>
    {#END ACCORDION 2#}


    {#ACCORDION 2#}
    {% embed '../Components/Scomposers/scomposer_nationalities.twig' with {'rights': 'A'} %}
    {% endembed %}
    <scomposer-nationalities-list-component params="parent_id: {{ scomposer.id }}">
      <span>{{ __('loading') }} ...</span>
    </scomposer-nationalities-list-component>
    {#END ACCORDION 2#}

    {#ACCORDION 1#}
    {% embed '../Components/Scomposers/scomposer_groups.twig' with {'rights': 'A'} %}
    {% endembed %}
    <scomposer-groups-list-component params="parent_id:{{ scomposer.id }}">
      <span>{{ __('loading') }} ...</span>
    </scomposer-groups-list-component>
    {#END ACCORDION 1#}
  </div>

{% endblock %}
