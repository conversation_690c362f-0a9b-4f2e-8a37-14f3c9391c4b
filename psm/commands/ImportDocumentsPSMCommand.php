<?php

namespace Customer\psm\commands;

use Customer\lso\commands\Import442hzWorks\Import442hzWorks;
use Customer\psm\commands\ImportDocumentsPSM\ImportDocumentsPSM;

use Cake\Console\Arguments;
use Cake\Console\Command;
use Cake\Console\ConsoleIo;
use Cake\Console\Exception\StopException;
use Exception;

/*
 * JH ONCUST-3340
 *
 *
 */

class ImportDocumentsPSMCommand extends Command
{
    public function execute(Arguments $args, ConsoleIo $io)
    {
        $importFile = 'DOCUMENT.PSOMZ16042024.zip';

        try {

            $io->warning('This command import a zip file with documents to ON.');

            $continue = $io->askChoice('Do you want to continue?', ['Yes', 'No'], 'Yes');

            if (strtolower($continue) == 'no') {
                $io->abort('Stop import');
            }

            //$answer = $io->askChoice('Do you want to update current CLASSIC records in the documents tabele or append new NEXT records?',
            //    ['Update', 'Append'],
            //    'Append')
            //;
            // $append = (strtolower($answer) !== 'update');
            $append = true;

            // $answer = $io->askChoice('Do you want run the command as dry run?', ['Yes', 'No'], 'No');
            // $dryRun = (strtolower($answer) == 'yes');

            $importFile = $io->ask('Enter the name of the file that must be inside the tmp folder.', $importFile);

            if (trim($importFile) === '') {
                $io->abort('No file name entered.');
            }

            $importPath = TMP . $importFile;

            if (!file_exists($importPath)) {
                $io->abort('The file "' . $importPath . '" does not exists.');
            }

            $importer = new ImportDocumentsPSM;

            $message = $importer->importDocuments($importPath, $append, 'DOCUMENT');

            $io->out($message);

        } catch (StopException $exception) {
        } catch (Exception $exception) {
            $io->error('Unexpected error: ' . $exception->getMessage());
        }

    }
}
