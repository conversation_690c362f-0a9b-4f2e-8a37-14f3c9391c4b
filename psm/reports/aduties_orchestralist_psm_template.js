/**
 * Nachgeladene JS-Erweiterung fÃ¼r das Template
 *
 * Hier wird in dem Namespace FUNCTIONS unter dem Namen des Templates eine Funktion abgelegt, die sobald aufgerufen
 * wird, wenn das Template im Wizard angezeigt wird.
 */

var NAMESPACE = "aduties_orchestralist_psm_template";

var REPORTS = REPORTS || {};

REPORTS.aduties_orchestralist_psm_template = function () {

  $('.cb').change(function() {
    $name =$(this).attr('name');
    $is_checked = ($(this).is(":checked") ? 1:0);
    writeToStorage($name, $is_checked);
  });

  $('#order_by').change(function() {
    $name = $(this).attr('name');
    $val = $(this).val();
    writeToStorage($name, $val);
  });

  function writeToStorage(key, value) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = serializedData ? JSON.parse(serializedData) : {};
    data[key] = value;
    localStorage.setItem(NAMESPACE, JSON.stringify(data));
  }

  function readFromStorage(key) {
    const serializedData = localStorage.getItem(NAMESPACE);
    const data = JSON.parse(serializedData);
    return data ? data[key] : undefined;
  }

  function setValues() {
    const serializedData = localStorage.getItem(NAMESPACE);

    const data = JSON.parse(serializedData);

    if(data) {
      $.each(data, function (key, val) {
        $val = val > 0;

         if ($("#" + key).length) {
          //$("#" + key).val(val);
          $("#" + key).prop("checked", $val);
        }
      });

      $('#order_by').val(data['order_by']);
    }
  }
  setValues();
};

