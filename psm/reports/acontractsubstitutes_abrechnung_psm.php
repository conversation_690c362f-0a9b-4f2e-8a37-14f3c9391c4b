<?php

//namespace App\Utility\Reports;

use \App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;

use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;
/*
PSM Aushilfenabrechnung
20240212 ONCUST-2790
 */

require_once('acontractsubstitutes_vertrag_psm.php');

class acontractsubstitutes_abrechnung_psm extends acontractsubstitutes_vertrag_psm

{
    private $aexpenses_contract = null;

    /*********************************************/
    function initialize()
    {
        parent::initialize();
        $this->template_filename = CUSTOMER_REP_DIR . 'acontractsubstitutes_abrechnung_psm.docx';
    }

    function getVertVerst() {

        //war für nachstehende Dienste als	 Vertretung  Verstärkung verpflichtet.
        $textrun = new TextRun();

        //$checkedBox='<w:sym w:font="Wingdings" w:char="F0FE"/>';
        //$unCheckedBox = '<w:sym w:font="Wingdings" w:char="F0A8"/>';

        $checkedBox = true;
        $unCheckedBox = false;

        $textrun->addText(htmlspecialchars('war für nachstehende Dienste als '), $this->styleFont);
        $textrun->addFormField('checkbox')->setValue(($this->vertretung ? $checkedBox : $unCheckedBox));

        $textrun->addText(htmlspecialchars(' Vertretung '), array_merge($this->styleFont, $this->styleFont_Bold));
        $textrun->addFormField('checkbox')->setValue(($this->vertretung ? $unCheckedBox : $checkedBox));

        $textrun->addText(htmlspecialchars(' Verstärkung '), array_merge($this->styleFont, $this->styleFont_Bold));
        $textrun->addText(htmlspecialchars('verpflichtet.'), $this->styleFont);

        return $textrun;
    }

    function getSchedule_expenses() {

        $this->aexpenses_contract = TableRegistry::getTableLocator()->get('Aexpenses')
            ->find('all')
            ->contain(['Sexpensetypes'])
            ->where(
                    [
                        'Aexpenses.contractsubs_id' => $this->acontract->id
                    ]
            );

        $this->acol_widths = array();
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(2.5);
        $this->acol_widths[] = Converter::cmToTwip(4.3);
        $this->acol_widths[] = Converter::cmToTwip(5.9);
        $this->acol_widths[] = Converter::cmToTwip(2.25);

        $this->width_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->width_table += $col_width;
        }

        $styleIdent = array('left' => Converter::cmToTwip(0), 'right' => Converter::cmToTwip(0));

        $borderSize = 0;
        $tableProperties = array(
            'borderSize' => $borderSize,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.12),
            'cellMarginLeft' => Converter::cmToTwip(0.12),
            'cellMarginRight' => Converter::cmToTwip(0.12),
            'width' => $this->width_table,
            'unit' => TblWidth::TWIP,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED,
            'spacing' => 0,
            'indentation' => $styleIdent
        );

        //,
        //'indentation' => array('left' => Converter::cmToTwip(1.5), 'right' => Converter::cmToTwip(1.5))

        $table = new Table($tableProperties);
        //$table = $section->addTable($tableProperties);

        $styleCell = array();
        //$styleCell_border = array_merge($this->styleCell_borderTop_grey, $this->styleCell_borderBottom_grey);
        $styleCell_border = array();

        $amount_total = 0;
        $count = 0;
        $cdate_old = '';

        $aexpense_ids_date = array();
        foreach ($this->acontract->acontract_dates as $acontract_date) {

            $adate = $acontract_date->adate;

            $count++;

            $eventtype = $adate->seventtype ? $adate->seventtype->name : '';

            $abold = array();
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : '');
            if($l_performance == 1) {
                $abold = $this->styleFont_bold;
            }

            $eventtypegroup_code = ($adate->seventtype && $adate->seventtype->seventtypegroup ? $adate->seventtype->seventtypegroup->code : '');

            $cdate = ($adate->date_ ? $adate->date_->format('d.m.y') : '');

            $startend = $this->reporttools->getTime($adate, false);

            $location = $adate->locationaddress->name1;
            //$location .= (!empty($location) && !empty($adate->locationaddress->street) ? ', ' : '') . $adate->locationaddress->street;
            $location .= (!empty($location) && !empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place;


            $amount = 0;
            $camount = '';

            $table->addRow();

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            if($cdate_old<>$cdate) {
                $cell->addText(htmlspecialchars($cdate), []);
            }
            $cdate_old = $cdate;


            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($startend), []);

            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell->addText(htmlspecialchars($eventtype), []);

            $cell3 = $table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell4 = $table->addCell($this->acol_widths[4], array_merge($styleCell));

            $count_expenses = 0;
            foreach($this->aexpenses_contract as $k=>$aexpense) {
                //if($aexpense->date_id == $adate->id && $aexpense->sysbase_id==1) {
                //AcontractsubsEventtype, Attendancetype
                //if($aexpense->date_id == $adate->id && ($aexpense->sysbase_id==1 || in_array($aexpense->rule_id, [6,13]))) {
                if($aexpense->date_id == $adate->id) {

                    $aexpense_ids_date[] = $aexpense->id;

                    $count_expenses++;

                    $amount = $aexpense->amount*$aexpense->number_;

                    $expensetype = ($aexpense->sexpensetype ? $aexpense->sexpensetype->name : '');

                    $amount_total += $amount;
                    $camount = number_format($amount, 2, ',', '.').' €';
                    $cell3->addText(htmlspecialchars($expensetype));
                    if(!empty($aexpense->text)) {
                        $cell3->addText(htmlspecialchars($aexpense->text));
                        $cell4->addTextBreak();
                    }

                    $cell4->addText(htmlspecialchars($camount), [], $this->styleAlign_right);
                }
            }
        }

        $aexpenses_wo_date = array();
        $count_wo_date = 0;
        foreach($this->aexpenses_contract as $k=>$aexpense) {
            if(in_array($aexpense->id, $aexpense_ids_date)) {continue;}
            $count_wo_date++;

            $expensetype = ($aexpense->sexpensetype ? $aexpense->sexpensetype->name : '');
            $amount = $aexpense->amount*$aexpense->number_;
            $amount_total += $amount;

            $camount = number_format($amount, 2, ',', '.').' €';

            if($count_wo_date==1) {
                $table->addRow();
                $cell = $table->addCell($this->width_table, array_merge($styleCell, $this->cellColSpan5));
            }

            $table->addRow();
            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars(($aexpense->date_ ? $aexpense->date_->format('d.m.y') : '')), []);

            $cell = $table->addCell($this->acol_widths[1]+$this->acol_widths[2]+$this->acol_widths[3], $this->cellColSpan3);
            $cell->addText(htmlspecialchars($expensetype), []);
            if($aexpense->text>'') {
                $cell->addText(htmlspecialchars($aexpense->text), []);
            }

            $cell = $table->addCell($this->acol_widths[4], array_merge($styleCell));
            $cell->addText(htmlspecialchars($camount), [], $this->styleAlign_right);

        }

        $table->addRow();
        $cell = $table->addCell($this->width_table, array_merge($styleCell, $this->cellColSpan5));

        $table->addRow();
        $cell = $table->addCell($this->acol_widths[0]);
        $cell->addText(htmlspecialchars(trim('Summe:')), $this->styleFont_Bold);

        $cell = $table->addCell($this->acol_widths[1]+$this->acol_widths[2]+$this->acol_widths[3], $this->cellColSpan3);


        $camount = number_format($amount_total, 2, ',', '.').' €';
        $cell = $table->addCell($this->acol_widths[4], array_merge($styleCell));
        $cell->addText(htmlspecialchars($camount), $this->styleFont_Bold, $this->styleAlign_right);

        return $table;
    }
}
