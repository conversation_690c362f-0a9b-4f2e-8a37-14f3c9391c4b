<?php
/*
******** ONCUST-3117

PSM Wochenplan
*/

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;

require_once('adates_monatsplan_psm.php');

class adates_wochenplan_psm extends adates_monatsplan_psm
{

    function initialize()
    {
        parent::initialize();
        $this->default_font_size = 11;
        //$this->styleCantSplit
        $this->styleKeepNext = array();
    }

    function addSection() {
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(2),
                'marginRight' => Converter::cmToTwip(1.2),
                'marginTop' => Converter::cmToTwip(1.25),
                'marginBottom' => Converter::cmToTwip(0.95))
        );

        $sectionStyle = $this->section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_LANDSCAPE);

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(8.5)),
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(16))
                    )
            );

        $footer = $this->section->addFooter();
        $footer->addText(
            htmlspecialchars('ÄNDERUNGEN VORBEHALTEN'),
            array('name' => 'Arial Narrow', 'size'=>12, $this->styleFont_bold, $this->styleBackground_black, 'color'=>'white')
        );

        $footer->addText(
            htmlspecialchars('Schätz _________________________________                      	Orchestervorstand________________________   '."\t\t".' Personalrat ______________________________'),
            array('name' => 'Arial Narrow', 'size'=>8)
        );


    }

    function fill_phpWord()
    {
        $this->prepare_aweeks();

        $this->phpWord->setDefaultFontSize(10);
        $this->phpWord->setDefaultFontSize($this->default_font_size);
        $this->phpWord->setDefaultFontName($this->default_font);
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $this->count_weeks = 0;
        foreach ($this->aweeks as $week=>$this->aweek) {
            $this->count_weeks++;

            if($this->count_weeks>1) {
                $this->section->addPageBreak();
            }

            $this->showWeekPage();
            //$this->showSchedule();
        }
    }

    function prepare_aweeks() {
        //$this->addSection();

        $this->aweeks = array();

        foreach ($this->adates_selected as $adate) {

            $project_id = $adate->project_id;
            $season_id = $adate->season_id;
            $week = $adate->week;

            $d_date = $adate->date_->format('Ymd');

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);


            $sseason_weeksoff = TableRegistry::getTableLocator()->get('SseasonWeeksoff')
                ->find('all')

                ->where(
                    ['SseasonWeeksoff.season_id'=>$season_id, 'SseasonWeeksoff.week'=>$week]
                )
                ->order(['SseasonWeeksoff.week' => 'ASC'])
                ->first();

            $text = '';
            if($sseason_weeksoff) {
                $text = $sseason_weeksoff->text;
            }


            if (!key_exists($week, $this->aweeks)) {
                $this->aweeks[$week] = array(
                    'cmindate' => '',
                    'cmaxdate' => '',
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'week' => $week . '#XXX',
                    'text' => $text,
                    'pweek' => $adate->pweek,
                    'block1' => $adate->block1,
                    'year' => $adate->year,
                    'adays' => array()
                );

                // Alle Tage der Woche holen
                $adays = TableRegistry::getTableLocator()->get('Adays')
                    ->find('all')
                    ->contain(['Sholydays'])
                    ->where(["Adays.week='" . $week . "' AND Adays.planninglevel=1"])
                    ->orderAsc('Adays.date_')
                    ->distinct();


                $count = 0;
                foreach ($adays as $aday) {
                    $count++;

                    $d_day = $aday->date_->format('Ymd');

                    if ($count == 1) {
                        $this->aweeks[$week]['cmindate'] = $aday->date_->format('d.m.Y');
                    }

                    $this->aweeks[$week]['cmaxdate'] = $aday->date_->format('d.m.Y');

                    $this->aweeks[$week]['adays'][$d_day] = array(
                        'date_' => $aday->date_,
                        'aday' => $aday,
                        'dow' => $aday->date_->format('N'),
                        'holyday_id' => (int)$aday->holyday_id,
                        'cdate' => $aday->date_->format('d.m.Y'),
                        'cday' => $aday->weekday . ', ' . $aday->date_->format('d.m.Y'),
                        'holyday' => ($aday->sholyday ? $aday->sholyday->name : ''),
                        'l_performance' => 0,
                        'weekday' => $aday->weekday,
                        'adates' => array()
                    );
                }
                //$this->section->addText($week_date.'#'.$d_date);
                //$this->section->addText($adate->id.'#'.$adate->date_->format('d.m.Y'));
            }

            if ($l_performance == 1) {
                $this->aweeks[$week]['adays'][$d_date]['l_performance'] = 1;
            }

            $this->aweeks[$week]['adays'][$d_date]['adates'][$adate->id] = $adate;
        }
    }

    function showSchedule()
    {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(1.27);
        $this->acol_widths[] = Converter::cmToTwip(2.29);
        $this->acol_widths[] = Converter::cmToTwip(2.54);
        $this->acol_widths[] = Converter::cmToTwip(1.52);
        $this->acol_widths[] = Converter::cmToTwip(7.87);
        $this->acol_widths[] = Converter::cmToTwip(1.31);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(7.25);

        $this->w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->w_table += $col_width;
        }

        $this->otableProperties = array(
            'borderSize' => 0,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.1),
            'cellMarginBottom' => Converter::cmToTwip(0.1),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $this->w_table,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED,
            'unit' => TblWidth::TWIP
        );

        $this->otable = $this->section->addTable($this->otableProperties);
        $this->showHeaderWeek();
        $this->showWeekSchedule();
    }

    function showHeaderWeek() {
        $this->otable->addRow(null,$this->styleCantSplit);

        $styleCell = $this->styleBackground_black;
        $this->styleFont = array_merge(array('color' => 'white'), $this->styleBackground_black, $this->styleFont_bold);

        $cell = $this->otable->addCell($this->w_table, array_merge($styleCell, $this->styleCell_borderTop, $this->cellColSpan8));

        // 20240223
        //An der Stelle, wo jetzt im Kopf bei dem angehängten Beispiel AZ 2 / 2024 angedruckt ist, soll bitte der Wert des Feldes „Text“ (Register Spielzeiten; hier 24/25)eingefügt werden,
        //'WOCHENDIENSTPLAN   KW '.substr($this->aweek['week'], 3,2).'     AZ '.$this->aweek['block1'].' / '.$this->aweek['year'].'    '.$this->aweek['cmindate'].'  bis  '.$this->aweek['cmaxdate']
        $cell->addText(htmlspecialchars(
            'WOCHENDIENSTPLAN   KW '.substr($this->aweek['week'], 3,2).'     '.$this->aweek['text'].'    '.$this->aweek['cmindate'].'  bis  '.$this->aweek['cmaxdate']
            ),
            array_merge($this->styleFont_24, $this->styleFont),
            array_merge($this->styleKeepNext)
        );


        $this->otable->addRow(Converter::cmToTwip(0.01), array("exactHeight" => true));
        $cell = $this->otable->addCell($this->w_table, array_merge($this->cellColSpan8));
        $cell->addText('', $this->styleFont_4);

        $this->styleFont = array_merge($this->styleFont_12, $this->styleFont);

        $this->otable->addRow(null,$this->styleCantSplit);

        //*** 20200623
        //***this.oTable.cells(this.oTable.nnum_of_rows,2).value = 'AZ ' + ALLT(STR(this.nBlock3))

        $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Tag'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Datum'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[2], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Uhrzeit'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[3], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Art'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[4], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Produktion'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[5], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Ort'), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[6], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Dienstwert'), array_merge($this->styleFont, $this->styleFont_10), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[7], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('Info'), array_merge($this->styleFont), $this->styleKeepNext);

        $this->styleFont = array();
    }

    function showWeekPage() {
        $this->addSection();

        //$this->section->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(3.15), 'align' => 'right'));

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(26))
                    )
            );
        //new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(3.5)),


        $textrun = $this->section->addTextRun($styleTabs);
        $textrun->addText(htmlspecialchars('SPIELZEIT '.$this->aweek['season'].'    '), array_merge($this->styleFont_18, $this->styleFont_bold, ));
        //$textrun->addText(htmlspecialchars('Monatsvorschau '.$this->amonth['cmonth']), array_merge($this->styleFont_14, $this->styleFont_bold));
        $textrun->addText("\t");
        $textrun->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(3.15), 'align' => 'right'));
        $this->section->addTextBreak();

        $this->showSchedule();
    }

    function showWeekSchedule() {


        $count_days = 0;

        foreach ($this->aweek['adays'] as $k=>$aday) {

            $count_days++;

            $styleCell = array();

            $this->styleFont = array();

            //$this->otable->addRow(Converter::cmToTwip(0.3), array("exactHeight" => true));
            $this->otable->addRow(Converter::cmToTwip(0.3), $this->styleCantSplit);

            //keine Termine
            if(sizeof($aday['adates']) == 0) {

                $styleCell = $this->styleCell_borderTop;

                if($count_days == sizeof($aday['adates'])) {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderBottom);
                }


                $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                $cell->addText(htmlspecialchars($aday['weekday']), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell));
                $cell->addText(htmlspecialchars($aday['cdate']), array_merge($this->styleFont), $this->styleKeepNext);
                if(!empty($aday['holyday'])) {
                    $cell->addText(htmlspecialchars($aday['holyday']), array_merge($this->styleFont), $this->styleKeepNext);
                }

                for($i=2; $i<=sizeof($this->acol_widths)-1; $i++) {
                    $cell = $this->otable->addCell($this->acol_widths[$i], array_merge($styleCell));
                }
            }

            $this->styleFont = array();

            $count_dates = 0;
            foreach ($aday['adates'] as $adate) {
                $count_dates++;

                $styleCell = [];
                if($count_dates == sizeof($aday['adates'])) {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderBottom);
                }


                if($count_dates==1) {
                    $this->styleFont = array();
                    if($aday['l_performance'] == 1) {
                        $this->styleFont = $this->styleFont_bold;
                    }


                    $styleCell = array_merge($styleCell, $this->styleCell_borderTop);
                    $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell->addText(htmlspecialchars($aday['weekday']), array_merge($this->styleFont), $this->styleKeepNext);
                    $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell->addText(htmlspecialchars($aday['cdate']), array_merge($this->styleFont), $this->styleKeepNext);

                    if(!empty($aday['holyday'])) {
                        $cell->addText(htmlspecialchars($aday['holyday']), array_merge($this->styleFont), $this->styleKeepNext);
                    }

                    $this->styleFont = array();

                } else {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderTop_none);
                    $this->otable->addRow(Converter::cmToTwip(0.3), $this->styleCantSplit);
                    $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell->addText('', [], $this->styleKeepNext);
                    $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell));
                    $cell->addText('', [], $this->styleKeepNext);
                }



                $start = $this->reporttools->getTime($adate, false);
                $location = ($adate->locationaddress ?
                    ($adate->locationaddress->code>'' ?
                        $adate->locationaddress->code :
                        $adate->locationaddress->name1 . (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place
                    ) : '');
                $eventtype_code = ($adate->seventtype ? ($adate->seventtype->code > '' ? $adate->seventtype->code : $adate->seventtype->name) : '');
                $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
                $dress = ($adate->sdress ? $adate->sdress->name : '');
                $project = ($adate->sproject ? $adate->sproject->name : '');
                $conductor = ($adate->conductoraddress ? trim($adate->conductoraddress->name2.' '.$adate->conductoraddress->name1) : '');

                $text = $adate->text;



                $this->styleFont = array();

                if ($l_performance == 1) {
                    $this->styleFont = $this->styleFont_bold;
                }

                $cell = $this->otable->addCell($this->acol_widths[2], array_merge($styleCell));
                if (!empty($adate->start_)) {
                    $cell->addText(htmlspecialchars($start), array_merge($this->styleFont), $this->styleKeepNext);
                } else {
                    $cell->addText('', array_merge($this->styleFont), $this->styleKeepNext);
                }

                $cell = $this->otable->addCell($this->acol_widths[3], array_merge($styleCell));
                $cell->addText(htmlspecialchars($eventtype_code), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[4], array_merge($styleCell));
                $cell->addText(htmlspecialchars($project), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[5], array_merge($styleCell));
                $cell->addText(htmlspecialchars($location), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[6], array_merge($styleCell));
                $cell->addText(htmlspecialchars($adate->duties), array_merge($this->styleFont), array_merge($this->styleAlign_center, $this->styleKeepNext));

                $cell = $this->otable->addCell($this->acol_widths[7], array_merge($styleCell));
                $col8 = $adate->text;
                $col8 .= (!empty($col8) && !empty($conductor) ? ', ' : '').$conductor;
                $col8 .= (!empty($col8) && !empty($dress) ? ', ' : '').$dress;
                $cell->addText(htmlspecialchars($col8), array_merge($this->styleFont), $this->styleKeepNext);
            }
        }
        $this->styleFont = array();

    }
}
