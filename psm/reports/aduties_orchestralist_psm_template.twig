<script type="text/html" id="aduties_orchestralist_psm_template">

  <div class="wizard-message">

    <h3 class="wizard-headline">{{ i18n.templateHeadline }}</h3>

    <p>{{ i18n.templateDescription }}</p>

    <form class="smart-form" id="wizard-template-form">

      <fieldset>

        <div class="row">

          <section class="col col-6">
          <div class="form-group">
            <div class="input-group">
              <label class="">
                <input type="checkbox" name="template_cb1" id="template_cb1" value="1" class="cb" checked> {{ i18n.with_absent_musicians }}
              </label>
            </div>
          </div>

          <div class="form-group">
            <div class="input-group">
              <label class="">
                <input type="checkbox" name="template_cb2" id="template_cb2" value="1" class="cb" checked> {{ i18n.with_function }}
              </label>
            </div>
          </div>

          </section>
        </div>

        <div class="form-group">
              <label for for="order_by" class="select" style="float:left;">
                {{ i18n.order_by }}


                <select name="order_by" id="order_by">
                  <option value="name" selected>{{ data.cname }}</option>
                  <option value="seat_d">{{ data.cseat_duties }}</option>
                  <option value="order_1_d">{{ data.corder_1_duties }}</option>
                  <option value="order_2_d">{{ data.corder_2_duties }}</option>
                  <option value="seat_a">{{ data.cseat_address }}</option>
                  <option value="order_1_a">{{ data.corder_1_address }}</option>
                  <option value="order_2_a">{{ data.corder_2_address }}</option>

                </select>
              </label>
        </div>
      </fieldset>
    </form>
  </div>
</script>
