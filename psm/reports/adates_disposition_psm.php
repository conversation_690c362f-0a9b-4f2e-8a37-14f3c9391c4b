<?php
/*
******** ONCUST-2789
PSM Disposition
*/

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;

class adates_disposition_psm extends ReportWord
{

    private $postData = null;


    private $adates_selected = null;

    private $aprojects = array();
    private $aproject = array();
    private $k_project = '';

    private $aprogramcode = null;

    private $reporttools = null;

    private $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_8 = array('size' => 8);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_20 = array('size' => 20);

    private $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => '#black');
    private $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan5 = array('gridSpan' => 5);
    private $cellColSpan8 = array('gridSpan' => 8);

    private $works = null;

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];
    }

    public function collect(array $where = [])
    {

        $this->reporttools = new ReportTools_client();

        $this->postData = $this->getRequest()->getData();
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        // nur AusgewÃ»ÃŠhlte Termine
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere=array());

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->prepare_aprojects();

        $this->phpWord->setDefaultFontSize(14);
        $this->phpWord->setDefaultFontName('Calibri');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        foreach($this->aprojects as $this->aproject) {
            $this->showProject();
        }
    }

    function prepare_aprojects() {
        //$this->addSection();

        $this->aprojects = array();

        foreach ($this->adates_selected as $adate) {
            $season_id = $adate->season_id;
            $project_id = $adate->project_id;
            $planninglevel = (int)$adate->planninglevel;

            if($project_id == 0) {continue;}

            $this->k_project = $season_id . '#' . $project_id . '#' . $planninglevel;

            if (!array_key_exists($this->k_project, $this->aprojects)) {

                $this->aprojects[$this->k_project] = array(
                    'season_id' => $season_id,
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'project_id' => $project_id,
                    'planninglevel' => $planninglevel,
                    'project' => ($adate->sproject ? $adate->sproject->name : ''),
                    'project_name2' => ($adate->sproject ? $adate->sproject->name2 : ''),
                    'project_code' => ($adate->sproject ? $adate->sproject->code : ''),
                    'adates' => array(),
                    'apds' => array(),
                    'aconductors' => array(),
                    'asoloists' => array(),
                    'apersons' => array(),
                    'aworks' => array(),
                    'aprogramcodes' => array()
                );

                // 20230508 ONCUST-1486
                // Informationen aus der ersten AuffÃ¼hrung
                //Wenn die AuffÃ¼hrung der ersten oder letzten Produktion nicht in der Datenauswahl liegt, dann soll auch der Dirigent aus der ersten AuffÃ¼hrung geholt werden. Beispiel Produktion 2122BBH3 Notenkraker, Dirigent steht beim 11. Dezember 2021



                $awhere = [
                    'Adates.project_id' => $adate->project_id,
                    'Adates.season_id' => $adate->season_id,
                    'Adates.planninglevel' => $adate->planninglevel
                ];

                // alle Termine des Projektes
                $arows = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

                foreach ($arows as $arow) {
                    if ($arow->seventtype && $arow->seventtype->l_performance == 1) {
                        $this->aprojects[$this->k_project]['apds'][$arow->id] = $arow;
                    }
                    $this->aprojects[$this->k_project]['adates'][$arow->id] = $arow;


                    $this->prepare_conductors($arow);
                    $this->prepare_soloists($arow->id);
                    $this->prepare_persons($arow);
                    $this->prepare_program($arow);

                }
            }
        }

    }

    function prepare_conductors($adate) {
        $conductor = '';

        if($adate->conductoraddress) {
            $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, $adate->conductoraddress->name5, $adate->conductoraddress->name1);
        }

        if(!empty($conductor)) {
            if (!in_array($conductor, $this->aprojects[$this->k_project]['aconductors'])) {
                $this->aprojects[$this->k_project]['aconductors'][] = $conductor;
            }
        }
    }

    function prepare_soloists($date_id)
    {
        $arows = $this->reporttools->getSoloists($date_id);

        foreach ($arows as $arow) {
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            if(mb_strtoupper($instrument) == 'KOOR') {
                $instrument = '';
            }

            if($arow->artist_id > 0 && !key_exists($arow->artist_id, $this->aprojects[$this->k_project]['asoloists'])) {
                $this->aprojects[$this->k_project]['asoloists'][$arow->artist_id] = array(
                    'name' => $this->reporttools->getLongName($arow->saddress->name2, $arow->saddress->name5, $arow->saddress->name1),
                    'instrument' => $instrument
                );
            }
        }
    }

    function prepare_persons($adate)
    {

        foreach($adate->adate_persons as $arow) {

            $addressfunction = ($arow->saddressfunctionitem ? $arow->saddressfunctionitem->name : '');
            $instrument = ($arow->sinstrinstrument ? $arow->sinstrinstrument->name : '');
            $addressgroup = ($arow->saddressgroup ? $arow->saddressgroup->name : '');
            $ifg = $instrument.(($instrument>'' && $addressfunction>'') ? ', ' : '').$addressfunction;
            if(empty($ifg)) {
                $ifg = $addressgroup;
            }

            if($arow->address_id > 0 && !key_exists($arow->address_id, $this->aprojects[$this->k_project]['apersons'])) {
                $this->aprojects[$this->k_project]['apersons'][$arow->address_id] = array(
                    'name' => $this->reporttools->getLongName($arow->saddress->name2, $arow->saddress->name5, $arow->saddress->name1),
                    'ifg' => $ifg
                );
            }
        }
    }

    function prepare_program($adate) {
        //$this->section->addText('date_id='.$adate->id);

        $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

        $programcode = $this->getProgramCode($adate);

        if ($l_performance == 1 && $programcode>'') {

            if(!key_exists($programcode, $this->aprojects[$this->k_project]['aprogramcodes'])) {
                $this->aprojects[$this->k_project]['aprogramcodes'][$programcode] = array(
                    'apds' => array(),
                    'aworks' => array(),
                    'instrumentation_max' => ''
                );

                foreach ($adate->adate_works as $adate_work) {
                    $datework_id = $adate_work->id;
                    //$this->section->addText('datework_id='.$datework_id);
                    $work_id = $adate_work->work_id;
                    // 20230731 ONCUST-1886
                    // bitte immer sworks.title1 anzeigen lassen
                    //$title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);
                    $title = $adate_work->swork->title1;
                    //20220409
                    ////Can you put in full names (e.g. Samuel Barber: Work)
                    $composer = ($adate_work->swork->scomposer ? $adate_work->swork->scomposer->lastname : '');
                    $composer_21 = ($adate_work->swork->scomposer ? trim($adate_work->swork->scomposer->firstname . ' ' . $adate_work->swork->scomposer->lastname) : '');
                    $nduration = (int)substr($adate_work->duration, 0, 2) * 60 + (int)substr($adate_work->duration, 3, 2);
                    $cduration = '';
                    if($nduration>0) {
                        $cduration = ' ('.$nduration."')";
                    }

                    $l_encore = $adate_work->l_encore;

                    $oinstrumentation = new instrumentation_psm();
                    $oinstrumentation->datework_id = $datework_id;
                    $oinstrumentation->date_id = 0;
                    $oinstrumentation->getInstrumentation();

                    if ($adate_work->swork->l_intermission == 1) {
                        $cduration = '';
                    }

                    $row = array(
                        'l_encore' => $l_encore,
                        'l_intermission' => $adate_work->swork->l_intermission,
                        'title' => $title,
                        'duration' => $cduration,
                        'cduration' => $cduration,
                        'composer' => $composer,
                        'composer_21' => $composer_21,
                        'holz' => $oinstrumentation->holz,
                        'blech' => $oinstrumentation->blech,
                        'others' => $oinstrumentation->others,
                        'strings' => $oinstrumentation->strings,
                        'details' => $oinstrumentation->details,
                        'date_work' => $adate_work
                    );

                    //if ($adate_work->swork->l_intermission == 0) {
                        if (!array_key_exists($work_id, $this->aprojects[$this->k_project]['aprogramcodes'][$programcode]['aworks'])) {

                            //$this->aprojects[$this->k_project_project]['nduration_total'] += $nduration; //#'.$nduration.'#'.$adate_work->duration;
                            $this->aprojects[$this->k_project]['aprogramcodes'][$programcode]['aworks'][$work_id] = $row;
                        }
                    //}
                }
                $oinstrumentation = new instrumentation_psm();
                $oinstrumentation->datework_id = 0;
                $oinstrumentation->date_id = $adate->id;
                $oinstrumentation->getInstrumentation();

                $this->aprojects[$this->k_project]['aprogramcodes'][$programcode]['instrumentation_max'] = $oinstrumentation->instrumentation_max;
            }

            $this->aprojects[$this->k_project]['aprogramcodes'][$programcode]['apds'][$adate->id] = $adate;
        }
    }

    function addSection() {
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(2),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(0.5),
                'marginBottom' => Converter::cmToTwip(1))
        );

        $sectionStyle = $this->section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(8.5)),
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(16))
                    )
            );

        $header = $this->section->addHeader();
        /*$textrun = $header->addTextRun($styleTabs);
        $textrun->addText("\t");
        $textrun->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(6.91), 'align' => 'center'));
        $textrun->addText("\t");
        $textrun->addField("PAGE", array());
        */

    }

    function showProject() {
        $this->addSection();

        $this->section->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(3.15), 'align' => 'right'));

        $project = $this->aproject['project'];

        $this->section->addText(htmlspecialchars($project), array_merge($this->styleFont_20, $this->styleFont_bold));
        $this->section->addTextBreak();

        $this->showProgram();

        $this->section->addTextBreak();

        foreach ($this->aproject['aconductors'] as $conductor) {
            $textrun = $this->section->addTextrun();
            $textrun->addText(htmlspecialchars($conductor), array_merge($this->styleFont));
            $textrun->addText(htmlspecialchars(', Leitung'), array_merge($this->styleFont));
        }

        foreach ($this->aproject['asoloists'] as $asoloist) {
            $textrun = $this->section->addTextrun();
            $textrun->addText(htmlspecialchars($asoloist['name']), array_merge($this->styleFont));
            if($asoloist['instrument']>'') {
                $textrun->addText(htmlspecialchars(', '.$asoloist['instrument']), array_merge($this->styleFont));
            }
        }

        foreach ($this->aproject['apersons'] as $aperson) {

            $textrun = $this->section->addTextrun();
            $textrun->addText(htmlspecialchars($aperson['name']), array_merge($this->styleFont, $this->styleFont_bold));
            if($aperson['ifg']>'') {
                $textrun->addText(htmlspecialchars(', '.$aperson['ifg']), array_merge($this->styleFont));
            }
        }

        $this->section->addTextBreak();
        $this->section->addTextBreak();
        $this->showSchedule();


   }

    function showPDs() {
        if(sizeof($this->aproject['apds'])==0) {return;}

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(3.5);
        $this->acol_widths[] = Converter::cmToTwip(2.5);
        $this->acol_widths[] = Converter::cmToTwip(12);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.1),
            'cellMarginBottom' => Converter::cmToTwip(0.1),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP
        );

        $styleCell = array();
        $styleFont = array();

        $table = $this->section->addTable($tableProperties);



        foreach ($this->aproject['apds'] as $adate) {
            $location = ($adate->locationaddress ? $adate->locationaddress->name1. (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place : '');

            $table->addRow();

            $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($adate->date_->format('d.m.Y')), array_merge($this->styleFont));

            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));

            if(!empty($adate->start_)) {
                $cell->addText(htmlspecialchars($adate->start_->format('H:i'). ' Uhr'), array_merge($this->styleFont));
            }


            $cell = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($location), array_merge($this->styleFont));
        }
    }

    function showSchedule() {


        $this->section->addText(htmlspecialchars('*****************************************************************'), array_merge($this->styleFont_bold));
        $this->section->addText(htmlspecialchars('PROBENDISPOSITION'), array_merge($this->styleFont_bold));

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(1.3);
        $this->acol_widths[] = Converter::cmToTwip(1.7);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(2);
        $this->acol_widths[] = Converter::cmToTwip(9);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'black',
            'cellMarginTop' => Converter::cmToTwip(0.1),
            'cellMarginBottom' => Converter::cmToTwip(0.1),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'layout'      => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED,
            'unit' => TblWidth::TWIP
        );

        $styleCell = array();
        // 20231128 ONCUST-2610
        //Bei dem Bericht aus OPAS Next „MSYM Probenplan“ ist in der Werk Spalte eine andere Schriftgrößen als in den restlichen Spalten.
        // Ist es möglich die zur vereinheitlichen? (Gerne die größere Schriftgröße)
        //$this->styleFont = $this->styleFont_10;
        $this->styleFont = array();

        $table = $this->section->addTable($tableProperties);

        $count_days = 0;
        $d_old = '#';
        foreach ($this->aproject['adates'] as $adate) {
            $count_days++;
            $styleCell = [];
            if($count_days%2>0) {
                //$styleCell = ['bgColor' => 'F2F2F2'];
            } else {
                $styleCell = [];
            }
            $location = ($adate->locationaddress ? $adate->locationaddress->name1. (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place : '');
            $eventtype = ($adate->seventtype ? ($adate->seventtype->code>'' ? $adate->seventtype->code : $adate->seventtype->name) : '');
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
            $dress = ($adate->sdress ? $adate->sdress->name : '');
            $text = $adate->text;

            $table->addRow();

            $this->styleFont = array();

            $cell0 = $table->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell1 = $table->addCell($this->acol_widths[1], array_merge($styleCell));
            $d = $adate->date_->format('d.m.');
            if($d_old<>$d) {
                $cell0->addText(htmlspecialchars($adate->weekday.'.'), array_merge($this->styleFont));
                $cell1->addText(htmlspecialchars($d), array_merge($this->styleFont));
                $count_dates = 0;
            }
            $d_old = $d;
            $count_dates++;

            if($l_performance==1) {
                $this->styleFont = $this->styleFont_bold;
            }


            $cell = $table->addCell($this->acol_widths[2], array_merge($styleCell));
            if(!empty($adate->start_)) {
                $cell->addText(htmlspecialchars($adate->start_->format('H:i')), array_merge($this->styleFont));
            }

            $cell = $table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell->addText(htmlspecialchars($eventtype), array_merge($this->styleFont));

            $cell = $table->addCell($this->acol_widths[4], array_merge($styleCell));
            $cell->addText(htmlspecialchars($adate->text), array_merge($this->styleFont));


        }
        $this->styleFont = array();

    }

    function showProgram() {

        $count = 0;
        foreach($this->aproject['aprogramcodes'] as $this->aprogramcode) {

            //$this->showPDs();

            //$this->section->addText('aworks='.sizeof($this->aprogramcode['aworks']));
            //$this->section->addText(sizeof($this->aprogramcode['aworks']));

            if(sizeof($this->aprogramcode['aworks'])>0) {
                //$this->section->addTextBreak();
            } else {
                continue;
            }
            $count++;
            if($count>1) {
                $this->section->addTextBreak();
            }

            foreach($this->aprogramcode['apds'] as $adate) {
                $location = ($adate->locationaddress ? $adate->locationaddress->name1 . (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place : '');


                $start = '';
                if (!empty($adate->start_)) {
                    $start = $adate->start_->format('H') . ($adate->start_->format('i') > 0 ? ':' . $adate->start_->format('i') : '') . ' Uhr';
                }

                $dress = ($adate->sdress ? $adate->sdress->name : '');
                if (!empty($dress)) {
                    $dress = 'Kleiderordnung: ' . $dress;
                }

                $styleTabs =
                    array(
                        'tabs' =>
                            array(
                                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(5)),
                                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(11.75))
                            )
                    );


                $this->section->addText(htmlspecialchars(
                    $this->reporttools->getWeekday($adate->date_) . ', ' .
                    $adate->date_->format('d.m.') . "\t" .
                    $start .
                    (!empty($start) && !empty($location) ? ' | ' : '') .
                    $location . "\t" .
                    $dress
                ), array_merge($this->styleFont, $this->styleFont_12), $styleTabs);
            }

            $this->section->addTextBreak(1, $this->styleFont_12);

            $styleFont = array();

            foreach ($this->aprogramcode['aworks'] as $awork) {
                //*** Bitte Dauer für jedes Stück hinzufügen
                $adate_work = $awork['adate_work'];
                $this->section->addTextBreak();
                $this->section->addText(htmlspecialchars(
                    ($awork['l_intermission'] == 0 ? $awork['composer_21'] . ' | ' : '') . $awork['title'].' '.$adate_work->duration
                ), $this->styleFont_bold);

                if($awork['l_intermission']==0) {
                    $this->section->addText(htmlspecialchars($awork['holz']));
                    $this->section->addText(htmlspecialchars($awork['blech']));
                    $this->section->addText(htmlspecialchars($awork['others']));


                    if(!empty($awork['strings'])) {
                        $this->section->addText(htmlspecialchars('Streicher:' . $awork['strings']));
                    }

                    if(!empty($awork['details'])) {
                        $this->reporttools->addMemo($this->section, $awork['details']);
                    }
                }
            }
        }
    }

    function getProgramCode($adate) {

        //$conductor_id = $this->adate->conductor_id;
        //$programCode = $conductor_id;
        $programCode = '';

        foreach ($adate->adate_works as $works) {
            $programCode .= '#'. $works->swork->id;
        }
        return $programCode;
    }
}
