<?php
set_time_limit(0);
/*
20240506 OOCUST-2079
PSM Orchestralist
gewünscht wird die Orchesterliste folgendermaßen: Kopf wie im Standardbericht aber ohne Programm; die Musiker in zwei Spalten untereinander, Aushilfen kursiv.
Als Beispiel kann genutzt werden das "5. Sinfoniekonzert" am 9.3.24
*/
use Cake\Core\Configure;

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;

class aduties_orchestralist_psm_common extends ReportWord
{

    public $paperSize = 'Letter';

    public $awhere = null;
    private $postData = null;


    private $aduties_selected = null;

    private $aprojects = array();
    private $aproject = array();

    private $reporttools = null;

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_8 = array('size' => 8);

    private $l_absent = 0;
    private $l_function = 0;
    private $order_by = 0;

    private $section = null;
    private $cell1 = null;
    private $cell2 = null;

    protected $templates = [
        [
            'name' => 'aduties_orchestralist_psm_template',
            'file' => 'aduties_orchestralist_psm_template.php',
            'jsFile' => 'aduties_orchestralist_psm_template.js',
            'fileLocationType' => 'direct'
        ]
    ];

    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];
    }

    public function collect(array $where = [])
    {
        $this->paperSize = Configure::read('opasReports.paperFormat', 'A4');

        $this->reporttools = new ReportTools();

        $this->postData = $this->getRequest()->getData();
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        $this->l_absent = (isset($this->postData['formData']['template_cb1']) ? $this->postData['formData']['template_cb1'] : 0);
        $this->l_function = (isset($this->postData['formData']['template_cb2']) ? $this->postData['formData']['template_cb2'] : 0);
        $this->order_by = (isset($this->postData['formData']['order_by']) ? $this->postData['formData']['order_by'] : 1);

        $aorder_by = ['Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order'];

        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        $this->aduties_selected = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses',
                    'AdateWorks'=>
                        function (Query $query) {
                            return $query
                                ->contain(['Sworks'=>['Scomposers']])
                                ->orderAsc('AdateWorks.work_order');
                        }
                ],
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses',
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']
            ])
            ->where($where)
            ->order($aorder_by);

        $aduties = TableRegistry::getTableLocator()->get('Aduties');

        $this->aduties_selected = $aduties
            ->find('all')
            ->select()
            ->contain([
                'Adates' => ['Sprojects', 'Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses',
                    'AdateWorks'=>
                        function (Query $query) {
                            return $query
                                ->contain(['Sworks'=>['Scomposers']])
                                ->orderAsc('AdateWorks.work_order');
                        }
                ],
                'Sdutytypes',
                'Saddressfunctionitems',
                'Artistaddresses',
                'Sinstrinstruments' => ['Sinstrsections'=>['Sinstrsyssections']],
                'Saddressgroups' => ['Saddresssysgroups']
            ])
            ->where($where)
            ->order(['Sdutytypes.l_present'=>'DESC', 'Seventtypes.l_performance'=>'DESC', 'Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order', 'Aduties.order_1'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']);

        $this->prepare_aprojects();
        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(8);
        $this->phpWord->setDefaultFontName('Tahoma');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        /*
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(2),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(2),
                'marginBottom' => Converter::cmToTwip(1))
        );

        $this->prepare_aprojects();
*/
        $count = 0;
        foreach($this->aprojects as $this->aproject) {
            $count++;

            $this->section = $this->phpWord->addSection(
                array(
                    'paperSize' => $this->paperSize,
                    'headerHeight' => Converter::cmToTwip(1.25),
                    'footerHeight' => Converter::cmToTwip(1.25),
                    'marginLeft' => Converter::cmToTwip(2),
                    'marginRight' => Converter::cmToTwip(1),
                    'marginTop' => Converter::cmToTwip(2),
                    'marginBottom' => Converter::cmToTwip(1))
            );

            $sectionStyle = $this->section->getStyle();
            $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

            $this->addHeader($this->section);

//            if($count>1) {
//                $this->section->addText('<w:br w:type="page"/>');
//            }

            $this->showProject($this->section);
        }
    }

    function addHeader($section) {
        return;
        $header = $section->addHeader();

        //new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(13.5)),
        $styleTabs =
            array(
                'tabs' => array(
                    new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(8)),
                    new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(18))
                )
            );

        $textrun = $header->addTextRun($styleTabs);



        $textrun->addText('OPAS', array());
        $textrun->addText("<w:r><w:tab/></w:r>");
        $textrun->addText(htmlspecialchars($this->report->title), array('bold'=>true));
        $textrun->addText("<w:r><w:tab/></w:r>");
        $textrun->addText(date("d.m.Y"), array());

        //$textrun->addField("PAGE", array());
        //$header->addText($this->template_start_end, array('bold'=>true), array('align' => 'center'));
    }

    function prepare_aprojects($section=null) {

        $this->aprojects = array();

        foreach ($this->aduties_selected as $aduty) {
            $season_id = $aduty->adate->season_id;
            $project_id = $aduty->adate->project_id;
            $planninglevel = $aduty->adate->planninglevel;
            $programno = ($aduty->adate->programno ? $aduty->adate->programno : '');


            $k_project = $season_id . '#' . $project_id . '#' . $planninglevel;
            $l_present = ($aduty->sdutytype ? $aduty->sdutytype->l_present : 0);
            $k_artist = $aduty->artist_id . '#' .$l_present;

            $syssection_id = -1;
            $section_id = -1;
            $section = 'unknown';
            $section_order = 0;

            if ($aduty->sinstrinstrument->sinstrsection) {
                $syssection_id = $aduty->sinstrinstrument->sinstrsection->syssection_id;
                $section_id = $aduty->sinstrinstrument->sinstrsection->id;
                $section = $aduty->sinstrinstrument->sinstrsection->name;

                $section_order = $aduty->sinstrinstrument->sinstrsection->section_order;
            }

            $sysgroup_id = ($aduty->saddressgroup ?$aduty->saddressgroup->sysgroup_id : -1);

            if (!array_key_exists($k_project, $this->aprojects)) {

                $this->aprojects[$k_project] = array(
                    'season_id' => $season_id,
                    'season' => ($aduty->adate->sseason ? $aduty->adate->sseason->name : ''),
                    'project_id' => $project_id,
                    'planninglevel' => $planninglevel,
                    'project' => ($aduty->adate->sproject ? $aduty->adate->sproject->name : ''),
                    'programno' => $programno,
                    'adates' => array(),
                    'apds' => array(),
                    'aconductors' => array(),
                    'asoloists' => array(),
                    'alocations' => array(),
                    'asections' => array()
                );
            }

            if (!array_key_exists($aduty->date_id, $this->aprojects[$k_project]['adates'])) {
                $this->aprojects[$k_project]['adates'][$aduty->date_id] = $aduty->adate;
            }

            if (isset($aduty->date->seventtype->l_performance) && $aduty->date->seventtype->l_performance == 1) {
                if (!array_key_exists($aduty->date_id, $this->aprojects[$k_project]['apds'])) {
                    $this->aprojects[$k_project]['apds'][$aduty->date_id] = $aduty->adate;
                }
            }

            if ($aduty->adate->location_id > 0) {
                if (!array_key_exists($aduty->adate->location_id, $this->aprojects[$k_project]['alocations'])) {
                    $location = trim($aduty->adate->locationaddress->name1 . ($aduty->adate->locationaddress->place > '' ? ', ' : '') . $aduty->adate->locationaddress->place);

                    $this->aprojects[$k_project]['alocations'][$aduty->adate->location_id] = array(
                        'name' => $location
                    );
                }
            }

            //Soloists
            $adatework_soloists = $this->getSoloists($aduty->date_id);
            foreach ($adatework_soloists as $adatework_soloist) {
                $k_soloist = $adatework_soloist->artist_id . '_' . $adatework_soloist->instrument_id;

                $instrument = ($adatework_soloist->sinstrinstrument ? $adatework_soloist->sinstrinstrument->name : '');
                $name = $this->getLongName($adatework_soloist->saddress->name2, '', $adatework_soloist->saddress->name1);

                $name_order = $this->getLongName($adatework_soloist->saddress->name2, $adatework_soloist->saddress->name5, $adatework_soloist->saddress->name1);
                if (!array_key_exists($k_soloist, $this->aprojects[$k_project]['asoloists'])) {
                    $this->aprojects[$k_project]['asoloists'][$k_soloist] = array(
                        'name' => $name,
                        'instrument' => $instrument
                    );
                }
            }

            if ($aduty->adate->conductor_id > 0) {
                if (!array_key_exists($aduty->adate->conductor_id, $this->aprojects[$k_project]['aconductors'])) {
                    $conductor = $this->getLongName($aduty->adate->conductoraddress->name2, $aduty->adate->conductoraddress->name5, $aduty->adate->conductoraddress->name1);

                    $this->aprojects[$k_project]['aconductors'][$aduty->adate->conductor_id] = array(
                        'name' => $conductor
                    );
                }
            }

            if (!array_key_exists($section_id, $this->aprojects[$k_project]['asections'])) {
                $this->aprojects[$k_project]['asections'][$section_id] = array(
                    'syssection_id' => $syssection_id,
                    'section' => $section,
                    'section_order' => $section_order,
                    'aartists' => array()
                );
            }

            $l_present = ($aduty->sdutytype ? $aduty->sdutytype->l_present : 0);

            if ($this->l_absent == 1 || $l_present==1) {
                if (!array_key_exists($k_artist, $this->aprojects[$k_project]['asections'][$section_id]['aartists'])) {

                    /*
                    Es soll folgendermaßen Gruppiert werden
                    Nach Instrument Section (passiert gerade schon)
                    Innerhalb der Sections soll nach Musikern mit der aduties.addressgroup_id mit Systemgruppe Staff und mit der aduties.addressgroup_id mit Systemgruppe Sub gruppiert werden
                    erst Staff, dann Aushilfen,
                    Aushilfen sollen kursiv sein
                    innerhalb von Staff und Sub soll nach anwesend und Abwesend gruppiert werden
                    erst anwesend, dann abwesend
                    Abwesende sollen in einem Grauton hinterlegt werden
                    Aushilfen, die Abwesend sind, sind sowohl grau als auch kursiv
                     * */

                    //['Sdutytypes.l_present'=>'DESC', 'Seventtypes.l_performance'=>'DESC', 'Adates.date_', 'Adates.start_', 'Sinstrsyssections.section_order', 'Aduties.order_1'=>'ASC', 'Artistaddresses.name1' => 'ASC', 'Artistaddresses.name2' => 'ASC']

                    $name = ($aduty->artistaddress ? $aduty->artistaddress->name1.', '.$aduty->artistaddress->name2 : '');
                    $order_ = 'o'.
                        ($sysgroup_id == 12 ? 2 : 1).
                        ($l_present<>0 ? 1 : 2);
                    $order_ .= '#'.$this->order_by;
                    switch($this->order_by) {
                        case 'name':
                            $order_ .= '#'.$name;
                            break;
                        case 'seat_d':
                            $order_ .= '#'.(!empty($aduty->seat) ? $aduty->seat : 'ZZZ');
                            break;
                        case 'order_1_d':
                            $order_ .= '#'.(!empty($aduty->order_1) ? $aduty->order_1 : 'ZZZ');
                            break;
                        case 'order_2_d':
                            $order_ .= '#'.(!empty($aduty->order_2) ? $aduty->order_2 : 'ZZZ');
                            break;
                        case 'seat_a':
                            $order_ .= '#'.(!empty($aduty->artistaddress->seat) ? $aduty->artistaddress->seat : 'ZZZ');
                            break;
                        case 'order_1_a':
                            $order_ .= '#'.(!empty($aduty->artistaddress->order_1) ? $aduty->artistaddress->order_1 : 'ZZZ');
                            break;
                        case 'order_2_a':
                            $order_ .= '#'.(!empty($aduty->artistaddress->order_2) ? $aduty->artistaddress->order_2 : 'ZZZ');
                            break;
                    }

                    $aduty->order_ = $order_;

                    $this->aprojects[$k_project]['asections'][$section_id]['aartists'][$k_artist] = $aduty;
                }
            }
        }
    }

    function usort_artists($a, $b) {
        $frm_order_a = strtoupper($a->order_);
        $frm_order_b = strtoupper($b->order_);

        $l_greater = strcmp($frm_order_a, $frm_order_b);

        //$this->section->addText($frm_order_a.'#'.$frm_order_b.'#'.$l_greater);
        return $l_greater;
    }

    function showProject($section)
    {

        $project = $this->aproject['project'] . ($this->aproject['programno'] > '' ? ' (' . $this->aproject['programno'] . ')' : '');

        $section->addText(htmlspecialchars($project), array('size' => 14, 'bold' => true));

        $this->showMinMaxDays($section);
        $this->showLocations($section);
        $this->showConductors($section);
        $this->showSoloists($section);

        //$section->addTextBreak();
        //$section->addTextBreak();


        $this->showSections($section);
    }

    public function getLongName($tname2 = '', $tname5 = '', $tname1 = '')
    {
        $name = trim(trim($tname2 . ' ' . $tname5) . ' ' . $tname1);
        return $name;
    }

    /**
     * Get the time
     *
     * If there is a end time both, the start and end time will be returned, in other case only the start time will be
     * returned or an empty string if no time is defined.
     *
     * @param $date
     * @return string
     */
    public function getTime($adate)
    {
        $startend = '';

        $start = '';
        $end = '';

        if ($adate->start_) {
            $start = $adate->start_->format('H:i');
        }
        if ($adate->end_) {
            $end = $adate->end_->format('H:i');
        }
        if ($start == '00:00') {
            $start = '';
        }
        if ($end == '00:00') {
            $end = '';
        }

        $startend = $start . (!empty($end) ? ' - ' : '') . $end;

        return $startend;
    }

    public function addMemo($section, $text) {

        $text = str_replace("\r\n", "\n", $text);
        $textlines = explode("\n", $text);
        $count = 0;
        foreach($textlines as $line) {
            //print_r($line.'#');
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line));
        }
    }

    function showLocations($section) {
        foreach ($this->aproject['alocations'] as $alocation) {
            $section->addText(htmlspecialchars($alocation['name']));
        }
    }

    function showConductors($section) {
        if(sizeof($this->aproject['aconductors'])==0) { return;}

        $caption = (sizeof($this->aproject['aconductors'])==1 ? __('conductor') : __('conductors')).': ';
        $section->addTextBreak();
        $section->addText(htmlspecialchars($caption), array('bold'=>true));
        foreach ($this->aproject['aconductors'] as $aconductor) {
            $section->addText(htmlspecialchars($aconductor['name']));
        }
    }

    function showSoloists($section) {
        if(sizeof($this->aproject['asoloists'])==0) { return;}

        $caption = (sizeof($this->aproject['asoloists'])==1 ? __('soloist') : __('soloists')).': ';
        $section->addTextBreak();
        $section->addText(htmlspecialchars($caption), array('bold'=>true));
        foreach ($this->aproject['asoloists'] as $asoloist) {
            $section->addText(htmlspecialchars($asoloist['name']. ($asoloist['instrument']>'' ? ', ' : '').$asoloist['instrument']));
        }
    }

    function showSections($section) {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(8);
        $this->acol_widths[] = Converter::cmToTwip(8);


        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'cellMarginBottom' => Converter::cmToTwip(0.5),
            'width' => $w_table,
            'unit' => TblWidth::TWIP
        );

        $styleCell = array();
        $styleFont = array();


        $table = $section->addTable($tableProperties);

        $table->addRow();
        $this->cell1 = $table->addCell($this->acol_widths[0], array_merge($styleCell));
        $this->cell2 = $table->addCell($this->acol_widths[0], array_merge($styleCell));


        //$section_2cols = $this->->addSection(array($section = $this->phpWord->addSection(
/*
        $section_2cols = $this->phpWord->addSection(
            array(
            'colsNum'   => 2,
            'colsSpace' => 100,
                'breakType' => 'nextColumn'

                //'breakType' => 'continuous'
        ));
*/
        //$phpWord = new \PhpOffice\PhpWord\PhpWord();

        $styleTabs =
            array(
                'tabs' => array(
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(4)),
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(9)),
                    new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(12))
                )
            );

        //$textrun = $section->addTextRun($styleTabs);
        //$textrun->addText(htmlspecialchars(__('adates.project_id')), array_merge($this->styleFont_bold));

        $count_sections = 0;
        foreach($this->aproject['asections'] as $asection) {

            //$section_section = $section_2cols;
            $section_section = ($asection['syssection_id'] <=5 ? $this->cell1 : $this->cell2);

            $count_sections++;
            //if($count_sections>1) {
                $section_section->addTextBreak();
            //}
            $section_section->addText(htmlspecialchars($asection['section']), array('bold'=>true));
            $section_section->addTextBreak();

            uasort($asection['aartists'], array($this,"usort_artists"));
            //uasort($this->aprojects[$k_project]['asections'][$section_id]['aartists'], array($this,"usort_artists"));

            /*uasort(
                $asection['aartists'],
                function ($a, $b) {

                    $this->section->addText($a->order_.'#'.$b->order_.'#'.$l_greater);

                    if($a->order_ > $b->order_) {
                        return 1;
                    }
                    return 0;
                }
            );
*/
            foreach($asection['aartists'] as $k_artist=>$aartist) {

                $l_present = ($aartist->sdutytype ? $aartist->sdutytype->l_present : 0);
                $style_abesent = array();
                if($l_present==0) {
                    $style_abesent = array('color'=>'808080');
                }

                $sysgroup_id = ($aartist->saddressgroup ? $aartist->saddressgroup->sysgroup_id : -1);
                $l_sub = ($sysgroup_id == 12 ? 2 : 1);
                $style_sub = array();
                if($l_sub==2) {
                    $style_sub = array('italic' => true);
                }

                $textrun = $section_section->addTextRun($styleTabs);
                $name = ($aartist->artistaddress ? $this->getLongName($aartist->artistaddress->name2, $aartist->artistaddress->name5, $aartist->artistaddress->name1) : '');

                //$name .='#'.$sysgroup_id.'#'.$asection['syssection_id'];
                //$textrun->addText(htmlspecialchars($aartist->order_.'#'), $style_abesent);



                //$textrun->addText(htmlspecialchars($k_artist), $style_abesent);
                //$textrun->addTextBreak();
                //$textrun->addText(htmlspecialchars($aartist->order_), $style_abesent);
                //$textrun->addTextBreak();

                $textrun->addText(htmlspecialchars($name), array_merge($style_abesent, $style_sub));

                $textrun->addText("\t");
                if($this->l_function==1) {
                    $function = ($aartist->saddressfunctionitem ? $aartist->saddressfunctionitem->name : '');
                    $textrun->addText(htmlspecialchars($function), array_merge($style_abesent, $style_sub));
                }
            }
        }
    }

    function showMinMaxDays($section)
    {
        $minmax = '';

        $count = 0;
        foreach ($this->aproject['adates'] as $date) {
            $count++;
            if($count==1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if($count>0) {
            $y_min = $date_min->format('Y');
            $m_min = $date_min->format('m');
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $date_max->format('m');
            $d_max = $date_max->format('d');

            $minmax =
                $d_min.'.'.
                ($m_min<>$m_max ? $m_min.'.' : '').
                ($y_min<>$y_max ? $y_min : '').
                '-'.
                $d_max.'.'.$m_max.'.'.$y_max;
        }

        $section->addText($minmax);
        //return $minmax;
    }

    public function getSoloists(int $date_id)
    {

        $where = 'AdateWorks.date_id=' . $date_id;

        $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');

        $arows = $AdateworkSoloistsTable
            ->find('all')
            ->select([
                'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                'AdateworkSoloists.notes',
                'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5',
                'Sinstrinstruments.name', 'Sinstrinstruments.name2'
            ])
            ->contain([
                'AdateWorks',
                'Saddresses',
                'Sinstrinstruments'
            ])
            ->where($where)
            ->orderAsc('artist_order2')
            ->distinct();

        return $arows;
    }
}
