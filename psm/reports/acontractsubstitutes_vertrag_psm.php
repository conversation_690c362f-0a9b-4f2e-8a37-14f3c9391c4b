<?php

//namespace App\Utility\Reports;

use \App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;

use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;
/*
PSM Aushilfenvertrag
20240208 ONCUST-2788
 */

class acontractsubstitutes_vertrag_psm extends ReportWord
{
    public $caption = '';

    public $acontracts = null;
    public $acontract = null;

    public $adates_contract = array();
    public $apds_contract = array();
    public $amvs_contract = array();

    public $arehearsals_contract = array();

    public $aprogram = array();
    public $contract_id = 0;
    public $acol_widths = array();

    public $styleFont = array('name'=>'Calibri', 'size'=>10);
    public $styleFont_Bold = array('bold'=>true);
    public $styleFont_Italic = array('italic'=>true);

    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    public $cellColSpan3 = array('gridSpan' => 3);
    public $cellColSpan4 = array('gridSpan' => 4);
    public $cellColSpan5 = array('gridSpan' => 5);
    public $cellColSpan6 = array('gridSpan' => 6);


    public $reporttools = null;
    public $oinstrumentation = null;

    public $mindate = null;
    public $ndays = 0;
    public $nduties_C = 0;
    public $nduties_P = 0;
    public $l_privat = false;
    public $honorar = 0;

    /*********************************************/
    function initialize()
    {
        parent::initialize();
        $this->template_filename = CUSTOMER_REP_DIR . 'acontractsubstitutes_vertrag_psm.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        /*$this->styleTabs_1_46 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.46)))
        );
        */
        $this->styleTabs_1_5 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.5)))
        );

        $this->styleTabs_1_65 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.65)))
        );

        $this->styleTabs_works = array(
            'tabs' => array(
                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(3)),
                new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(9.6))
            )
        );

        $this->oinstrumentation = new instrumentation_psm();
    }

    public function collect(array $where = [])
    {
        ini_set('pcre.backtrack_limit', 999999999);

        // Get the POST data
        $postData = $this->getRequest()->getData();

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($postData['id']);

        // Collect the selected dates with all required data

//        ->contain(['Adates' => ['Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses', 'AdateWorks'=>['Sworks'=>['Scomposers']]]])

        $this->acontracts = $this->model
            ->find('all')
            ->select()
            ->contain([
                'Sprojects',
                'Sseasons',
                'Sjobreasons',
                'Scontractstatus',
                'Scontractgroups',
                'Sinstrinstruments',
                'Aexpenses' => ['Adates'=>['Seventtypes'=>['Seventtypegroups']], 'Sexpensetypes' => ['Sexpensetypegroups'], 'Scurrencies'],
                'Artistaddresses' => [
                    'Ssalutations',
                    'SaddressPersdata' => [
                        'SaddresspersdataBanks' => function (Query $query) {
                            return $query
                                ->contain(['Sbanks'=>['Saddresses']])
                                ->where(['SaddresspersdataBanks.l_main' => 1]);
                        },
                        'SaddresspersdataPassports' => function (Query $query) {
                            return $query
                                ->contain(['Spassporttypes', 'Scountries'])
                                ->where(['SaddresspersdataPassports.l_main' => 1]);
                        }
                    ],
                ],
                'Artist2addresses' => [
                    'Scountries',
                    'Ssalutations',
                    'SaddressPersdata' => [
                        'SaddresspersdataBanks' => function (Query $query) {
                            return $query
                                ->contain(['Sbanks'=>['Saddresses']])
                                ->where(['SaddresspersdataBanks.l_main' => 1]);
                        },
                        'SaddresspersdataPassports' => function (Query $query) {
                            return $query
                                ->contain(['Spassporttypes', 'Scountries'])
                                ->where(['SaddresspersdataPassports.l_main' => 1]);
                        }
                    ],
                ],
                'AcontractDates' => function (Query $query) {
                    return $query
                        ->contain(['Adates' => ['Seventtypes', 'Sdresses', 'Locationaddresses', 'Conductoraddresses',
                            'AdateWorks'=>
                                function (Query $query) {
                                    return $query
                                        ->contain(['Sworks'=>['Scomposers']])
                                        ->orderAsc('AdateWorks.work_order');
                                }
                        ]])
                        ->orderAsc('Adates.date_', 'Adates.start_');
                },
                'AcontractClauses' => ['Scontractclauses' => ['Scontractclausetypes']],
                'AcontractAccountings' => ['Sexpensetypes']
            ])
            ->where(['Acontractsubstitutes.id IN' => $postData['dataItemIds']])
            ->order(['Acontractsubstitutes.id' => 'ASC']);

        //print_r($this->acontracts); die;
        return $this;
    }

    // Create the file and return the filename
    /*********************************************/
    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    /*********************************************/
    function fill_search_patterns()
    {
        $today = date("d.m.Y");

        //print_r($this->acontracts);


        $contracts_no = 0;
        foreach($this->acontracts as $this->acontract) {
            $contracts_no++;
        }
        //$contracts_no = sizeof($this->acontracts);

        $this->templateProcessor->cloneBlock('contracts', $contracts_no, true, true);

        $i=0;
        foreach($this->acontracts as $this->acontract) {
            $i++;

            $this->contract_id = $this->acontract->id;

            //$pagebreak = new PhpOffice\PhpWord\Element\TextRun();
            if($i<$contracts_no) {
                $pagebreak = '<w:br w:type="page"/>';

            } else {
                $pagebreak = '';
            }

            //$salutation = ($this->acontract->artistaddress->ssalutation ? $this->acontract->artistaddress->ssalutation->name2 : '');
            $salutation = ($this->acontract->artistaddress->saddress_persdata->sex == 'F' ? 'Frau' : ($this->acontract->artistaddress->saddress_persdata->sex == 'M'  ? 'Herrn' : ''));
            $artist_name = '';
            $artist_street = '';
            $artist_zipplace = '';
            if($this->acontract->artistaddress) {
                $artist_name = trim($this->acontract->artistaddress->name2 . ' ' . $this->acontract->artistaddress->name1);
                $artist_street = $this->acontract->artistaddress->street;
                $artist_zipplace = trim($this->acontract->artistaddress->zipcode.' '.$this->acontract->artistaddress->place);
            }

            $musiker_in = 'Musiker';
            $der_die_musiker_in = 'Der Musiker';
            $small_der_die_musiker_in = 'der Musiker';
            $gastkuenstler = 'selbstständig tätiger Gastkünstler';
            $er_sie = 'Er';
            $seiner_ihrer = 'seiner';
            $seine_ihre = 'seine';
            $small_er_sie = 'er';
            if($this->acontract->artistaddress->saddress_persdata->sex == 'F') {
                $musiker_in = 'Musikerin';
                $der_die_musiker_in = 'Die Musikerin';
                $small_der_die_musiker_in = 'die Musikerin';
                $er_sie = 'Sie';
                $small_er_sie = 'sie';
                $seiner_ihrer = 'ihrer';
                $seine_ihre = 'ihre';
                $gastkuenstler = 'selbstständig tätige Gastkünstlerin';
            }


            $email = $this->reporttools->getNumbers_by_type($this->acontract->artist_id, 'l_email');
            $mobile = $this->reporttools->getNumbers_by_type($this->acontract->artist_id, 'l_mobile');
            $phone = $this->reporttools->getNumbers_by_type($this->acontract->artist_id, 'l_phone');


            $contract_no = $this->acontract->contract_no;

            $instrument = ($this->acontract->sinstrinstrument ? $this->acontract->sinstrinstrument->name : '');
            $project = ($this->acontract->sproject ? $this->acontract->sproject->name : '');
            $jobreason = ($this->acontract->sjobreason ? $this->acontract->sjobreason->name : '');

            $accountholder = '';
            $accountno = '';
            $bank = '';
            $swift = '';
            $iban = '';

            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0])) {
                $accountholder = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->accountholder;
                $accountno = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->accountno;
                $iban = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->iban;
                $bank = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->bankname;
                $swift = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->swift;
            }

            //print_r($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]);

            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress)) {
                $bank = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress->name1;
                $swift = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress->code;
            }

            $nationality = '';
            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry)) {
                $nationality = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry->nationality;
                if(empty($nationality)) {
                    $nationality = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry->name;
                }
            }

            $taxno = ($this->acontract->artistaddress->saddress_persdata ? $this->acontract->artistaddress->saddress_persdata->taxno : '');

            //******** ONCUST-2790
            //solange nicht taxoffice und taxofficeno beide als Standardfelder in ON existieren, bleibt PSM bei
            //den adddata Felder
            //taxoffice - saddresses.text_6 und
            //taxofficeno - saddresses.text_1 .
            //$taxoffice = ($this->acontract->artistaddress->saddress_persdata ? $this->acontract->artistaddress->saddress_persdata->taxoffice : '');
            $taxoffice = ($this->acontract->artistaddress ? $this->acontract->artistaddress->text_6 : '');
            $taxofficeno = ($this->acontract->artistaddress ? $this->acontract->artistaddress->text_1 : '');


            $this->l_privat = false;
            if(strpos(mb_strtolower($jobreason), 'privat') !== false) {
                $this->l_privat = true;
            }

            $privat = '';
            $artist2 = '';

            if($this->l_privat && $this->acontract->artist2address) {
                $privat = '(private Aushilfe für';
                $artist2 = trim($this->acontract->artist2address->name2 . ' ' . $this->acontract->artist2address->name1).')';
            }

            $this->adates_contract = array();
            $this->apds_contract = array();
            $this->arehearsals_contract = array();

            $this->aprogram = array();
            $this->asoloists = array();
            $this->adresses = array();

            $this->mindate = null;
            $mindate = '';
            $maxdate = '';
            $count = 0;
            $adays = array();
            $this->nduties_C = 0;
            $this->nduties_P = 0;
            $dress = '';
            foreach($this->acontract->acontract_dates as $acontract_date) {
                $count++;

                $adate = $acontract_date->adate;
                $date_id = $adate->id;
                if($count==1) {
                    $this->mindate = $adate->date_;
                    $mindate = $adate->date_->format('d.m.Y');
                }
                $maxdate = $adate->date_->format('d.m.Y');

                $minmax = $mindate . ' - '. $maxdate;


                if(!in_array($adate->date_, $adays)) {
                    $adays[] = $adate->date_;
                }

                if($adate->seventtype->l_performance==1) {
                    if(empty($dress)) {
                        $dress = ($adate->sdress ? $adate->sdress->name : '');
                    }

                    $lastpd = $adate->date_->format('d.m.Y');
                    $this->nduties_C += $adate->duties;
                } else {
                    $this->nduties_P += $adate->duties;
                }

                $this->adates_contract[$date_id] = $adate;

                if($adate->seventtype->l_performance==1) {
                    $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
                    //*** Musiktheater-Vorstellungen: sind Terminart: Premiere oder Vorstellung
                    //*** 20190416
                    //*** Die Termine müssen nur einmal entweder unter Konzerte oder unter Musiktheater erscheinen abhängig von der Terminart.

                    if(in_array($eventtype, ['Premiere', 'Vorstellung'])) {
                        $this->amvs_contract[$date_id] = $adate;
                    } else {
                        $this->apds_contract[$date_id] = $adate;
                    }

                    foreach ($adate->adate_works as $awork) {
                        if(!array_key_exists($awork->work_id, $this->aprogram)) {
                            $this->aprogram[$awork->work_id] = $awork;
                        }
                    }

                    if($adate->dress_id>0 and !array_key_exists($adate->dress_id, $this->adresses)) {
                        $this->adresses[$adate->adresses] = $adate->adress;
                    }
                } else {
                    $this->arehearsals_contract[$date_id] = $adate;
                }

            }
            $this->ndays = sizeof($adays);

            /*
             *** 20190321
             *** Ein Kreuz soll automatisch erscheinen nach Anwesenheitstyp: Aushilfe = Vertretung, Verstärkung = Verstärkung

             *** 20190416
             *** Das hier funktioniert nicht richtig. Kreuz muss vom eingeteilten Anwesenheitstyp abgeleitet werden
             *** (Vertretung = Aushilfe, Verstärkung = Verstärkung).

             *** 20190418
             *** ich verstehe jetzt, warum die Kreuze Vertretung / Verstärkung nicht immer funktionieren.
             *** Es hat zu tun mit der Tatsache, dass die beiden Anwesenheitstype für Aushilfe ("Aushilfe" und "Aushilfe eingeteilt")
             *** sowie für Verstärkung ("Verstärkung" und "Verstärkung eingeteilt") berücksichtigt werden müssen.
             *** Im Moment werden, vermute ich, nur die "Aushilfe" und "Verstärkung" Anwesenheitstype berücksichtig.
             *** Im Moment aber, dass wir den Vertrag erstellen, ist der Musiker noch nur eingeteilt. Deswegen brauchen wir diese Erweiterung.
             */

            $adate_ids = array_keys($this->adates_contract);
            $where_Duties_Artists = ['Aduties.artist_id' => $this->acontract->artist_id, 'Aduties.date_id IN '=>$adate_ids];

            $aduties_contract = TableRegistry::getTableLocator()->get('Aduties')
                ->find('all')
                ->contain(['Sdutytypes'])
                ->select()
                ->where($where_Duties_Artists);

            // 20240318 ONCUST-3224
            //wenn artit2_id>0 - “Vertretung”, sonst “Verstärkung”

            $this->vertretung = $this->acontract->artist2_id>0;
            /*
             $this->vertretung = false;
             foreach($aduties_contract as $aduty) {
                if(substr(mb_strtoupper($aduty->sdutytype->name), mb_strtoupper('Aushilfe')) !== false) {
                    $this->vertretung = true;
                }

            }*/

            $this->templateProcessor->setValue('date#'.$i, date('d.m.Y'));
            $this->templateProcessor->setValue('lastpd#'.$i, $lastpd);


            $this->templateProcessor->setValue('artist_street#'.$i, htmlspecialchars($artist_street));
            $this->templateProcessor->setValue('artist_zipplace#'.$i, htmlspecialchars($artist_zipplace));
            $this->templateProcessor->setComplexBlock('artist_address#'.$i, $this->getArtist_address());
            $this->templateProcessor->setValue('salutation#'.$i, htmlspecialchars($salutation));
            $this->templateProcessor->setValue('artistname#'.$i, htmlspecialchars($artist_name));
            $this->templateProcessor->setValue('artist_name#'.$i, htmlspecialchars($artist_name));
            $this->templateProcessor->setValue('nationality#'.$i, htmlspecialchars($nationality));
            $this->templateProcessor->setValue('privat#'.$i, htmlspecialchars($privat.''));
            $this->templateProcessor->setValue('privat2#'.$i, htmlspecialchars($privat.' '));
            $this->templateProcessor->setValue('dress#'.$i, htmlspecialchars($dress));
            $this->templateProcessor->setValue('artist2#'.$i, htmlspecialchars($artist2));


            $this->templateProcessor->setValue('minmax#'.$i, htmlspecialchars($minmax));

            $reh_count = sizeof($this->arehearsals_contract);
            $pd_count = sizeof($this->apds_contract);
            //*** Honorar Probe - §P
            //*** Honorar Konzert - §K
            $honorar_p = $this->reporttools->getAmount('P', $this->mindate, 'amount_'.$this->acontract->accounting_category, false);
            $honorar_k = $this->reporttools->getAmount('K', $this->mindate, 'amount_'.$this->acontract->accounting_category, false);

            $honorar_p_sum = $honorar_p * $reh_count;
            $honorar_k_sum = $honorar_k * $pd_count;

            $this->honorar = 0;
            foreach($this->acontract->aexpenses as $aexpense) {
                $this->honorar += ($aexpense->number_ * $aexpense->amount);
            }

            $this->templateProcessor->setValue('reh_count#'.$i, htmlspecialchars($reh_count));
            $this->templateProcessor->setValue('pd_count#'.$i, htmlspecialchars($pd_count));

            $fmt = new NumberFormatter( 'de_DE', NumberFormatter::CURRENCY );

            $this->templateProcessor->setValue('hon_p#'.$i, $fmt->formatCurrency($honorar_p, 'EUR'));
            $this->templateProcessor->setValue('hon_k#'.$i, $fmt->formatCurrency($honorar_k, 'EUR'));
            $this->templateProcessor->setValue('hon_p_sum#'.$i, $fmt->formatCurrency($honorar_p_sum, 'EUR'));
            $this->templateProcessor->setValue('hon_k_sum#'.$i, $fmt->formatCurrency($honorar_k_sum, 'EUR'));

            $this->templateProcessor->setValue('hon_total#'.$i, $fmt->formatCurrency($honorar_p_sum + $honorar_k_sum, 'EUR'));
            $this->templateProcessor->setValue('honorar#'.$i, $fmt->formatCurrency($this->honorar, 'EUR'));


            $this->templateProcessor->setValue('musiker_in_1#'.$i, htmlspecialchars($musiker_in));
            $this->templateProcessor->setValue('musiker_in_2#'.$i, htmlspecialchars($musiker_in));
            $this->templateProcessor->setValue('musiker_in_3#'.$i, htmlspecialchars($musiker_in));

            $this->templateProcessor->setValue('seiner_ihrer#'.$i, htmlspecialchars($seiner_ihrer));
            $this->templateProcessor->setValue('seine_ihre#'.$i, htmlspecialchars($seine_ihre));
            $this->templateProcessor->setValue('gastkuenstler#'.$i, htmlspecialchars($gastkuenstler));

            for($j=1;$j<=10;$j++) {

                $this->templateProcessor->setValue('er_sie_'.$j.'#'.$i, htmlspecialchars($er_sie));
                $this->templateProcessor->setValue('small_er_sie_'.$j.'#'.$i, htmlspecialchars($small_er_sie));
                $this->templateProcessor->setValue('der_die_musiker_in_'.$j.'#'.$i, htmlspecialchars($der_die_musiker_in));
                $this->templateProcessor->setValue('small_der_die_musiker_in_'.$j.'#'.$i, htmlspecialchars($small_der_die_musiker_in));
            }


            $this->templateProcessor->setValue('project#'.$i, htmlspecialchars($project));
            $this->templateProcessor->setValue('instrument#'.$i, htmlspecialchars($instrument));
            $this->templateProcessor->setValue('jobreason#'.$i, htmlspecialchars($jobreason));

            $this->templateProcessor->setComplexBlock('arrangements#'.$i, $this->getArrangements());


            $this->templateProcessor->setValue('mindate#'.$i, htmlspecialchars($mindate));
            $lastdate = $adate->date_->format('d.').' '.$this->reporttools->getMonthname($adate->date_).' '.$adate->date_->format('Y');
            $this->templateProcessor->setValue('maxdate#'.$i, htmlspecialchars($lastdate));


            $this->templateProcessor->setValue('phone#'.$i, htmlspecialchars($phone));
            $this->templateProcessor->setValue('email#'.$i, htmlspecialchars($email));

            $this->templateProcessor->setValue('taxno#'.$i, htmlspecialchars($taxno));
            $this->templateProcessor->setValue('taxoffice#'.$i, htmlspecialchars($taxoffice));
            $this->templateProcessor->setValue('taxofficeno#'.$i, htmlspecialchars($taxofficeno));


            $this->templateProcessor->setComplexBlock('rehearsals#'.$i, $this->getRehearsals());
            $this->templateProcessor->setComplexBlock('concerts#'.$i, $this->getConcerts());
            $this->templateProcessor->setComplexBlock('mv#'.$i, $this->getMVs());


            //$this->templateProcessor->setValue('honorar#'.$i, $this->acontracts->sql());



            $this->templateProcessor->setValue('contract_no#'.$i, htmlspecialchars($contract_no));


            $this->templateProcessor->setValue('accountholder#'.$i, htmlspecialchars($accountholder));
            $this->templateProcessor->setValue('bank#'.$i, htmlspecialchars($bank));
            $this->templateProcessor->setValue('swift#'.$i, htmlspecialchars($swift));
            $this->templateProcessor->setValue('iban#'.$i, htmlspecialchars($iban));

            $this->templateProcessor->setComplexBlock('clauses#'.$i, $this->getClauses());
            $this->templateProcessor->setComplexBlock('VertVerst#'.$i, $this->getVertVerst());
            $this->templateProcessor->setComplexBlock('Verguetung#'.$i, $this->getVerguetung());

            $this->templateProcessor->setComplexBlock('schedule_expenses#'.$i, $this->getSchedule_expenses());



            $this->templateProcessor->setValue('pageBreak#'.$i, $pagebreak);
        }

    }

    function getSchedule_expenses() {
        $textrun = new TextRun();
        $textrun->addText('Schedule_expenses');
        return $textrun;
    }

    function getVertVerst() {



        $styleText = array('padding-left' => 0);

        $textrun = new TextRun($styleText);

        //$checkedBox='<w:sym w:font="Wingdings" w:char="F0FE"/>';
        //$unCheckedBox = '<w:sym w:font="Wingdings" w:char="F0A8"/>';

        $checkedBox = true;
        $unCheckedBox = false;

        $textrun->addText(htmlspecialchars('ist für nachstehende Dienste als '), $this->styleFont);
        $textrun->addFormField('checkbox')->setValue(($this->vertretung ? $checkedBox : $unCheckedBox));

        $textrun->addText(htmlspecialchars(' Vertretung '), array_merge($this->styleFont, $this->styleFont_Bold));
        $textrun->addFormField('checkbox')->setValue(($this->vertretung ? $unCheckedBox : $checkedBox));
        $textrun->addText(htmlspecialchars(' Verstärkung '), array_merge($this->styleFont, $this->styleFont_Bold));
        $textrun->addText(htmlspecialchars('verpflichtet.'), $this->styleFont);
        //$textrun->addText(htmlspecialchars('Für die vereinbarten Vorstellungen wird in Anwendung der Regelungen zum JEP eine Aufwandspauschale in Höhe von XX€ gezahlt.'), $this->styleFont);

        return $textrun;
    }

    function getVerguetung()
    {
        //Für die vereinbarten Vorstellungen wird in Anwendung der Regelungen zum JEP eine Aufwandspauschale in Höhe von ${honorar} gezahlt.
        $styleText = array('padding-left' => 0);
        $textrun = new TextRun($styleText);

        switch (true) {


          //  case $this->l_privat:
                //*** ********
                //*** Wenn es um eine private Aushilfe geht(diese also für den Bericht "Aushilfenabrechnung" gewählt wird) soll der Satz "Die Vergütung wird nach der Vereinbarung Privataushilfe" erscheinen .
                //*** Dieser Satz ersetzt den Satz: "Die Vergütung wird nach den Aushilfssätzen der Vereinbarung DBV-LV Mitte gezahlt."

            //break;

            case $this->acontract->accounting_category <= 3 :
                //*** Honorar Probe - §P
                //*** Honorar Konzert - §K
                $honorar_p = $this->reporttools->getAmount('P', $this->mindate, 'amount_'.$this->acontract->accounting_category, false);
                $honorar_k = $this->reporttools->getAmount('K', $this->mindate, 'amount_'.$this->acontract->accounting_category, false);

                $demMusiker = 'dem Musiker';
                if($this->acontract->artistaddress->saddress_persdata->sex == 'F') {
                    $demMusiker = 'der Musikerin';
                }

                $textrun->addText(htmlspecialchars(
                    'Die Bruttovergütung wird nach den folgenden Sätzen gezahlt und ist von '.$demMusiker.
                    ' zu versteuern: € '.number_format($honorar_p, 2, ',', '.').'/Probe, € '.
                    number_format($honorar_k, 2, ',', '.'). '/Konzert.')
                    );

                if(!$this->l_privat) {
                    $textrun->addTextBreak();
                    $textrun->addText(htmlspecialchars('Fahrtkosten werden gegen Nachweis erstattet.'));
                }

                break;

            //case $this->acontract->accounting_category >3
            default:

                $textrun->addText(htmlspecialchars('Für die vereinbarten Vorstellungen wird in Anwendung der Regelungen zum JEP eine Aufwandspauschale in Höhe von '.$this->honorar.' € gezahlt.'));

                /*$textrun->addText(htmlspecialchars(
                    'Die Vergütung wird nach den Aushilfssätzen '.
                    ($this->l_privat ?
                        'der Vereinbarung Privataushilfe' :
                        'den Aushilfssätzen der Vereinbarung DBV-LV Mitte'
                    ) .
                    ' gezahlt und ist vom Musiker zu versteuern.'
                ));
                */
            break;

        }
        return $textrun;
    }

    function getArtist_address() {

        $artist_address = new TextRun();

        if($this->acontract->artistaddress) {
            if (!empty($this->acontract->artistaddress->street)) {
                $artist_address->addTextBreak();
                $artist_address->addText(htmlspecialchars($this->acontract->artistaddress->street), array_merge($this->styleFont, $this->styleFont_Italic));
            }

            if (!empty($this->acontract->artistaddress->zipcode) || !empty($this->acontract->artistaddress->place)) {
                $artist_address->addTextBreak();
                $artist_address->addText(htmlspecialchars(trim($this->acontract->artistaddress->zipcode.' '.$this->acontract->artistaddress->place)), array_merge($this->styleFont, $this->styleFont_Italic));
            }
        }
        return $artist_address;
    }


    function getRehearsals() {
        $textrun = new TextRun();
        $count = 0;
        foreach($this->arehearsals_contract as $adate) {
            $count++;
            $location = ($adate->locationaddress ? $adate->locationaddress->name1.($adate->locationaddress->place>'' ? ', ' : '').$adate->locationaddress->place : '');
            $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
            if($count>1) {
                $textrun->addTextBreak();
            }

            $textrun->addText(htmlspecialchars(
                $adate->date_->format('d.m.y').
                ' um '.
                $this->getTime($adate, true, false).
                ($eventtype>'' ? ', ':'').$eventtype.
                ($location>'' ? ', ':'').$location
            ));
        }
        return $textrun;
    }

    function getConcerts() {
        $textrun = new TextRun();
        $count = 0;
        foreach($this->apds_contract as $adate) {
            $count++;
            $location = ($adate->locationaddress ? $adate->locationaddress->name1.($adate->locationaddress->place>'' ? ', ' : '').$adate->locationaddress->place : '');
            $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
            if($count>1) {
                $textrun->addTextBreak();
            }

            $textrun->addText(htmlspecialchars(
                $adate->date_->format('d.m.y').
                ' um '.
                $this->getTime($adate, true, false).
                ($eventtype>'' ? ', ':'').$eventtype.
                ($location>'' ? ', ':'').$location
            ));
        }
        return $textrun;
    }

    function getMVs() {
        $textrun = new TextRun();
        $count = 0;
        foreach($this->amvs_contract as $adate) {
            $count++;
            $location = ($adate->locationaddress ? $adate->locationaddress->name1.($adate->locationaddress->place>'' ? ', ' : '').$adate->locationaddress->place : '');
            $eventtype = ($adate->seventtype ? $adate->seventtype->name : '');
            if($count>1) {
                $textrun->addTextBreak();
            }

            $textrun->addText(htmlspecialchars(
                $adate->date_->format('d.m.y').
                ' um '.
                $this->getTime($adate, true, false).
                ($eventtype>'' ? ', ':'').$eventtype.
                ($location>'' ? ', ':'').$location
            ));
        }
        return $textrun;
    }

    function getArrangements() {
        $textrun = new TextRun();
        $this->addMemo($textrun, $this->acontract->arrangements);
        return $textrun;
    }


    function getClauses() {

        $clauses = new TextRun();

        $count = 0;
        foreach($this->acontract->acontract_clauses as $acontract_clause) {
            $count++;
            $text = $acontract_clause->text;
            $standard_text = ($acontract_clause->scontractclause ? $acontract_clause->scontractclause->text : '');
            $clause = (!empty($text) ? $text : $standard_text);

            if($count>1) {
                $clauses->addTextBreak();
                $clauses->addTextBreak();
            }
            $this->addMemo($clauses, $clause);

        }
        return $clauses;
    }

    /**
     * Get the time
     *
     * If there is a end time both, the start and end time will be returned, in other case only the start time will be
     * returned or an empty string if no time is defined.
     *
     * @param $date
     * @return string
     */
    public function getTime($adate, $l_start=true, $l_end=true)
    {
        $startend = '';

        $start = '';
        $end = '';

        if ($adate->start_) {
            $start = $adate->start_->format('H:i');
        }
        if ($adate->end_) {
            $end = $adate->end_->format('H:i');
        }
        if ($start == '00:00' or !$l_start) {
            $start = '';
        }
        if ($end == '00:00' or !$l_end) {
            $end = '';
        }

        $startend = $start . (!empty($end) ? ' - ' : '') . $end;

        return $startend;
    }

    public function addMemo($section, $text) {

        $text = str_replace("\r\n", "\n", $text);
        $textlines = explode("\n", $text);
        $count = 0;
        foreach($textlines as $line) {
            //print_r($line.'#');
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line), array_merge($this->styleFont));
        }
    }
}

