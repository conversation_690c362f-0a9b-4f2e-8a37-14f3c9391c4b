<?php
/*
******** ONCUST-2787
PSM Monatsplan
*/

use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateAccountingamountsTable;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use Cake\ORM\Query;
use App\Reports\Tools\ReportTools;
use Customer\psm\reports\ReportTools_client;
use Customer\psm\reports\instrumentation_psm;

class adates_monatsplan_psm extends ReportWord
{

    public $default_font_size= 10;
    public $default_font = 'Calibri';

    public $postData = null;

    public $section = null;
    public $adates_selected = null;

    public $amonths = array();
    public $amonth = array();
    public $aweeks = array();
    public $aweek = [];

    public $aproject = array();

    public $reporttools = null;

    public $acol_widths = array();

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_4 = array('size' => 4);
    public $styleFont_8 = array('size' => 8);
    public $styleFont_12 = array('size' => 12);
    public $styleFont_11 = array('size' => 11);
    public $styleFont_10 = array('size' => 10);
    public $styleFont_14 = array('size' => 14);
    public $styleFont_18 = array('size' => 18);
    public $styleFont_20 = array('size' => 20);
    public $styleFont_24 = array('size' => 24);
    public $styleBackground_black = array('bgColor'=>'black');

    public $styleAlign_right = array('align' => 'right');
    public $styleAlign_center = array('align' => 'center');

    public $styleKeepNext = ['keepNext' => true, 'keepLines'=>true];
    public $styleCantSplit = ['cantSplit' => true];


    public $otable = null;
    public $w_table = 0;


    //public $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => 'black');
    public $styleCell_borderTop1 = array();
    public $styleCell_borderTop = array(
        'borderTopSize' => 6, 'borderTopColor' => 'black',
        'borderLeftSize' => 6, 'borderLeftColor' => 'black',
        'borderRightSize' => 6, 'borderRightColor' => 'black'
    );
    public $styleCell_borderTop_none1 = array();
    public $styleCell_borderTop_none = array(
        'borderTopSize' => 0, 'borderTopColor' => 'white',
        'borderLeftSize' => 6, 'borderLeftColor' => 'black',
        'borderRightSize' => 6, 'borderRightColor' => 'black'
    );

    public $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');

    public $cellColSpan2 = array('gridSpan' => 2);
    public $cellColSpan7 = array('gridSpan' => 7);
    public $cellColSpan8 = array('gridSpan' => 8);

    public $works = null;
    public $oinstrumentation = null;

    function initialize()
    {
        parent::initialize();
    }

    public function collect(array $where = [])
    {

        $this->reporttools = new ReportTools_client();
        $this->oinstrumentation = new instrumentation_psm();

        $this->postData = $this->getRequest()->getData();
        //$this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);

        // nur AusgewÃ»ÃŠhlte Termine
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model);

        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->prepare_amonths();

        $this->phpWord->setDefaultFontSize(10);
        $this->phpWord->setDefaultFontSize($this->default_font_size);
        $this->phpWord->setDefaultFontName($this->default_font);
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        foreach($this->amonths as $this->amonth) {
            $this->showMonth();
        }
    }

    function prepare_amonths() {
        //$this->addSection();

        $this->amonths = array();

        foreach ($this->adates_selected as $adate) {

            $season_id = $adate->season_id;
            $project_id = $adate->project_id;
            $month = $adate->month;
            $year = $adate->year;
            $week = $adate->week;
            $k = $adate->year . '_' . $adate->month;

            $d_date = $adate->date_->format('Ymd');

            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

            if (!array_key_exists($k, $this->amonths)) {

                $this->amonths[$k] = array(
                    'year' => $year,
                    'month' => $month,
                    'cmindate' => '',
                    'cmaxdate' => '',
                    'season' => ($adate->sseason ? $adate->sseason->name : ''),
                    'cmonth' => $this->reporttools->getMonthName($adate->date_) . ' ' . $year,
                    'aweeks' => array(),
                    'aprojects' => array()
                );
            }

            if (!key_exists($week, $this->amonths[$k]['aweeks'])) {

                $sseason_weeksoff = TableRegistry::getTableLocator()->get('SseasonWeeksoff')
                    ->find('all')

                    ->where(
                        ['SseasonWeeksoff.season_id'=>$season_id, 'SseasonWeeksoff.week'=>$week]
                    )
                    ->order(['SseasonWeeksoff.week' => 'ASC'])
                    ->first();

                $text = '';
                if($sseason_weeksoff) {
                    $text = $sseason_weeksoff->text;
                }

                $this->amonths[$k]['aweeks'][$week] = array(
                    'cmindate' => '',
                    'cmaxdate' => '',
                    'week' => $week . '#XXX',
                    'text' => $text,
                    'pweek' => $adate->pweek,
                    'block1' => $adate->block1,
                    'year' => $adate->year,
                    'adays' => array()
                );

                // Alle Tage der Woche holen
                $adays = TableRegistry::getTableLocator()->get('Adays')
                    ->find('all')
                    ->contain(['Sholydays'])
                    ->where(["Adays.week='" . $week . "' AND Adays.planninglevel=1"])
                    ->orderAsc('Adays.date_')
                    ->distinct();


                $count = 0;
                foreach ($adays as $aday) {
                    $count++;

                    $d_day = $aday->date_->format('Ymd');

                    if ($count == 1) {
                        $this->amonths[$k]['cmindate'] = $aday->date_->format('d.m.Y');
                        $this->amonths[$k]['aweeks'][$week]['cmindate'] = $aday->date_->format('d.m.Y');
                    }

                    $this->amonths[$k]['cmaxdate'] = $aday->date_->format('d.m.Y');
                    $this->amonths[$k]['aweeks'][$week]['cmaxdate'] = $aday->date_->format('d.m.Y');

                    $this->amonths[$k]['aweeks'][$week]['adays'][$d_day] = array(
                        'date_' => $aday->date_,
                        'aday' => $aday,
                        'dow' => $aday->date_->format('N'),
                        'holyday_id' => (int)$aday->holyday_id,
                        'cdate' => $aday->date_->format('d.m.Y'),
                        'cday' => $aday->weekday . ', ' . $aday->date_->format('d.m.Y'),
                        'holyday' => ($aday->sholyday ? $aday->sholyday->name : ''),
                        'l_performance' => 0,
                        'weekday' => $aday->weekday,
                        'adates' => array()
                    );
                }
                //$this->section->addText($week_date.'#'.$d_date);
                //$this->section->addText($adate->id.'#'.$adate->date_->format('d.m.Y'));
            }

            if (!key_exists($project_id, $this->amonths[$k]['aprojects'])) {
                $this->amonths[$k]['aprojects'][$project_id] = array(
                    'project' => ($adate->sproject ? $adate->sproject->name : ''),
                    'aworks' => array()
                );
            }

            if ($l_performance == 1) {
                $this->amonths[$k]['aweeks'][$week]['adays'][$d_date]['l_performance'] = 1;
            }

            $this->amonths[$k]['aweeks'][$week]['adays'][$d_date]['adates'][$adate->id] = $adate;

            $oinstrumentation = new instrumentation_psm();

            if ($l_performance == 1) {
                foreach ($adate->adate_works as $awork) {
                    $work_id = $awork->work_id;
                    $title = ($awork->title2 > '' ? $awork->title2 : $awork->swork->title1);

                    $composer = ($awork->swork->scomposer ? $awork->swork->scomposer->lastname : '');
                    $composer_21 = ($awork->swork->scomposer ? trim($awork->swork->scomposer->firstname . ' ' . $awork->swork->scomposer->lastname) : '');
                    $nduration = (int)substr($awork->duration, 0, 2) * 60 + (int)substr($awork->duration, 3, 2);
                    $cduration = $nduration . "'";

                    $l_encore = $awork->l_encore;
                    $l_intermission = $awork->swork->l_intermission;
                    if ($awork->swork->l_intermission == 1) {
                        $cduration = '';
                    }

                    $oinstrumentation->datework_id = $awork->id;
                    $oinstrumentation->getInstrumentation();

                    $row = array(
                        'l_encore' => $l_encore,
                        'l_intermission' => $l_intermission,
                        'title' => $title,
                        'duration' => $awork->duration,
                        'cduration' => $cduration,
                        'composer' => $composer,
                        'composer_21' => $composer_21,
                        'date_work' => $awork,
                        'instrumentation' => $oinstrumentation->instrumentation
                    );

                    if ($awork->swork->l_intermission == 0) {
                        if (!array_key_exists($work_id, $this->aworks_all)) {
                            $this->aworks_all[$work_id] = $row;
                        }
                    }

                    if (!array_key_exists($work_id, $this->amonths[$k]['aprojects'][$project_id]['aworks'])) {
                        $this->amonths[$k]['aprojects'][$project_id]['aworks'][$work_id] = $row;
                    }
                }
            }
        }
    }

    function addSection() {
        $this->section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.25),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(1.75),
                'marginRight' => Converter::cmToTwip(1.75),
                'marginTop' => Converter::cmToTwip(0.75),
                'marginBottom' => Converter::cmToTwip(0.5))
        );

        $sectionStyle = $this->section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('center', Converter::cmToTwip(8.5)),
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(16))
                    )
            );

        //$header = $this->section->addHeader();
        /*$textrun = $header->addTextRun($styleTabs);
        $textrun->addText("\t");
        $textrun->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(6.91), 'align' => 'center'));
        $textrun->addText("\t");
        $textrun->addField("PAGE", array());
        */

    }

    function showMonth() {
        $this->addSection();

        $this->section->addImage(CUSTOMER_REP_DIR . 'logo_psm.png', array('width' => Converter::cmToPoint(3.15), 'align' => 'right'));

        $textrun = $this->section->addTextRun();
        $textrun->addText(htmlspecialchars('SPIELZEIT '.$this->amonth['season'].'    '), array_merge($this->styleFont_10, $this->styleFont_bold));
        $textrun->addText(htmlspecialchars('Monatsvorschau '.$this->amonth['cmonth']), array_merge($this->styleFont_14, $this->styleFont_bold));
        $this->section->addTextBreak();

        $this->showSchedule();

        $this->showProgram();
   }

    function showPDs() {
        if(sizeof($this->aproject['apds'])==0) {return;}

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(3.5);
        $this->acol_widths[] = Converter::cmToTwip(2.5);
        $this->acol_widths[] = Converter::cmToTwip(12);

        $this->w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->w_table += $col_width;
        }

        $this->otableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.1),
            'cellMarginBottom' => Converter::cmToTwip(0.1),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $this->w_table,
            'unit' => TblWidth::TWIP
        );

        $styleCell = array();
        $styleFont = array();

        $this->otable = $this->section->addTable($this->otableProperties);



        foreach ($this->aproject['apds'] as $adate) {
            $location = ($adate->locationaddress ? $adate->locationaddress->name1. (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place : '');

            $this->otable->addRow();

            $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
            $cell->addText(htmlspecialchars($adate->date_->format('d.m.Y')), array_merge($this->styleFont));

            $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell));

            if(!empty($adate->start_)) {
                $cell->addText(htmlspecialchars($adate->start_->format('H:i'). ' Uhr'), array_merge($this->styleFont));
            }


            $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell->addText(htmlspecialchars($location), array_merge($this->styleFont));
        }
    }

    function showSchedule()
    {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(3.13);
        $this->acol_widths[] = Converter::cmToTwip(1.67);
        $this->acol_widths[] = Converter::cmToTwip(1);
        $this->acol_widths[] = Converter::cmToTwip(4.75);
        $this->acol_widths[] = Converter::cmToTwip(1.41);
        $this->acol_widths[] = Converter::cmToTwip(1.34);
        $this->acol_widths[] = Converter::cmToTwip(4.25);

        $this->w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $this->w_table += $col_width;
        }

        $this->otableProperties = array(
            'borderSize' => 0,
            'borderColor' => 'white',
            'cellMarginTop' => Converter::cmToTwip(0.1),
            'cellMarginBottom' => Converter::cmToTwip(0.1),
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $this->w_table,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED,
            'unit' => TblWidth::TWIP
        );



        $this->count_weeks = 0;
        foreach ($this->amonth['aweeks'] as $week=>$this->aweek) {
            $this->count_weeks++;



            if($this->count_weeks>1) {
                $this->section->addTextBreak();
                //$this->otable->addRow(Converter::cmToTwip(0.1));
                //$cell = $this->otable->addCell($this->w_table, array_merge($this->styleCell_borderTop, $this->cellColSpan7));

            }
            $this->otable = $this->section->addTable($this->otableProperties);
            $this->showHeaderWeek();
            $this->showWeekSchedule();
        }

    }

    function showHeaderWeek() {


        $this->otable->addRow(null,$this->styleCantSplit);

        $styleCell = $this->styleBackground_black;
        $this->styleFont = array('color' => 'white');
        $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars($this->aweek['pweek'].'. Woche'), array_merge($this->styleFont), array_merge($this->styleAlign_right, $this->styleKeepNext));

        //$cell->addText(htmlspecialchars(print_r($this->aweek,true)), array_merge($this->styleFont), $this->styleAlign_right);



        //*** 20200623
        //***this.oTable.cells(this.oTable.nnum_of_rows,2).value = 'AZ ' + ALLT(STR(this.nBlock3))
        $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('AZ '.$this->aweek['block1']), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[2], array_merge($styleCell, $this->styleCell_borderTop));

        $cell = $this->otable->addCell($this->acol_widths[3], array_merge($styleCell, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars('KW '.substr($this->aweek['week'], 3,2)), array_merge($this->styleFont), $this->styleKeepNext);

        $cell = $this->otable->addCell($this->acol_widths[4], array_merge($styleCell, $this->styleCell_borderTop));
        $cell = $this->otable->addCell($this->acol_widths[5], array_merge($styleCell, $this->styleCell_borderTop));
        $cell = $this->otable->addCell($this->acol_widths[6], array_merge($styleCell, $this->styleCell_borderTop));

        $styleCell = [];
        $this->styleFont = array();
    }

    function showWeekSchedule() {


        $count_days = 0;

        foreach ($this->aweek['adays'] as $k=>$aday) {

            $count_days++;

            $styleCell = array();

            $this->styleFont = array();

            //$this->otable->addRow(Converter::cmToTwip(0.3), array("exactHeight" => true));
            $this->otable->addRow(Converter::cmToTwip(0.3), $this->styleCantSplit);

            //keine Termine
            if(sizeof($aday['adates']) == 0) {

                $styleCell = $this->styleCell_borderTop;

                if($count_days == sizeof($aday['adates'])) {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderBottom);
                }


                $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                $cell->addText(htmlspecialchars($aday['cday']), array_merge($this->styleFont), $this->styleKeepNext);

                if(!empty($aday['holyday'])) {
                    $cell->addText(htmlspecialchars($aday['holyday']), array_merge($this->styleFont), $this->styleKeepNext);
                }

                for($i=1; $i<=6; $i++) {
                    $cell = $this->otable->addCell($this->acol_widths[$i], array_merge($styleCell));
                }
            }

            $this->styleFont = array();

            $count_dates = 0;
            foreach ($aday['adates'] as $adate) {
                $count_dates++;

                $styleCell = [];
                if($count_dates == sizeof($aday['adates'])) {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderBottom);
                }


                if($count_dates==1) {
                    $this->styleFont = array();
                    if($aday['l_performance'] == 1) {
                        $this->styleFont = $this->styleFont_bold;
                    }


                    $styleCell = array_merge($styleCell, $this->styleCell_borderTop);
                    $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell->addText(htmlspecialchars($aday['cday']), array_merge($this->styleFont), $this->styleKeepNext);

                    if(!empty($aday['holyday'])) {
                        $cell->addText(htmlspecialchars($aday['holyday']), array_merge($this->styleFont), $this->styleKeepNext);
                    }

                    $this->styleFont = array();

                } else {
                    $styleCell = array_merge($styleCell, $this->styleCell_borderTop_none);
                    $this->otable->addRow(Converter::cmToTwip(0.3), $this->styleCantSplit);
                    $cell = $this->otable->addCell($this->acol_widths[0], array_merge($styleCell));
                    $cell->addText('', [], $this->styleKeepNext);
                }



                $start = $this->reporttools->getTime($adate, false);
                $location = ($adate->locationaddress ?
                    ($adate->locationaddress->code>'' ?
                        $adate->locationaddress->code :
                        $adate->locationaddress->name1 . (!empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->place
                    ) : '');
                $eventtype_code = ($adate->seventtype ? ($adate->seventtype->code > '' ? $adate->seventtype->code : $adate->seventtype->name) : '');
                $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);
                $dress = ($adate->sdress ? $adate->sdress->name : '');
                $project = ($adate->sproject ? $adate->sproject->name : '');

                $text = $adate->text;



                $this->styleFont = array();

                if ($l_performance == 1) {
                    $this->styleFont = $this->styleFont_bold;
                }

                //*** Wenn Terminart Code nicht = PS, OV, PR+ÖA, OA, OS, BO, lBO(kleines L), HP, GP, GP öff, P, WA, Gast, K, V,  dann Spalten 3-5 nicht füllen, sondern
                //*** In Spalte 6"Info"  eintragen:     Uhrzeit Code Terminart | Name des Projekts

			    if(!in_array($eventtype_code, ['PS', 'OV', 'PR+ÖA', 'OA', 'OS', 'BO', 'lBO', 'HP', 'GP', 'GP öff', 'P', 'WA', 'Gast', 'K', 'V'])) {
                    $col4 = $start.' '.$eventtype_code. ' | '.$project;
                    $eventtype_code = '';
                    $project = '';
                    $location = '';
                } else {
                    $col4 = $project;
                }

                $cell = $this->otable->addCell($this->acol_widths[1], array_merge($styleCell));
                if (!empty($adate->start_)) {
                    $cell->addText(htmlspecialchars($start), array_merge($this->styleFont), $this->styleKeepNext);
                } else {
                    $cell->addText('', array_merge($this->styleFont), $this->styleKeepNext);
                }

                $cell = $this->otable->addCell($this->acol_widths[2], array_merge($styleCell));
                $cell->addText(htmlspecialchars($eventtype_code), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[3], array_merge($styleCell));
                $cell->addText(htmlspecialchars($col4), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[4], array_merge($styleCell));
                $cell->addText(htmlspecialchars($location), array_merge($this->styleFont), $this->styleKeepNext);

                $cell = $this->otable->addCell($this->acol_widths[5], array_merge($styleCell));
                $cell->addText(htmlspecialchars($adate->duties), array_merge($this->styleFont), array_merge($this->styleAlign_center, $this->styleKeepNext));

                $cell = $this->otable->addCell($this->acol_widths[6], array_merge($styleCell));
                $cell->addText(htmlspecialchars($adate->text), array_merge($this->styleFont), $this->styleKeepNext);

            }
        }
        $this->styleFont = array();

    }

    function showProgram() {

        $this->section->addTextBreak(2);
        $this->section->addText(htmlspecialchars('VERBINDLICH IST DER JEWEILS GÜLTIGE WOCHENPLAN'), array_merge($this->styleFont_bold, $this->styleFont_underline, $this->styleFont_14), $this->styleAlign_center);

        $this->styleFont = $this->styleFont_11;
        $this->section->addTextBreak();
        $this->section->addText(htmlspecialchars('Besetzungen'), array_merge($this->styleFont, $this->styleFont_bold, $this->styleFont_underline));

        $styleTabs =
            array(
                'tabs' =>
                    array(
                        new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(17.5))
                    )
            );

        foreach($this->amonth['aprojects'] as $aproject) {
            if(sizeof($aproject['aworks'])==0) {
                continue;
            }

            $this->section->addTextBreak();
            $this->section->addText(htmlspecialchars($aproject['project']), array_merge($this->styleFont, $this->styleFont_bold, $this->styleFont_underline));

            foreach($aproject['aworks'] as $awork) {
                $textrun = $this->section->addTextRun($styleTabs);
                $textrun->addText(htmlspecialchars($awork['composer_21']), array_merge($this->styleFont, $this->styleFont_bold));
                $textrun->addText(htmlspecialchars(', '.$awork['title']),array_merge($this->styleFont));
                $textrun->addText(htmlspecialchars("\t".$awork['duration']),array_merge($this->styleFont));

                $this->section->addText(htmlspecialchars($awork['instrumentation']), array_merge($this->styleFont));
            }
        }
    }

    function getProgramCode($adate) {

        //$conductor_id = $this->adate->conductor_id;
        //$programCode = $conductor_id;
        $programCode = '';

        foreach ($adate->adate_works as $works) {
            $programCode .= '#'. $works->swork->id;
        }
        return $programCode;
    }
}
