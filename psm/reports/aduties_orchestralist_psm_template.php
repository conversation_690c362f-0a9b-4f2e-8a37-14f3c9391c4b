<?php

use App\Reports\ReportTemplate;
use Cake\ORM\TableRegistry;

class aduties_orchestralist_psm_template extends ReportTemplate
{
    /**
     * @inheritdoc
     */
    protected $templateName = 'aduties_orchestralist_psm_template';

    /**
     * @inheritdoc
     */
    protected $fileLocationType = 'direct';

    public function getTemplateData(array $params = [])
    {

        return [
            'cgroup_by' => 'Grouped by',
            'corder_by' => 'Order by',
            'cname' => __('saddresses.name1').', '.__('saddresses.name2'),
            'cseat_duties' => __('aduties.seat'). ' ('.__('aduties').')',
            'corder_1_duties' => __('aduties.order_1'). ' ('.__('aduties').')',
            'corder_2_duties' => __('aduties.order_2'). ' ('.__('aduties').')',
            'cseat_address' => __('saddresses.seat'). ' ('.__('saddresses').')',
            'corder_1_address' => __('saddresses.order_1'). ' ('.__('saddresses').')',
            'corder_2_address' => __('saddresses.order_2'). ' ('.__('saddresses').')'
        ];

        return $this->templateName;
    }

    public function submit($formDataString = null)
    {
        parse_str($formDataString, $formDataArray);

        $this->data['formData'] = $formDataArray;

        return parent::submit($formDataString);
    }

}
