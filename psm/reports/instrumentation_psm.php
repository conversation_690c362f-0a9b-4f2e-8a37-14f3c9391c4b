<?php

namespace Customer\psm\reports;
use App\Reports\Tools\InstrumentationCommon;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;

/**
 * Class Instrumentation_kap
 * @package App\Reports\Tools
 *
 * formatiert Instrumentierung nach KAP-Vorgaben
 */
class instrumentation_psm extends InstrumentationCommon {
    public $holz = '';
    public $blech = '';
    public $others = '';

    function formatInstrumentation()
    {
        parent::formatInstrumentation();

        $this->cast_separator = ', ';
        $this->cast_open_bracket = '(';
        $this->cast_close_bracket = ')';
        $this->cast_dash = ' ';

        $row = $this->data_row;

        $this->holz =
            $row->flute . ((isset($row->flute_text) && $row->flute_text > '') ? $this->cast_open_bracket . $row->flute_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->oboe . ((isset($row->oboe_text) && $row->oboe_text > '') ? $this->cast_open_bracket . $row->oboe_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->clarinet . ((isset($row->clarinet_text) && $row->clarinet_text > '') ? $this->cast_open_bracket . $row->clarinet_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->bassoon . ((isset($row->bassoon_text) && $row->bassoon_text > '') ? $this->cast_open_bracket . $row->bassoon_text . $this->cast_close_bracket : '');

        $this->blech =
            $row->horn . ((isset($row->horn_text) && $row->horn_text > '') ? $this->cast_open_bracket . $row->horn_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->trumpet . ((isset($row->trumpet_text) && $row->trumpet_text > '') ? $this->cast_open_bracket . $row->trumpet_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->trombone . ((isset($row->trombone_text) && $row->trombone_text > '') ? $this->cast_open_bracket . $row->trombone_text . $this->cast_close_bracket : '') . $this->cast_separator .
            $row->tuba . ((isset($row->tuba_text) && $row->tuba_text > '') ? $this->cast_open_bracket . $row->tuba_text . ']' : '');

        $timpani =
            ((isset($row->timpani_text) && !empty($row->timpani_text)) ? $row->timpani_text :
                ($row->timpani == 0 ? '' :
                    ($row->timpani > 1 ? $row->timpani . $this->cast_dash  : '') . $this->cast_timpani
                )
            );
        $percussion =
            ((isset($row->percussion_text) && !empty($row->percussion_text)) ? $row->percussion_text :
                ($row->percussion == 0 ? '' :
                    ($row->percussion > 1 ? $row->percussion . $this->cast_dash : '') . $this->cast_percussion
                )
            );

        //$percussion = 'p'.'#'.$row->percussion_text.'#'.$row->percussion;

        $harp =
            ((isset($row->harp_text) && !empty($row->harp_text)) ? $row->harp_text :
                ($row->harp == 0 ? '' :
                    ($row->harp > 1 ? $row->harp . $this->cast_dash : '') . $this->cast_harp
                )
            );

        $keyboard =
            ((isset($row->keyboard_text) && !empty($row->keyboard_text)) ? $row->keyboard_text :
                ($row->keyboard == 0 ? '' :
                    ($row->keyboard > 1 ? $row->keyboard . $this->cast_dash : '') . $this->cast_keyboard
                )
            );

        $extra =
            ((isset($row->extra_text) && !empty($row->extra_text)) ? $row->extra_text :
                ($row->extra == 0 ? '' :
                    ($row->extra > 1 ? $row->extra . $this->cast_dash : '') . $this->cast_extra
                )
            );

        $vocals =
            ((isset($row->vocals_text) && !empty($row->vocals_text)) ? $row->vocals_text :
                ($row->vocals == 0 ? '' :
                    ($row->vocals > 1 ? $row->vocals . $this->cast_dash : '') . 'voc'
                )
            );

        $this->others = $timpani;
        $this->others .= (($this->others>'' && $percussion>'') ? $this->cast_separator : '') . $percussion;
        $this->others .= (($this->others>'' && $harp>'') ? $this->cast_separator : '') . $harp;
        $this->others .= (($this->others>'' && $keyboard>'') ? $this->cast_separator : '') . $keyboard;
        $this->others .= (($this->others>'' && $extra>'') ? $this->cast_separator : '') . $extra;
        $this->others .= (($this->others>'' && $vocals>'') ? $this->cast_separator : '') . $vocals;

        $this->strings =
            $row->violin1 . $this->cast_separator .
            $row->violin2 . $this->cast_separator .
            $row->viola . $this->cast_separator .
            $row->cello . $this->cast_separator .
            $row->bass;
    }
}
