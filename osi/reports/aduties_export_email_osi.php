<?php
/*
*   JH 20240216 ONCUST-2677
*/

//namespace App\Utility\Reports;
use App\Reports\Tools\ReportTools;
use App\Reports\ReportSpreadsheet;


//use \App\Reports\ReportRtf;

use Cake\Datasource\ConnectionManager;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use App\Model\Table\AdateWorksTable;

class aduties_export_email_osi extends ReportSpreadsheet
{
    private $user = '';

    private $dbConn;
    private $emailResult;
    private $sheetCaption;
    private $emailCaption;
    private $emailSeparator = ';';

    private $sheet;
    private $borders_thin_all = array();
    private $borders_thin_tblr = array();
    private $borders_thin_lr = array();
    private $borders_thin_top = array();
    private $borders_thin_bottom = array();
    private $borders_thin_left = array();
    private $borders_thin_right = array();

    private $borders_medium_top = array();
    private $borders_medium_r = array();
    private $borders_medium_outside = array();
    private $borders_medium_all = array();
    private $borders_dashed_inside = array();

    function initialize()
    {
        parent::initialize();

        $this->user = $_SESSION['Auth']['User']['sh'];

        //$this->reporttools = new ReportTools_client();

        $this->dbConn = ConnectionManager::get("default");

        $repFileText = $this->getI18n();

        $this->sheetCaption = $repFileText['sheet_name'];
        $this->emailCaption = $repFileText['all_emails'];

        // $this->prepare_borders();

    }

    public function collect(array $where = [])
    {
        // Get the POST data
        $dutyIDs = $this->getRequest()->getData()['dataItemIds'];
        $dutyIDsList = implode(',', $dutyIDs);

        $collectQuery = <<<SQL
            SELECT DISTINCT
                aduties.artist_id,
                concat_ws(', ', saddresses.name1, saddresses.name2) as artist,
                saddress_numbers.number_ AS email
            FROM aduties
                INNER JOIN saddresses ON aduties.artist_id = saddresses.id
                LEFT JOIN saddress_numbers ON  aduties.artist_id = saddress_numbers.address_id
                    LEFT JOIN snumbertypes ON saddress_numbers.numbertype_id = snumbertypes.id
                WHERE aduties.id IN ($dutyIDsList)
                    AND saddress_numbers.number_ LIKE '%@%'
                    AND snumbertypes.l_email = 1
            ORDER BY saddresses.name1, saddresses.name2, saddress_numbers.number_order
            SQL;

        $this->emailResult = $this->dbConn
            ->Query($collectQuery)
            ->fetchAll('assoc');

        return $this;
    }

    public function write_sheets($view = null, $layout = null) {

        $count=1;

        $this->ospreadsheet->setActiveSheetIndex($count-1);
        $this->sheet = $this->ospreadsheet->getActiveSheet();
        // $this->sheet->getParent()->getDefaultStyle()->getAlignment()->setVertical('top');

        $this->sheet->setTitle($this->sheetCaption);

        $this->write_sheet();

    }

    function write_sheet()
    {

        $lastArtistId = 0;
        // $allEmails = [];
        $row = 4;
        foreach($this->emailResult as $email) {

            $row++;

            if ($email['artist_id'] !== $lastArtistId) {
                $lastArtistId = $email['artist_id'];
                $this->sheet->setCellValue($this->getColumnLetter(1).$row,
                    htmlspecialchars($email['artist'])
                );
            }
            $this->sheet->setCellValue($this->getColumnLetter(2).$row,
                $email['email']
            );
            // $allEmails[] = $email['email'];
        }

        $this->sheet->getCell('B1')->getStyle()->getFont()->setBold(true);
        $this->sheet->setCellValue('B1',
            htmlspecialchars($this->emailCaption)
        );

        $row += 10;
        $this->sheet->setCellValue('B3',
            '=TEXTJOIN("'.$this->emailSeparator.'",TRUE,B5:B'.$row.')'
        );

        $this->sheet->getColumnDimension('A')->setAutoSize(true);
        $this->sheet->getColumnDimension('B')->setAutoSize(true);

        $this->sheet->freezePane('C4');

    }

    function prepare_borders() {
        $this->borders_thin_tblr = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_dashed_inside = [
            'borders' => [
                'inside' => [
                    'borderStyle' => Border::BORDER_DASHED,
                ],
            ],
        ];

        $this->borders_thin_all = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_lr = [
            'borders' => [
                'vertical' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];
        $this->borders_thin_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_bottom = [
            'borders' => [
                'bottom' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_left = [
            'borders' => [
                'left' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_thin_right = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ];

        $this->borders_medium_top = [
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_r = [
            'borders' => [
                'right' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];
        $this->borders_medium_outside = [
            'borders' => [
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ];

    }

}
