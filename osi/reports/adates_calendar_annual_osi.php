<?php
/*
20221219 ONCUST-994
OSI Calendario dettagliato
*/



use App\Reports\ReportWord;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;
use App\Model\Table\AdateworkMovementsTable;
use Customer\osi\reports\ReportTools_client;
use Customer\osi\reports\instrumentation_osi;
use App\Model\Table\AdateWorksTable;
use Cake\I18n\Date;
use App\Model\Table\AdaysTable;

class adates_calendar_annual_osi extends ReportWord
{

    private $postData = null;
    private $reporttools = null;

    private $adates_selected = null;
    private $agroups = array();
    private $agroup = array();

    private $atextes = array();
    private $aconductors = array();
    private $asoloists = array();
    private $aworks = array();
    private $aworks_encore = array();
    private $adate_work = null;

    private $group_no = 0;
    private $ayears = array();
    private $duties_total = 0;
    private $duties2_total = 0;

    private $aprojects_total = array();
    private $acondsolo = array();

    private $table = null;
    private $acol_widths = array();

    private $cellRowSpan = array('vMerge' => 'restart');
    private $cellRowContinue = array('vMerge' => 'continue');

    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');
    public $styleFont_8 = array('size' => 8);
    public $styleFont_11 = array('size' => 11);


    private $styleCell_borderTop = array('borderTopSize' => 6, 'borderTopColor' => '#black');
    private $styleCell_borderBottom = array('borderBottomSize' => 6, 'borderBottomColor' => 'black');

    private $cellColSpan2 = array('gridSpan' => 2);
    private $cellColSpan4 = array('gridSpan' => 4);
    private $cellColSpan5 = array('gridSpan' => 5);

    private $styleTabs_4_75 = array();

    public $oinstrumentation = null;

    private $l_instrumentation = 0;
    private $l_movements = 0;

    /*
        protected $templates = [
            [
                'name' => 'adates_calendar_annual_osi_template',
                'file' => 'adates_calendar_annual_osi_template.php',
                'jsFile' => 'adates_calendar_annual_osi_template.js',
                'fileLocationType' => 'direct'
            ]
        ];
    */



    function initialize()
    {
        parent::initialize();

        //*-- Template File Name (x:\path\pattern.rtf)
        //$this->template_filename = CUSTOMER_REP_DIR.'xxx.docx';

        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();
        $this->styleTabs_4_75 = array('tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(4.75))));

        $this->oinstrumentation = new instrumentation_osi();
    }


    public function collect(array $where = [])
    {
        $this->postData = $this->getRequest()->getData();

        // nur Ausgewählte PDs
        $awhere = ['Adates.id IN' => $this->postData['dataItemIds']];
        $this->adates_selected = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);

        $adate_min = null;
        $adate_max = null;

        $count = 0;

        $this->ayears = array();
        foreach ($this->adates_selected as $adate) {
            if(!in_array($adate->year, $this->ayears)) {
                $this->ayears[] = $adate->year;
            }

            $count++;
            if ($count == 1) {
                $adate_min = $adate;
            }
            $adate_max = $adate;
        }

        $awhere = ["Adays.date_>='" . $adate_min->date_->format('Y-m-d')."' and Adays.date_<='" . $adate_max->date_->format('Y-m-d')."'"];

        $table = new AdaysTable();

        $adays = $table
            ->find('all')
            ->select([
                'Adays.date_',
                'Adays.weekday'
            ])
            ->where($awhere)
            ->orderAsc('Adays.date_')
            ->distinct();

        $count_groups = 0;

        $project_key_old = '#';

        $this->aprojects_total = array();


        //*** Kombinationen Day + Project vorbereiten
        foreach($adays as $aday) {
            //N 	ISO 8601 numeric representation of the day of the week 	1 (for Monday) through 7 (for Sunday)
            $l_monday = $aday->date_->format('N') == 1;

            $day_key = $aday->date_->format('Ymd');

            //&& Montag = Projektwechsel
            //if($l_monday) {
            //    $project_key_old = '#';
            //}

            $aprojects = array();
            $aday->count_dates = 0;
            $aday->count_pds = 0;

            foreach ($this->adates_selected as $adate) {


                if ($aday->date_ == $adate->date_) {
                    $aday->count_dates++;

                    if($adate->seventtype && $adate->seventtype->l_performance==1) {
                        $aday->count_pds++;
                    }

                    //$programno = (is_null($adate->programno) ? 'XXX' : $adate->programno);
                    //$programno = trim($adate->programno);
                    //$k = $adate->season_id . '_' . $adate->project_id . '_' .$programno;
                    $k = $adate->season_id . '_' . $adate->project_id;
                    //$k = $adate->project_id;

                    if (!array_key_exists($k, $this->aprojects_total)) {
                        $this->aprojects_total[$k] = array(
                            'sumduties'=>0,
                            'sumduties2'=>0,
                            'l_showed' => false,
                        );
                    }


                    if (!array_key_exists($k, $aprojects)) {
                        $aprojects[$k] = array(
                            'project_id' => $adate->project_id,
                            'season_id' => $adate->season_id,
                            'project' => $adate->sproject->name,
                            'season' => $adate->sseason->name,
                            'adates' => array()
                        );
                    }
                    $aprojects[$k]['adates'][$adate->id] = $adate;

                    //$this->aprojects_total[$k]['sumduties'] .= '#'.$adate->date_->format('d.m.Y').'='.(is_null($adate->duties) ? 0: $adate->duties);
                    $this->aprojects_total[$k]['sumduties'] += (is_null($adate->duties) ? 0: $adate->duties);
                    $this->aprojects_total[$k]['sumduties2'] += (is_null($adate->duties2) ? 0: $adate->duties2);
                    $this->duties_total += (is_null($adate->duties) ? 0: $adate->duties);
                    $this->duties2_total += (is_null($adate->duties2) ? 0: $adate->duties2);

                }
            }

            foreach ($aprojects as $project_key => $aproject) {
                //&& Projektwechsel oder Montag nach
                if ($project_key<>$project_key_old) {
                    $count_groups++;
                    $this->agroups[$count_groups]['adays'] = array();
                    //$this->agroups[$count_groups]['adates'] = array();
                    $this->agroups[$count_groups]['aproject'] = $aproject;
                    $this->agroups[$count_groups]['project_key'] = $project_key;
                    //$this->agroups[$count_groups]['sumduties'] = $project_key;

                    //$aprojects = array();
                }


                if(!in_array($aday, $this->agroups[$count_groups]['adays'])) {
                    $this->agroups[$count_groups]['adays'][$day_key] = $aday;
                }
                $l_monday = false;
                $project_key_old = $project_key;
            }

            if($aday->count_dates==0) {
                //if($l_monday || $count_groups==0) {
                //20221227 ONCUST-994
                //Du fängst jedesmal den Montag mit einem neuen Block an. Wenn Du das rausnimmst und die Zeilenabstände zwischen den Tagen rausnimmst, passt es.
                if($count_groups==0) {
                    $count_groups++;
                }
                $l_monday = false;
                $this->agroups[$count_groups]['adays'][$day_key] = $aday;
                //$this->agroups[$count_groups]['aproject'] = array();
            }


        }
        return $this;
    }

    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    function fill_phpWord()
    {
        $this->phpWord->setDefaultFontSize(9);
        $this->phpWord->setDefaultFontName('Arial');
        //'spacing'    => (120*0.75), //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
        $this->phpWord->setDefaultParagraphStyle(
            array(
                'align' => 'left',
                'spaceAfter' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(0),
                'spacing' => 0, //Space between lines in twip. If spacingLineRule is auto, 240 (height of 1 line) will be added, so if you want a double line height, set this to 240.
            )
        );

        $section = $this->phpWord->addSection(
            array(
                'headerHeight' => Converter::cmToTwip(1.75),
                'footerHeight' => Converter::cmToTwip(1.25),
                'marginLeft' => Converter::cmToTwip(2),
                'marginRight' => Converter::cmToTwip(1),
                'marginTop' => Converter::cmToTwip(1.5),
                'marginBottom' => Converter::cmToTwip(1.6))
        );

        $sectionStyle = $section->getStyle();
        $sectionStyle->setOrientation($sectionStyle::ORIENTATION_PORTRAIT);

        $header = $section->addHeader();

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(9);
        $this->acol_widths[] = Converter::cmToTwip(9);


        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.01),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP
        );
        //'cellMarginBottom' => Converter::cmToTwip(0.1),

        $table = $header->addTable($tableProperties);

        $table->addRow();

        $cell = $table->addCell($this->acol_widths[0]);
        $cell->addImage(CUSTOMER_REP_DIR . 'osi_logo_1.png',
            array(
                'width' => Converter::cmToPoint(2.04),
                'align' => 'left',
            ));
        $cell = $table->addCell($this->acol_widths[1]);
        $cell->addImage(CUSTOMER_REP_DIR . 'osi_logo_2.png',
            array(
                'width' => Converter::cmToPoint(2.86),
                'align' => 'right'
            ));

/*        $styleTabs_17 = array('tabs' => array(new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(17))));

        $textrun = $header->addTextRun();
        $textrun->addImage(CUSTOMER_REP_DIR . 'osi_logo_1.png', array('width' => Converter::cmToPoint(2.04), 'align' => 'left'));
        $textrun->addText("\t");
        $textrun->addImage(CUSTOMER_REP_DIR . 'osi_logo_2.png', array('width' => Converter::cmToPoint(2.86), 'align' => 'right'));
        */
        $header->addTextBreak();
        //$header->addField("PAGE", array());

        $this->show_header($section);

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(4);
        $this->acol_widths[] = Converter::cmToTwip(4.35);
        $this->acol_widths[] = Converter::cmToTwip(5);
        $this->acol_widths[] = Converter::cmToTwip(2.5);
        $this->acol_widths[] = Converter::cmToTwip(2.75);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'white',
            'cellMarginLeft' => Converter::cmToTwip(0.1),
            'cellMarginRight' => Converter::cmToTwip(0.01),
            'width' => $w_table,
            'unit' => TblWidth::TWIP,
            'layout' => \PhpOffice\PhpWord\Style\Table::LAYOUT_FIXED
        );
        //'cellMarginBottom' => Converter::cmToTwip(0.1),

        $styleCell = array();
        $styleFont = array();


        $this->table = $section->addTable($tableProperties);

        $count = 0;
        foreach($this->agroups as $this->group_no => $this->agroup) {
            $this->show_group();
        }

        // Dienste
        $this->table->addRow();
        $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));

        $this->table->addRow();
        $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[2] + $this->acol_widths[3], $this->cellColSpan2);
        $cell->addText(htmlspecialchars('Totale servizi suonati:'), array_merge($styleFont, $this->styleFont_bold), array('align' => 'right'));

        $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));
        $cell->addText(htmlspecialchars($this->duties_total), array_merge($styleFont, $this->styleFont_bold));

        $this->table->addRow();
        $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell = $this->table->addCell($this->acol_widths[2] + $this->acol_widths[3], $this->cellColSpan2);
        $cell->addText(htmlspecialchars('Totale servizi tecnici:'), array_merge($styleFont, $this->styleFont_bold), array('align' => 'right'));

        $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));
        $cell->addText(htmlspecialchars($this->duties2_total), array_merge($styleFont, $this->styleFont_bold));

    }

    function show_header($section)
    {

        $this->acol_widths = array();

        $this->acol_widths[] = Converter::cmToTwip(18);

        $w_table = 0;
        foreach ($this->acol_widths as $col_width) {
            $w_table += $col_width;
        }

        $this->tableProperties = array(
            'borderSize' => 6,
            'borderColor' => 'black',
            'cellMarginTop' => Converter::cmToTwip(0.5),
            'cellMarginBottom' => Converter::cmToTwip(0.5),
            'width' => $w_table,
            'unit' => TblWidth::TWIP
        );
        //'cellMarginBottom' => Converter::cmToTwip(0.1),

        $styleCell = array();
        $styleFont = array();


        $table = $section->addTable($this->tableProperties);
        $table->addRow();

        $cell = $table->addCell($this->acol_widths[0], array_merge($styleCell));
        $cYears = implode(', ', $this->ayears);
        $cell->addText(htmlspecialchars('ORCHESTRA DELLA SVIZZERA ITALIANA CALENDARIO ' . $cYears), array_merge($styleFont, $this->styleFont_bold, $this->styleFont_11), array('align' => 'center'));
        $cell->addText(htmlspecialchars('Il presente calendario può essere oggetto di modifiche di dettaglio'), array_merge($styleFont, $this->styleFont_bold), array('align' => 'center'));
        $cell->addText(htmlspecialchars('Prima di prendere qualsiasi impegno extra, verificare con la direzione le date indicate'), array_merge($styleFont, $this->styleFont_bold), array('align' => 'center'));
        $cell->addText(htmlspecialchars('Stampato il '.date('d.m.Y')), array_merge($styleFont, $this->styleFont_bold), array('align' => 'center'));

        $section->addText('');
        $section->addText('');
    }


    function show_group() {

        $this->table->addRow();

        $styleCell = array();
        $styleFont = array();

        $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowSpan, $this->styleCell_borderTop));

        foreach($this->agroup['adays'] as $day_key=>$aday) {
            //sabato, 17 dicembre
            $cday =
                mb_strtolower(
                    $this->reporttools->getWeekday($aday->date_). ', '.
                    $aday->date_->format('d'). ' '.
                    $this->reporttools->getMonthname($aday->date_)
                );
            //$cday = $this->group_no.'#'.$cday;
            $style = array();
            if($aday->count_dates>0) {
                /*
                const FGCOLOR_YELLOW = 'yellow';
                const FGCOLOR_LIGHTGREEN = 'green';
                const FGCOLOR_CYAN = 'cyan';
                const FGCOLOR_MAGENTA = 'magenta';
                const FGCOLOR_BLUE = 'blue';
                const FGCOLOR_RED = 'red';
                const FGCOLOR_DARKBLUE = 'darkBlue';
                const FGCOLOR_DARKCYAN = 'darkCyan';
                const FGCOLOR_DARKGREEN = 'darkGreen';
                const FGCOLOR_DARKMAGENTA = 'darkMagenta';
                const FGCOLOR_DARKRED = 'darkRed';
                const FGCOLOR_DARKYELLOW = 'darkYellow';
                const FGCOLOR_DARKGRAY = 'darkGray';
                const FGCOLOR_LIGHTGRAY = 'lightGray';
                const FGCOLOR_BLACK = 'black';
                */

                //$style = array('fgColor' => \PhpOffice\PhpWord\Style\Font::FGCOLOR_LIGHTGRAY);
                //$style = array('bgColor' => \PhpOffice\PhpWord\Style\Font::FGCOLOR_LIGHTGRAY);
                $style = array('bgColor' => 'BFBFBF');
            }

            //20240425
            //jetzt kommt die Präzisierung zurück, dass dass dunklere Grau nur gemeint war für die Tage mit mind. 1 l_performance Termin,
            //So würde man direkt sehen, an welchem Tag das Konzert ist, so:

            if($aday->count_pds>0) {
                $style = array('bgColor' => 'A6A6A6');
                //$style = array('fgColor' => \PhpOffice\PhpWord\Style\Font::FGCOLOR_LIGHTGRAY);
            }

            $cday .= "\t";

            $styleTabs = array('tabs' => array(new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(4))));
            //$cday = sizeof($this->agroup['adays']).'#'.$cday.'#'.$day_key;
            $cell->addText(htmlspecialchars($cday), $style, $styleTabs);
        }

        $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell, $this->cellColSpan4, $this->styleCell_borderTop));
        $cell->addText(htmlspecialchars($this->agroup['aproject']['project']), array_merge($styleFont));

        $l_showed = false;
        if(isset($this->aprojects_total[$this->agroup['project_key']]['l_showed'])) {
            $l_showed = $this->aprojects_total[$this->agroup['project_key']]['l_showed'];
        }
        if(!$l_showed) {

            //XXX
            $this->table->addRow();

            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
            $cell = $this->table->addCell(null, array_merge($styleCell, $this->cellColSpan4));

            $this->prepareProjectInfo();
            $this->showProjectInfo();
            $this->aprojects_total[$this->agroup['project_key']]['l_showed'] = true;
        }
    }

    function showProjectInfo()
    {
        $styleCell = array();


        //$fpd_id = $this->reporttools->getFPD_id($this->agroup['aproject']['season_id'], $this->agroup['aproject']['project_id'], $this->agroup['aproject']['programno'], true);
        // FPD
        //$awhere = ['Adates.id='.$fpd_id];
        //$afpds = $this->reporttools->prepareData_selected($this->postData, $this->model, $awhere);
        $styleFont = array();
        //foreach($afpds as $afpd) {
            // Text
            foreach($this->atextes as $text) {
                $this->table->addRow();

                $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
                $cell = $this->table->addCell(null, array_merge($styleCell, $this->cellColSpan4));
                $cell->addText(htmlspecialchars($text), array_merge($styleFont));
            }


            // 20240312
            // Align the directors and soloists column on the same row
            //Conductor

            //if($conductor>'') {
                $this->table->addRow();
                $cell0 = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
                $cell1 = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));

                foreach ($this->aconductors as $conductoraddress) {
                    $textrun = $cell1->addTextRun();
                    $textrun->addText(htmlspecialchars('Mo. '), array_merge($styleFont));
                    $conductor = trim($conductoraddress->name2 . ' ' . $conductoraddress->name1);
                    $textrun->addText(htmlspecialchars($conductor), array_merge($styleFont, $this->styleFont_bold));
                }


                $cell2 = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
                $cell34 = $this->table->addCell(null, array_merge($styleCell, $this->cellColSpan2));

            //$cell = $this->table->addCell(null, array_merge($styleCell, $this->cellColSpan4));
            //}

            // Soloists

            $count_soloists = 0;
            foreach($this->asoloists as $arow) {
                $cell2->addText(htmlspecialchars(trim($arow->saddress->name2 . ' ' . $arow->saddress->name1)), array_merge($styleFont, $this->styleFont_bold), array('align' => 'right'));
                $cell34->addText(htmlspecialchars($arow->sinstrinstrument->name), array_merge($styleFont, $this->styleFont_italic));
                /*
                $count_soloists++;
                if($count_soloists>1) {
                    $this->table->addRow();
                    $cell0 = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
                    $cell1 = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
                    $cell2 = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
                    $cell34 = $this->table->addCell(null, array_merge($styleCell, $this->cellColSpan2));
                }

                $cell2->addText(htmlspecialchars(trim($arow->saddress->name2 . ' ' . $arow->saddress->name1)), array_merge($styleFont, $this->styleFont_bold), array('align' => 'right'));
                $cell34->addText(htmlspecialchars($arow->sinstrinstrument->name), array_merge($styleFont, $this->styleFont_italic));
                */
            }

            $this->table->addRow();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));

            //*** Program
            //$duration_total = 0;
            //und die l_encore muss in der Werkreihenfolge immer als letztes stehen

            foreach($this->aworks as $this->adate_work) {
                $this->showAdate_work();
            }

            foreach($this->aworks_encore as $this->adate_work) {
                $this->showAdate_work();
            }

            $this->table->addRow();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[2], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[3], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));


            //20231229
            //3. bitte Feldbezeichnungen umändern:  "sevvizi", soll sein “S. suonati :"
            //4. unterhalb bitte “S. tecnici" hinzufügen (idem wie ADATES_CALENDARIO_DETTAGLIATO_OSI.XREP und Wert aus dates_duration_2 ausgeben
            //5. am Ende totale umbenennen/bzw. einfügen
            // Dienste
            $this->table->addRow();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[2] + $this->acol_widths[3], $this->cellColSpan2);
            //$cell->addText(htmlspecialchars('sevvizi:'), array_merge($styleFont), array('align' => 'right'));
            $cell->addText(htmlspecialchars('S. suonati:'), array_merge($styleFont), array('align' => 'right'));

            $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));
            $sumduties = 0;
            $sumduties_2 = 0;
            if(isset($this->aprojects_total[$this->agroup['project_key']]['sumduties'])) {
                $sumduties = $this->aprojects_total[$this->agroup['project_key']]['sumduties'];
                $sumduties2 = $this->aprojects_total[$this->agroup['project_key']]['sumduties2'];
            }
            //$sumduties = $this->agroup['aproject']['project_id'];
            //$this->agroup['aproject']['project_key'];

            $cell->addText(htmlspecialchars($sumduties), array_merge($styleFont));

            // dates_duration_2
            $this->table->addRow();
            $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
            $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
            $cell = $this->table->addCell($this->acol_widths[2] + $this->acol_widths[3], $this->cellColSpan2);
            //$cell->addText(htmlspecialchars('sevvizi:'), array_merge($styleFont), array('align' => 'right'));
            $cell->addText(htmlspecialchars('S. tecnici:'), array_merge($styleFont), array('align' => 'right'));

            $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell));
            $cell->addText(htmlspecialchars($sumduties2), array_merge($styleFont));
        //}
    }

    function showAdate_work() {
        $styleCell = array();
        $styleFont = array();

        $bd = ($this->adate_work->swork->scomposer->birthyear>'' ? $this->adate_work->swork->scomposer->birthyear:'');
        $bd .= ($this->adate_work->swork->scomposer->deathyear>'' ? ' - '.$this->adate_work->swork->scomposer->deathyear:'');

        $composer = trim($this->adate_work->swork->scomposer->firstname.' '.$this->adate_work->swork->scomposer->lastname);
        $catalog = $this->adate_work->swork->catalog;


        //20240312 OCCUST-2765
        //$title = ($this->adate_work->title2>'' ? $this->adate_work->title2 : $this->adate_work->swork->title1);

        //20240422 ONCUST-2676
        // Zugaben bitte kennzeichnen mit “BIS :” vor dem Komponistennamen
        //20240424
        //bitte “BIS :” vor dem Komponistennamen

        $title = $this->adate_work->swork->title1;

        if($this->adate_work->l_encore == 1) {
            $composer = 'BIS: '.$composer;
        }

        $instrumentation = '';
        //bitte keine instrumentation anzeigen (das war in der letzten Version nicht drin).
        //20240425 ONCUST-2676
        //können wir die Instrumentierung doch wieder hinzunehmen? Ich dachte, die wäre nie da gewesen, weil in Version vorher nicht dabei. aber anscheinend war es doch gewollt. Danke!

        //if($afpd->l_print_details==1) {
            $this->oinstrumentation->datework_id = $this->adate_work->id;
            $this->oinstrumentation->getInstrumentation();

            if($this->oinstrumentation->instrumentation>'') {
                $instrumentation = ' ('.$this->oinstrumentation->instrumentation.')';
            }
        //}



        if ($this->adate_work->swork->l_intermission == 1) {
            //20240312 OCCUST-2765
            //l_pause als Werk nicht anzeigen
            return;

            $composer = '';
        }

        $duration = $this->adate_work->duration;
        $hour = (int)substr($duration,0,2);
        $minute = (int)substr($duration,3,2);
        $sec = (int)substr($duration,6,2);
        $nduration = $hour*60 + $minute;
        //$duration_total+=$nduration;


        $cduration = '('.$nduration . "'".($sec>0 ? $sec.'"':'').')';

        $this->table->addRow();

        $cell = $this->table->addCell($this->acol_widths[0], array_merge($styleCell, $this->cellRowContinue));
        $cell = $this->table->addCell($this->acol_widths[1], array_merge($styleCell));
        $cell->addText(htmlspecialchars($composer), array_merge($styleFont));
        $cell = $this->table->addCell($this->acol_widths[2]+$this->acol_widths[3], array_merge($styleCell, $this->cellColSpan2));
        $cell->addText(htmlspecialchars($title.' '.$cduration), array_merge($styleFont, $this->styleFont_italic));
        if($instrumentation>'') {
            $cell->addText(htmlspecialchars($instrumentation), array_merge($styleFont, $this->styleFont_italic));
        }
        $cell = $this->table->addCell($this->acol_widths[4], array_merge($styleCell, $this->cellColSpan2));
        $cell->addText(htmlspecialchars($catalog), array_merge($styleFont, $this->styleFont_italic));
    }

    function prepareProjectInfo() {

        $aproject = $this->agroup['aproject'];
        $project_id = $aproject['project_id'];
        $season_id = $aproject['season_id'];

        $this->atextes = array();
        $this->aconductors = array();
        $this->asoloists = array();
        $this->aworks = array();
        $this->aworks_encore = array();

        foreach($this->adates_selected as $adate) {
            $l_performance = ($adate->seventtype ? $adate->seventtype->l_performance : 0);

            if(
                !($adate->season_id == $season_id)
                ||
                !($adate->project_id == $project_id)
                ||
                $l_performance == 0
            ) {continue;}



            if(!empty($adate->text)) {
                if(!in_array($adate->text, $this->atextes)) {
                    $this->atextes[] = $adate->text;
                }
            }

            foreach($adate->adate_works as $adate_work) {
                if($adate_work->l_encore==1) {
                    if(!key_exists($adate_work->work_id, $this->aworks_encore)) {
                        $this->aworks_encore[$adate_work->work_id] = $adate_work;
                    }
                } else {
                    if(!key_exists($adate_work->work_id, $this->aworks)) {
                        $this->aworks[$adate_work->work_id] = $adate_work;
                    }
                }
            }


            if($adate->conductor_id>0 && $adate->conductoraddress && !key_exists($adate->conductor_id, $this->aconductors)) {
                $this->aconductors[$adate->conductor_id] = $adate->conductoraddress;
            }

            // Soloists
            $where = 'AdateWorks.date_id='.$adate->id;

            $AdateworkSoloistsTable = TableRegistry::getTableLocator()->get('AdateworkSoloists');


            $arows = $AdateworkSoloistsTable
                ->find('all')
                ->select([
                    'AdateworkSoloists.artist_order2', 'AdateworkSoloists.artist_id',
                    'AdateworkSoloists.notes',
                    'Saddresses.id', 'Saddresses.name1', 'Saddresses.name2', 'Saddresses.name5','Saddresses.notes',
                    'Sinstrinstruments.name'
                ])
                ->contain([
                    'AdateWorks',
                    'Saddresses',
                    'Sinstrinstruments'
                ])
                ->where($where)
                ->distinct();

            foreach($arows as $arow) {

                $k_soloist = $arow->artist_id.'#'.$arow->instrument_id;
                if($arow->artist_id && $arow->saddress && !key_exists($k_soloist, $this->asoloists)) {
                    $this->asoloists[$k_soloist] = $arow;
                }
            }

        }
    }
}
