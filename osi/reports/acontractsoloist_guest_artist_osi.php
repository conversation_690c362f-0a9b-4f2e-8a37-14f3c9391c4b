<?php

use App\Reports\ReportWord;
use Cake\Core\Configure;
use Cake\Database\Expression\QueryExpression;
use Cake\I18n\Time;
use Cake\ORM\Query;
use Cake\ORM\TableRegistry;
use PhpOffice\PhpWord\Shared\Converter;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\Element\TextRun;

use Customer\osi\reports\ReportTools_client;
use Customer\osi\reports\instrumentation_osi;


/**
 * Class acontractsoloist_guest_artist_osi
 * 20231229 ONCUST-2678
 */

class acontractsoloist_guest_artist_osi extends ReportWord
{
    public $caption = '';
    private $postData = array();

    public $acontracts = null;
    public $acontract = null;
    public $adates_contract = array();
    public $apds_contract = array();
    public $arehearsals_contract = array();
    public $aprogram = array();
    public $asoloists = array();
    public $adresses = array();
    public $aconductors = array();
    public $l_CHF = false;
    public $acurrencies = array();
    public $adates_fee = array();

    public $aclauses_HON = array();
    public $aclauses_REH = array();
    public $aclauses_OTHER = array();

    public $contract_id = 0;
    public $artist_id = 0;
    public $acol_widths = array();

    public $styleIdent_2_75 = array();
    //public $styleSpaceAfter_12 = array();
    public $styleFont = array();
    public $styleFont_bold = array('bold' => true);
    public $styleFont_italic = array('italic' => true);
    public $styleFont_underline = array('underline' => 'single');


    public $nationality = '';
    public $taxno = '';
    public $taxoffice = '';

    public $sex = '';

    public $reporttools = null;
    public $oinstrumentation = null;


    public $honorar = 0;
    public $honorar_iw = '';

    public $l_conductor = false;
    //private $report_type = 'artist';
    //private $language = 'en';

    /*********************************************/
    function initialize()
    {
        parent::initialize();



        $this->user = $_SESSION['Auth']['User']['sh'];

        $this->reporttools = new ReportTools_client();

        /*$this->styleTabs_1_46 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.46)))
        );
        */
        $this->styleTabs_1_5 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.5)))
        );

        $this->styleTabs_1_65 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1.65)))
        );

        $this->styleTabs_works = array(
            'tabs' => array(
                new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(3)),
                new \PhpOffice\PhpWord\Style\Tab('right', Converter::cmToTwip(9.6))
            )
        );

        $this->styleIdent_2_75 = array(
            'tabs' => array(new \PhpOffice\PhpWord\Style\Tab('left', Converter::cmToTwip(1))),
            'spaceBefore' => \PhpOffice\PhpWord\Shared\Converter::pointToTwip(12),
            'indentation' => array(
                'left' => Converter::cmToTwip(3.5),
                'hanging' => Converter::cmToTwip(2.75)
            )
        );

        $this->oinstrumentation = new instrumentation_osi();
    }

    public function collect(array $where = [])
    {
        ini_set('pcre.backtrack_limit', 999999999);

        // Get the POST data
        $this->postData = $this->getRequest()->getData();


        /*$this->report_type = $this->postData['formData']['report_type'];
        $this->language = $this->postData['formData']['language'];

        if(!in_array($this->report_type, array('artist', 'agent'))) {
            $this->report_type = 'artist';
        }

        if(!in_array($this->language, array('en', 'esp'))) {
            $this->language = 'en';
        }
        */

        $this->template_filename = CUSTOMER_REP_DIR . 'acontractsoloist_guest_artist_osi.docx';

        // Read the Report for the text entries in the XML description file
        $this->report = TableRegistry::getTableLocator()->get('opasreports')->get($this->postData['id']);


        // Collect the selected contracts with all required data
        $this->acontracts = $this->reporttools->prepareContracts_selected($this->postData, $this->model);

        return $this;
    }

    // Create the file and return the filename
    /*********************************************/
    function save_phpWord()
    {
        $fileName = $this->_createFileName();

        $path = ROOT_REP_IN_DIR . $fileName . $this->getFileExtension();
        //echo $path;
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($this->phpWord, 'Word2007');
        $objWriter->save($path);

        return $fileName . $this->getFileExtension();
    }

    /*********************************************/
    function fill_search_patterns()
    {
        $today = date("d.m.Y");

        //print_r($this->acontracts);


        $contracts_no = 0;
        foreach($this->acontracts as $this->acontract) {
            $contracts_no++;
        }
        //$contracts_no = sizeof($this->acontracts);

        $this->templateProcessor->cloneBlock('contracts', $contracts_no, true, true);





        $i=0;
        foreach($this->acontracts as $this->acontract) {
            $i++;

            $this->prepareData_Contract();

            $this->contract_id = $this->acontract->id;
            $this->artist_id = $this->acontract->artist_id;


            //$pagebreak = new PhpOffice\PhpWord\Element\TextRun();
            if($i<$contracts_no) {
                $pagebreak = '<w:br w:type="page"/>';

            } else {
                $pagebreak = '';
            }

            $season = ($this->acontract->sseason ? $this->acontract->sseason->code : '');
            $project = ($this->acontract->sproject ? $this->acontract->sproject->name : '');
            $contractgroup = ($this->acontract->scontractgroup ? $this->acontract->scontractgroup->name : '');
            $job = ($this->acontract->sjob ? $this->acontract->sjob->name : '');


            $artist_name = '';
            if($this->acontract->artistaddress) {
                $artist_name = trim($this->acontract->artistaddress->name2 . ' ' . $this->acontract->artistaddress->name1);
            }

            $agent_name = '';
            if($this->acontract->agentaddress) {
                $agent_name = trim($this->acontract->agentaddress->name1);
            }

            $contract_no = $this->acontract->contract_no;
            if(!empty($contract_no)) {
                $contract_no = '('.$contract_no.') ';
            }

            $instrument = htmlspecialchars(($this->acontract->sinstrinstrument ? $this->acontract->sinstrinstrument->name : ''));

            $this->l_conductor = strpos(mb_strtolower($instrument), 'conductor') !== false;
            foreach($this->acontract->acontract_dates as $acontract_date) {
                if($acontract_date->adate->conductor_id == $this->acontract->artist_id) {
                    $this->l_conductor = true;
                }
            }

            $accountholder = '';
            $accountno = '';
            $bank = '';
            $bank_address = '';
            $iban = '';
            $swift = '';


            //print_r($this->acontract->artistaddress->saddress_persdata);die;


            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0])) {
                $accountholder = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->accountholder;
                $accountno = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->accountno;
                $iban = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->iban;
                $swift = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->swift;

                if($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank) {
                    $bank = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->bankname;
                    $swift = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->swift;
                }
            }

            //print_r($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]);
            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress)) {
                $saddress_bank = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_banks[0]->sbank->saddress;
                $bank = $saddress_bank->name1;

                $bank_address = $saddress_bank->street;
                if (!empty($saddress_bank->zipcode) || !empty($saddress_bank->place)) {
                    $bank_address .= ', ';
                    $bank_address .= trim($saddress_bank->zipcode . ' ' . $saddress_bank->place);
                }
            }

            $this->nationality = '';
            $passportno = '';
            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry)) {
                $this->nationality = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry->nationality;
                if(empty($this->nationality)) {
                    $this->nationality = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->scountry->name;
                }
            }

            if(isset($this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0])) {
                $passportno = $this->acontract->artistaddress->saddress_persdata->saddresspersdata_passports[0]->passportno;
            }

            $this->taxno = ($this->acontract->artistaddress->saddress_persdata ? $this->acontract->artistaddress->saddress_persdata->taxno : '');
            $this->taxoffice = ($this->acontract->artistaddress->saddress_persdata ? $this->acontract->artistaddress->saddress_persdata->taxoffice : '');

            $taxno_agent = ($this->acontract->agentaddress && $this->acontract->agentaddress->saddress_persdata ? $this->acontract->agentaddress->saddress_persdata->taxno : '');



            //$this->templateProcessor->setValue('fullname#'.$i, $this->acontract->instrument_id.'#'.$this->acontract->sinstrinstrument->id.'# project='.$this->acontract->project_id.'#'.$this->acontract->sproject->id);

            $this->templateProcessor->setValue('season#'.$i, $season);
            $this->templateProcessor->setValue('project#'.$i, $project);
            $this->templateProcessor->setValue('job#'.$i, $job);

            $this->templateProcessor->setComplexBlock('artist_agent#'.$i, $this->getArtist_Agent());
            $this->templateProcessor->setValue('artist_address#'.$i, $this->getArtist_address());
            $this->templateProcessor->setValue('agent_address#'.$i, $this->getAgent_address());

            //$this->templateProcessor->setComplexBlock('date_long#'.$i, $this->getDatelong());
            $this->templateProcessor->setValue('date#'.$i, date('d.m.Y'));


            $this->templateProcessor->setValue('artistname#'.$i, $artist_name);
            $this->templateProcessor->setValue('agent_name#'.$i, $agent_name);

            $this->templateProcessor->setValue('contract_no#'.$i, $contract_no);
            $this->templateProcessor->setValue('instrument#'.$i, $instrument);
            $this->templateProcessor->setValue('nationality#'.$i, htmlspecialchars($this->nationality));
            $this->templateProcessor->setValue('passportno#'.$i, $passportno);
            $this->templateProcessor->setValue('passportno_2#'.$i, $passportno);
            $this->templateProcessor->setValue('contractgroup#'.$i, $contractgroup);
            $this->templateProcessor->setValue('taxno#'.$i, htmlspecialchars($this->taxno));
            $this->templateProcessor->setValue('taxoffice#'.$i, htmlspecialchars($this->taxoffice));
            $this->templateProcessor->setValue('taxno_agent#'.$i, htmlspecialchars($taxno_agent));


            $this->templateProcessor->setValue('accountholder#'.$i, $accountholder);
            $this->templateProcessor->setValue('bank#'.$i, $bank);
            $this->templateProcessor->setValue('bank_address#'.$i, $bank_address);

            $this->templateProcessor->setValue('accountno#'.$i, $accountno);
            $this->templateProcessor->setValue('iban#'.$i, $iban);
            $this->templateProcessor->setValue('swift#'.$i, $swift);

            $this->templateProcessor->setComplexBlock('concerts#'.$i, $this->getConcerts());
            $this->templateProcessor->setComplexBlock('conductors#'.$i, $this->getConductors());
            $this->templateProcessor->setComplexBlock('soloists#'.$i, $this->getSoloists());


            $this->templateProcessor->setValue('firstreh#'.$i, $this->getFirstReh());
            //$this->templateProcessor->setComplexBlock('rehearsals#'.$i, $this->getRehearsals());

            $this->templateProcessor->setComplexBlock('program#'.$i, $this->getProgram());

            $this->templateProcessor->setComplexBlock('clauses_hon#'.$i, $this->getClauses_HON());
            $this->templateProcessor->setValue('honorar_curr_text#'.$i, $this->getHonorar_curr_text());
            $this->templateProcessor->setComplexBlock('honorar#'.$i, $this->getHonorar());
            $this->templateProcessor->setValue('travel#'.$i, $this->getTravel());
            $this->templateProcessor->setComplexBlock('concerts_fee#'.$i, $this->getConcerts_fee());

            $this->templateProcessor->setValue('hotel#'.$i, $this->getHotel());


            $this->templateProcessor->setComplexBlock('clauses_oth_arr#'.$i, $this->getClauses_Oth_arr());

            $adates = array();
            foreach($this->acontract->acontract_dates as $acontract_date) {
                $adates[] = $acontract_date->adate;
            }
            $minmax = $this->getMinMaxDays($adates);
            $this->templateProcessor->setValue('minmax#'.$i, $minmax);


            $text_ausland = '';
            if($this->acontract->l_logic_1==1) {
                $text_ausland = 'Vom Gesamthonorar werden 15% Ausländersteuer und hieraus 5,5% Solidaritätszuschlag einbehalten.';
            }

            $this->templateProcessor->setValue('text_ausland#'.$i, htmlspecialchars($text_ausland));


            //$this->templateProcessor->setComplexBlock('payment#'.$i, $this->getPayment());

            $this->templateProcessor->setValue('pageBreak#'.$i, $pagebreak);
        }

    }

    function prepareData_Contract() {

        $this->sex = $this->acontract->artistaddress->saddress_persdata->sex;


        $this->adates_contract = array();
        $this->apds_contract = array();
        $this->arehearsals_contract = array();
        $this->aprogram = array();
        $this->aconductors = array();
        $this->asoloists = array();
        $this->adresses = array();

        $this->aclauses_HON = array();
        $this->aclauses_REH = array();
        $this->aclauses_OTHER = array();

        foreach($this->acontract->acontract_clauses AS $aclause) {
            switch(true) {
                case strtoupper($aclause->scontractclause->scontractclausetype->code) == '§HON':
                    $this->aclauses_HON[] = $aclause;
                    break;
                case strtoupper($aclause->scontractclause->scontractclausetype->code) == '§REH':
                    $this->aclauses_REH[] = $aclause;
                    break;
                default:
                    $this->aclauses_OTHER[] = $aclause;
            }
        }

        foreach($this->acontract->acontract_dates as $acontract_date) {
            $adate = $acontract_date->adate;
            $date_id = $adate->id;

            $this->adates_contract[$date_id] = $adate;

            if($adate->seventtype->l_performance==1) {
                $this->apds_contract[$date_id] = $adate;

                foreach ($adate->adate_works as $adate_work) {
                    if(!array_key_exists($adate_work->work_id, $this->aprogram)) {
                        $this->aprogram[$adate_work->work_id] = $adate_work;
                    }
                }

                if($adate->dress_id>0 and !array_key_exists($adate->dress_id, $this->adresses)) {
                    $this->adresses[$adate->adresses] = $adate->adress;
                }
            } else {
                $this->arehearsals_contract[$date_id] = $adate;
            }

            if(!array_key_exists($adate->conductor_id, $this->aconductors)) {
                $conductor = $this->reporttools->getLongName($adate->conductoraddress->name2, '', $adate->conductoraddress->name1);
                $this->aconductors[$adate->conductor_id] = array(
                    'artist_id'=>$adate->conductor_id,
                    'name'=>$conductor
                );
            }
            //Soloists
            $adatework_soloists = $this->reporttools->getSoloists($date_id);
            foreach ( $adatework_soloists as $adatework_soloist) {
                $k=$adatework_soloist->artist_id.'_'.$adatework_soloist->instrument_id;

                $instrument = ($adatework_soloist->sinstrinstrument ? $adatework_soloist->sinstrinstrument->name : '');
                $name = $this->reporttools->getLongName($adatework_soloist->saddress->name2, '', $adatework_soloist->saddress->name1);
                //$name .= ($instrument>'' ? ', ' : '').$instrument;

                $name_order = $adatework_soloist->saddress->name1.','.$adatework_soloist->saddress->name2;
                if(!array_key_exists($k, $this->asoloists)) {
                    $this->asoloists[$k] = array(
                        'artist_id'=>$adatework_soloist->artist_id,
                        'name'=>$name,
                        'name_order' => $name_order,
                        'instrument'=>$instrument
                    );
                }
            }
        }

        // 20240212
        //4. Gage: Kostenartengruppe Code CaD oder CaS (wie das mit € oder CHF ist, muss ich noch klären),
        // bei Gagen für mehrere Konzerte diese einzeln listen und mit Datum versehen und den Satz s. Vorlage dafür nutzen; wenn kein zugeordnetes Datum, den Satz löschen.

        $this->acurrencies = array();
        $this->adates_fee = array();
        $this->l_CHF = true;
        foreach($this->acontract->aexpenses as $aexpense) {
            $expensetypegroup_code = '';
            if($aexpense->sexpensetype && $aexpense->sexpensetype->sexpensetypegroup) {
                $expensetypegroup_code = $aexpense->sexpensetype->sexpensetypegroup->code;
            }

            if(!(in_array(mb_strtoupper($expensetypegroup_code), array(mb_strtoupper('CaD'), mb_strtoupper('CaS'))))) {
                continue;
            }

            $currency = ($aexpense->scurrency ? $aexpense->scurrency->code : '');
            if(empty($currency)) {
                $currency = '';
            }

            if(!($currency=='CHF')) {
                $this->l_CHF = false;
            }

            if(!array_key_exists($currency, $this->acurrencies)) {
                $this->acurrencies[$currency] = 0;
            }
            $namount = $aexpense->number_ * $aexpense->amount;
            $this->acurrencies[$currency] += $namount;

            if($aexpense->adate) {
                $adate = $aexpense->adate;
                if($adate->seventtype && $adate->seventtype->l_performance==1) {
                    $cdate = $adate->date_->format(Configure::read('Formats.date'));
                    $eventtype = $adate->seventtype->name;
                    $location = '';
                    if($adate->locationaddress){
                        //$location = $adate->locationaddress->place;
                        $location .= (!empty($location) && !empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->name1;
                    }

                    $this->adates_fee[$adate->id] = array(
                        'camount' => $currency.' '.number_format($namount, 2, ',', '.').
                            ' for the '.$eventtype.' at '.$cdate.' in '.$location
                    );
                }
            }
        }
    }

    function getArtist_Agent() {

        /*
        <first name> <last name>, <address>,
        referred to hereafter also as the Artist,
        represented by <name, surname and address of the agent or business name of the agency representing the Artist>;
        */

        $textrun = new TextRun(array('align' => 'left'));

        $artist_address = '';

        //saddresses.street, saddresses.zipcode_poboxsaddresses.pobox.

        $artist_name = '';
        $artist_address = '';

        $agent_name = '';
        $agent_address = '';
        if($this->acontract->artistaddress) {

            $artist_name = trim($this->acontract->artistaddress->name2.' '.$this->acontract->artistaddress->name1);

            $country = '';
            if($this->acontract->artistaddress->scountry) {
                $country = $this->acontract->artistaddress->scountry->name;
            }


            if ($this->acontract->artistaddress->street > '') {
                $artist_address = $this->acontract->artistaddress->street;
            }

            if ($this->acontract->artistaddress->pobox > '') {
                $artist_address .= ($artist_address>'' ? ', ' : '').$this->acontract->artistaddress->pobox;
            }

            if (!empty($this->acontract->artistaddress->zipcode) || !empty($this->artistaddress->artistaddress->place)) {
                $artist_address .= ($artist_address>'' ? ', ' : '').trim($this->acontract->artistaddress->zipcode . ' ' . $this->acontract->artistaddress->place);
            }

            if ($country > '') {
                $artist_address .= ($artist_address>'' ? ', ' : '').$country;
            }

        }

        if($this->acontract->agentaddress) {

            $agent_name = $this->acontract->agentaddress->name1.($this->acontract->agentaddress->name2>'' ? ', ' : '').$this->acontract->agentaddress->name2;

            $country = '';
            if($this->acontract->agentaddress->scountry) {
                $country = $this->acontract->agentaddress->scountry->name;
            }


            if ($this->acontract->agentaddress->street > '') {
                $agent_address = $this->acontract->agentaddress->street;
            }

            if ($this->acontract->agentaddress->pobox > '') {
                $agent_address .= ($agent_address>'' ? ', ' : '').$this->acontract->agentaddress->pobox;
            }

            if (!empty($this->acontract->agentaddress->zipcode) || !empty($this->agentaddress->agentaddress->place)) {
                $agent_address .= ($agent_address>'' ? ', ' : '').trim($this->acontract->agentaddress->zipcode . ' ' . $this->acontract->agentaddress->place);
            }

            if ($country > '') {
                $agent_address .= ($agent_address>'' ? ', ' : '').$country;
            }

        }

        /*
        <first name> <last name>, <address>,
        referred to hereafter also as the Artist,
        represented by <name, surname and address of the agent or business name of the agency representing the Artist>;
        */
        $textrun->addText(htmlspecialchars($artist_name), $this->styleFont_bold);
        if(!empty($artist_address)) {
            $textrun->addText(htmlspecialchars(', '.$artist_address));
        }

        $textrun->addTextBreak();
        $textrun->addText(htmlspecialchars('referred to hereafter also as the Artist'));

        if(!empty($agent_name)) {
            $textrun->addTextBreak();
            $textrun->addText(htmlspecialchars('represented by '.$agent_name));
            if(!empty($agent_address)) {
                $textrun->addText(htmlspecialchars(', '.$agent_address));
            }
        }

        $textrun->addText(htmlspecialchars(';'));

        return $textrun;
    }

    function getArtist_address() {

        $address = '';

        //saddresses.street, saddresses.zipcode_poboxsaddresses.pobox.

        if($this->acontract->artistaddress) {


            $country = '';
            if($this->acontract->artistaddress->scountry) {
                $country = $this->acontract->artistaddress->scountry->name;
            }


            if ($this->acontract->artistaddress->street > '') {
                $address = $this->acontract->artistaddress->street;
            }

            if ($this->acontract->artistaddress->pobox > '') {
                $address .= ($address>'' ? ', ' : '').$this->acontract->artistaddress->pobox;
            }

            if (!empty($this->acontract->artistaddress->zipcode) || !empty($this->artistaddress->artistaddress->place)) {
                $address .= ($address>'' ? ', ' : '').trim($this->acontract->artistaddress->zipcode . ' ' . $this->acontract->artistaddress->place);
            }

            if ($country > '') {
                $address .= ($address>'' ? ', ' : '').$country;
            }

        }

        return $address;
    }

    function getAgent_address() {

        $address = '';

        //saddresses.street, saddresses.zipcode_poboxsaddresses.pobox.

        if($this->acontract->agentaddress) {


            $country = '';
            if($this->acontract->agentaddress->scountry) {
                $country = $this->acontract->agentaddress->scountry->name;
            }


            if ($this->acontract->agentaddress->street > '') {
                $address = $this->acontract->agentaddress->street;
            }

            if ($this->acontract->agentaddress->pobox > '') {
                $address .= ($address>'' ? ', ' : '').$this->acontract->agentaddress->pobox;
            }

            if (!empty($this->acontract->agentaddress->zipcode) || !empty($this->agentaddress->agentaddress->place)) {
                $address .= ($address>'' ? ', ' : '').trim($this->acontract->agentaddress->zipcode . ' ' . $this->acontract->agentaddress->place);
            }

            if ($country > '') {
                $address .= ($address>'' ? ', ' : '').$country;
            }

        }

        return $address;
    }

    function getFirstReh() {

        $count = 0;
        $cdate = '';

        foreach ($this->arehearsals_contract as $adate) {
            $count++;
            if($count==1) {
                $cdate = $adate->date_->format(Configure::read('Formats.date'));
                $start = $this->reporttools->getTime($adate, false);

                $location = '';
                if($adate->locationaddress){
                    $location = $adate->locationaddress->place;
                    $location .= (!empty($location) && !empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->name1;
                }

                //‐	concerts:	<Date, Time, City, Hall>;
                $cdate .= (!empty($start) ? ', ' : '').$start;
                $cdate .= (!empty($location) ? ', ' : '').$location;
            } else {
                continue;
            }
        }

        return $cdate;
    }

    function getConcerts() {

        $textrun = new TextRun($this->styleIdent_2_75);

        $count = 0;
        $afpd = null;
        foreach ($this->apds_contract as $adate) {
            $count++;

            $cdate = $adate->date_->format(Configure::read('Formats.date'));
            $start = $this->reporttools->getTime($adate, false);

            $location = '';
            if($adate->locationaddress){
                $location = $adate->locationaddress->place;
                $location .= (!empty($location) && !empty($adate->locationaddress->place) ? ', ' : '') . $adate->locationaddress->name1;
            }

            //‐	concerts:	<Date, Time, City, Hall>;
            $cdate .= (!empty($start) ? ', ' : '').$start;
            $cdate .= (!empty($location) ? ', ' : '').$location;

            if($count>1) {
                $textrun->addTextBreak();
            } else {
                $textrun->addText(htmlspecialchars('‐'."\t".'concerts:'."\t"));
            }

            $textrun->addText(htmlspecialchars($cdate), array_merge($this->styleFont));

        }
        return $textrun;
    }

    function getConductors() {
        $textrun = new TextRun($this->styleIdent_2_75);

        $count = 0;
        if(!$this->l_conductor) {
            foreach($this->aconductors as $aconductor) {
                $count++;
                if($count>1) {
                    $textrun->addTextBreak();
                } else {
                    $textrun->addText(htmlspecialchars('‐'."\t".(sizeof($this->aconductors)>1 ? 'conductors' : 'conductor').':'."\t"));
                }
            }

            $textrun->addText(htmlspecialchars($aconductor['name']));
        }
        //$textrun->addText('');
        return $textrun;
    }

    function getSoloists() {
        $textrun = new TextRun($this->styleIdent_2_75);

        $count = 0;
        foreach($this->asoloists as $asoloist) {
            $count++;

            if($count>1) {
                $textrun->addTextBreak();
            } else {
                $textrun->addText(htmlspecialchars('‐'."\t".(sizeof($this->asoloists)>1 ? 'soloists' : 'soloist').':'."\t"));
            }

            $textrun->addText(htmlspecialchars($asoloist['name'].($asoloist['instrument']>'' ? ', ': '').$asoloist['instrument']), []);
        }

        $textrun->addText('');

        return $textrun;
    }

    function getProgram() {
        //$styleIdent = array('indentation' => array('left' => Converter::cmToTwip(1.27), 'right' => Converter::cmToTwip(0)), 'align' => 'left');

        //<for conductors: complete programme with names of soloists; for soloists: their programme with name of the conductor>;
        $textrun = new TextRun($this->styleIdent_2_75);

        $count = 0;
        foreach ($this->aprogram as $adate_work) {
            $aunderline = array();

            if(!$this->l_conductor) {
                $aunderline = $this->styleFont_underline;

                $l_played = false;

                if($adate_work->adatework_soloists) {
                    foreach ($adate_work->adatework_soloists as $soloist) {
                        if($soloist->artist_id == $this->acontract->artist_id) {
                            $l_played = true;
                        }
                    }
                }

                if(!$l_played) {
                    //continue;
                    $aunderline = array();
                }
            }

            $count++;


            //20241118 ONCUST-4383
            //bitte Titel 1 statt Titel 2 ausgeben
            //$title = ($adate_work->title2 > '' ? $adate_work->title2 : $adate_work->swork->title1);
            $title = $adate_work->swork->title1;

            //$composer = ($adate_work->swork->scomposer ? $adate_work->swork->scomposer->lastname : '');
            $composer = ($adate_work->swork->scomposer ? trim($adate_work->swork->scomposer->firstname.' '.$adate_work->swork->scomposer->lastname) : '');

            if($adate_work->swork->l_intermission==1) {
                $composer = '';
            }
            //$nduration = (int)substr($adate_work->duration, 0, 2) * 60 + (int)substr($adate_work->duration, 3, 2);
            //$cduration = $nduration . "'";

            if($count>1) {
                $textrun->addTextBreak();
            } else {
                $textrun->addText(htmlspecialchars('‐'."\t".'programme:'."\t"));
            }

            if($composer>'') {
                $textrun->addText(htmlspecialchars($composer.': '), array_merge($this->styleFont, $aunderline));
            }

            if(!empty($title)) {
                $textrun->addText(htmlspecialchars($title), array_merge($this->styleFont, $aunderline));
            }
        }

        return $textrun;
    }


    function getClauses_Oth_arr() {
        $textrun = new TextRun();
        $count = 0;



        if($this->acontract->arrangements>'') {
            $count++;
            $this->addMemo($textrun, $this->acontract->arrangements, $this->styleFont);
        }

        $this->addClauses($textrun, $count, $this->aclauses_OTHER);

        return $textrun;
    }


    function addClauses($textrun, $count, $aclauses) {
        foreach($aclauses as $aclause) {
            $count++;

            $text = $aclause->text;
            $standard_text = ($aclause->scontractclause ? $aclause->scontractclause->text : '');
            $clause = (!empty($text) ? $text : $standard_text);

            if($count>1) {
                $textrun->addTextBreak();
                $textrun->addTextBreak();
            }
            $this->addMemo($textrun, $clause, $this->styleFont);

        }
    }


    function getClauses_HON() {

        $textrun = new TextRun();
        $count = 0;
        $this->addClauses($textrun, $count, $this->aclauses_HON);
        return $textrun;
    }


    function getHonorar_curr_text() {
        if($this->l_CHF) {
            return '';
        } else {
            return 'or, in case of a currency other than CHF: equal to the consideration in Swiss francs at the time of payment of';
        }
    }



    function getConcerts_fee() {

        // 20240212
        //bei Gagen für mehrere Konzerte diese einzeln listen und mit Datum versehen und den Satz s. Vorlage dafür nutzen; wenn kein zugeordnetes Datum, den Satz löschen.
        $textrun = new TextRun();
        if(sizeof($this->adates_fee)<=1) {
            return $textrun;
        }

        $textrun ->addTextBreak();
        $textrun ->addText(htmlspecialchars('Which is to be intended as '));
        $count = 0;

        foreach($this->adates_fee as $adate_fee) {
            $count++;

            $textrun ->addText(htmlspecialchars(($count>1 ? ($count==sizeof($this->adates_fee) ? ' and ' : ', ') : ''). $adate_fee['camount']));
        }

        $textrun ->addTextBreak();

        return $textrun;
    }

    function getTravel() {

        // 20240212
        //5. travel expenses = Kostenartgruppe Code via “Spese di viaggio”

        $amount = 0;
        $currency = '';

        foreach($this->acontract->aexpenses as $aexpense) {
            $expensetypegroup_code = '';
            if($aexpense->sexpensetype && $aexpense->sexpensetype->sexpensetypegroup) {
                $expensetypegroup_code = $aexpense->sexpensetype->sexpensetypegroup->code;
            }

            if(!(mb_strtoupper($expensetypegroup_code) == mb_strtoupper('via'))) {
                continue;
            }

            $currency = ($aexpense->scurrency ? $aexpense->scurrency->code : '');
            if(empty($currency)) {
                $currency = '';
            }

            $amount += $aexpense->number_ * $aexpense->amount;
        }

        return $currency . ' ' .number_format($amount, 2, ',', '.') ;
    }


    function getHotel() {
        $hotel = '';

        foreach($this->acontract->acontract_schedules as $itinerary) {
            if($itinerary->saddress) {
                foreach ($itinerary->saddress->saddress_addressgroups as $saddress_addressgroup) {
                    //6. hotel: Adresse Hotel aus Vertrag/Itinary = Adressgruppe Code Hotel
                    if ($saddress_addressgroup->saddressgroup && mb_strtoupper($saddress_addressgroup->saddressgroup->code) == mb_strtoupper('hotel')) {
                        $zip_place = trim($itinerary->saddress->zipcode . ' ' . $itinerary->saddress->place);
                        $hotel = $itinerary->saddress->name1 .
                            (!empty($itinerary->saddress->street) ? ', ' : '') . $itinerary->saddress->street .
                            (!empty($zip_place) ? ', ' : '') . $zip_place;
                    }
                }
            }
        }
            return $hotel;
    }

    function getHonorar() {

        $textrun = new TextRun(array('align'=>'center'));

        $this->honorar = 0;
        $this->honorar_iw = 0;

        $count = 0;
        foreach($this->acurrencies as $currency=>$namount) {
            $count++;


            $amount = number_format($namount, 2, ',', '.') . ' '. $currency;
            $amount .= '('.$this->reporttools->AmountInWords($namount).')';
            if($count>1) {
                $textrun->addTextBreak();
            }
            //$expenses->addText($amount.' '.$currency);
            $textrun->addText(htmlspecialchars($amount), $this->styleFont_bold);
        }

        return $textrun;

    }

    public function addMemo($section, $text) {

        $text = str_replace("\r\n", "\n", $text);
        $textlines = explode("\n", $text);
        $count = 0;
        foreach($textlines as $line) {
            //print_r($line.'#');
            $count++;
            if($count>1) {
                $section->addTextBreak();
            }
            $section->addText(htmlspecialchars($line), $this->styleFont);
        }
    }

    function getMinMaxDays($adates)
    {
        $minmax = '';

        $count = 0;
        foreach ($adates as $date) {
            $count++;
            if($count==1) {
                $date_min = $date->date_;
            }
            $date_max = $date->date_;
        }

        if (sizeof($adates)>0) {
            $y_min = $date_min->format('Y');
            $m_min = $this->reporttools->getMonthName($date_min);
            $d_min = $date_min->format('d');

            $y_max = $date_max->format('Y');
            $m_max = $this->reporttools->getMonthName($date_max);
            $d_max = $date_max->format('d');

            $minmax =
                $d_min.'.'.
                ($m_min<>$m_max ? $m_min.' ' : '').
                ($y_min<>$y_max ? $y_min : '').
                '-'.
                $d_max.'. '.$m_max.' '.$y_max;
        }

        return $minmax;
    }
}

